# -*- coding: UTF8 -*-
import json
config = {
    "in_test_env": False,
    "default_kess_client_name": "grpc_ad_index_adapter_Server",
    "grpc": {
      "client_map": {}, 
      "test": False,
      "server": {
        "thread_num": 100,
        "kess_name": "grpc_ad_index_adapter_Server",
        "port": 20022
      },
    },
     # 基于 grpc 的 tcpcopy, 可以设置 dryrun 环境
    "grpc_tcp_copy": {
      "default" : "online"
    },
    "dump_conf": {
      "table_dump_configs" : [
      # --------- charge database config ------------
      {
        "type": "ACCOUNT_BALANCE",
        "table_name": "ad_dsp_account_balance",
      },
      {
        "type": "ACCOUNT_DAY_CHARGE",
        "table_name": "ad_dsp_account_daily_charge",
      },
      {
        "type": "CAMPAIGN_DAY_CHARGE",
        "table_name": "ad_dsp_campaign_daily_charge",
      },
      {
        "type": "UNIT_DAY_CHARGE",
        "table_name": "ad_dsp_unit_daily_charge",
      },
      # ---------- default database config ------------
      {
        "type": "ACCOUNT",
        "table_name": "ad_dsp_account",
      },
      {
        "type": "AD_DSP_ACCOUNT_SUPPORT_INFO",
        "table_name": "ad_dsp_account_support_info",
      },
      {
        "type": "AD_APP",
        "table_name": "ad_dsp_app",
      },
      {
        "type": "UNIT_SUPPORT_INFO",
        "table_name": "ad_dsp_unit_support_info",
      },
      {
        "type": "CAMPAIGN",
        "table_name": "ad_dsp_campaign",
      },
      {
        "type": "UNIT",
        "table_name": "ad_dsp_unit",
      },
      {
        "type": "CREATIVE",
        "table_name": "ad_dsp_creative",
      },
      {
        "type": "AGENT",
        "table_name": "ad_dsp_agent",
      },
      {
        "type": "AGENT_ACCOUNT",
        "table_name": "ad_dsp_agent_account",
      },
      {
        "type": "TARGET",
        "table_name": "ad_dsp_target",
      },
      {
        "type": "TARGET_PAID_AUDIENCE",
        "table_name": "ad_dsp_target_paid_audience",
      },
      {
        "type": "PHOTO_STATUS",
        "table_name": "ad_dsp_photo",
      },
      {
        "type": "AD_POSITION",
        "table_name": "ad_dsp_position",
      },
      {
        "type": "AD_POSITION_RESOURCE",
        "table_name": "ad_dsp_position_resource",
      },
      {
        "type": "AD_POSITION_STRATEGY",
        "table_name": "ad_dsp_position_strategy",
      },
      {
        "type": "AD_CREATIVE_PREVIEW",
        "table_name": "ad_dsp_creative_preview",
      },
      {
        "type": "MATERIAL",
        "table_name": "ad_dsp_material",
      },
      {
        "type": "INDUSTRY_V3",
        "table_name": "ad_dsp_industry_v3",
      },
      {
        "type": "PKG_BG",
        "table_name": "ad_dsp_package_bg",
      },
      {
        "type": "RISK_CREATIVE_TARGET",
        "table_name": "ad_risk_creative_target",
      },
      {
        "type": "RISK_TARGET",
        "table_name": "ad_risk_target",
      },
      {
        "type": "RISK_NAME_LIST",
        "table_name": "ad_risk_name_list",
      },
      {
        "type": "AD_MATRIX_STYLE_MATERIAL",
        "table_name": "ad_matrix_style_material",
      },
      {
        "type": "AD_MATRIX_STYLE_MATERIAL_SCENE_RULE",
        "table_name": "ad_matrix_style_material_scene_rule",
      },
      {
        "type": "RISK_ACCOUNT_INITIATIVE",
        "table_name": "ad_risk_account_initiative",
      },
      {
        "type": "RISK_INDUSTRY_INITIATIVE",
        "table_name": "ad_risk_industry_initiative",
      },
      {
        "type": "RISK_PHOTO_INITIATIVE",
        "table_name": "ad_risk_photo_initiative",
      },
      {
        "type": "FACTORY_CREATIVE_INFO",
        "table_name": "factory_creative_info",
      },
      {
        "type": "RISK_UNIT_INITIATIVE",
        "table_name": "ad_risk_unit_initiative",
      },
      {
        "type": "RISK_INDUSTRY_WHITE_ACCOUNT",
        "table_name": "ad_risk_industry_white_account",
      },
      {
        "type": "TRACE_UTIL",
        "table_name": "ad_dsp_trace_util",
      },
      {
        "type": "MERCHANT_APP_UNIT",
        "table_name": "ad_dsp_merchant_app_unit",
      },
      {
        "type": "CARD_SHOW_DATA",
        "table_name": "ad_dsp_card_show_data",
      },
      {
        "type": "TRACE_API_DETECTION",
        "table_name": "ad_dsp_trace_api_detection",
      },
      {
        "type": "AD_DSP_COVER",
        "table_name": "ad_dsp_cover",
      },
      {
        "type": "UPLOAD_POPULATION_ORIENTATION",
        "table_name": "ad_dsp_upload_population_orientation",
      },
      {
        "type": "SITE_EXT_INFO",
        "table_name": "ad_dsp_site_ext_info",
      },
      {
        "type": "CREATIVE_BARRAGES",
        "table_name": "ad_dsp_creative_barrages",
      },
      {
        "table_name": "ad_dsp_unit_small_shop",
        "type": "AD_DSP_MERCHANT_SMALL_SHOP",
      },
      {
        "table_name": "ad_dsp_unit_small_shop_merchant_support_info",
        "type": "AD_DSP_UNIT_SMALL_SHOP_MERCHANT_SUPPORT_INFO",
      },
      {
        "table_name": "ad_dsp_small_shop_product_spu",
        "type": "AD_DSP_SMALL_SHOP_SPU",
      },
      {
        "table_name":"ad_dpa_category_target",
        "type": "DPA_CATEGORY_TARGET",
      },
      {
        "table_name": "ad_dsp_unit_merchant_support_info",
        "type": "MERCHANT_SUPPORT_INFO",
      },
      {
        "table_name": "ad_dsp_hosting_project",
        "type": "AD_DSP_HOSTING_PROJECT",
      },
      {
        "table_name": "ad_dsp_ecom_hosting_project",
        "type": "AD_DSP_ECOM_HOSTING_PROJECT",
      },
      {
        "table_name": "ad_dsp_ecom_hosting_project_creative_create_param",
        "type": "AD_DSP_ECOM_HOSTING_PROJECT_CREATIVE_CREATE_PARAM",
      },
      {
        "table_name": "ad_dsp_adv_card",
        "type": "AD_DSP_ADV_CARD",
      },
      {
        "table_name": "ad_dsp_live_stream_user_info",
        "type": "LIVE_STREAM_USER_INFO",
      },
      {
        "table_name": "ad_dsp_target_media",
        "type": "AD_DSP_TARGET_MEDIA",
      },
      {
        "table_name": "ad_risk_material_target",
        "type": "AD_RISK_MATERIAL_TARGET",
      },
      {
        "table_name": "ad_lp_page_das",
        "type": "AD_LP_PAGE_DAS",
      },
      {
        "table_name": "ad_dsp_campaign_fanstop_support_info",
        "type": "FANSTOP_CAMPAIGN_SUPPORT_INFO",
      },
      {
        "table_name": "ad_dsp_creative_fanstop_support_info",
        "type": "FANSTOP_CREATIVE_SUPPORT_INFO",
      },
      {
        "table_name": "ad_dsp_unit_fanstop_support_info",
        "type": "FANSTOP_UNIT_SUPPORT_INFO",
      },
      {
        "table_name": "ad_charge_balance",
        "type": "AD_CHARGE_BALANCE",
      },
      {
        "table_name": "ad_unit_range_charge",
        "type": "AD_UNIT_RANGE_CHARGE",
      },
      {
        "table_name": "ad_dsp_mini_app",
        "type": "AD_DSP_MINI_APP"
      },
      {
          "table_name": "ad_dsp_play_info",
          "type": "AD_DSP_PLAY_INFO",
      },
      {
          "table_name": "ad_dsp_winfo",
          "type": "AD_DSP_WINFO",
      },
      {
        "table_name": "ad_dsp_fanstop_live_hosting_project",
        "type": "AD_DSP_FANSTOP_LIVE_HOSTING_PROJECT",
      },
      {
        "table_name": "ad_crm_account_operator_define_label",
        "type": "AD_CRM_ACCOUNT_OPERATOR_DEFINE_LABEL",
      },
      {
          "table_name": "ad_dsp_negative_word",
          "type": "AD_DSP_NEGATIVE_WORD",
      },
      {
        "table_name": "ad_dpa_library",
        "type": "AD_DPA_LIBRARY"
      },
       # --------- aihosting database config ------------
      {
          "table_name": "ad_dsp_hosting_project_target",
          "type": "AD_DSP_HOSTING_PROJECT_TARGET",
      },
      {
        "table_name": "ad_dsp_hosting_project_fiction",
        "type": "AD_DSP_HOSTING_PROJECT_FICTION"
      },
       # --------- bonus config ------------     
      {
        "table_name": "ad_dsp_bonus_support_group",
        "type": "BONUS_SUPPORT_GROUP",
      },
      {
        "table_name": "ad_dsp_bonus_support_project",
        "type": "BONUS_SUPPORT_PROJECT"
      },
      {
        "type": "AD_UNIT_DAILY_CHARGE_TAG",
        "table_name": "ad_dsp_unit_daily_charge_tag",
      },
      {
        "table_name": "ad_fans_top_campaign_range_charge_tag",
        "type": "AD_CAMPAIGN_RANGE_CHARGE_TAG",
      },
      {
        "table_name": "ad_fans_top_unit_range_charge_tag",
        "type": "AD_UNIT_RANGE_CHARGE_TAG",
      },
      {
        "type": "AD_CAMPAIGN_DAILY_CHARGE_TAG",
        "table_name": "ad_dsp_campaign_daily_charge_tag",
      },
      {
        "type": "AD_ACCOUNT_DAILY_CHARGE_TAG",
        "table_name": "ad_dsp_account_daily_charge_tag",
      },
       # --------- style config ------------  
      {
        "table_name": "ad_style_material",
        "type": "AD_STYLE_MATERIAL",
      },
      {
        "table_name": "ad_style_material_bind",
        "type": "AD_STYLE_MATERIAL_BIND"
      },
      {
        "type": "AD_STYLE_TEMPLATE",
        "table_name": "ad_style_template",
      },
      {
        "table_name": "ad_style_template_scene_rule",
        "type": "AD_STYLE_TEMPLATE_SCENE_RULE",
      },
      {
        "table_name": "ad_matrix_style_material",
        "type": "AD_MATRIX_STYLE_MATERIAL",
      },
      {
        "type": "AD_MATRIX_STYLE_MATERIAL_SCENE_RULE",
        "table_name": "ad_matrix_style_material_scene_rule",
      },
      {
        "type": "AD_DPA_LIBRARY",
        "table_name": "ad_dpa_library",
      },
    ]
  }
}
print json.dumps(config, indent=2);
