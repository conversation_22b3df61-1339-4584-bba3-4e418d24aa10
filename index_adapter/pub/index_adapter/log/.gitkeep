#pragma once
#include <map>
#include <string>
#include <vector>

#include "teams/ad/index_builder/utils/builder.pb.h"
#include "teams/ad/index_builder/utils/kconf.h"
#include "teams/ad/index_builder/utils/message_stream.h"

namespace ks {
namespace index_builder {

class StreamMapBase {
 public:
  explicit StreamMapBase(std::string type) : type_(type) {}
  virtual ~StreamMapBase() {}
  virtual bool CheckLatestValidVersion(std::string* latest_version) = 0;
  virtual bool InitHdfsMessageStreamMap(const std::string& version,
                                        MessageStreamMap* stream_map) = 0;
  virtual bool CheckBenchmarkFiles(
      const std::string& version,
      kuaishou::ad::tables::DumpInfo* dump_info) = 0;

 protected:
  std::string type_;
};

class StreamMapPb : public StreamMapBase {
 public:
  explicit StreamMapPb(std::string type) : StreamMapBase(type) {
    auto stream_map_config = AdKconfUtil::streamMapConfig()->data();
    if (stream_map_config.name2map().find(type) !=
        stream_map_config.name2map().end()) {
      stream_map_config_ = stream_map_config.name2map().at(type);
      LOG(INFO) << stream_map_config_.ShortDebugString();
    } else {
      LOG_ASSERT(false) << "no exist streammap type: " << type_;
    }
  }
  virtual ~StreamMapPb() {}

  bool CheckLatestValidVersion(std::string* latest_version) override;
  bool InitHdfsMessageStreamMap(const std::string& version,
                                MessageStreamMap* stream_map) override;
  bool CheckBenchmarkFiles(const std::string& version,
                           kuaishou::ad::tables::DumpInfo* dump_info) override;

 private:
  std::vector<std::string> GetExpectType();
  StreamMap stream_map_config_;
};

class StreamMapMakerFactory {
 public:
  static StreamMapMakerFactory* GetInstance() {
    static StreamMapMakerFactory instance;
    return &instance;
  }
  StreamMapBase* GetStreamMap() {
    std::string stream_maker_type =
        AdKconfUtil::indexBuilderConf()->data().stream_maker_type();
    // 后续依靠不同 kconf value 走不同逻辑进行迭代
    if (!stream_maker_type.empty()) {
      return new StreamMapPb(stream_maker_type);
    } else {
      return new StreamMapPb("non exist!");
    }
  }
};

}  // namespace index_builder
}  // namespace ks
