#include <gflags/gflags.h>
#include <sstream>
#include "ks/serving_util/server_base.h"
#include "serving_base/server_base/server_status.h"
#include "teams/ad/ad_base/src/ksp/ad_web_request_handler.h"
#include "teams/ad/ad_base/src/ksp/dynamic_port.h"
#include "teams/ad/index_adapter/utils/dumper_controller.h"
#include "teams/ad/index_builder/multiform_filter/common_tools.h"

DEFINE_string(hdfs_base_url,
              "http://webhdfs-lt.corp.kuaishou.com:14000/webhdfs/v1",
              "hdfs base url");
DEFINE_string(hadoop_home_dir, "/home/<USER>/software/hadoop",
              "hadoop bin home dir");

DEFINE_int32(web_server_port, 20080, "the port for web service");
DEFINE_int32(web_thread_num, 1, "the number of threads for web service");
DEFINE_int32(rpc_server_port, 20090, "the port for grpc service");

class QueryHandler : public serving_base::WebRequestHandler {
 public:
  QueryHandler() {}
  ~QueryHandler() {}
  void ProcessRequest(net::HTTPSession *session) override {
    std::stringstream ss;
    ss << "server running...";
    session->response.data = ss.str();
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(QueryHandler);
};

namespace ks {
namespace index_adapter {
void WarmUp() { ks::index_builder::InitEnum2Type(); }
}  // namespace index_adapter
}  // namespace ks

int main(int argc, char **argv) {
  base::InitApp(&argc, &argv, "index_adapter");

  ks::index_adapter::WarmUp();

  ks::ad_base::DefaultAdWebService web_service(
      FLAGS_web_thread_num, []() -> serving_base::WebRequestHandlerDict * {
        ks::ad_base::DefaultAdWebServiceDict::CallDict handler;
        handler["/status"] = new QueryHandler();
        return new ks::ad_base::DefaultAdWebServiceDict(handler);
      });

  net::WebServer::Options web_server_option;
  web_server_option.port =
      ks::ad_base::DynamicPort::Instance().Port("AUTO_PORT0");
  web_server_option.backlog = 1024;
  if (web_server_option.port <= 0) {
    web_server_option.port = FLAGS_web_server_port;
    LOG(WARNING) << "may be not running in kcs mode! can't get port AUTO_PORT0 "
                 << "use gflag port " << FLAGS_web_server_port;
  }
  net::WebServer web_server(web_server_option, &web_service);
  web_server.Start();
  LOG(INFO) << "web server started";
  base::ServerStatus::Singleton()->SetStatusCode(
      base::ServerStatusCode::STARTING);

  ks::index_adapter::DataDumper dumper;
  if (dumper.Start()) {
    LOG(INFO) << "dump controller start ok, server status change to running";
    base::ServerStatus::Singleton()->SetStatusCode(
        base::ServerStatusCode::RUNNING);
    //等待停止的信号
    ks::ServerBase::Singleton()->WaitForSignal();
    base::ServerStatus::Singleton()->SetStatusCode(
        base::ServerStatusCode::STOPPING);
    LOG(INFO) << "ad index_adapter server begin quit";
    dumper.Stop();
  } else {
    LOG(ERROR) << "dump controller start failed!";
  }
  LOG(INFO) << "ad index_adapter server quit safety";
  web_server.Stop();
  LOG(WARNING) << "WebServer shutdown!";
  ::google::FlushLogFiles(::google::INFO);

  base::ServerStatus::Singleton()->SetStatusCode(
      base::ServerStatusCode::STOPPED);
}
