#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_adapter/utils/consumer/message_consumer.h"
#include "teams/ad/index_adapter/utils/kconf/kconf.h"

int main(int argc, char** argv) {
  LOG(INFO) << "enableAdIndexMutiFileTxtFormatConsumer: " <<
      ks::index_adapter::Kconf::enableAdIndexMutiFileTxtFormatConsumer();
  ::ks::index_builder::InitEnum2Type();
  ks::index_builder::TableConfigManager::GetInstance()->InitByKconf();
  std::shared_ptr<ks::index_adapter::MessageConsumer> message_consumer =
      std::shared_ptr<ks::index_adapter::MessageConsumer>(
      new ks::index_adapter::AdIndexMutiFileTxtFormatConsumer(
      "ad_dsp_account", "",
      "/home/<USER>/index_adapter/ad-index-adapter-hub-ktable_PROD/"));
  message_consumer->Prepare();
  for (int i = 0; i < 10; i++) {
      LOG(INFO) << "i: " << i;
      kuaishou::ad::AdInstance ad;
      ad.set_type(::kuaishou::ad::AdEnum_AdInstanceType::AdEnum_AdInstanceType_ACCOUNT);
      ad.set_binlog_time(111111);
      ad.set_process_time(i);
      ad.MutableExtension(kuaishou::ad::tables::Account::account_old)->set_id(i);
      ad.MutableExtension(kuaishou::ad::tables::Account::account_old)->set_agent_id(i);
      LOG(INFO) << "i: " << i << "ad: " << ad.ShortDebugString();
      message_consumer->Consume(ad);
  }

  message_consumer->Finish();
  kuaishou::ad::tables::DumpInfo dump_info;
  std::mutex mtx;
  message_consumer->UpdateTableInfo(&dump_info, &mtx);

  message_consumer = std::shared_ptr<ks::index_adapter::MessageConsumer>(
      new ks::index_adapter::AdIndexMutiFileTxtFormatConsumer(
      "ad_dsp_unit", "",
      "/home/<USER>/index_adapter/ad-index-adapter-hub-ktable_PROD/"));
  message_consumer->Prepare();
  for (int i = 0; i < 10; i++) {
      LOG(INFO) << "i: " << i;
      kuaishou::ad::AdInstance ad;
      ad.set_type(::kuaishou::ad::AdEnum_AdInstanceType::AdEnum_AdInstanceType_UNIT);
      ad.set_binlog_time(111111);
      ad.set_process_time(i);
      ad.MutableExtension(kuaishou::ad::tables::Unit::unit_old)->set_id(i);
      LOG(INFO) << "i: " << i << "ad: " << ad.ShortDebugString();
      message_consumer->Consume(ad);
  }

  message_consumer->Finish();
  dump_info.Clear();
  message_consumer->UpdateTableInfo(&dump_info, &mtx);
}
