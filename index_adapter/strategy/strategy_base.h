#pragma once

#include <functional>
#include <memory>
#include <string>
#include <unordered_map>

#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/data_build/schemafree/table_row.h"
#include "teams/ad/index_adapter/utils/kconf/kconf.pb.h"
#include "teams/ad/index_builder/admit_filters/admit_filter.pb.h"
#include "teams/ad/index_builder/multiform_filter/multiform_kconf.pb.h"
#include "teams/ad/index_builder/utils/common.pb.h"

namespace ks {
namespace index_adapter {

class StrategyBase;

using GetStrategyFunc = std::function<StrategyBase*()>;
using ks::index_builder::AdmitFilterEnum;
using ks::index_builder::DeployParam;
class StrategyBase {
 public:
  explicit StrategyBase(const std::string& name) : name_(name) {}
  virtual ~StrategyBase() {}
  virtual bool Process(kuaishou::ad::AdInstance* ad) = 0;
  virtual bool ProcessSchemaFree(ks::ad::build_service::TableRow* table_row) { return false; }
  virtual void Init() {}
  virtual std::string Name() { return name_; }
  virtual void SetShardNo(int64_t shard_no) {}

 protected:
  std::string name_;
};

class StrategyFactory{
 public:
  virtual ~StrategyFactory() = default;
  static StrategyFactory* GetInstance() {
    static StrategyFactory instance;
    return &instance;
  }
  virtual StrategyBase* GetStrategy(AdmitFilterEnum::Type index_param) {
    auto map_it = index_func_map_.find(index_param);
    if (map_it != index_func_map_.end()) {
      return (map_it->second)();
    }
    return nullptr;
  }
  virtual StrategyBase* GetStrategy(ValidParam::Param valid_param) {
    auto map_it = valid_func_map_.find(valid_param);
    if (map_it != valid_func_map_.end()) {
      return (map_it->second)();
    }
    return nullptr;
  }
  virtual StrategyBase* GetStrategy(DeployParam::Param deploy_param) {
    auto map_it = deploy_func_map_.find(deploy_param);
    if (map_it != deploy_func_map_.end()) {
      return (map_it->second)();
    }
    return nullptr;
  }
  virtual StrategyBase* GetStrategy(ShardParam::Param shard_param) {
    auto map_it = shard_func_map_.find(shard_param);
    if (map_it != shard_func_map_.end()) {
      return (map_it->second)();
    }
    return nullptr;
  }
  virtual std::shared_ptr<StrategyBase> GetShardStrategy(int32_t shard_id,
                                                         ks::index_builder::ShardOptions shard_options);

  virtual void RegisterStrategy(AdmitFilterEnum::Type index_param,
                                GetStrategyFunc func) {
    index_func_map_.emplace(index_param, func);
  }
  virtual void RegisterStrategy(ValidParam::Param valid_param,
                                GetStrategyFunc func) {
    valid_func_map_.emplace(valid_param, func);
  }
  virtual void RegisterStrategy(DeployParam::Param deploy_param,
                                GetStrategyFunc func) {
    deploy_func_map_.emplace(deploy_param, func);
  }
  virtual void RegisterStrategy(ShardParam::Param shard_param,
                                GetStrategyFunc func) {
    shard_func_map_.emplace(shard_param, func);
  }

 private:
  std::unordered_map<AdmitFilterEnum::Type, GetStrategyFunc> index_func_map_;
  std::unordered_map<ValidParam::Param, GetStrategyFunc> valid_func_map_;
  std::unordered_map<DeployParam::Param, GetStrategyFunc> deploy_func_map_;
  std::unordered_map<FieldParam::Param, GetStrategyFunc> field_func_map_;
  std::unordered_map<ShardParam::Param, GetStrategyFunc> shard_func_map_;
};
}  // namespace index_adapter
}  // namespace ks
