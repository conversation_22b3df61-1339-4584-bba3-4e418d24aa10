#pragma once
#include <algorithm>
#include <functional>
#include <set>
#include <string>
#include <unordered_map>
#include <vector>
#include "teams/ad/index_adapter/strategy/strategy_base.h"
#include "teams/ad/index_builder/multiform_filter/multiform_kconf.pb.h"

namespace ks {
namespace index_adapter {

class FieldProcessorV1 {
 public:
  FieldProcessorV1();
  explicit FieldProcessorV1(const std::string& conf_key);
  ~FieldProcessorV1() = default;
  void Process(kuaishou::ad::AdInstance* ad);
  void ClearField(google::protobuf::Message* msg);
  bool IsWorking() const;

 private:
  void Init(ks::index_builder::ProtoList proto_list);
  std::unordered_map<std::string, std::set<std::string>> proto_white_list_;
  std::unordered_map<std::string, std::set<std::string>> proto_black_list_;
};

class FieldFilterStrategy : public StrategyBase {
 public:
  explicit FieldFilterStrategy(const std::string& name,
                               const std::string& config_key)
      : StrategyBase(name), field_processor_(config_key) {}
  explicit FieldFilterStrategy(const std::string& name) : StrategyBase(name) {}
  void Init() override{};

  bool Process(kuaishou::ad::AdInstance* ad) override {
    if (field_processor_.IsWorking()) {
      field_processor_.Process(ad);
      return true;
    }

    return true;
  }
  FieldProcessorV1 field_processor_;
};

}  // namespace index_adapter
}  // namespace ks
