#include "teams/ad/index_adapter/strategy/field_strategy.h"
#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"
#include "teams/ad/index_builder/multiform_filter/multiform_kconf.h"

namespace ks {
namespace index_adapter {

using ks::index_builder::GetExtensionField;

FieldProcessorV1::FieldProcessorV1() {
  auto kconf_proto_list =
      ks::index_builder::MultiFormKconfUtil::protoWBList()->data();
  LOG(INFO) << kconf_proto_list.ShortDebugString();
  Init(kconf_proto_list);
}

FieldProcessorV1::FieldProcessorV1(const std::string& conf_key) {
  if (conf_key.empty()) {
    auto kconf_proto_list =
        ks::index_builder::MultiFormKconfUtil::protoWBList()->data();
    LOG(INFO) << kconf_proto_list.ShortDebugString();
    Init(kconf_proto_list);
  } else {
    auto kconf_map = ks::index_builder::MultiFormKconfUtil::multiProtoList()
                         ->data()
                         .multi_conf();
    auto map_it = kconf_map.find(conf_key);
    LOG_ASSERT(map_it != kconf_map.end())
        << "conf_key not found in kconf_map!!!";
    auto kconf_proto_list = map_it->second;
    LOG(INFO) << kconf_proto_list.ShortDebugString();
    Init(kconf_proto_list);
  }
}

void FieldProcessorV1::Init(ks::index_builder::ProtoList proto_list) {
  const auto& proto_white_lists = proto_list.proto_white_lists();
  for (const auto& item : proto_white_lists) {
    proto_white_list_[item.first].insert(item.second.fields().begin(),
                                         item.second.fields().end());
  }

  const auto& proto_black_lists = proto_list.proto_black_lists();
  for (const auto& item : proto_black_lists) {
    proto_black_list_[item.first].insert(item.second.fields().begin(),
                                         item.second.fields().end());
  }
}


void FieldProcessorV1::Process(kuaishou::ad::AdInstance* ad) {
  auto* message = GetExtensionField(ad);
  const google::protobuf::Reflection* extend_ref = message->GetReflection();
  const google::protobuf::Descriptor* extend_desc = message->GetDescriptor();
  ClearField(message);
  for (int i = 0; i < extend_desc->field_count(); ++i) {
    const google::protobuf::FieldDescriptor* to_field = extend_desc->field(i);
    if (!to_field || to_field->is_repeated()) {
      continue;
    }
    if (to_field->cpp_type() ==
        google::protobuf::FieldDescriptor::CPPTYPE_MESSAGE) {
      google::protobuf::Message* sub_message =
          extend_ref->MutableMessage(message, to_field);
      ClearField(sub_message);
    }
  }
}

void FieldProcessorV1::ClearField(google::protobuf::Message *message) {
  auto type_name = message->GetTypeName();
  auto white_map_it = proto_white_list_.find(type_name);
  if (white_map_it != proto_white_list_.end()) {
    const auto& single_proto_list = white_map_it->second;
    auto* ref = message->GetReflection();
    auto* desc = message->GetDescriptor();
    for (int i = 0; i < desc->field_count(); i++) {
      auto* field_desc = desc->field(i);
      if (single_proto_list.count(field_desc->name()) == 0) {
        ref->ClearField(message, field_desc);
      }
    }
  }
  auto black_map_it = proto_black_list_.find(type_name);
  if (black_map_it != proto_black_list_.end()) {
    const auto& single_proto_list = black_map_it->second;
    auto* ref = message->GetReflection();
    auto* desc = message->GetDescriptor();
    for (int i = 0; i < desc->field_count(); i++) {
      auto* field_desc = desc->field(i);
      if (single_proto_list.count(field_desc->name()) > 0) {
        ref->ClearField(message, field_desc);
      }
    }
  }
}

bool FieldProcessorV1::IsWorking() const {
  return proto_white_list_.size() > 0 || proto_black_list_.size() > 0;
}

}  // namespace index_adapter
}  // namespace ks
