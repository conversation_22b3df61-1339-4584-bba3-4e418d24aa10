#include "teams/ad/index_adapter/strategy/common_shard_strategy.h"

#include "teams/ad/index_builder/utils/table_config.h"

namespace ks {
namespace index_adapter {

using ks::index_builder::TableConfigManager;

CommonShardStrategy::CommonShardStrategy(int32_t shard_id, ShardOptions shard_options)
    : shard_id_(shard_id), shard_options_(shard_options), StrategyBase("common_shard_strategy") {
  shard_num_ = shard_options.shard_num();
  for (const auto &[table_name, table_opt] : shard_options.shard_table()) {
    auto type_name = TableConfigManager::GetInstance()->GetEnumNameByTableName(table_name);
    kuaishou::ad::AdEnum::AdInstanceType proto_type;
    if (kuaishou::ad::AdEnum::AdInstanceType_Parse(type_name, &proto_type)) {
      type_2_key_.emplace(proto_type, table_opt.shard_key());
    } else {
      LOG(ERROR) << "init CommonShardStrategy error, invalid table_name=" << table_name
                 << " not proto type for it";
    }
  }
  LOG(INFO) << "init common shard strategy success, shard_options=" << shard_options.ShortDebugString()
            << " shard_id=" << shard_id;
}

bool CommonShardStrategy::Process(kuaishou::ad::AdInstance *ad) {
  auto msg = ks::index_builder::GetExtensionField(ad);
  using ::google::protobuf::FieldDescriptor;
  auto ins_type = ad->type();
  auto it = type_2_key_.find(ins_type);
  if (it == type_2_key_.end()) return true;

  if (shard_num_ <= 0) return true;
  if (shard_id_ >= shard_num_) return false;

  auto shard_key = it->second;
  auto *des = msg->GetDescriptor();
  auto *ref = msg->GetReflection();
  auto field_des = des->FindFieldByName(shard_key);
  if (field_des == nullptr) {
    LOG_EVERY_N(ERROR, 10000) << "filter cb, field_des is nullptr, instance_type=" << ins_type
                              << " shard_field=" << shard_key;
    return false;
  }
  int64_t id = 0;
  switch (field_des->cpp_type()) {
    case FieldDescriptor::CPPTYPE_INT32:
      id = ref->GetInt32(*msg, field_des);
      break;
    case FieldDescriptor::CPPTYPE_INT64:
      id = ref->GetInt64(*msg, field_des);
      break;
    case FieldDescriptor::CPPTYPE_UINT32:
      id = ref->GetUInt32(*msg, field_des);
      break;
    case FieldDescriptor::CPPTYPE_UINT64:
      id = ref->GetUInt64(*msg, field_des);
      break;
    default:
      LOG_EVERY_N(ERROR, 10000) << "filter cb, not support field type, instance_type=" << ins_type
                                << " shard_field=" << shard_key << " field_type=" << field_des->cpp_type();
      return false;
  }

  auto shard_hit = false;
  if (id % shard_num_ == shard_id_) {
    shard_hit = true;
  }
  VLOG_EVERY_N(4, 10000) << "filter cb, finish, table_=" << ins_type << " shard_field=" << shard_key
                         << " id=" << id << " hit=" << shard_hit;

  return shard_hit;
}

}  // namespace index_adapter
}  // namespace ks
