#pragma once
#include <algorithm>
#include <string>
#include <vector>
#include "teams/ad/index_adapter/strategy/strategy_base.h"
#include "teams/ad/index_builder/admit_filters/admit_common.h"
#include "teams/ad/index_builder/multiform_filter/deploy_tools.h"
#include "teams/ad/index_builder/multiform_filter/instance_filter.h"
#include "teams/ad/index_builder/multiform_filter/multiform_kconf.h"
#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"
#include "teams/ad/index_builder/utils/extra_adapter/creative_adapter.h"
#include "teams/ad/index_builder/utils/extra_adapter/label_adapter.h"
#include "teams/ad/index_builder/utils/kconf.h"
namespace ks {
namespace index_adapter {

using ks::index_builder::GetExtensionField;
using ks::index_builder::DeployParam;

#define REGISTER_DEPLOY_PROCESS(DEPLOY_STRATEGY, FUNC, PB_ENUM)               \
  class DEPLOY_##DEPLOY_STRATEGY : public StrategyBase {                      \
   public:                                                                    \
    DEPLOY_##DEPLOY_STRATEGY(const std::string& name) : StrategyBase(name) {} \
    bool Process(kuaishou::ad::AdInstance* ad) override {                     \
      FUNC(ad);                                                               \
      return true;                                                            \
    }                                                                         \
    void Init() override{};                                                   \
  };                                                                          \
  __attribute__(                                                              \
      (constructor, weak)) void AddDeployStrategy_##DEPLOY_STRATEGY() {       \
    GetStrategyFunc func = []() -> StrategyBase* {                            \
      return new DEPLOY_##DEPLOY_STRATEGY(#DEPLOY_STRATEGY);                  \
    };                                                                        \
    StrategyFactory::GetInstance()->RegisterStrategy(PB_ENUM, func);          \
  }

REGISTER_DEPLOY_PROCESS(Ori, ks::index_builder::CreativeScoreExtOri, DeployParam::ORIGINAL)
REGISTER_DEPLOY_PROCESS(Ext1, ks::index_builder::CreativeScoreExt1, DeployParam::EXT1)
REGISTER_DEPLOY_PROCESS(Ext2, ks::index_builder::CreativeScoreExt2, DeployParam::EXT2)
REGISTER_DEPLOY_PROCESS(Ext3, ks::index_builder::CreativeScoreExt3, DeployParam::EXT3)
REGISTER_DEPLOY_PROCESS(Ext4, ks::index_builder::CreativeScoreExt4, DeployParam::EXT4)
REGISTER_DEPLOY_PROCESS(All, ks::index_builder::CreativeScoreExtAll, DeployParam::ALL)

class DeployStrategy : public StrategyBase {
 public:
  explicit DeployStrategy(const std::string& name) : StrategyBase(name) {}
  bool Process(kuaishou::ad::AdInstance* ad) override {
    if (ks::index_builder::AdKconfUtil::enableAdapterCreative()) {
      ks::index_builder::AdapterForCreativeServer::GetInstance()->ProcessForFull(ad);
    }
    if (ks::index_builder::AdKconfUtil::enableLabel()) {
      ks::index_builder::LabelAdapter::GetInstance()->ProcessForFull(ad);
    }
    return true;
  }
  void Init() override {}
};

}  // namespace index_adapter
}  // namespace ks
