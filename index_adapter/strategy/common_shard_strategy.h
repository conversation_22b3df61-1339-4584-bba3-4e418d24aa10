#pragma once

#include <algorithm>
#include <functional>
#include <set>
#include <string>
#include <unordered_map>
#include <vector>

#include "teams/ad/index_adapter/strategy/strategy_base.h"
#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"
#include "teams/ad/index_builder/utils/common.pb.h"

namespace ks {
namespace index_adapter {

using ks::index_builder::ShardOptions;
using kuaishou::ad::AdEnum;

class CommonShardStrategy : public StrategyBase {
 public:
  CommonShardStrategy(int32_t shard_id, ShardOptions shard_options);
  virtual ~CommonShardStrategy() = default;

  bool Process(kuaishou::ad::AdInstance* ad) override;

 private:
  int32_t shard_id_{0};
  int32_t shard_num_{0};
  ShardOptions shard_options_;
  std::unordered_map<AdEnum::AdInstanceType, std::string> type_2_key_;
};

}  // namespace index_adapter
}  // namespace ks
