#pragma once
#include <algorithm>
#include <vector>
#include <string>
#include <set>
#include <functional>
#include "teams/ad/index_adapter/strategy/strategy_base.h"
#include "teams/ad/index_adapter/utils/kconf/kconf.h"
#include "teams/ad/index_builder/multiform_filter/common_tools.h"
#include "teams/ad/index_builder/multiform_filter/multiform_kconf.h"
#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"
namespace ks {
namespace index_adapter {

using kuaishou::ad::AdEnum;

/*
  没有返回 -1,其他返回 unit_id
*/
static int64_t GetUnitId(kuaishou::ad::AdInstance* ad) {
  auto type = ad->type();
  static const std::set<AdEnum::AdInstanceType> check_type{
      AdEnum::UNIT, AdEnum::CREATIVE,
      AdEnum::AD_DSP_UNIT_SMALL_SHOP_MERCHANT_SUPPORT_INFO,
      AdEnum::FANSTOP_UNIT_SUPPORT_INFO, AdEnum::FANSTOP_CREATIVE_SUPPORT_INFO};
  if (check_type.count(type) == 0) {
    return -1;
  }
  int64_t unit_id = -1;
  if (type == AdEnum::UNIT) {
    const auto& unit = ad->GetExtension(kuaishou::ad::tables::Unit::unit_old);
    unit_id = unit.id();
  } else if (type == AdEnum::CREATIVE) {
    const auto& creative =
        ad->GetExtension(kuaishou::ad::tables::Creative::creative_old);
    unit_id = creative.unit_id();
  } else if (type == AdEnum::AD_DSP_UNIT_SMALL_SHOP_MERCHANT_SUPPORT_INFO) {
    const auto& merchant_support_info = ad->GetExtension(
        kuaishou::ad::tables::UnitSmallShopMerchantSupportInfo::
            unit_small_shop_merchant_support_info_old);
    unit_id = merchant_support_info.unit_id();
  } else if (type == AdEnum::FANSTOP_UNIT_SUPPORT_INFO) {
    const auto& unit_fanstop_support_info =
        ad->GetExtension(kuaishou::ad::tables::UnitFanstopSupportInfo::
                             unit_fanstop_support_info_old);
    unit_id = unit_fanstop_support_info.unit_id();
  } else if (type == AdEnum::FANSTOP_CREATIVE_SUPPORT_INFO) {
    const auto& creative_fanstop_support_info =
        ad->GetExtension(kuaishou::ad::tables::CreativeFanstopSupportInfo::
                             creative_fanstop_support_info_old);
    unit_id = creative_fanstop_support_info.unit_id();
  }
  return unit_id;
}

class BaseShardStrategy : public StrategyBase {
 public:
  explicit BaseShardStrategy(const std::string& name) : StrategyBase(name) {
    shard_num_ = 10;
  }
  void Init() override {};
  bool Process(kuaishou::ad::AdInstance* ad) override {
    if (shard_no_ == -1) {
      return true;
    }
    int64_t account_id = ks::index_builder::GetAccountIdOfAdInstanceV3(ad);
    if (account_id == -1) {
      return true;
    }
    if (account_id % shard_num_ == shard_no_) {
      return true;
    } else {
      return false;
    }
  }
  void SetShardNo(int64_t shard_no) override { shard_no_ = shard_no; }

 protected:
  int64_t shard_no_{-1};
  int64_t shard_num_{1};
};


class StyleShardStrategy : public StrategyBase {
 public:
  explicit StyleShardStrategy(const std::string& name) : StrategyBase(name) {
    shard_num_ = 2;
  }
  void Init() override {};
  bool Process(kuaishou::ad::AdInstance* ad) override {
    if (shard_no_ == -1) {
      return true;
    }
    auto type = ad->type();
    int64_t key_id = -1;
    if (type == kuaishou::ad::AdEnum::AD_STYLE_MATERIAL) {
      const auto& style_material = ad->GetExtension(
          kuaishou::ad::tables::AdStyleMaterial::ad_style_material_old);
      key_id = style_material.id();
    } else if (type == kuaishou::ad::AdEnum::AD_DPA_STYLE) {
      const auto& dpa_style_material =
          ad->GetExtension(kuaishou::ad::tables::AdDpaStyle::ad_dpa_style_old);
      key_id = dpa_style_material.id();
    } else if (type == kuaishou::ad::AdEnum::AD_MATRIX_STYLE_MATERIAL) {
      const auto& matrix_style_material =
          ad->GetExtension(kuaishou::ad::tables::AdMatrixStyleMaterial::
                               ad_matrix_style_material_old);
      key_id = matrix_style_material.id();
    }
    if (key_id == -1) {
      return true;
    }

    auto shard_id = key_id % shard_num_;
    if (shard_id == shard_no_) {
      return true;
    } else {
      return false;
    }
  }
  void SetShardNo(int64_t shard_no) override { shard_no_ = shard_no; }

 protected:
  int64_t shard_no_{-1};
  int64_t shard_num_{1};
};

class AdCacheShardStrategy : public StrategyBase {
 public:
  explicit AdCacheShardStrategy(const std::string& name) : StrategyBase(name) {
    shard_num_ = 4;
  }
  void Init() override {};
  bool Process(kuaishou::ad::AdInstance* ad) override {
    if (shard_no_ == -1) {
      return true;
    }
    auto shard_id =
        ks::index_builder::GetAccountIdOfAdInstanceV2(ad) % shard_num_;

    if (shard_id == shard_no_) {
      return true;
    } else {
      return false;
    }
  }
  void SetShardNo(int64_t shard_no) override { shard_no_ = shard_no; }

 protected:
  int64_t shard_no_{-1};
  int64_t shard_num_{1};
};

class AdColdShardStrategy : public StrategyBase {
 public:
  explicit AdColdShardStrategy(const std::string& name) : StrategyBase(name) {
    shard_num_ = 3;
  }
  void Init() override {};
  bool Process(kuaishou::ad::AdInstance* ad) override {
    if (shard_no_ == -1) {
      return true;
    }
    int64_t unit_id = GetUnitId(ad);
    if (unit_id == -1) return true;
    auto shard_id = unit_id % shard_num_;

    if (shard_id == shard_no_) {
      return true;
    } else {
      return false;
    }
  }
  void SetShardNo(int64_t shard_no) override { shard_no_ = shard_no; }

 protected:
  int64_t shard_no_{-1};
  int64_t shard_num_{1};
};

class BidwordSearchShardStrategy : public StrategyBase {
 public:
  explicit BidwordSearchShardStrategy(const std::string& name) : StrategyBase(name) {
    shard_num_ = 4;
  }
  void Init() override {};
  bool Process(kuaishou::ad::AdInstance* ad) override {
    if (shard_no_ == -1) {
      return true;
    }
    auto type = ad->type();
    static const std::set<AdEnum::AdInstanceType> check_type{
        AdEnum::UNIT, AdEnum::CREATIVE, AdEnum::AD_DSP_WINFO};
    if (check_type.count(type) == 0) {
      return true;
    }
    int64_t unit_id = 0;
    if (type == AdEnum::UNIT) {
      const auto& unit = ad->GetExtension(kuaishou::ad::tables::Unit::unit_old);
      unit_id = unit.id();
    } else if (type == AdEnum::CREATIVE) {
      const auto& creative = ad->GetExtension(kuaishou::ad::tables::Creative::creative_old);
      unit_id = creative.unit_id();
    } else if (type == AdEnum::AD_DSP_WINFO) {
      const auto& winfo = ad->GetExtension(kuaishou::ad::tables::AdDspWinfo::ad_dsp_winfo_old);
      unit_id = winfo.unit_id();
    }
    auto shard_id = unit_id % shard_num_;
    if (shard_id == shard_no_) {
      return true;
    } else {
      return false;
    }
  }
  void SetShardNo(int64_t shard_no) override { shard_no_ = shard_no; }

 protected:
  int64_t shard_no_{-1};
  int64_t shard_num_{1};
};

class AdShardEightStrategy : public StrategyBase {
 public:
  explicit AdShardEightStrategy(const std::string& name) : StrategyBase(name) {
    shard_num_ = 8;
  }
  void Init() override{};
  bool Process(kuaishou::ad::AdInstance* ad) override {
    if (shard_no_ == -1) {
      return true;
    }
    int64_t unit_id = GetUnitId(ad);
    if (unit_id == -1) return true;
    auto shard_id = unit_id % shard_num_;
    if (shard_id == shard_no_) {
      return true;
    } else {
      return false;
    }
  }
  void SetShardNo(int64_t shard_no) override { shard_no_ = shard_no; }

 protected:
  int64_t shard_no_{-1};
  int64_t shard_num_{1};
};

#define REGISTER_SHARD_PROCESS(SHARDSTRATEGY, PB_ENUM)                         \
  __attribute__((constructor, weak)) void AddShardStrategy_##SHARDSTRATEGY() { \
    GetStrategyFunc func = []() -> StrategyBase* {                             \
      return new SHARDSTRATEGY(#SHARDSTRATEGY);                                \
    };                                                                         \
    StrategyFactory::GetInstance()->RegisterStrategy(PB_ENUM, func);           \
  }

REGISTER_SHARD_PROCESS(BaseShardStrategy, ShardParam::SHARD_10)
REGISTER_SHARD_PROCESS(StyleShardStrategy, ShardParam::SHARD_STYLE_MATERIAL_2)
REGISTER_SHARD_PROCESS(AdCacheShardStrategy, ShardParam::SHARD_ADCACHE_4)
REGISTER_SHARD_PROCESS(AdColdShardStrategy, ShardParam::SHARD_COLD_3)
REGISTER_SHARD_PROCESS(BidwordSearchShardStrategy, ShardParam::SHARD_BIDWORD_4)
REGISTER_SHARD_PROCESS(AdShardEightStrategy, ShardParam::SHARD_ALL_SCENE_8)


}  // namespace index_adapter
}  // namespace ks
