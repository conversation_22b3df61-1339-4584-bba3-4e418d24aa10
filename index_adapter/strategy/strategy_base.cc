#include "teams/ad/index_adapter/strategy/strategy_base.h"

#include <memory>

#include "glog/logging.h"
#include "teams/ad/index_adapter/strategy/common_shard_strategy.h"

namespace ks {
namespace index_adapter {

std::shared_ptr<StrategyBase> StrategyFactory::GetShardStrategy(int32_t shard_id,
                                                                ShardOptions shard_options) {
  auto shard_num = shard_options.shard_num();
  if (shard_num <= 0 || shard_num < shard_id) {
    LOG(INFO) << "invalid shard options, skip it, shard_id=" << shard_id
              << " shard_options=" << shard_options.ShortDebugString();
    return nullptr;
  }
  return std::make_shared<CommonShardStrategy>(shard_id, shard_options);
}

}  // namespace index_adapter
}  // namespace ks
