#pragma once
#include <algorithm>
#include <cstdint>
#include <unordered_set>
#include <vector>
#include <string>
#include <set>
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/index_adapter/strategy/strategy_base.h"
#include "teams/ad/index_builder/multiform_filter/common_tools.h"
#include "teams/ad/index_builder/multiform_filter/instance_filter.h"
#include "teams/ad/index_builder/multiform_filter/ad_filter.h"
#include "teams/ad/index_builder/multiform_filter/fanstop_filter.h"
#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"
namespace ks {
namespace index_adapter {

using ks::index_builder::GetExtensionField;
// 格式转化
class ConvertStrategy : public StrategyBase {
 public:
  explicit ConvertStrategy(const std::string& name) : StrategyBase(name) {}
  void Init() override {}
  bool Process(kuaishou::ad::AdInstance* ad) override {
    ks::index_builder::TableParserProcess(ad);
    ks::index_builder::TableValidate(ad);
    static const std::unordered_set<kuaishou::ad::AdEnum::AdInstanceType> tag_type{
        kuaishou::ad::AdEnum::UNIT, kuaishou::ad::AdEnum::CREATIVE,
        kuaishou::ad::AdEnum::CAMPAIGN, kuaishou::ad::AdEnum::ACCOUNT};
    if (tag_type.count(ad->type()) != 0) {
      int64_t tag = 0;
      if (index_builder::IsAmdData(ad)) {
        tag |= kuaishou::ad::AdEnum::INNER_HARD;
      }
      if (index_builder::IsFansTopData(ad)) {
        tag |= kuaishou::ad::AdEnum::INNER_SOFT;
      }
      auto *msg = GetExtensionField(ad);
      const auto *actual_ref = msg->GetReflection();
      const auto *actual_desc = msg->GetDescriptor();
      auto field = actual_desc->FindFieldByName("index_bit_tag");
      if (!field) {
        return true;
      }
      actual_ref->SetInt64(msg, field, tag);
    }
    // mark external/internal
    static const std::set<kuaishou::ad::AdEnum::AdInstanceType> check_type{
        kuaishou::ad::AdEnum::UNIT, kuaishou::ad::AdEnum::CREATIVE,
        kuaishou::ad::AdEnum::CAMPAIGN};
    if (check_type.count(ad->type()) > 0) {
      auto *msg = GetExtensionField(ad);
      const auto *actual_ref = msg->GetReflection();
      const auto *actual_desc = msg->GetDescriptor();
      auto field = actual_desc->FindFieldByName("circulation_type");
      if (!field) {
        return true;
      }
      bool is_bid_word = ks::index_builder::IsSearchOnlyData(ad);
      if (is_bid_word) {
        actual_ref->SetEnumValue(msg, field,
                                 kuaishou::ad::AdEnum::BID_SEARCH_ONLY_TYPE);
        return true;
      }
      bool is_internal = ks::index_builder::IsInteralDataOnly(ad);
      if (is_internal) {
        actual_ref->SetEnumValue(msg, field,
                                 kuaishou::ad::AdEnum::INTERNAL_CIRCULATION_TYPE);
      } else {
        actual_ref->SetEnumValue(msg, field,
                                 kuaishou::ad::AdEnum::EXTERNAL_CIRCULATION_TYPE);
      }
    }
    return true;
  }
};
}  // namespace index_adapter
}  // namespace ks
