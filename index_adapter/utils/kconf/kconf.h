#pragma once

#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/index_adapter/utils/kconf/kconf.pb.h"
#include "teams/ad/index_builder/admit_filters/admit_filter.pb.h"
#include "teams/ad/index_builder/utils/builder.pb.h"
#include "teams/ad/index_message_proxy/kconf/kconf.h"

namespace ks {
namespace index_adapter {
using namespace ks::ad_base::kconf;  //NOLINT
class Kconf {
 public:
  DEFINE_PROTOBUF_NODE_KCONF(TableConfig, ad.index_builder, tableConfig);
  DEFINE_PROTOBUF_NODE_KCONF(AdapterConfig, ad.index_builder, adapterConfig);
  DEFINE_PROTOBUF_NODE_KCONF(StreamMaps, ad.index_builder, streamMapAdapter);
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, disableUploadToHdfs, false);
  DEFINE_BOOL_KCONF_NODE(
      ad.index_builder, enableDupCheck,
      false);  //  开启重复检测, AD_DSP_UNIT_SMALL_SHOP_MERCHANT_SUPPORT_INFO
              //  有双写问题
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableDumpOnKfs,
                         false);  //  将选择 Kfs 为导出位置
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableLoadFromKfs,
                         false);  //  将选择 kfs 为加载源
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableAdIndexMutiFileTxtFormatConsumer,
                         false);  //  导出 txtformat 格式数据
  DEFINE_INT64_KCONF_NODE(ad.index_builder, indexDumpShardSize,
                          0);  //  索引分片数据大小, 0 为不分片
  DEFINE_INT64_KCONF_NODE(
      ad.index_builder, taskBuilderLimit,
      2.5 * 3600 *
          1000);  //  开启 target 重复检测 纵级联时开启,否则内存可能有问题
  DEFINE_BOOL_KCONF_NODE(
      ad.index_builder, enableMaxDumpThread,
      false);  //  dump 线程开到最大
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableExeuntInactiveAccount, false);
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableMergeWildroseData, false);
  DEFINE_INT64_KCONF_NODE(ad.index_builder, accountInactiveDaysConfig, 30);  // 粉条非活跃账户退场阈值，单位天
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableDasVersionMs, false);
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, disableDump, false);
};
}  // namespace index_adapter
}  // namespace ks
