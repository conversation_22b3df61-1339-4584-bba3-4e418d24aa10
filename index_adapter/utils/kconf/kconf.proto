syntax = "proto3";
package ks.index_adapter;
import "teams/ad/ad_proto/kuaishou/ad/ad_inc.proto";
import "teams/ad/index_builder/utils/builder.proto";
import "teams/ad/index_builder/admit_filters/admit_filter.proto";
import "teams/ad/index_builder/multiform_filter/multiform_kconf.proto";
option cc_enable_arenas = true;


message ValidParam {
  enum Param {
    FILTER = 0;
    UNFITLER = 1;
  }
}

message FieldParam {
  enum Param {
    UNKNOWN_FIELD_PARAM = 0;
    AD_INDEX = 1;
    FORWARD = 2;
    COLLECTIVE = 3;
  }
}

message ShardParam {
  enum Param {
    NO_SHARD = 0;
    SHARD_10 = 1;
    SHARD_STYLE_MATERIAL_2 = 2;
    SHARD_ADCACHE_4 = 3;
    SHARD_COLD_3 = 4;
    SHARD_BIDWORD_4 = 5;
    SHARD_ALL_SCENE_8 = 6;
  }
}

message AdapterConfig {
  ks.index_builder.DeployParam.Param deploy_param = 1;  //  对应部署类型
  string output_dir = 2;  //  输出目录
  string stream_map_type = 3;  //  输入类型
  repeated string tables = 4;  // 需要输出的目录
  ks.index_builder.AdmitFilterEnum.Type filter_param = 5;  //  业务过滤类型
  ValidParam.Param valid_param = 6;  //  有效字段过滤
  FieldParam.Param field_param = 7;  //  字段过滤
  repeated string p2p_dirs = 8;  // p2p推送目录
  bool valid_check_skip = 9; //  是否跳过 valid check
  ShardParam.Param shard_param = 10; //  分片策略
  repeated int64 shard_no_vec = 11; //  分片编号
  string hot_data_path = 12; // 预热数据 hdfs 路径
  bool dump_table_lite = 13; // dump 为 table_lite 格式
  // 空字符串默认走kconf, 其他情况走 https://kconf.corp.kuaishou.com/#/ad/index_message_proxy/multiProtoList
  string white_list_config_key = 14;
  string kafka2hive_key = 15; // kafka2hive config key
  string creative_adapter_key = 16; // CreativeAdapterConfigs key
  bool use_aggr_data = 17;
  bool enable_ktable = 18;  // 是否产出 ktable 格式全量
}

message TableConfig {
  repeated ks.index_builder.TableDumpConfig table_dump_configs = 1;
}

message FilterTables {
  repeated string tables = 1;
}

message StreamMap {
  // 输入文件 HDFS 路径  如 ad_server->[/home/<USER>/das/benchmark_online_platform/base, /home/<USER>/benchmark/charge]
  repeated string input_dirs = 1;
  // 封禁某些目录文件输入 如 "/home/<USER>/das/benchmark_online_platform/base" : [ad_dsp_creative, ad_dsp_target]
  map<string, FilterTables> forbid_input_hdfs = 2;
  // 重定向 如 ad_dsp_creative_advanced_programmed->ad_dsp_creative 进行输出
  map<string, string> redirections = 3;
}

// 根据类型选种类
message StreamMaps {
  map<string, StreamMap> name2map = 1;
}
