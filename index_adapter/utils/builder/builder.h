#pragma once

#include <memory>
#include <string>
#include <vector>
#include <set>
#include <unordered_map>

#include "kenv/kenv.h"
#include "gflags/gflags.h"
#include "absl/strings/substitute.h"
#include "base/file/file_path.h"
#include "teams/ad/engine_base/utils/deploy_variable.h"
#include "teams/ad/index_adapter/utils/consumer/consumer_manager_base.h"
#include "teams/ad/index_adapter/utils/kconf/kconf.h"
#include "teams/ad/index_adapter/utils/perf_manager/perf_manager.h"
#include "teams/ad/index_adapter/strategy/strategy_common.h"
#include "teams/ad/index_adapter/utils/adapter_stream_map.h"
#include "teams/ad/index_builder/utils/unified_config_manager/unified_adapter_config.h"

DECLARE_bool(enable_exeunt_inactive_account);

namespace ks {
namespace index_adapter {

using ks::engine_base::DeployVariable;

class Builder {
 public:
  Builder() {}
  explicit Builder(const std::string path) : base_dir_(path) {}
  ~Builder();

  // das data ready to build index
  bool IsReadyImp();

  bool IsReady() {
    if (!is_valid_.load()) {
      return false;
    }
    is_valid_.store(IsReadyImp());
    return is_valid_.load();
  }

  // version detect
  virtual bool VersionDetectAndUpdate();

  // init comsumer && stream
  bool PrepareImp();

  bool Prepare() {
    if (!is_valid_.load()) {
      return false;
    }
    is_valid_.store(PrepareImp());
    return is_valid_.load();
  }

  // consumer_manager 处理收尾
  bool Finish();

  bool BuilderRun();

  bool StatusCheck() {return true;}

  // 设置 das 数据版本
  void SetDasVersion(const std::string& das_version) {
    das_version_ = das_version;
  }

  void SetStreamMap(ks::index_builder::index_base::MessageStreamMap stream_map) {
    stream_map_ = stream_map;
  }

  std::vector<std::string> GetOutputTablesOfProtoName(std::string proto_name);

  void RegisterTablesOfProtoName(std::string proto_name, int times = 1) {
    auto tables = GetOutputTablesOfProtoName(proto_name);
    for (int i = 0; i < times; i++) {
      for (const auto& table : tables) {
        consumer_manager_->RegisterTable(table);
      }
    }
  }

  void UnRegisterTablesOfProtoName(std::string proto_name, int times = 1) {
    auto tables = GetOutputTablesOfProtoName(proto_name);
    for (int i = 0; i < times; i++) {
      for (const auto& table : tables) {
        consumer_manager_->UnRegisterTable(table);
      }
    }
  }

  void SingleStreamRun(ks::index_builder::index_base::MessageStreamPtr hdfs_stream);

  void SetSharedNo(int64_t shard_no) { shard_no_ = shard_no; }

  bool Consumer(const kuaishou::ad::AdInstance* input_ad, std::string* filter_reason = nullptr);

  int GetShardId() const { return shard_no_; }

 protected:
  void Init();
  std::string GetBaseDir() {
    return base::FilePath(base_dir_).Append(GetPrefix()).ToString();
  }

  std::string GetUploadDir() {
    return base::FilePath(base_upload_path_).Append(GetPrefix()).ToString();
  }

  std::string GetPrefix() {
    const auto& ksn = ks::infra::KEnv::GetKWSInfo()->GetKSN();
    const auto& stage = ks::infra::KEnv::GetKWSInfo()->GetServiceStage();
    const auto& group = ks::infra::KEnv::GetKWSInfo()->GetServiceGroup();
    const auto& az = ks::infra::KEnv::GetKWSInfo()->GetAZ();
    auto prefix = absl::Substitute("$0_$1", ksn, stage);
    // yz 的部署作为备案，老路径不变，单独更改 yz 的导出路径
    if (!az.empty() && az == "YZ" && stage == "PROD") {
      prefix = absl::Substitute("$0_$1", prefix, az);
    }
    if (!group.empty()) {
      prefix = absl::Substitute("$0_$1", prefix, group);
    }
    if (shard_no_ != -1) {
      prefix = absl::Substitute("$0_$1", prefix, shard_no_);
    }
    return prefix;
  }

  void StrategyInit();

  // 通用过滤
  bool CommonAdAdmit(kuaishou::ad::AdInstance* input_ad);

  // 业务准入
  bool AdAdmit(kuaishou::ad::AdInstance* input_ad);

  // 其他准入判定 & 格式转化
  bool ValidateAndCovert(kuaishou::ad::AdInstance* input_ad, std::string* filter_reason = nullptr);

  bool DeployProcess(kuaishou::ad::AdInstance* input_ad);

  bool ConvertAdInstance(kuaishou::ad::AdInstance* input_ad);

  bool ShardAdmit(kuaishou::ad::AdInstance* input_ad);

  bool FieldFilter(kuaishou::ad::AdInstance* input_ad);

  bool ValidCheck(kuaishou::ad::AdInstance* input_ad);

  bool IsDupTarget(kuaishou::ad::AdInstance* input_ad);

  bool WTTableValidCheck(kuaishou::ad::AdInstance* input_ad);
  bool WTCreativeValidCheck(kuaishou::ad::AdInstance* input_ad);
  // 开屏 rtb 白名单打标
  void RtbWhiteList(kuaishou::ad::AdInstance* input_ad);

  // 合并 wildrose 数据
  void MergeWildRoseTable(thread::ThreadPool* pool);
  void MergeWildRoseTableImpl(int table_index);

  const std::string base_dir_ = {"../ad_index/"};
  const std::string base_upload_path_ = {"/home/<USER>/index_adapter/"};
  std::string output_dir_;
  std::string upload_dir_;
  std::string das_version_;
  std::string index_version_;
  std::string wildrose_version_;
  int64_t das_version_ms_{0};

  // index_adapter config
  AdapterConfig adapter_config_;
  bool enable_ktable_ {false};

  std::shared_ptr<ks::index_builder::index_base::StreamMapBase> stream_map_maker_ptr_;
  std::shared_ptr<ConsumerManagerBase> consumer_manager_;
  ks::index_builder::index_base::MessageStreamMap stream_map_;
  std::mutex mutex_;
  int64_t shard_no_{-1};

  // strategy here
  std::shared_ptr<StrategyBase> admit_strategy_;
  std::shared_ptr<StrategyBase> shared_strategy_;
  std::shared_ptr<StrategyBase> deploy_strategy_;
  std::shared_ptr<StrategyBase> convert_strategy_;
  std::shared_ptr<StrategyBase> field_strategy_;

  std::set<std::string> forbid_pb_set_;
  std::unordered_map<std::string, std::string> adinstance_split_config_;
  ks::ad::index_message_proxy::AdInstanceAdvanceConfig advanced_config_;
  std::unordered_map<int64_t, std::string> target_map_;
  std::atomic<bool> is_valid_{true};
};

class KfsBuilder : public Builder {
 public:
  KfsBuilder()
      : Builder(
            "/home/<USER>/kuaishou-worker/project/kfs-ad-index/"
            "index-adapter-base") {
    mkdir(base_dir_.c_str(), 0777);
  }
  bool VersionDetectAndUpdate() override;
 private:
  void UpLoadDirToHdfs(std::string local_path, std::string hdfs_path);
  bool UpLoadKfsIndexToHdfs(std::string kfs_path, std::string hdfs_path);
};
}  // namespace index_adapter
}  // namespace ks
