#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <set>
#include <vector>

#include "absl/strings/str_cat.h"
#include "absl/time/time.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_adapter/utils/builder/builder.h"
#include "base/common/closure.h"
#include "base/file/file_util.h"
#include "base/strings/string_util.h"
#include "teams/ad/index_adapter/utils/kconf/kconf.h"
#include "teams/ad/index_builder/base/index_read/index_read_proto.pb.h"
#include "teams/ad/index_builder/utils/kconf.h"
#include "teams/ad/index_builder/utils/table_config.h"
#include "teams/ad/index_adapter/utils/perf_manager/perf_manager.h"
#include "teams/ad/index_builder/utils/extra_adapter/creative_adapter.h"
#include "base/file/dir_reader_posix.h"
#include "teams/ad/index_adapter/utils/stream_map/stream_map_maker.h"
#include "teams/ad/index_builder/utils/kafka2hive/kafka2hive_manager.h"
#include "teams/ad/index_builder/utils/cache_loader/rtb_white_list_no_smr.h"
#include "teams/ad/index_adapter/utils/consumer/consumer_manager.h"
#include "teams/ad/index_adapter/utils/consumer/table_lite_consumer_mgr.h"
#include "teams/ad/index_builder/utils/unified_config_manager/unified_adapter_config.h"
#include "teams/ad/index_builder/utils/extra_adapter/wt_creative_score_parser.h"
#include "teams/ad/index_builder/multiform_filter/instance_filter.h"
#include "teams/ad/index_adapter/utils/hot_data/all_scene_hot_data_center.h"

using ks::index_builder::Kafka2HiveManager;
using ks::ad::index_message_proxy::FilterEnum;
namespace ks {
namespace index_adapter {

Builder::~Builder() {
  PerfManager::GetInstance()->UpdateBuildEndTime(GetPrefix());
}

void Builder::Init() {
  // adapter_config_ = Kconf::adapterConfig()->data();
  stream_map_maker_ptr_ = StreamMapMakerForAdapter::GetInstance().GetStreamMap();
  advanced_config_ = ks::ad::index_message_proxy::AdKconfUtil::adInstanceAdvanceConfig()->data();
  for (const auto& split_pair : advanced_config_.split_config_map()) {
    adinstance_split_config_.emplace(split_pair.first, split_pair.second);
  }
  auto dump_table_lite = index_builder::UnifiedAdapterConfigManager::GetInstanace()->IsDumpTableLite();
  if (dump_table_lite) {
    consumer_manager_ = std::make_shared<TableLiteConsumerMgr>(shard_no_);
  } else {
    consumer_manager_ = std::make_shared<ConsumerManager>();
  }

  const auto& kconf_forbid_types = advanced_config_.forbid_types();
  for (const auto& forbid_type : kconf_forbid_types) {
    forbid_pb_set_.insert(forbid_type);
  }

  int keep_num = 4;
  if (!Kconf::enableDumpOnKfs()) {
    // 本地 dump 只保留 2 份
    keep_num = 2;
  }
  ks::index_builder::index_base::LocalFileWrapper().CleanIndex(
      GetBaseDir().c_str(), keep_num);
  ks::index_builder::index_base::HdfsFileWrapper().CleanIndex(GetUploadDir(),
                                                              24);
  StrategyInit();
  enable_ktable_ = index_builder::UnifiedAdapterConfigManager::GetInstanace()->EnableKtableBuild();
}

std::vector<std::string> Builder::GetOutputTablesOfProtoName(std::string proto_name) {
  static const auto split_config_map =
      advanced_config_.split_config_map();
  auto* table_config = ks::index_builder::TableConfigManager::GetInstance();
  std::vector<std::string> ret;
  ret.push_back(table_config->GetTableNameByPbName(proto_name));
  for (const auto& m_pair : split_config_map) {
    size_t pos = m_pair.first.rfind(".");
    const auto& upper_pb_name = m_pair.first.substr(0, pos);
    const auto& lower_pb_name = m_pair.second;

    if (upper_pb_name ==  proto_name) {
      ret.push_back(table_config->GetTableNameByPbName(lower_pb_name));
    }
  }
  std::stringstream ss;
  ss << "proto_name:" << proto_name << " lower_pb_name: ";
  for (auto& table_name : ret) {
    ss << table_name << ", ";
  }
  LOG(INFO) << ss.str();
  return ret;
}

bool Builder::BuilderRun() {
  int32_t max_thread_needed = 0;
  for (auto& vec : stream_map_) {
    max_thread_needed += vec.second.size();
  }
  if (!Kconf::enableMaxDumpThread()) {
    max_thread_needed = 200;
  }
  thread::ThreadPool dump_pool{max_thread_needed};
  for (auto hdfs_stream_vec : stream_map_) {
    for (auto hdfs_stream_ptr : hdfs_stream_vec.second) {
      LOG(INFO) << "stream: " << hdfs_stream_ptr->DebugInfo()
                << " begin to dump";
      dump_pool.AddTask(
          ::NewCallback(this, &Builder::SingleStreamRun, hdfs_stream_ptr));
    }
  }
  // 合并 wildrose 数据
  if (Kconf::enableMergeWildroseData()) {
    MergeWildRoseTable(&dump_pool);
  }
  dump_pool.JoinAll();
  Finish();
  return true;
}

void Builder::SingleStreamRun(
    ks::index_builder::index_base::MessageStreamPtr hdfs_stream) {
  auto proto_name = hdfs_stream->MessageName();
  if (forbid_pb_set_.count(proto_name) > 0) {
    UnRegisterTablesOfProtoName(proto_name);
    return;
  }
  int read_cnt = 0;
  int valid_cnt = 0;
  std::unordered_map<std::string, int> filter_cnt;
  std::string filter_reason;
  while (hdfs_stream->Valid()) {
    const auto* ad_inst = hdfs_stream->NextAdInstance();
    auto ad_tmp = *ad_inst;
    read_cnt++;
    Consumer(&ad_tmp, &filter_reason);
    if (filter_reason.empty()) {
      valid_cnt++;
    } else {
      filter_cnt[filter_reason]++;
    }
  }
  PerfManager::GetInstance()->RecordOneForBuilderRead(shard_no_, proto_name, read_cnt);
  PerfManager::GetInstance()->RecordOneForBuilderValid(shard_no_, proto_name, valid_cnt);
  for (const auto& filter : filter_cnt) {
    PerfManager::GetInstance()->RecordOneForBuilderFilter(shard_no_, proto_name, filter.first, filter.second);
  }
  UnRegisterTablesOfProtoName(proto_name);
}

bool Builder::Consumer(const kuaishou::ad::AdInstance* input_ad, std::string* filter_reason) {
  std::string tmp_filter_reason;
  if (filter_reason == nullptr) {
    filter_reason = &tmp_filter_reason;
  }
  filter_reason->clear();
  auto* tmp_adinst = const_cast<kuaishou::ad::AdInstance*>(input_ad);
  if (!CommonAdAdmit(tmp_adinst)) {
    Kafka2HiveManager::GetInstance()->KafkaFilterReason(
        tmp_adinst, ks::ad::index_message_proxy::AD_FILTER);
    *filter_reason = "common_admit_failed";
    return false;
  }
  if (!AdAdmit(tmp_adinst)) {
    Kafka2HiveManager::GetInstance()->KafkaFilterReason(
        tmp_adinst, ks::ad::index_message_proxy::AD_FILTER);
    *filter_reason = "admit_failed";
    return false;
  }
  auto adinstance_split_vec =
      ks::index_builder::AdInstanceSplit(input_ad, adinstance_split_config_);
  for (auto adinstance_ptr : adinstance_split_vec) {
    if (ValidateAndCovert(adinstance_ptr.get(), filter_reason)) {
      consumer_manager_->Consume(*adinstance_ptr);
    }
  }
  return true;
}

bool Builder::ValidateAndCovert(kuaishou::ad::AdInstance* input_ad, std::string* filter_reason) {
  if (!is_valid_.load()) {
    *filter_reason = "builder_invalid";
    return false;
  }
  // 重复 Target 清理提前
  if (IsDupTarget(input_ad)) {
    *filter_reason = "is_dup_target";
    return false;
  }
  if (!ShardAdmit(input_ad)) {
    *filter_reason = "shard_admit_failed";
    // 分片重复数据太多了，暂不落数据库
    return false;
  }
  if (!DeployProcess(input_ad)) {
    *filter_reason = "deplay_filter";
    Kafka2HiveManager::GetInstance()->KafkaFilterReason(
        input_ad,
        ks::ad::index_message_proxy::DEPLOY_FILTER);
    return false;
  }
  if (!ValidCheck(input_ad)) {
    *filter_reason = "invalid_status";
    Kafka2HiveManager::GetInstance()->KafkaFilterReason(
        input_ad,
        ks::ad::index_message_proxy::INVALID_STATUS);
    return false;
  }
  // account 打标从 ConvertAdInstance 中单独拆出来
  ks::index_builder::AccountMarkUpdate(input_ad);
  if (enable_ktable_) {
    // lazy 表标注
    AllSceneHotDataCenter::GetInstance()->ProcessForFull(input_ad);
    RtbWhiteList(input_ad);
  }
  // ktable 产出不走以下逻辑
  if (!enable_ktable_) {
    // 格式转化
    if (!ConvertAdInstance(input_ad)) {
      *filter_reason = "convert_filter";
      Kafka2HiveManager::GetInstance()->KafkaFilterReason(
          input_ad,
          ks::ad::index_message_proxy::CONVERT_FITLER);  // never trigger
      return false;
    }
    // 裁剪字段
    if (!FieldFilter(input_ad)) {
      *filter_reason = "field_filter";
      Kafka2HiveManager::GetInstance()->KafkaFilterReason(
          input_ad,
          ks::ad::index_message_proxy::FIELD_FITER);  // never trigger
      return false;
    }
    // 对宽表数据额外做 check
    if (!WTTableValidCheck(input_ad)) {
      *filter_reason = "invalid_status";
      Kafka2HiveManager::GetInstance()->KafkaFilterReason(
          input_ad,
          ks::ad::index_message_proxy::INVALID_STATUS);  // never trigger
      return false;
    }
    // 对 WTCreative 做额外 check
    if (!WTCreativeValidCheck(input_ad)) {
      *filter_reason = "invalid_wt_creative";
      Kafka2HiveManager::GetInstance()->KafkaFilterReason(
          input_ad,
          ks::ad::index_message_proxy::INVALID_STATUS);  // never trigger
      return false;
    }
  }
  // 搜索 winfo 裁剪黑名单
  if (index_builder::AdKconfUtil::enableWinfoBlacklist()) {
    if (index_builder::IsWinfoInBlacklist(input_ad)) {
      PerfUtil::CountLogStash(1, "ad.index_adapter", "in_winfo_blacklist");
      return false;
    }
  }
  return true;
}

bool Builder::IsDupTarget(kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type != kuaishou::ad::AdEnum::TARGET) {
    return false;
  }
  std::string debug_str_new;
  ad->SerializeToString(&debug_str_new);
  auto map_it = target_map_.end();
  auto* msg = GetExtensionField(ad);
  auto primary_key = GetPrimaryKey(msg);
  {
    std::lock_guard<std::mutex> guard(mutex_);
    map_it = target_map_.find(primary_key);
    if (map_it == target_map_.end()) {
      target_map_.emplace(primary_key, debug_str_new);
      return false;
    }
  }
  auto& debug_str_old = map_it->second;
  // warning !!! two diff target has same target_id!
  if (debug_str_old != debug_str_new) {
    kuaishou::ad::AdInstance new_ad;
    kuaishou::ad::AdInstance old_ad;
    new_ad.ParseFromArray(debug_str_new.data(), debug_str_new.size());
    old_ad.ParseFromArray(debug_str_old.data(), debug_str_old.size());
    LOG(ERROR) << "same id diff target: " << new_ad.ShortDebugString() << " "
               << old_ad.ShortDebugString();
    PerfUtil::CountLogStash(1, "ad.index_adapter", "diff_target_same_id",
                            GetPrefix(), std::to_string(primary_key));
  }
  return true;
}

// init comsumer && stream
bool Builder::PrepareImp() {
  mkdir(base_dir_.c_str(), 0777);
  mkdir(GetBaseDir().c_str(), 0777);
  // CleanLocalIndex(base_dir_, 5);
  // const auto& tables = adapter_config_.tables();
  auto* config_manager = index_builder::UnifiedAdapterConfigManager::GetInstanace();
  const auto& tables = config_manager->GetTables();
  // std::vector<std::string> table_names{tables.begin(), tables.end()};
  consumer_manager_->SetPrefix(GetPrefix());
  ExtraConsumerDataInfo extra_consumer_data_info;
  // auto& p2p_dirs = extra_consumer_data_info.p2p_dirs;
  // p2p_dirs.insert(p2p_dirs.end(), adapter_config_.p2p_dirs().begin(), adapter_config_.p2p_dirs().end());
  // extra_consumer_data_info.hot_data_path = adapter_config_.hot_data_path();
  extra_consumer_data_info.p2p_dirs = config_manager->GetP2pDirs();
  extra_consumer_data_info.hot_data_path = config_manager->GetHotDataPath();
  consumer_manager_->Init(tables, output_dir_, upload_dir_, extra_consumer_data_info);

  if (stream_map_.empty()) {
    if (!stream_map_maker_ptr_->InitMessageStreamMap(das_version_,
                                                         &stream_map_)) {
      LOG(ERROR) << "fail to init hdfs message stream";
      return false;
    }
    absl::Time t;
    std::string err;
    if (!absl::ParseTime("%Y-%m-%d_%H%M%S", das_version_, absl::LocalTimeZone(), &t, &err)) {
      LOG(ERROR) << "absl::ParseTime(" << das_version_ << ") failed. err: " << err;
      return false;
    }
    int64_t ts = absl::ToUnixSeconds(t);
    if (ks::index_builder::AdKconfUtil::enableAdapterCreative()) {
      auto adapter_creative_server = ks::index_builder::AdapterForCreativeServer::GetInstance();
      adapter_creative_server->SetDasVersion(ts);
      adapter_creative_server->WaitForVersionReady(ts);
    }
    if (Kconf::enableDasVersionMs()) {
      das_version_ms_ = absl::ToUnixMillis(t);
    }
    LOG(INFO) << "das_version_ is " << das_version_ << " das_version_ms_ is " << das_version_ms_;
    ks::index_builder::index_base::MessageStreamMap temp_stream_map;
    for (const auto& table : tables) {
      auto map_it = stream_map_.find(table);
      if (map_it != stream_map_.end()) {
        temp_stream_map.emplace(table, map_it->second);
      }
    }
    std::swap(temp_stream_map, stream_map_);
  }
  // message_consumer ref update
  for (const auto& table_and_streams : stream_map_) {
    auto table_name = table_and_streams.first;
    auto stream_ptr_vec = table_and_streams.second;
    auto stream_ptr = stream_ptr_vec.front();
    std::stringstream ss;
    ss << "table_name: " << table_name << " size: " << stream_ptr_vec.size()
       << " dump_info: ";
    for (auto tmp_stream_ptr : stream_ptr_vec) {
      ss << tmp_stream_ptr->DebugInfo() << ", ";
    }
    auto proto_name = stream_ptr->MessageName();
    LOG(INFO) << ss.str();
    RegisterTablesOfProtoName(proto_name, stream_ptr_vec.size());
  }
  consumer_manager_->DebugInfo();
  return true;
}

// das data ready to build index
bool Builder::IsReadyImp() {
  Init();
  if (!VersionDetectAndUpdate()) {
    LOG(INFO) << "das_version: " << das_version_
              << "index_version: " << index_version_
              << " matched, wait for next version";
    return false;
  } else {
    LOG(INFO) << "das_verison: " << das_version_ << " begin to build index";
  }
  if (enable_ktable_) {
    AllSceneHotDataCenter::GetInstance()->CheckStatus(index_version_);
  }
  return true;
}

void Builder::RtbWhiteList(kuaishou::ad::AdInstance* input_ad) {
  auto msg_type = input_ad->type();
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    auto* creative = input_ad->MutableExtension(
        kuaishou::ad::tables::Creative::creative_old);
    auto creative_id = creative->id();
    if (ks::index_builder::RtbWhiteList::GetInstance()->IsInWhiteList(creative_id)) {
      creative->set_is_rtb_gray(true);
    }
  }
}

// version detect
bool Builder::VersionDetectAndUpdate() {
  if (!stream_map_maker_ptr_->CheckLastetValidVersion(
          &das_version_)) {
    return false;
  }
  std::string hdfs_dir = GetUploadDir();

  std::string prefix = GetPrefix();

  PerfManager::GetInstance()->RegisterIndexMonitorTask(prefix, hdfs_dir);
  PerfManager::GetInstance()->UpdateBuildBeginTime(prefix);
  ks::index_builder::index_base::HdfsFileWrapper().FindLastestSuccessVersion(
      hdfs_dir, &index_version_,
      ks::index_builder::index_base::TargetIndex().SuccessFlag());
  auto index_version_transformed = index_version_ + "00";
  if (das_version_ != index_version_transformed) {
    index_version_ = das_version_.substr(0, das_version_.length() - 2);
    output_dir_ =
        base::FilePath(GetBaseDir()).Append(index_version_).ToString();
    upload_dir_ =
        base::FilePath(GetUploadDir()).Append(index_version_).ToString();
    return true;
  }
  return false;
}

void Builder::MergeWildRoseTableImpl(int table_index) {
  static const char kWildRosePath[] = "/home/<USER>/wildrose-fullbuild/algo-index-test";
  static const std::vector<std::string> source {
    "AlgoILAuthor.base",
    "AlgoILLive.base",
    "AlgoILPhoto.base",
    "AlgoILItem.base"
  };
  static const std::vector<std::string> tables {
    "ad_algo_il_author",
    "ad_algo_il_live",
    "ad_algo_il_photo",
    "ad_algo_il_item"
  };
  static const std::vector<kuaishou::ad::AdEnum::AdInstanceType> pb_type {
    kuaishou::ad::AdEnum::ALGO_IL_AUTHOR,
    kuaishou::ad::AdEnum::ALGO_IL_LIVE,
    kuaishou::ad::AdEnum::ALGO_IL_PHOTO,
    kuaishou::ad::AdEnum::ALGO_IL_ITEM
  };

  if (table_index < 0 || table_index >= 4) { return; }
  if (wildrose_version_.empty()) { return; }
  const auto& table = tables[table_index];
  const auto& type = pb_type[table_index];
  std::string file_path = absl::StrCat(kWildRosePath, "/", wildrose_version_, "/", source[table_index]);
  LOG(INFO) << "merge wildrose file:" << file_path;
  using index_builder::index_base::IndexType;
  using index_builder::index_base::ReaderType;
  using index_builder::index_base::TransType;
  auto stream = std::make_shared<index_builder::index_base::MessageStream>(table);
  stream->Init(ReaderType::CACHE_READER, TransType::HDFS, file_path, IndexType::TARGET, type);
  int valid_cnt = 0;
  auto proto_name = stream->MessageName();
  RegisterTablesOfProtoName(proto_name, 1);
  while (stream->Valid()) {
    const auto* ad_inst = stream->NextAdInstance();
    auto ad_tmp = *ad_inst;
    valid_cnt++;
    consumer_manager_->Consume(ad_tmp);
  }
  LOG(INFO) << "wildrose valid cnt:" << valid_cnt;
  PerfManager::GetInstance()->RecordOneForBuilderValid(shard_no_, proto_name, valid_cnt);
  UnRegisterTablesOfProtoName(proto_name);
}

void Builder::MergeWildRoseTable(thread::ThreadPool* pool) {
  static const char kWildRosePath[] = "/home/<USER>/wildrose-fullbuild/algo-index-test";
  // 获取 wildrose 数据最新目录
  ignore_result(FindLastestSuccessVersionOnHdfs(kWildRosePath, &wildrose_version_, "_SUCCESS"));
  LOG(INFO) << "Find lastest wildrose data version:" << wildrose_version_;
  // 多线程并行导出表格
  for (int i = 0; i < 4; i++) {
    pool->AddTask(::NewCallback(this, &Builder::MergeWildRoseTableImpl, i));
  }
}

// consumer_manager 处理收尾
bool Builder::Finish() {
  std::string prefix = GetPrefix();
  if (!is_valid_.load()) {
    return false;
  }
  consumer_manager_->Finish();
  consumer_manager_->DebugInfo();
  return true;
}

bool Builder::DeployProcess(kuaishou::ad::AdInstance* input_ad) {
  LOG_EVERY_N(INFO, 100000) << deploy_strategy_->Name();
  return deploy_strategy_->Process(input_ad);
}

bool Builder::FieldFilter(kuaishou::ad::AdInstance* input_ad) {
  LOG_EVERY_N(INFO, 100000) << field_strategy_->Name();
  return field_strategy_->Process(input_ad);
}

bool Builder::CommonAdAdmit(kuaishou::ad::AdInstance* input_ad) {
  auto type = input_ad->type();
  auto account_common_admit = [this](const kuaishou::ad::AdInstance* ad) -> bool {
    static const int64 kExpireTime = Kconf::accountInactiveDaysConfig() * 24L * 3600 * 1000;
    const auto &account = ad->GetExtension(kuaishou::ad::tables::Account::account_old);
    // 粉条非活账户退场相关
    if (FLAGS_enable_exeunt_inactive_account) {
      auto last_active_time = account.last_active_time();
      auto account_type = account.account_type();
      if (account_type == AdEnum::ACCOUNT_ESP_MOBILE ||
          account_type == AdEnum::ACCOUNT_FANSTOP_V2) {
        int64_t now_time_ms = das_version_ms_ == 0? base::GetTimestamp() / 1000 : das_version_ms_;
        if (now_time_ms - last_active_time >= kExpireTime) {
          return false;
        }
      }
    }
    return true;
  };

  using TYPE2FUNC =
      std::unordered_map<AdEnum::AdInstanceType, std::function<bool(const kuaishou::ad::AdInstance *)>>;

  static const TYPE2FUNC kAdmitFuncMap {
    {AdEnum::ACCOUNT, account_common_admit}
  };

  auto func_it = kAdmitFuncMap.find(type);
  if (func_it != kAdmitFuncMap.end()) {
    return (func_it->second)(input_ad);
  } else {
    return true;
  }
}

bool Builder::AdAdmit(kuaishou::ad::AdInstance* input_ad) {
  if (admit_strategy_) {
    LOG_EVERY_N(INFO, 100000) << admit_strategy_->Name();
    return admit_strategy_->Process(input_ad);
  }
  return true;
}

bool Builder::ConvertAdInstance(kuaishou::ad::AdInstance* input_ad) {
  LOG_EVERY_N(INFO, 100000) << convert_strategy_->Name();
  return convert_strategy_->Process(input_ad);
}

bool Builder::ShardAdmit(kuaishou::ad::AdInstance* input_ad) {
  if (shared_strategy_) {
    LOG_EVERY_N(INFO, 100000) << shared_strategy_->Name();
    return shared_strategy_->Process(input_ad);
  }
  return true;
}

bool Builder::ValidCheck(kuaishou::ad::AdInstance* input_ad) {
  static bool valid_check_skip =
      index_builder::UnifiedAdapterConfigManager::GetInstanace()->IsSkipValidCheck();
  if (valid_check_skip) {
    return true;
  }
  auto msg_type = input_ad->type();
  auto* msg = GetExtensionField(input_ad);
  if (ks::engine_base::IsValidTable(input_ad)) {
    return true;
  }
  return false;
}

bool Builder::WTTableValidCheck(kuaishou::ad::AdInstance* input_ad) {
  static bool valid_check_skip =
      index_builder::UnifiedAdapterConfigManager::GetInstanace()->IsSkipValidCheck();
  if (valid_check_skip) {
    return true;
  }
  auto msg_type = input_ad->type();
  auto* msg = GetExtensionField(input_ad);
  if (msg_type == AdEnum::WT_PHOTO) {
    const auto& wt_photo = input_ad->GetExtension(kuaishou::ad::tables::WTPhoto::wt_photo_old);
    return ks::engine_base::IsValidTable(wt_photo.photo_status());
  } else {
    return true;
  }
}

bool Builder::WTCreativeValidCheck(kuaishou::ad::AdInstance* input_ad) {
  auto msg_type = input_ad->type();
  if (msg_type == AdEnum::WT_CREATIVE) {
    bool ret = ks::index_builder::WTCreativeScoreParser::GetInstance().ProcessWTCreativeFull(input_ad);
    if (!ret) {
      return false;
    }
    // 打分过滤
    const auto& wt_creative = input_ad->GetExtension(kuaishou::ad::tables::WTCreative::wt_creative_old);
    return ks::engine_base::IsValidTable(wt_creative);
  }
  return true;
}

void Builder::StrategyInit() {
  auto filer_type = index_builder::UnifiedAdapterConfigManager::GetInstanace()->GetAdmitFilterType();
  admit_strategy_.reset(StrategyFactory::GetInstance()->GetStrategy(filer_type));
  deploy_strategy_.reset(new DeployStrategy("deploy_strategy"));
  convert_strategy_.reset(new ConvertStrategy("convert_strategy"));
  auto white_list_key = index_builder::UnifiedAdapterConfigManager::GetInstanace()->GetWhiteListKey();
  field_strategy_.reset(new FieldFilterStrategy("field_strategy", white_list_key));
  auto shard_param = index_builder::UnifiedAdapterConfigManager::GetInstanace()->GetShardParam();
  shared_strategy_.reset(StrategyFactory::GetInstance()->GetStrategy(shard_param));
  if (shared_strategy_) {
    shared_strategy_->SetShardNo(shard_no_);
  }
}
// version detect
bool KfsBuilder::VersionDetectAndUpdate() {
  if (!stream_map_maker_ptr_->CheckLastetValidVersion(
          &das_version_)) {
    return false;
  }
  std::string hdfs_dir = GetUploadDir();
  std::string kfs_dir = GetBaseDir();
  std::string prefix = GetPrefix();

  PerfManager::GetInstance()->RegisterIndexMonitorTask(prefix, hdfs_dir);
  PerfManager::GetInstance()->UpdateBuildBeginTime(prefix);
  ks::index_builder::index_base::LocalFileWrapper().FindLastestSuccessVersion(
      kfs_dir, &index_version_,
      ks::index_builder::index_base::TargetIndex().SuccessFlag());
  auto index_version_transformed = index_version_ + "00";
  // DAS 新版本已到 KFS 不存在 则开始 Dump
  if (das_version_ != index_version_transformed) {
    index_version_ = das_version_.substr(0, das_version_.length() - 2);
    output_dir_ =
        base::FilePath(GetBaseDir()).Append(index_version_).ToString();
    upload_dir_ =
        base::FilePath(GetUploadDir()).Append(index_version_).ToString();
    //防止 HDFS 上有残余
    const std::string hdfs_dir_path =
        base::FilePath(GetUploadDir()).Append(index_version_).ToString();
    if (hadoop::HDFSExists(hdfs_dir_path.c_str())) {
      if (hadoop::HDFSRmr(hdfs_dir_path.c_str())) {
        LOG(INFO) << "Delete Existed Dir " << hdfs_dir_path << " Success";
      } else {
        LOG(ERROR) << "Delete Existed Dir " << hdfs_dir_path << " Fail";
      }
    }
    return true;
  }
  std::string hdfs_version;
  ks::index_builder::index_base::HdfsFileWrapper().FindLastestSuccessVersion(
      hdfs_dir, &hdfs_version,
      ks::index_builder::index_base::TargetIndex().SuccessFlag());
  // KFS 存在
  // 但 HDFS 不存在
  if (index_version_ != hdfs_version) {
    // 上传 HDFS
    // 不需要重新 dump
    // 上传前将需将目录删除

    const std::string kfs_dir_path =
        base::FilePath(GetBaseDir()).Append(index_version_).ToString();
    const std::string hdfs_dir_path =
        base::FilePath(GetUploadDir()).Append(index_version_).ToString();
    if (hadoop::HDFSExists(hdfs_dir_path.c_str())) {
      if (hadoop::HDFSRmr(hdfs_dir_path.c_str())) {
        LOG(INFO) << "Delete Existed Dir " << hdfs_dir_path << " Success";
      } else {
        LOG(ERROR) << "Delete Existed Dir " << hdfs_dir_path << " Fail";
      }
    }
    UpLoadKfsIndexToHdfs(kfs_dir_path, hdfs_dir_path);
    return false;
  }
  // HDFS 与 KFS 一致
  return false;
}

void KfsBuilder::UpLoadDirToHdfs(std::string local_path, std::string hdfs_path) {
  int upload_start = time(nullptr);
  LOG(INFO) << "start HDFSPut| " << local_path << " -> " << hdfs_path;
  int ret = hadoop::HDFSPut(local_path.c_str(), hdfs_path.c_str());
  if (ret == 0) {
    LOG(INFO) << "finish HDFSPut| " << local_path << " -> " << hdfs_path
              << " success. cost: " << (time(nullptr) - upload_start) << "s";
  } else {
    LOG(ERROR) << "finish HDFSPut| " << local_path << " -> " << hdfs_path
               << " failed. ret: " << ret;
  }
}
bool KfsBuilder::UpLoadKfsIndexToHdfs(std::string kfs_path,
                                   std::string hdfs_path) {
  std::vector<std::string> elems;
  std::string flag_file =
      base::FilePath(kfs_path).Append("_SUCCESS").ToString();
  std::string dot_dir =
      base::FilePath(kfs_path).Append(".").ToString();
  std::string dot_dot_dir =
      base::FilePath(kfs_path).Append("..").ToString();
  std::set<std::string> black_paths {flag_file, dot_dir, dot_dot_dir};
  HdfsUploader temp_uploader;
  thread::ThreadPool hdfs_thread_pool(40);
  temp_uploader.SetUploadThreadPool(&hdfs_thread_pool);
  //  目录上传

  base::DirReaderPosix dir_reader(kfs_path.c_str());
  while (dir_reader.Next()) {
    auto curr_path = base::FilePath(kfs_path)
                         .Append(dir_reader.name())
                         .ToString();  //当前路径
    auto curr_hdfs_path = base::FilePath(hdfs_path)
                              .Append(dir_reader.name())
                              .ToString();  //当前 hdfs 路径
    if (black_paths.count(curr_path) != 0) {  // 标志文件略过
      continue;
    }
    base::PlatformFileInfo temp_info;
    if (base::file_util::GetFileInfo(curr_path, &temp_info)) {
      if (temp_info.is_directory) {
        // 文件夹
        hdfs_thread_pool.AddTask(::NewCallback(this, &KfsBuilder::UpLoadDirToHdfs,
                                               curr_path, curr_hdfs_path));
      } else {
        //非文件夹上传
        LOG(INFO) << " local: " << curr_path << " to hdfs: " << hdfs_path;
        temp_uploader.AddUploadTask(curr_path, hdfs_path);
      }
    } else {
      LOG(ERROR) << "GetFileInfo [" << curr_path << "] failed";
    }
  }

  hdfs_thread_pool.JoinAll();
  temp_uploader.TouchSuccess(hdfs_path);
  return true;
}

}  // namespace index_adapter
}  // namespace ks
