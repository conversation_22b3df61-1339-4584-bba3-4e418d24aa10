#pragma once
#include <memory>
#include <string>
#include "base/file/file_path.h"
#include "teams/ad/index_builder/base/index_read/stream_map_maker.h"
#include "teams/ad/index_adapter/utils/kconf/kconf.h"
#include "absl/strings/match.h"
#include "teams/ad/index_builder/utils/unified_config_manager/unified_adapter_config.h"

namespace ks {
namespace index_adapter {

class StreamMapMakerForAdapter: public ks::index_builder::index_base::StreamMapMaker {
 public:
  static StreamMapMakerForAdapter& GetInstance() {
    static StreamMapMakerForAdapter instance;
    return instance;
  }

  std::shared_ptr<ks::index_builder::index_base::StreamMapBase> GetStreamMap() {
    // auto type = Kconf::adapterConfig()->data().stream_map_type();
    auto type = index_builder::UnifiedAdapterConfigManager::GetInstanace()->GetStreamMapType();
    if (Kconf::streamMapAdapter()->data().name2map().find(type) ==
        Kconf::streamMapAdapter()->data().name2map().end()) {
      LOG(FATAL) << "unvalid type: " << type;
    }
    ks::index_adapter::StreamMap stream_map =
        Kconf::streamMapAdapter()->data().name2map().at(type);
    HookIndexSource(&stream_map);
    InjectionWildTableInputDirs(&stream_map);
    return DetectMutiPathStreamMap(
        ks::index_builder::index_base::ReaderType::CACHE_READER, stream_map);
  }

  // 手动注入不同 kfs 集群的数据路径
  void InjectionWildTableInputDirs(ks::index_adapter::StreamMap* stream_map) {
    if (stream_map == nullptr) { return; }
    std::string wild_table_path = "";
    if (Kconf::enableLoadFromKfs()) {
      wild_table_path = index_builder::UnifiedAdapterConfigManager::GetInstanace()->GetWildTablePath();
    } else {
      wild_table_path = index_builder::UnifiedAdapterConfigManager::GetInstanace()->GetWildTableHdfsPath();
    }
    if (wild_table_path.empty()) { return; }
    stream_map->add_input_dirs(wild_table_path);
  }

 protected:
  static void HookIndexSource(ks::index_adapter::StreamMap* stream_map) {
    if (Kconf::enableLoadFromKfs()) {
      auto* input_dirs = stream_map->mutable_input_dirs();
      for (auto& dir : *input_dirs) {
        dir = GetKfsDirOfHDFS(dir);
      }
      auto forbids = stream_map->forbid_input_hdfs();
      stream_map->clear_forbid_input_hdfs();
      for (auto forbid : forbids) {
        stream_map->mutable_forbid_input_hdfs()->insert(
            {GetKfsDirOfHDFS(forbid.first), forbid.second});
      }
      LOG(INFO) << "stream Map after hook:" << stream_map->ShortDebugString();
    }
  }
  static std::string GetKfsDirOfHDFS(const std::string& ori_path) {
    std::string hdfs_prefix = "/home/<USER>/das/online";
    std::string kfs_prefix =
        "/home/<USER>/kuaishou-worker/project/kfs-ad-das/home/<USER>/das/online";

    std::string hdfs_prefix_preonline = "/home/<USER>/das/pre_online";
    std::string kfs_prefix_preonline =
        "/home/<USER>/kuaishou-worker/project/kfs-ad-das-preonline/home/<USER>/das/pre_online";

    std::string kfs_prefix_preonline_v2 =
        "/home/<USER>/kuaishou-worker/project/kfs-ad-das/home/<USER>/das/pre_online";

    bool prod = absl::StartsWith(ori_path, hdfs_prefix);
    bool preonline = absl::StartsWith(ori_path, hdfs_prefix_preonline);

    LOG_ASSERT(prod || preonline) << " error path: " << ori_path;
    base::FilePath child(ori_path);
    if (prod) {
      base::FilePath parent(hdfs_prefix);
      base::FilePath path(kfs_prefix);
      parent.AppendRelativePath(child, &path);
      return path.ToString();
    } else {
      base::FilePath parent(hdfs_prefix_preonline);
      base::FilePath path(kfs_prefix_preonline_v2);
      parent.AppendRelativePath(child, &path);
      return path.ToString();
    }
  }
};

}  // namespace index_adapter
}  // namespace ks
