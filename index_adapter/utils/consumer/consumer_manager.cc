#include "teams/ad/index_adapter/utils/consumer/consumer_manager.h"
#include <map>
#include <memory>
#include <set>
#include <string>
#include <vector>
#include "absl/container/flat_hash_map.h"
#include "absl/strings/substitute.h"
#include "base/file/file_path.h"
#include "base/thread/thread_pool.h"
#include "teams/ad/ad_base/src/ksp/ad_web_request_handler.h"
#include "teams/ad/index_adapter/utils/hdfs_util/hdfs_wrapper.h"

namespace ks {
namespace index_adapter {

ConsumerManager::ConsumerManager() {
  lazy_tables_ = {"ad_dsp_unit", "ad_dsp_photo",
                  "ad_dsp_unit_small_shop_merchant_support_info"};
}
ConsumerManager::~ConsumerManager() {
  if (!thread_pool_stop_.load()) {
    hdfs_upload_thread_pool_.JoinAll();
    dump_info_update_pool_.JoinAll();
  }
}

void ConsumerManager::Init(const std::vector<std::string>& table_names,
                           const std::string& output_path,
                           const std::string& upload_path,
                           const ExtraConsumerDataInfo& extra_data) {
  output_path_ = base::FilePath(output_path).ToString();
  upload_path_ = base::FilePath(upload_path).ToString();
  hdfs_uploader_.SetUploadThreadPool(&hdfs_upload_thread_pool_);
  hdfs_uploader_.Mkdir(upload_path);
  if (Kconf::disableUploadToHdfs()) {
    hdfs_uploader_.DisableUpload();
  }
  // prepare local dir
  if (base::file_util::PathExists(base::FilePath(output_path))) {
    if (!base::file_util::Delete(base::FilePath(output_path), true)) {
      LOG(ERROR) << "Delete(" << output_path << ") failed";
    }
  }
  mkdir(output_path.c_str(), 0777);
  for (const auto& table_name : table_names) {
    consumer_map_[table_name] = GetConsumer(table_name, output_path, upload_path, extra_data);
    consumer_map_[table_name]->SetPrefix(prefix_);
    consumer_map_[table_name]->Prepare();
    table_ref_count_.emplace(table_name, 0);
  }
  // std::vector<std::string> p2p_dirs{adapter_config.p2p_dirs().begin(),
  //                                   adapter_config.p2p_dirs().end()};
  p2p_manager_.Init(extra_data.p2p_dirs);

  p2p_manager_.RegisterTask(prefix_, output_path, upload_path);
  LOG(INFO) << "local output_path: " << output_path;
}

std::shared_ptr<MessageConsumer> ConsumerManager::GetConsumer(
    const std::string& table_name,
    const std::string& output_path,
    const std::string& upload_path,
    const ExtraConsumerDataInfo& extra_data) {
  if ((!extra_data.hot_data_path.empty()) &&
      lazy_tables_.count(table_name) > 0) {
    return std::shared_ptr<MessageConsumer>(new AdIndexLazyConsumer(
        table_name, output_path, upload_path));
  }
  LOG(INFO) << "enableAdIndexMutiFileTxtFormatConsumer: "
      << Kconf::enableAdIndexMutiFileTxtFormatConsumer();
  if (Kconf::enableAdIndexMutiFileTxtFormatConsumer()) {
    return std::shared_ptr<MessageConsumer>(new AdIndexMutiFileTxtFormatConsumer(
        table_name, output_path, upload_path));
  }
  if (Kconf::indexDumpShardSize() != 0) {
    return std::shared_ptr<MessageConsumer>(new AdIndexMutiFileConsumer(
        table_name, output_path, upload_path));
  }
  return std::shared_ptr<MessageConsumer>(new AdIndexConsumer(
      table_name, output_path, upload_path));
}

// if a table ref = 0, then config has error
bool ConsumerManager::IsValid() {
  for (auto table_ref : table_ref_count_) {
    if (table_ref.second == 0) {
      return false;
    }
  }
  return true;
}

void ConsumerManager::DebugInfo() {
  for (const auto& table_ptr : consumer_map_) {
    LOG(INFO) << table_ptr.second->DebugInfo()
              << " ref_counter: " << table_ref_count_.at(table_ptr.first);
  }
}

void ConsumerManager::Finish() {
  // 等待 dump_info 更新完成
  dump_info_update_pool_.JoinAll();
  // 先本地写 dump_info
  std::string dump_info_str;
  google::protobuf::util::JsonPrintOptions options;
  options.add_whitespace = true;
  google::protobuf::util::MessageToJsonString(dump_info_, &dump_info_str,
                                              options);
  auto dump_info_path = base::FilePath(output_path_).Append("dump_info");
  base::file_util::WriteFile(dump_info_path, dump_info_str.c_str(),
                            dump_info_str.size());
  LOG(INFO) << dump_info_.ShortDebugString();

  // 传 P2p 目录
  p2p_manager_.DoUploadTask();

  UpdateTableRecordToPerf();
  //本地写 Success
  hdfs_uploader_.TouchLocalSuccess(output_path_);
  auto bin_version_path = base::FilePath(output_path_).Append("bin_version");
  auto version_info = ad_base::VersionInfo::Instance();
  std::string bin_version = absl::Substitute("$0_$1", version_info.build_.user,
                                            version_info.build_.time);
  base::file_util::WriteFile(bin_version_path, bin_version.c_str(),
                            bin_version.size());
  LOG(INFO) << bin_version;

  //等待上传完成
  hdfs_upload_thread_pool_.JoinAll();
  thread_pool_stop_.store(true);

  if (Kconf::enableAdIndexMutiFileTxtFormatConsumer()) {
    hdfs_uploader_.TouchLocalSuccess(output_path_);
    for (auto const& table_ptr : consumer_map_) {
      auto table_dump_info_path = base::FilePath(output_path_).Append(table_ptr.first+".dump_info");
      hdfs_uploader_.PutFileToHdfs(table_dump_info_path.ToString(), upload_path_);
      auto table_dump_done_path = base::FilePath(output_path_).Append(table_ptr.first+".dump_done");
      hdfs_uploader_.PutFileToHdfs(table_dump_done_path.ToString(), upload_path_);
    }
  }

  //上传 dump_info
  hdfs_uploader_.PutFileToHdfs(dump_info_path.ToString(), upload_path_);

  //上传 bin_version
  hdfs_uploader_.PutFileToHdfs(bin_version_path.ToString(), upload_path_);
  // HDFS 写 Success
  hdfs_uploader_.TouchSuccess(upload_path_);

  if (Kconf::enableAdIndexMutiFileTxtFormatConsumer()) {
    hdfs_uploader_.TouchDumpDone(upload_path_);
  }
}

void ConsumerManager::UpdateTableRecordToPerf() {
  // 合并 表 信息
  std::map<std::string, kuaishou::ad::tables::DumpData> records;
  const auto& lazy_info = dump_info_;
  for (auto& info : lazy_info.info()) {
    auto table_name = info.table_name();
    auto& info_ref = records[table_name];
    int64_t last_num = info_ref.record_num();  // 原有记录数，初始为零
    info_ref = info;                           // 拷贝其余信息
    info_ref.set_record_num(last_num + info.record_num());  // 记录数累加
  }
  kuaishou::ad::tables::DumpInfo merge_info;
  for (auto& record : records) {
    auto info = merge_info.add_info();
    *info = record.second;
  }
  PerfManager::GetInstance()->UpdateTableRecord(prefix_, merge_info);
}

void ConsumerManager::UpdateTableInfo(
    MessageConsumerPtr consumer_ptr) {
  auto md5_str = ks::index_builder::MD5SumFile(consumer_ptr->GetOutputPath());
  std::lock_guard<std::mutex> guard(mtx_);
  auto* info = dump_info_.add_info();
  info->set_file_name(consumer_ptr->GetTableFile());
  info->set_record_num(consumer_ptr->GetRecordNum());
  info->set_md5(md5_str);
  info->set_table_name(consumer_ptr->GetTableName());
  info->set_proto_name(consumer_ptr->GetProtoName());
}

void ConsumerManager::FinishConsumer(MessageConsumerPtr consumer_ptr) {
  consumer_ptr->Finish();
  auto table_name = consumer_ptr->GetTableName();
  LOG(INFO) << "upload table_name: " << table_name
            << " local: " << consumer_ptr->GetOutputPath()
            << "hdfs: " << upload_path_;
  for (auto& path : consumer_ptr->GetOutputPaths()) {
    hdfs_uploader_.AddUploadTask(path, upload_path_);
  }

  dump_info_update_pool_.AddTask(
      ::NewCallback(consumer_ptr.get(), &MessageConsumer::UpdateTableInfo,
                    &dump_info_, &mtx_));
}

}  // namespace index_adapter
}  // namespace ks
