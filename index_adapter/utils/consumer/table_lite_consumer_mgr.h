#pragma once

#include <map>
#include <memory>
#include <set>
#include <string>
#include <vector>
#include "absl/container/flat_hash_map.h"
#include "absl/strings/substitute.h"
#include "base/file/file_path.h"
#include "base/thread/thread_pool.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/index_manager.h"
#include "teams/ad/engine_base/utils/deploy_variable.h"
#include "teams/ad/index_adapter/utils/consumer/consumer_manager_base.h"
#include "teams/ad/index_adapter/utils/consumer/message_consumer.h"
#include "teams/ad/index_adapter/utils/hdfs_uploader.h"
#include "teams/ad/index_adapter/utils/p2p/p2p_task.h"
#include "teams/ad/index_adapter/utils/perf_manager/perf_manager.h"

namespace ks {
namespace index_adapter {

class TableLiteConsumerMgr : public ConsumerManagerBase {
 public:
  explicit TableLiteConsumerMgr(int32_t shard_no);
  ~TableLiteConsumerMgr();

  void Init(const std::vector<std::string>& table_names,
            const std::string& output_path, const std::string& upload_path,
            const ExtraConsumerDataInfo& extra_data) override;
  bool Consume(const kuaishou::ad::AdInstance& input_ad) override;
  // if a table ref = 0, then config has error
  bool IsValid() override;

  std::shared_ptr<MessageConsumer> GetConsumer(
      const std::string& table_name, const std::string& output_path,
      const std::string& upload_path,
      const ExtraConsumerDataInfo& extra_data) override;

  void DebugInfo() override;

  void Finish() override;

  void UpdateTableRecordToPerf() override;

  void FinishConsumer(MessageConsumerPtr consumer_ptr) override;

  void UpdateTableInfo(MessageConsumerPtr consumer_ptr) override;
  bool RegisterTable(const std::string& table_name) override;

  void UnRegisterTable(const std::string& table_name) override;

 protected:
  bool FinshSingleTable(const std::string& table_name);

 protected:
  HdfsUploader hdfs_uploader_;
  std::string upload_path_;
  std::string output_path_;
  std::atomic<bool> thread_pool_stop_{false};
  thread::ThreadPool hdfs_upload_thread_pool_{40};
  thread::ThreadPool dump_info_update_pool_{40};
  kuaishou::ad::tables::DumpInfo dump_info_;
  P2pTaskManager p2p_manager_;
  ks::ad_table_lite::ContainerSet container_set_;
  // std::string prefix_;
};

}  // namespace index_adapter
}  // namespace ks
