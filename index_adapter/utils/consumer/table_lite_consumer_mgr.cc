#include "teams/ad/index_adapter/utils/consumer/table_lite_consumer_mgr.h"
#include <map>
#include <memory>
#include <set>
#include <string>
#include <vector>
#include "absl/container/flat_hash_map.h"
#include "absl/strings/substitute.h"
#include "base/file/file_path.h"
#include "base/thread/thread_pool.h"
#include "teams/ad/ad_base/src/ksp/ad_web_request_handler.h"

namespace ks {
namespace index_adapter {

TableLiteConsumerMgr::TableLiteConsumerMgr(int32_t shard_no)
    : container_set_(false) {
  container_set_.SetShardNo(shard_no);
}
TableLiteConsumerMgr::~TableLiteConsumerMgr() {
  if (!thread_pool_stop_.load()) {
    hdfs_upload_thread_pool_.JoinAll();
    dump_info_update_pool_.JoinAll();
  }
}

bool TableLiteConsumerMgr::Consume(const kuaishou::ad::AdInstance& input_ad) {
  LOG_EVERY_N(INFO, 100000) << input_ad.ShortDebugString();
  return container_set_.ParseMessage(input_ad);
}

void TableLiteConsumerMgr::Init(const std::vector<std::string>& table_names,
                                const std::string& output_path,
                                const std::string& upload_path,
                                const ExtraConsumerDataInfo& extra_data) {
  output_path_ = base::FilePath(output_path).ToString();
  upload_path_ = base::FilePath(upload_path).ToString();
  hdfs_uploader_.SetUploadThreadPool(&hdfs_upload_thread_pool_);
  hdfs_uploader_.Mkdir(upload_path);
  if (Kconf::disableUploadToHdfs()) {
    hdfs_uploader_.DisableUpload();
  }
  // prepare local dir
  if (base::file_util::PathExists(base::FilePath(output_path))) {
    if (!base::file_util::Delete(base::FilePath(output_path), true)) {
      LOG(ERROR) << "Delete(" << output_path << ") failed";
    }
  }
  mkdir(output_path.c_str(), 0777);

  static auto* table_config =
      ks::index_builder::TableConfigManager::GetInstance();
  for (const auto& table_name : table_names) {
    table_ref_count_.emplace(table_name, 0);
    kuaishou::ad::AdEnum::AdInstanceType type;
    auto enum_str = table_config->GetEnumNameByTableName(table_name);
    if (!kuaishou::ad::AdEnum_AdInstanceType_Parse(enum_str, &type)) {
      LOG(FATAL) << " Can not find correct AdInstanceType of table"
                 << table_name << ", enum str : " << enum_str;
    }
    container_set_.EnableTable(type);
  }
  container_set_.init();
  // std::vector<std::string> p2p_dirs{adapter_config.p2p_dirs().begin(),
  //                                   adapter_config.p2p_dirs().end()};
  p2p_manager_.Init(extra_data.p2p_dirs);

  p2p_manager_.RegisterTask(prefix_, output_path, upload_path);
  LOG(INFO) << "local output_path: " << output_path;
}

std::shared_ptr<MessageConsumer> TableLiteConsumerMgr::GetConsumer(
    const std::string& table_name, const std::string& output_path,
    const std::string& upload_path, const ExtraConsumerDataInfo& extra_data) {
  LOG(FATAL) << "Bad call";
  return nullptr;
}

// if a table ref = 0, then config has error
bool TableLiteConsumerMgr::IsValid() {
  for (auto table_ref : table_ref_count_) {
    if (table_ref.second == 0) {
      return false;
    }
  }
  return true;
}

void TableLiteConsumerMgr::DebugInfo() {
  for (const auto& table_ptr : consumer_map_) {
    LOG(INFO) << table_ptr.second->DebugInfo()
              << " ref_counter: " << table_ref_count_.at(table_ptr.first);
  }
}

void TableLiteConsumerMgr::Finish() {
  dump_info_update_pool_.JoinAll();
  // 先本地写 dump_info
  std::string local_path = base::FilePath(output_path_).ToString();

  std::string dump_info_str;
  google::protobuf::util::JsonPrintOptions options;
  options.add_whitespace = true;
  google::protobuf::util::MessageToJsonString(dump_info_, &dump_info_str,
                                              options);

  auto dump_info_path = base::FilePath(local_path).Append("dump_info");
  base::file_util::WriteFile(dump_info_path, dump_info_str.c_str(),
                             dump_info_str.size());

  auto bin_version_path = base::FilePath(local_path).Append("bin_version");
  auto version_info = ad_base::VersionInfo::Instance();
  std::string bin_version = absl::Substitute("$0_$1", version_info.build_.user,
                                             version_info.build_.time);
  base::file_util::WriteFile(bin_version_path, bin_version.c_str(),
                             bin_version.size());

  LOG(INFO) << dump_info_.ShortDebugString();
  LOG(INFO) << bin_version;
  // 传 P2p 目录
  p2p_manager_.DoUploadTask();

  UpdateTableRecordToPerf();
  //本地写 Success
  hdfs_uploader_.TouchLocalSuccess(output_path_);
  //等待上传完成
  hdfs_upload_thread_pool_.JoinAll();
  thread_pool_stop_.store(true);

  //上传 dump_info
  hdfs_uploader_.PutFileToHdfs(dump_info_path.ToString(), upload_path_);


  //上传 bin_version
  hdfs_uploader_.PutFileToHdfs(bin_version_path.ToString(), upload_path_);
  // HDFS 写 Success
  hdfs_uploader_.TouchSuccess(upload_path_);
}

void TableLiteConsumerMgr::UpdateTableRecordToPerf() {
  // 合并 表 信息
  std::map<std::string, kuaishou::ad::tables::DumpData> records;
  const auto& lazy_info = dump_info_;
  for (auto& info : lazy_info.info()) {
    auto table_name = info.table_name();
    auto& info_ref = records[table_name];
    int64_t last_num = info_ref.record_num();  // 原有记录数，初始为零
    info_ref = info;                           // 拷贝其余信息
    info_ref.set_record_num(last_num + info.record_num());  // 记录数累加
  }
  kuaishou::ad::tables::DumpInfo merge_info;
  for (auto& record : records) {
    auto info = merge_info.add_info();
    *info = record.second;
  }
  PerfManager::GetInstance()->UpdateTableRecord(prefix_, merge_info);
}

void TableLiteConsumerMgr::UpdateTableInfo(MessageConsumerPtr consumer_ptr) {
  auto md5_str = ks::index_builder::MD5SumFile(consumer_ptr->GetOutputPath());
  std::lock_guard<std::mutex> guard(mtx_);
  auto* info = dump_info_.add_info();
  info->set_file_name(consumer_ptr->GetTableFile());
  info->set_record_num(consumer_ptr->GetRecordNum());
  info->set_md5(md5_str);
  info->set_table_name(consumer_ptr->GetTableName());
  info->set_proto_name(consumer_ptr->GetProtoName());
}

void TableLiteConsumerMgr::FinishConsumer(MessageConsumerPtr consumer_ptr) {
  LOG(ERROR) << "Base call FinishConsumer ";
}

bool TableLiteConsumerMgr::RegisterTable(const std::string& table_name) {
  std::lock_guard<std::mutex> guard(mtx_);
  table_ref_count_[table_name]++;
  return true;
}

bool TableLiteConsumerMgr::FinshSingleTable(const std::string& table_name) {
  // dump single table
  kuaishou::ad::tables::DumpData dump_data;
  kuaishou::ad::AdEnum::AdInstanceType type;
  static auto* table_config =
      ks::index_builder::TableConfigManager::GetInstance();
  auto enum_str = table_config->GetEnumNameByTableName(table_name);
  if (!kuaishou::ad::AdEnum_AdInstanceType_Parse(enum_str, &type)) {
    LOG(FATAL) << " Can not find correct AdInstanceType of table" << table_name
               << ", enum str : " << enum_str;
  }
  std::string local_path = base::FilePath(output_path_).ToString();
  container_set_.DumpSingleTable(local_path, type, &dump_data);
  if (!dump_data.table_name().empty()) {
    {
      std::lock_guard<std::mutex> guard(mtx_);
      dump_info_.add_info()->CopyFrom(dump_data);
      LOG(INFO) << "table: " << table_name
                << "dump data: " << dump_data.ShortDebugString();
    }
    for (auto& elem : dump_data.files()) {
      hdfs_uploader_.AddUploadTask(
          base::FilePath(output_path_).Append(elem.file()).ToString(),
          upload_path_);
    }
  }
  return true;
}
void TableLiteConsumerMgr::UnRegisterTable(const std::string& table_name) {
  bool is_finish = false;
  {
    std::lock_guard<std::mutex> guard(mtx_);
    table_ref_count_[table_name]--;
    if (table_ref_count_[table_name] == 0) {
      // consumer finish callback!
      is_finish = true;
    }
  }
  // to do dump_pool
  if (is_finish) {
    FinshSingleTable(table_name);
  }
}
}  // namespace index_adapter
}  // namespace ks
