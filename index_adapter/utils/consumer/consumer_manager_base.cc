#include <memory>
#include <string>
#include <vector>
#include <set>
#include <map>
#include "teams/ad/index_adapter/utils/consumer/consumer_manager_base.h"

namespace ks {
namespace index_adapter {


bool ConsumerManagerBase::Consume(
    const kuaishou::ad::AdInstance& input_ad) {
  static auto* table_config =
      ks::index_builder::TableConfigManager::GetInstance();
  auto message_type = ks::index_builder::GetPbTypeStr(input_ad.type());
  auto table_name = table_config->GetTableNameByEnumName(message_type);
  // LOG_EVERY_N(INFO, 10000) << "table_name: " << table_name << " " <<
  // input_ad.ShortDebugString();
  auto map_it = consumer_map_.find(table_name);
  if (map_it == consumer_map_.end()) {
    return false;
  }
  auto consumer_ptr = consumer_map_[table_name];
  return consumer_ptr->Consume(input_ad);
}

bool ConsumerManagerBase::RegisterTable(const std::string& table_name) {
  std::lock_guard<std::mutex> guard(mtx_);
  if (consumer_map_.count(table_name) == 0) {
    return false;
  }
  table_ref_count_[table_name]++;

  return true;
}

void ConsumerManagerBase::UnRegisterTable(const std::string& table_name) {
  bool is_finish = false;
  {
    std::lock_guard<std::mutex> guard(mtx_);
    if (consumer_map_.count(table_name) == 0) {
      return;
    }
    table_ref_count_[table_name]--;
    if (table_ref_count_[table_name] == 0) {
      // consumer finish callback!
      is_finish = true;
    }
  }
  // to do dump_pool
  if (is_finish) {
    auto consumer_ptr = consumer_map_[table_name];
    FinishConsumer(consumer_ptr);
  }
}

}  // namespace index_adapter
}  // namespace ks
