#pragma once

#include <atomic>
#include <limits>
#include <map>
#include <memory>
#include <mutex>
#include <set>
#include <sstream>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>
#include "absl/strings/substitute.h"
#include "base/file/file_path.h"
#include "perfutil/perfutil.h"
#include "teams/ad/ad_index/framework/pb_reader/pb_file_loader.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/engine_base/utils/deploy_variable.h"
#include "teams/ad/engine_base/utils/is_valid_table.h"
#include "teams/ad/index_adapter/strategy/strategy_base.h"
#include "teams/ad/index_adapter/utils/hot_data/hot_data_center.h"
#include "teams/ad/index_adapter/utils/kconf/kconf.h"
#include "teams/ad/index_builder/multiform_filter/common_tools.h"
#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"
#include "teams/ad/index_builder/utils/hdfs_uploader.h"
#include "teams/ad/index_builder/utils/kafka2hive/kafka2hive_manager.h"
#include "teams/ad/index_builder/utils/md5_helper.h"
#include "teams/ad/index_builder/utils/table_config.h"
#include "teams/ad/index_adapter/utils/hdfs_util/hdfs_wrapper.h"
#include "teams/ad/index_adapter/utils/hdfs_util/hdfs_writer.h"
#include "teams/ad/index_adapter/utils/hdfs_util/file_writer.h"

namespace ks {
namespace index_adapter {

/*
  1. 数据按部署过滤
  2. 分片 & 分目录能力
*/

using ks::index_builder::GetExtensionField;
using ks::index_builder::GetPrimaryKey;
using ks::infra::PerfUtil;

class MessageConsumer {
 public:
  MessageConsumer(const std::string& table_name, const std::string& output_path,
                  const std::string& upload_path);

  virtual std::string DebugInfo();
  virtual bool Consume(const kuaishou::ad::AdInstance& input_ad) = 0;
  virtual bool Consume(google::protobuf::Message* msg) = 0;
  virtual bool Finish() = 0;
  virtual bool Prepare() = 0;

  virtual std::string GetOutputPath() { return output_path_; }

  virtual std::string GetUploadPath() { return upload_path_; }
  virtual std::vector<std::string> GetOutputPaths() { return {output_path_}; }
  virtual int64_t GetRecordNum() { return record_counter; }

  virtual std::string GetTableFile() { return table_name_ + ".base"; }

  virtual std::string GetTableName() { return table_name_; }

  virtual std::string GetProtoName() { return proto_name_; }

  virtual void SetPrefix(const std::string& prefix) { prefix_ = prefix; }

  virtual void UpdateTableInfo(kuaishou::ad::tables::DumpInfo* dump_info,
                               std::mutex* mtx) = 0;

 protected:
  std::string type_name_;
  std::string table_name_;
  std::string upload_path_;
  std::string output_path_;
  std::string proto_name_;
  std::string prefix_;

  int64_t record_counter{0};
  std::vector<std::string> local_output_dirs_;
  std::vector<std::string> hdfs_output_dirs_;
  kuaishou::ad::AdEnum::AdInstanceType msg_type_;
};

class AdIndexConsumer : public MessageConsumer {
 public:
  explicit AdIndexConsumer(const std::string& table_name,
                           const std::string& output_path,
                           const std::string& upload_path)
      : MessageConsumer(table_name, output_path, upload_path) {}

  ~AdIndexConsumer() = default;

  bool Prepare() override;

  bool IsDupDataCheck(kuaishou::ad::AdInstance* ad);

  bool Consume(const kuaishou::ad::AdInstance& input_ad) override;

  bool Consume(google::protobuf::Message* msg) { return true; }

  // upload and so
  bool Finish() override;

  bool DupCheck(kuaishou::ad::AdInstance* ad);
  void UpdateTableInfo(kuaishou::ad::tables::DumpInfo* dump_info,
                       std::mutex* mtx) override;

 protected:
  std::mutex mutex_;
  ad_base::PbWriter<kuaishou::ad::AdInstance> pb_writer_;
  std::string output_file_;
  std::unordered_map<int64_t, std::string> target_map_;
  int64_t file_size_{0};
  absl::flat_hash_set<int64_t> id_set_;
};

/*
  支持 lazy 模式
*/
class AdIndexLazyConsumer : public AdIndexConsumer {
 public:
  explicit AdIndexLazyConsumer(const std::string& table_name,
                               const std::string& output_path,
                               const std::string& upload_path)
      : AdIndexConsumer(table_name, output_path, upload_path) {
    lazy_output_path_ = base::FilePath(output_path)
                            .Append(table_name_ + "_lazy.base")
                            .ToString();
    LOG(INFO) << table_name_ << " append lazy data , path" << lazy_output_path_;
  }
  bool Prepare() override;
  bool IsLazyData(kuaishou::ad::AdInstance* input_ad);
  bool Consume(const kuaishou::ad::AdInstance& input_ad) override;
  bool Consume(google::protobuf::Message* msg);

  // upload and so
  bool Finish() override;

  void UpdateTableInfo(kuaishou::ad::tables::DumpInfo* dump_info,
                       std::mutex* mtx) override;
  std::vector<std::string> GetOutputPaths();

 protected:
  ad_base::PbWriter<kuaishou::ad::AdInstance> lazy_pb_writer_;
  std::string lazy_output_path_;
  int64_t lazy_record_counter_{0};
  int64_t lazy_file_size_{0};
};

/*
  输出多个分片而非单一文件
*/
class AdIndexMutiFileConsumer : public AdIndexConsumer {
 public:
  explicit AdIndexMutiFileConsumer(const std::string& table_name,
                                   const std::string& output_path,
                                   const std::string& upload_path)
      : AdIndexConsumer(table_name, output_path, upload_path) {
    dump_info_pool_ = std::make_shared<thread::ThreadPool>(1);
    output_path_ = output_path;
    kMaxFileSize = Kconf::indexDumpShardSize();
  }
  ~AdIndexMutiFileConsumer() {
    if (dump_info_pool_.get() != nullptr) {
      dump_info_pool_->JoinAll();
      dump_info_pool_.reset();
    }
  }
  bool Prepare() override;
  bool Consume(const kuaishou::ad::AdInstance& input_ad) override;

  bool Consume(google::protobuf::Message* msg);
  bool Finish() override;

  void UpdateTableInfo(kuaishou::ad::tables::DumpInfo* dump_info,
                       std::mutex* mtx) override;
  std::vector<std::string> GetOutputPaths() override;
  void CheckMd5(std::string path);

 protected:
  void UpdatePbWirter() {
    if (curr_file_size_ > kMaxFileSize) {
      files_size_.push_back(curr_file_size_);
      auto file_name =
          absl::Substitute("$0_$1.base", table_name_, file_paths_.size());
      pb_writer_.Flush();
      LOG_ASSERT(pb_writer_.FSync()) << "Fsync failed: " << file_paths_.back();
      auto path = base::FilePath(output_path_).Append(file_name).ToString();
      file_paths_.push_back(path);
      int ret = pb_writer_.OpenAppend(path.c_str());
      LOG_ASSERT(ret == 0) << "open pb file writer failed, path: " << path
                           << "ret: " << ret;
      instance_nums_.push_back(0);
      curr_file_size_ = sizeof(ad_base::PbHeader);
      dump_info_pool_->AddTask(
          ::NewCallback(this, &AdIndexMutiFileConsumer::CheckMd5,
                        file_paths_[file_paths_.size() - 2]));
    }
    return;
  }

 protected:
  std::vector<std::string> file_paths_;
  std::vector<int64_t> instance_nums_;
  int64_t kMaxFileSize;
  int64_t curr_file_size_{0};
  std::vector<int64_t> files_size_;
  std::map<std::string, std::string> files_md5_;
  std::shared_ptr<thread::ThreadPool> dump_info_pool_;
};

namespace { // NOLINT
// 单个 pb 实例最大长度
const size_t kMaxInstanceLength = 1024 * 1024;
}  // namespace
class AdIndexMutiFileTxtFormatConsumer : public AdIndexConsumer {
 public:
  explicit AdIndexMutiFileTxtFormatConsumer(const std::string& table_name,
                                   const std::string& output_path,
                                   const std::string& upload_path)
      : AdIndexConsumer(table_name, output_path, upload_path) {
    kMaxFileSize = Kconf::indexDumpShardSize();
    if (kMaxFileSize == 0) {
      kMaxFileSize = 536870912;
    }
    output_path_ = output_path;
    LOG(INFO) << "use AdIndexMutiFileTxtFormatConsumer, upload_path_: "
        << upload_path_ << ", kMaxFileSize: " << kMaxFileSize << ", table_name: " << table_name_
        << ", output_path: " << output_path_;
  }

  ~AdIndexMutiFileTxtFormatConsumer() {}

  bool Prepare() override;
  bool Consume(const kuaishou::ad::AdInstance& input_ad) override;
  bool Consume(google::protobuf::Message* msg);
  bool Finish() override;
  void UpdateTableInfo(kuaishou::ad::tables::DumpInfo* dump_info,
                       std::mutex* mtx) override;
  std::vector<std::string> GetOutputPaths() override;
  void UploadDumpInfoTable(kuaishou::ad::tables::DumpInfo* dump_info);
  bool RecordTableNumber(const std::string& table_name, int64_t record_count);
  bool GetLastVersionTableNumber(const std::string& table_name, int64_t* record_count);

 protected:
  void UpdateHdfsWrapper() {
    if (curr_file_size_ > kMaxFileSize) {
      auto file_name = absl::Substitute("$0-$1", table_name_, file_paths_.size());

      file_writer_.Flush();
      LOG_ASSERT(file_writer_.FSync()) << "Fsync failed: " << file_paths_.back();
      auto path = base::FilePath(output_path_).Append(file_name).ToString();
      file_paths_.push_back(path);
      instance_nums_.push_back(0);

      LOG(INFO) << "upload_paths_ size: " << file_paths_.size()
          << ", back: " << file_paths_.back() << ", path: "
          << path << ", curr_file_size_: " << curr_file_size_
          << ", kMaxFileSize: " << kMaxFileSize;
      curr_file_size_ = 0;
    }
  }

 protected:
  std::mutex mutex_;
  std::vector<std::string> file_paths_;
  std::vector<int64_t> instance_nums_;
  ks::index_adapter::HDFSWrapper hdfs_wrapper_;
  int64_t kMaxFileSize;
  int64_t curr_file_size_{0};
  FileWriter file_writer_;
};

using MessageConsumerPtr = std::shared_ptr<MessageConsumer>;
// 单点输出模式
using MessageConsumerMap = std::unordered_map<std::string, MessageConsumerPtr>;

}  // namespace index_adapter
}  // namespace ks
