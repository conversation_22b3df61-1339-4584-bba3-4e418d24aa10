#pragma once

#include <memory>
#include <string>
#include <vector>
#include <set>
#include <map>
#include "base/file/file_path.h"
#include "base/thread/thread_pool.h"
#include "absl/strings/substitute.h"
#include "absl/container/flat_hash_map.h"
#include "teams/ad/index_adapter/utils/p2p/p2p_task.h"
#include "teams/ad/index_adapter/utils/consumer/message_consumer.h"
#include "teams/ad/engine_base/utils/deploy_variable.h"
#include "teams/ad/index_adapter/utils/hdfs_uploader.h"
#include "teams/ad/index_adapter/utils/perf_manager/perf_manager.h"

namespace ks {
namespace index_adapter {

struct ExtraConsumerDataInfo {
  std::vector<std::string> p2p_dirs;
  std::string hot_data_path;
};

class ConsumerManagerBase {
 public:
  ConsumerManagerBase() {}
  virtual ~ConsumerManagerBase() {}

  virtual void Init(const std::vector<std::string>& table_names,
                    const std::string& output_path,
                    const std::string& upload_path,
                    const ExtraConsumerDataInfo& extra_data) = 0;

  virtual std::shared_ptr<MessageConsumer> GetConsumer(
      const std::string& table_name, const std::string& output_path,
      const std::string& upload_path, const ExtraConsumerDataInfo& extra_data) = 0;

  // if a table ref = 0, then config has error
  virtual bool IsValid() = 0;

  virtual bool Consume(const kuaishou::ad::AdInstance& input_ad);

  virtual bool RegisterTable(const std::string& table_name);

  virtual void UnRegisterTable(const std::string& table_name);

  virtual void DebugInfo() = 0;

  virtual void FinishConsumer(MessageConsumerPtr consumer_ptr) = 0;

  virtual void Finish() = 0;

  virtual void UpdateTableRecordToPerf() = 0;

  virtual void UpdateTableInfo(MessageConsumerPtr consumer_ptr) = 0;

  void SetPrefix(const std::string& prefix) { prefix_ = prefix; }

 protected:
  std::mutex mtx_;
  MessageConsumerMap consumer_map_;
  absl::flat_hash_map<std::string, int64_t> table_ref_count_;
  std::string prefix_;
};

}  // namespace index_adapter
}  // namespace ks
