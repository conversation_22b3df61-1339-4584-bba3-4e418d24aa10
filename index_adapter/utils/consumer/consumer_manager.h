#pragma once

#include <map>
#include <memory>
#include <set>
#include <string>
#include <vector>
#include "absl/container/flat_hash_map.h"
#include "absl/strings/substitute.h"
#include "base/file/file_path.h"
#include "base/thread/thread_pool.h"
#include "teams/ad/engine_base/utils/deploy_variable.h"
#include "teams/ad/index_adapter/utils/consumer/message_consumer.h"
#include "teams/ad/index_adapter/utils/hdfs_uploader.h"
#include "teams/ad/index_adapter/utils/p2p/p2p_task.h"
#include "teams/ad/index_adapter/utils/perf_manager/perf_manager.h"
#include "teams/ad/index_adapter/utils/consumer/consumer_manager_base.h"

namespace ks {
namespace index_adapter {

class ConsumerManager : public ConsumerManagerBase {
 public:
  ConsumerManager();
  ~ConsumerManager();

  void Init(const std::vector<std::string>& table_names,
            const std::string& output_path, const std::string& upload_path,
            const ExtraConsumerDataInfo& extra_data) override;

  // if a table ref = 0, then config has error
  bool IsValid() override;

  std::shared_ptr<MessageConsumer> GetConsumer(
      const std::string& table_name,
      const std::string& output_path,
      const std::string& upload_path,
      const ExtraConsumerDataInfo& extra_data) override;

  void DebugInfo() override;

  void Finish() override;

  void UpdateTableRecordToPerf() override;

  void FinishConsumer(MessageConsumerPtr consumer_ptr) override;

  void UpdateTableInfo(MessageConsumerPtr consumer_ptr) override;

 protected:
  HdfsUploader hdfs_uploader_;
  std::string upload_path_;
  std::string output_path_;
  std::atomic<bool> thread_pool_stop_{false};
  thread::ThreadPool hdfs_upload_thread_pool_{40};
  thread::ThreadPool dump_info_update_pool_{40};
  kuaishou::ad::tables::DumpInfo dump_info_;
  int64_t shard_no_{-1};  // 分片编号
  P2pTaskManager p2p_manager_;
  std::set<std::string> lazy_tables_;
  // std::string prefix_;
};

}  // namespace index_adapter
}  // namespace ks
