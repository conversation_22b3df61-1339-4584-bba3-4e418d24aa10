#pragma once

#include <third_party/abseil/absl/time/time.h>
#include <atomic>
#include <string>
#include <thread>
#include <utility>
#include <vector>
#include <set>
#include <memory>
#include "absl/container/flat_hash_map.h"
#include "absl/container/flat_hash_set.h"
#include "absl/strings/str_cat.h"
#include "falcon/counter.h"
#include "perfutil/perfutil.h"
#include "redis_proxy_client/redis_proxy_client.h"

#include "teams/ad/index_adapter/utils/hdfs_util/hdfs_wrapper.h"
#include "teams/ad/index_adapter/utils/builder/builder.h"

namespace ks {
namespace index_adapter {

class DataDumper {
 public:
  DataDumper();
  bool Start();
  void Stop();

 private:
  void DumpThread();
  void PointThread();
  void InitFlags();
  // get das_version and stream_map
  void Prepare();
  bool IsValid() { return (!das_version_.empty()) && (stream_map_.size() > 0); }
  void SingleStreamRun(ks::index_builder::index_base::MessageStreamPtr hdfs_stream,
                       std::vector<std::shared_ptr<Builder>> builder_ptr_vec);
  std::atomic<bool> running_{false};
  std::thread dump_thread_;
  std::thread point_thread_;
  absl::Time t{absl::FromTimeT(0)};

  Builder builder_;
  TableConfig table_conf_;
  // std::string output_dir_{"../ad_index/"};

  void BuilderRun(int64_t shard_no);
  std::string das_version_;
  ks::index_builder::index_base::MessageStreamMap stream_map_;
  std::set<std::string> forbid_pb_set_;
};
}  // namespace index_adapter
}  // namespace ks
