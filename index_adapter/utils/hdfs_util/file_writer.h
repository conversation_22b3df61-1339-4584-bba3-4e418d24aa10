#pragma once

#include <stdint.h>
#include <stdio.h>
#include <fcntl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <unistd.h>

#include <memory>
#include <string>

#include "base/common/logging.h"
#include "teams/ad/ad_base/src/common/common.h"
#include "teams/ad/ad_index/framework/pb_reader/pb_header.h"

namespace  ks {
namespace index_adapter {

class FileWriter {
 public:
  FileWriter() : fp_(nullptr), count_(0) {}
  ~FileWriter() {
    Close();
  }

  // 打开一个文件来写入；如果之前已经打开了文件，会关闭掉
  // 会写入 pb 文件头
  // 成功返回 0，失败返回非 0
  int Open(const char *path) {
    Close();
    fp_ = fopen(path, "wb");
    if (fp_ == nullptr) {
      LOG(ERROR) << "open pb write file failed, path: " << path << ", errno: " << errno;
      return -1;
    }
    return 0;
  }
  // 以追加的方式打开
  int OpenAppend(const char *path) {
    Close();
    fp_ = fopen(path, "rb+");
    if (fp_ == nullptr) {
      LOG(WARNING) << "open pb write file failed, may not exists path: " << path;
      fp_ = fopen(path, "wb+");
      if (fp_ == nullptr) {
        LOG(ERROR) << "open pb write file failed, path: " << path;
        return -1;
      }
    }
    fseek(fp_, 0, SEEK_END);
    return 0;
  }
  // 关闭一个文件；可以重复调用
  void Close() {
    count_ = 0;
    if (fp_ != nullptr) {
      fclose(fp_);
      fp_ = nullptr;
    }
  }
  // 同步数据到存储，保证数据持久化，磁盘慎用
  // 只保证内核数据持久化，用户态 buffer 写先调用 FFlush
  bool FSync() {
    if (fp_ == nullptr) {
      LOG(ERROR) << "FSync with no filestream";
      return false;
    }
    auto file_no = fileno(fp_);
    if (file_no == -1) {
      LOG(ERROR) << "Get file descriptor failed for: " << strerror(errno);
      return false;
    }
    if (fsync(file_no) != 0) {
      LOG(ERROR) << " Fsync failed  for: " << strerror(errno);
      return false;
    }
    return true;
  }

  // 写入一个 string
  // 成功返回 0，失败返回非 0
  // 写入失败时，文件会回退到写入前位置，可以继续写入
  int Write(const std::string &data) {
    if (fp_ == nullptr) {
      LOG(ERROR) << "cannot write pb, writer is no opened";
      return -1;
    }
    if (data.size() >= ks::ad_base::kMaxInstanceLength) {
      LOG(ERROR) << "pb is too large, byte size: " << data.size();
      return -1;
    }
    // 记录写入前位置
    int64_t offset = ftell(fp_);
    // 写入 length
    uint32_t length = (uint32_t)data.size();
    size_t write_len = fwrite(data.c_str(), 1, length, fp_);
    if (write_len != length) {
      LOG(ERROR) << "write pb bytes into file failed, expect bytes: " << length
                << ", actual bytes: " << write_len;
      fseek(fp_, offset, SEEK_SET);
      return -1;
    }
    ++count_;
    return 0;
  }

  // 刷新缓存数据到磁盘
  void Flush() {
    if (fp_ != nullptr) {
      fflush(fp_);
    }
  }

  // 最近一次 Open 以来成功写入的 pb 实例数
  size_t Count() const {
    return count_;
  }
  // 获取当前写入的位置偏移
  size_t CurrentOffset() const {
    if (fp_ != nullptr) {
      return ftell(fp_);
    }
    return 0;
  }

 private:
  FILE *fp_;
  char buffer_[ks::ad_base::kMaxInstanceLength];
  size_t count_;
};
}  // namespace index_adapter
}  // namespace ks
