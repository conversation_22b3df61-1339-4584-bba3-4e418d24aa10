#pragma once
#include <string>
#include <vector>

#include "serving_base/hdfs_read/hdfs_file_stream.h"

DECLARE_string(hdfs_host);
DECLARE_int32(hdfs_port);
DECLARE_int32(hdfs_max_retry_times);
DECLARE_int32(hdfs_retry_interval);

namespace ks {
namespace index_adapter {

struct HadoopEntry {
  uint64_t last_mod;  // 单位：秒
  uint64_t size;
  std::string
      full_name;     // 完整路径
                     // viewfs: //
                     // hadoop-lt-cluster/home/<USER>/embedding/fanstop_video_feature_embedding/2019-08-07_1306
  std::string name;  // file or dir name
};

class HDFSWrapper {
 public:
  HDFSWrapper();
  ~HDFSWrapper();
  int Initialize();
  bool Exists(const std::string &hdfs_path);
  int CreateDirectory(const std::string &hdfs_path);

  // 列出所有的子路径
  int ListSubDirectories(const std::string &hdfs_path, std::vector<HadoopEntry> *entries);
  int RemoveDirectory(const std::string &hdfs_path);
  int ListFiles(const std::string &hdfs_path, std::vector<HadoopEntry> *entries);
  int MoveFile(const std::string &src_path, const std::string &dest_path);

  int CopyFile(const std::string &src_path, const std::string &dest_path);

  int ReadFile(const std::string &path, std::string &content);  // NOLINT

  int WriteFileAppend(const std::string &path, const std::string &content);

  int WriteFile(const std::string &path, const std::string &content);

  int StreamOpen(const std::string &path);

  int FetchPartData(std::string *content, int line_num = 5000);

 private:
  std::string GetEntryName(const std::string &path);

  int ListAllEntries(const std::string &hdfs_path,
                     std::vector<HadoopEntry> *entries,
                     char item_type,
                     const std::vector<std::string> &pattern);

 private:
  hadoop::HDFSHandle hdfs_handle_;
  hdfsFS fs_handle_;
  bool init_;
  hadoop::HDFSFileStream *stream_ptr_{nullptr};
  bool stream_status_{false};
};
}  // namespace index_adapter
}  // namespace ks
