#include "teams/ad/index_adapter/utils/hdfs_util/hdfs_delete.h"

#include "base/common/logging.h"
#include "serving_base/hdfs_read/hdfs_file_stream.h"

DECLARE_string(hdfs_host);
DECLARE_int32(hdfs_port);

namespace ks {
namespace index_adapter {
int StaticRemoveDirectory(const std::string &hdfs_path) {
  /*
hadoop::HDFSHandle hdfs_handle_(FLAGS_hdfs_host.c_str(), FLAGS_hdfs_port);
if (!hdfs_handle_.Connect()) {
  LOG(ERROR) << "failed to connect to hdfs, errno: " << errno;
  return -1;
}
hdfsFS fs_handle_ = hdfs_handle_.get_handle();
int ret = hdfsDelete(fs_handle_, hdfs_path.c_str(), true);
hdfs_handle_.Disconnect();
*/
  return 0;
}
}  // namespace index_adapter
}  // namespace ks
