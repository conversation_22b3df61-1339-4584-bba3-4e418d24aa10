#include <regex>
#include <utility>
#include <algorithm>

#include "base/common/logging.h"
#include "base/strings/string_split.h"
#include "teams/ad/index_adapter/utils/hdfs_util/hdfs_wrapper.h"
#include "teams/ad/index_adapter/utils/hdfs_util/hdfs_delete.h"

DEFINE_string(hdfs_host, "default", "hdfs host");
DEFINE_int32(hdfs_port, 0, "hdfs port");

DEFINE_int32(hdfs_max_retry_times, 10, "hdfs max retry times");
DEFINE_int32(hdfs_retry_interval, 3, "hdfs retry interval");

namespace ks {
namespace index_adapter {

HDFSWrapper::HDFSWrapper() : hdfs_handle_(FLAGS_hdfs_host.c_str(), FLAGS_hdfs_port), init_(false) {}

HDFSWrapper::~HDFSWrapper() {
  if (init_) {
    hdfs_handle_.Disconnect();
  }
  if (stream_ptr_) {
    delete stream_ptr_;
    stream_ptr_ = nullptr;
  }
  init_ = false;
}
int HDFSWrapper::Initialize() {
  if (!hdfs_handle_.Connect()) {
    LOG(ERROR) << "failed to connect to hdfs, errno: " << errno;
    return -1;
  }
  init_ = true;
  fs_handle_ = hdfs_handle_.get_handle();
  return 0;
}

std::string HDFSWrapper::GetEntryName(const std::string& path) {
  size_t pos = path.find_last_of("/");
  if (pos == path.npos) {
    return "";
  }
  return path.substr(pos + 1);
}

int HDFSWrapper::ListAllEntries(const std::string& hdfs_path, std::vector<HadoopEntry>* entries,
                                char item_type, const std::vector<std::string>& pattern) {
  if (entries == nullptr) {
    LOG(ERROR) << "nullptr found";
    return -1;
  }
  entries->clear();
  int num_entries = 0;
  hdfsFileInfo* file_info = hdfsListDirectory(fs_handle_, hdfs_path.c_str(), &num_entries);
  if (file_info == nullptr) {
    LOG(WARNING) << "failed to list item by path " << hdfs_path;
    return -1;
  }
  auto free_file_info = [](hdfsFileInfo& file_info) {
    // typedef struct  {
    //     tObjectKind mKind;   /* file or directory */
    //     char *mName;         /* the name of the file */
    //     tTime mLastMod;      /* the last modification time for the file in seconds */
    //     tOffset mSize;       /* the size of the file in bytes */
    //     short mReplication;    /* the count of replicas */
    //     tOffset mBlockSize;  /* the block size for the file */
    //     char *mOwner;        /* the owner of the file */
    //     char *mGroup;        /* the group associated with the file */
    //     short mPermissions;  /* the permissions associated with the file */
    //     tTime mLastAccess;    /* the last access time for the file in seconds */
    // } hdfsFileInfo;
    delete[] file_info.mName;
    delete[] file_info.mOwner;
    delete[] file_info.mGroup;
  };
  for (int i = 0; i < num_entries; ++i) {
    auto& cur_file_info = file_info[i];
    if (cur_file_info.mKind != item_type) {
      free_file_info(cur_file_info);
      continue;
    }

    std::string n = GetEntryName(cur_file_info.mName);
    bool match = false;
    for (auto& p : pattern) {
      std::regex r(p);
      match = std::regex_match(n, r);
      if (match) break;
    }
    if (!match && pattern.size()) continue;

    HadoopEntry entry;
    entry.full_name = cur_file_info.mName;
    entry.name = std::move(n);
    entry.last_mod = static_cast<uint64_t>(cur_file_info.mLastMod);
    entry.size = static_cast<uint64_t>(cur_file_info.mSize);
    free_file_info(cur_file_info);
    entries->push_back(entry);
  }
  delete[] file_info;
  return 0;
}

int HDFSWrapper::ListSubDirectories(const std::string& hdfs_path, std::vector<HadoopEntry>* entries) {
  std::vector<std::string> pattern;
  //  pattern.push_back("\\d{8}");
  //  pattern.push_back("dt=\\d{4}-\\d{2}-\\d{2}");
  //  pattern.push_back("p_date=\\d{8}");
  //  pattern.push_back("\\d{4}-\\d{2}-\\d{2}_\\d{4}");
  return ListAllEntries(hdfs_path, entries, 'D', pattern);
}

int HDFSWrapper::ListFiles(const std::string& hdfs_path, std::vector<HadoopEntry>* entries) {
  std::vector<std::string> pattern;
  return ListAllEntries(hdfs_path, entries, 'F', pattern);
}

bool HDFSWrapper::Exists(const std::string& hdfs_path) {
  if (0 == hdfsExists(fs_handle_, hdfs_path.c_str())) {
    return true;
  }
  return false;
}

int HDFSWrapper::CreateDirectory(const std::string& hdfs_path) {
  return hdfsCreateDirectory(fs_handle_, hdfs_path.c_str());
}

int HDFSWrapper::RemoveDirectory(const std::string& hdfs_path) {
  return StaticRemoveDirectory(hdfs_path);
  // return hdfsDelete(fs_handle_, hdfs_path.c_str(), true);
}

int HDFSWrapper::MoveFile(const std::string& src_path, const std::string& dest_path) {
  return hdfsMove(fs_handle_, src_path.c_str(), fs_handle_, dest_path.c_str());
}

int HDFSWrapper::CopyFile(const std::string& src_path, const std::string& dest_path) {
  return hdfsCopy(fs_handle_, src_path.c_str(), fs_handle_, dest_path.c_str());
}

int HDFSWrapper::ReadFile(const std::string& path, std::string& content) {
  hadoop::HDFSFileStream stream(FLAGS_hdfs_host.c_str(), FLAGS_hdfs_port);
  stream.set_retry_times(FLAGS_hdfs_max_retry_times);
  stream.set_retry_interval(FLAGS_hdfs_retry_interval);

  if (!stream.Open(path.c_str(), O_RDONLY)) {
    LOG(ERROR) << "hdfs file open failed. path:" << path;
    return -1;
  }

  int buf_size = 512 * 1024;  // 512k
  std::string buf;
  buf.resize(buf_size + 1);
  while (true) {
    int read_size = stream.Read(&buf[0], buf_size);
    if (read_size <= 0) {
      break;
    }
    content.append(buf, 0, read_size);
  }

  if (!stream.Eof()) {
    LOG(ERROR) << "hdfs file can't read all";
    stream.Close();
    return -1;
  }

  stream.Close();
  return 0;
}

int HDFSWrapper::WriteFileAppend(const std::string& path, const std::string& content) {
  hadoop::HDFSFileStream stream(FLAGS_hdfs_host.c_str(), FLAGS_hdfs_port);
  stream.set_retry_times(FLAGS_hdfs_max_retry_times);
  stream.set_retry_interval(FLAGS_hdfs_retry_interval);

  if (!stream.Open(path.c_str(), O_WRONLY| O_APPEND)) {
    LOG(ERROR) << "hdfs file open failed. path:" << path;
    return -1;
  }
  size_t len = content.size();
  size_t written = 0;
  size_t patch_size = 512 * 1024;  // 512k
  while (written < len) {
    int write_size = std::min(patch_size, len - written);
    int cur_ret = stream.Write(content.c_str() + written, write_size);
    if (cur_ret == -1) {
      LOG(ERROR) << "hdfs write return -1, abort, path:" << path;
      return -1;
    }
    written += cur_ret;
  }
  if (!stream.Flush()) {
    LOG(ERROR) << "failed to flush to hdfs, errno: " << errno << ", path:" << path;
    return -1;
  }

  if (!stream.Close()) {
    LOG(ERROR) << "failed to close file stream of path:" << path << ", errno: " << errno;
    return -1;
  }
  return (content.size() == written) ? 0 : -1;
}

int HDFSWrapper::WriteFile(const std::string& path, const std::string& content) {
  hadoop::HDFSFileStream stream(FLAGS_hdfs_host.c_str(), FLAGS_hdfs_port);
  stream.set_retry_times(FLAGS_hdfs_max_retry_times);
  stream.set_retry_interval(FLAGS_hdfs_retry_interval);

  if (!stream.Open(path.c_str(), O_WRONLY)) {
    LOG(ERROR) << "hdfs file open failed. path:" << path;
    return -1;
  }

  size_t len = content.size();
  size_t written = 0;
  size_t patch_size = 512 * 1024;  // 512k
  while (written < len) {
    int write_size = std::min(patch_size, len - written);
    int cur_ret = stream.Write(content.c_str() + written, write_size);
    if (cur_ret == -1) {
      LOG(ERROR) << "hdfs write return -1, abort, path:" << path;
      return -1;
    }
    written += cur_ret;
  }

  if (!stream.Flush()) {
    LOG(ERROR) << "failed to flush to hdfs, errno: " << errno << ", path:" << path;
    return -1;
  }

  if (!stream.Close()) {
    LOG(ERROR) << "failed to close file stream of path:" << path << ", errno: " << errno;
    return -1;
  }
  return (content.size() == written) ? 0 : -1;
}

int HDFSWrapper::StreamOpen(const std::string& path) {
  stream_ptr_ =
      new hadoop::HDFSFileStream(FLAGS_hdfs_host.c_str(), FLAGS_hdfs_port);
  stream_ptr_->set_retry_times(FLAGS_hdfs_max_retry_times);
  stream_ptr_->set_retry_interval(FLAGS_hdfs_retry_interval);
  if (!stream_ptr_->Open(path.c_str(), O_RDONLY)) {
    LOG(ERROR) << "hdfs file open failed. path:" << path;
    stream_status_ = false;
    return -1;
  }
  stream_status_ = true;
  return 0;
}

int HDFSWrapper::FetchPartData(std::string* content, int line_num) {
  content->clear();
  if (!stream_status_) {
    return -1;
  }
  std::string line;
  const int buffer_length = 1000;
  line.reserve(buffer_length);
  for (int i = 0; i < line_num; i++) {
    if (stream_ptr_->ReadLine(&line)) {
      (*content) += line + "\n";
    } else {
      // reach the end
      stream_ptr_->Close();
      stream_status_ = false;
      break;
    }
  }
  if (content->size() > 0) {
    content->pop_back();
  }
  return (content->size() > 0)? 0 : -1;
}

}  // namespace index_adapter
}  // namespace ks
