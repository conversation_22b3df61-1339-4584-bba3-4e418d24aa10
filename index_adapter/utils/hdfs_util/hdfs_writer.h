#pragma once
#include <cstddef>
#include <string>

#include "base/common/logging.h"
#include "serving_base/util/hdfs.h"

namespace  ks {
namespace index_adapter {

class HdfsWriter {
 public:
  HdfsWriter() {
    fs_ = hdfsConnectAsUser("default", 0, "ad");
  }

  ~HdfsWriter() {
    Close();
  }

  // 打开一个文件来写入；如果之前已经打开了文件，会关闭掉
  // 成功返回 0，失败返回非 0
  int Open(const char* path) {
    if (fs_ == nullptr) {
      fs_ = hdfsConnectAsUser("default", 0, "ad");
    }
    file_ = hdfsOpenFile(CHECK_NOTNULL(fs_), path, O_WRONLY, 0, 0, 0);
    if (file_ == nullptr) {
      LOG(ERROR) << "open pb hdfs write file failed, path: " << path << ", errno: " << errno;
      return -1;
    }
    return 0;
  }

  // 关闭一个文件；可以重复调用
  void Close() {
    if (file_ && fs_) {
      hdfsCloseFile(fs_, file_);
      fs_ = nullptr;
      file_ = nullptr;
    }
    if (fs_) {
      hdfsDisconnect(fs_);
      fs_ = nullptr;
    }
  }
  // 同步数据到存储，保证数据持久化，磁盘慎用
  // 只保证内核数据持久化，用户态 buffer 写先调用 FFlush
  bool FSync();

  int Exists(const char* path) {
    return hdfsExists(fs_, path);
  }

  // 成功返回 0，失败返回非 0
  // 写入失败时，文件会回退到写入前位置，可以继续写入
  int Write(const std::string& data) {
    if (file_ == nullptr) {
      LOG(ERROR) << "cannot write pb, writer is no opened";
      return -1;
    }
    // 记录写入前位置
    int64_t offset = hdfsTell(fs_, file_);
    uint32_t length = data.length();
    size_t write_len = hdfsWrite(fs_, file_, data.data(), length);
    if (write_len != length) {
      LOG(ERROR) << "write pb bytes into file failed, expect bytes: " << length
                 << ", actual bytes: " << write_len;
      hdfsSeek(fs_, file_, offset);
      return -1;
    }
    return 0;
  }

  // 刷新缓存数据到磁盘
  void Flush() {
    if (fs_ && file_) {
      hdfsFlush(fs_, file_);
    }
  }

 private:
  hdfsFS fs_{nullptr};
  hdfsFile file_{nullptr};
};
}  // namespace index_adapter
}  // namespace ks
