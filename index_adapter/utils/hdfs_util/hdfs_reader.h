#pragma once
#include <iostream>
#include <string>

#include "serving_base/hdfs_read/hdfs_util.h"
#include "serving_base/hdfs_read/hdfs_file_stream.h"
// copy from teams/ad/ad_algorithm/util/hdfs_util.h
namespace ks {
namespace index_adapter {

class HDFSReader {
 private:
  char *buffer_;
  hdfsFS fs_;
  hdfsFile read_file_;
  size_t buffer_pos_;
  size_t buffer_size_;

 public:
  const int MAX_BUFFER_SIZE = 10240;

  explicit HDFSReader(const std::string file_path) {
    fs_ = hdfsConnect("default", 0);
    read_file_ = hdfsOpenFile(fs_, file_path.c_str(), O_RDONLY, 0, 0, 0);
    buffer_ = new char[MAX_BUFFER_SIZE];
    buffer_pos_ = 0;
    buffer_size_ = 0;
  }

  size_t ReadLine(std::string &line, size_t line_size) {  // NOLINT
    line.clear();
    size_t i = buffer_pos_;
    size_t c = 0;
    size_t len = line_size - 1;
    while (fs_ != NULL && read_file_ != NULL && c < len) {
      if (i < buffer_size_) {
        line += buffer_[i++];
        c++;
        if (buffer_[i - 1] == '\n') {
          break;
        }
      } else {
        size_t tsize = hdfsRead(fs_, read_file_, (void *)buffer_, MAX_BUFFER_SIZE);  // NOLINT
        i = 0;
        buffer_size_ = tsize;
        if (tsize == 0)
          break;
      }
    }
    buffer_pos_ = i;
    return c;
  }

  void ReadLine(std::string &line) {  // NOLINT
    line.clear();
    size_t i = buffer_pos_;
    while (fs_ != NULL && read_file_ != NULL) {
      if (i < buffer_size_) {
        if (buffer_[i] == '\n') {
          i += 1;
          break;
        } else {
          line += buffer_[i];
          i += 1;
        }
      } else {
        size_t tsize = hdfsRead(fs_, read_file_, (void *)buffer_, MAX_BUFFER_SIZE);  // NOLINT
        i = 0;
        buffer_size_ = tsize;
        if (tsize == 0)
          break;
      }
    }
    buffer_pos_ = i;
    // std::cout << "ReadLine " << line << std::endl;
  }

  ~HDFSReader() {
    if (hdfsCloseFile(fs_, read_file_) == -1) {
      LOG(ERROR) << "hdfs close error";
    }
    if (hdfsDisconnect(fs_) == -1) {
      LOG(ERROR) << "hdfs disconnect error";
    }
    delete[] buffer_;
  }
};

inline void UpdateCheckpointHDFS(const std::string &checkpoint_path, const std::string &model_path) {
  hdfsFS fs = hdfsConnect("default", 0);
  hdfsFile write_file = hdfsOpenFile(fs, checkpoint_path.c_str(), O_WRONLY | O_APPEND, 0, 0, 0);
  char *buffer = new char[1024];
  size_t size = sprintf(buffer, "%s\t%s\t1\n", model_path.c_str(), "md5");  // NOLINT
  hdfsWrite(fs, write_file, (void *)buffer, strlen(buffer));                // NOLINT
  CHECK(hdfsFlush(fs, write_file) == 0) << "Failed to flush model_version ";
  delete[] buffer;
  if (hdfsCloseFile(fs, write_file) == -1) {
    std::cout << "hdfs close error" << std::endl;
  }
}

inline std::string GetNowTimeStr() {
  time_t rawtime = 0;
  time(&rawtime);
  struct tm *dt;
  char buffer[30];
  dt = localtime(&rawtime);  // NOLINT
  strftime(buffer, sizeof(buffer), "%Y%m%d%H%M", dt);
  return std::string(buffer);
}

}  // namespace index_adapter
}  // namespace ks
