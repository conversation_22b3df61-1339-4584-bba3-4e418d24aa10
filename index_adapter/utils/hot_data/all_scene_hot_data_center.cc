#include "teams/ad/index_adapter/utils/hot_data/all_scene_hot_data_center.h"
#include <absl/time/clock.h>
#include <absl/time/time.h>
#include <cstring>
#include <mutex>
#include <algorithm>
#include <vector>
#include "base/common/closure.h"
#include "base/file/file_path.h"
#include "base/thread/thread_pool.h"
#include "teams/ad/index_adapter/utils/hdfs_util/hdfs_reader.h"
#include "serving_base/hdfs_read/hdfs_file_stream.h"
#include "serving_base/hdfs_read/hdfs_file_util.h"

namespace ks {
namespace index_adapter {

using kuaishou::ad::AdEnum;
void AllSceneHotDataCenter::Init(const std::unordered_map<int32_t, std::string>& paths) {
  for (const auto& [scene, path] : paths) {
    hot_map_[scene] = std::make_shared<HotDataLoader>(path);
  }
}

void AllSceneHotDataCenter::Start() {
  for (auto& loader : hot_map_) {
    loader.second->Start();
  }
}

AllSceneHotDataCenter::~AllSceneHotDataCenter() {
}

void AllSceneHotDataCenter::CheckStatus(const std::string dump_version) {
  for (auto& loader : hot_map_) {
    loader.second->CheckStatus(dump_version);
  }
}

#define MARKLAZY(scene, pb) \
  if (scene == \
      kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_EXTERNAL_CIRCULATION_UNIT) { \
    pb->set_lazy_external(lazy);  \
  } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_DEFAULT) {  \
    pb->set_lazy_internal(lazy); \
  } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_SEARCH) { \
    pb->set_lazy_search(lazy); \
  } else if (scene == \
             kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_UNIVERSE_DEFAULT) {  \
    pb->set_lazy_universe(lazy); \
  }

bool AllSceneHotDataCenter::ProcessForFull(kuaishou::ad::AdInstance* ad_instance) {
  auto type = ad_instance->type();
  if (type == AdEnum::UNIT) {
    auto* unit = ad_instance->MutableExtension(kuaishou::ad::tables::Unit::unit_old);
    int64_t id = unit->id();
    for (const auto& loader : hot_map_) {
      auto scene = loader.first;
      auto hot_data = loader.second;
      bool lazy = !hot_data->SearchUnit(id);
      MARKLAZY(scene, unit);
      LOG_EVERY_N(INFO, 1000000) << "vanke hot unit, scene=" << scene << ", lazy=" << lazy;
    }
    LOG_EVERY_N(INFO, 1000000) << "vanke hot unit, lazy_external=" << unit->lazy_external()
                               << ", lazy_internal=" << unit->lazy_internal()
                               << ", lazy_search=" << unit->lazy_search()
                               << ", lazy_universe=" << unit->lazy_universe();
  } else if (type == AdEnum::PHOTO_STATUS) {
    auto* photo = ad_instance->MutableExtension(kuaishou::ad::tables::PhotoStatus::photo_status_old);
    int64_t id = photo->photo_id();
    for (const auto& loader : hot_map_) {
      auto scene = loader.first;
      auto hot_data = loader.second;
      bool lazy = !hot_data->SearchPhoto(id);
      MARKLAZY(scene, photo);
      LOG_EVERY_N(INFO, 1000000) << "vanke hot photo, scene=" << scene << ", lazy=" << lazy;
    }
    LOG_EVERY_N(INFO, 1000000) << "vanke hot photo, lazy_external=" << photo->lazy_external()
                               << ", lazy_internal=" << photo->lazy_internal()
                               << ", lazy_search=" << photo->lazy_search()
                               << ", lazy_universe=" << photo->lazy_universe();
  }
  return true;
}
#undef MARKLAZY


}  // namespace index_adapter
}  // namespace ks
