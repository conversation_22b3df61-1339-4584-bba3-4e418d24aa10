#include "teams/ad/index_adapter/utils/hot_data/hot_data_center.h"
#include <absl/time/clock.h>
#include <absl/time/time.h>
#include <cstring>
#include <mutex>
#include <algorithm>
#include <vector>
#include "base/common/closure.h"
#include "base/file/file_path.h"
#include "base/thread/thread_pool.h"
#include "teams/ad/index_adapter/utils/hdfs_util/hdfs_reader.h"
#include "serving_base/hdfs_read/hdfs_file_stream.h"
#include "serving_base/hdfs_read/hdfs_file_util.h"

namespace ks {
namespace index_adapter {

const char kUnitFileName[] = "unit_id";
const char kPhotoFileName[] = "photo_id";
const char kFlagFileName[] = "_SUCCESS";

bool FindLastestUsefulVersionOnHdfs(const std::string& hdfs_path,
                              std::string* lastest_version,
                              const std::string& suc_file) {
  if (lastest_version == nullptr) {
    return false;
  }
  std::vector<hadoop::HDFSPathInfo> hdfs_files;
  if (!hadoop::HDFSListDirectory(hdfs_path.c_str(), &hdfs_files)) {
    LOG(ERROR) << "hadoop::HDFSListDirectory(" << hdfs_path << ") failed.";
    return false;
  }
  std::sort(hdfs_files.begin(), hdfs_files.end(),
            [](const hadoop::HDFSPathInfo& a, const hadoop::HDFSPathInfo& b) {
              return a.type > b.type || (a.type == b.type && a.name > b.name);
            });
  for (auto hdfs_file : hdfs_files) {
    if (hdfs_file.type != hadoop::kDirectory) {
      LOG(ERROR) << "Invalid latest path: " << hdfs_file.name
                 << ", it's not a directory.";
      continue;
    }

    std::string path = hdfs_file.name;
    if (path.back() == '/') {
      path.pop_back();
    }
    std::string flag_file = path + "/" + suc_file;
    if (!hadoop::HDFSExists(flag_file.c_str())) {
      LOG(ERROR) << "flag_file: " << flag_file
                 << " does not exist, maybe not prepared.";
      continue;
    }
    size_t pos = path.rfind('/');
    *lastest_version = path.substr(pos + 1);
    return true;
  }
  return false;
}


void HotDataCenter::Init(const std::string path) {
  hdfs_path_ = path;
  if (hdfs_path_.back() == '/') {
    hdfs_path_.pop_back();
  }
  LOG(INFO) << "Hot Data Hdfs Path: " << hdfs_path_;
}
void HotDataCenter::Start() {
  update_thread_ = std::thread([this]() { UpdateFunc(); });
  std::this_thread::sleep_for(std::chrono::seconds(10));
  LOG(INFO) << "Hot Data Center Started";
}

HotDataCenter::~HotDataCenter() {
  running_.store(false);
  update_thread_.join();
}

void HotDataCenter::CheckStatus(const std::string dump_version) {
  std::lock_guard<std::mutex> lock(mtx_);
  LOG(INFO) << "Check Hot Data Status";
  valid_ = false;
  if (GetWriteBuffer()->version > GetReadBuffer()->version) {
    LOG(INFO) << "Swap Buffer";
    SwapBuffer();
  }
  std::string export_version = GetReadBuffer()->version;
  absl::Time t;
  std::string err;
  LOG(INFO) << "dump version : " << dump_version
            << " , Hot dat  new Version:" << GetReadBuffer()->version;
  if (!absl::ParseTime("%Y-%m-%d_%H%M", export_version, absl::LocalTimeZone(),
                       &t, &err)) {
    LOG(ERROR) << "absl::ParseTime(" << export_version
               << ") failed. err: " << err;
    return;
  }
  absl::Time curr_t;
  if (!absl::ParseTime("%Y-%m-%d_%H%M%S", dump_version, absl::LocalTimeZone(),
                       &curr_t, &err)) {
    LOG(ERROR) << "absl::ParseTime(" << dump_version
               << ") failed. err: " << err;
    return;
  }
  // 如果最新版本距离当前时间超过 2 小时，说明版本过老
  if (absl::ToUnixSeconds(curr_t) - absl::ToUnixSeconds(t) > 7200) {
    LOG(ERROR) << "The latest version: " << export_version << " is not match.";
    valid_ = false;
    LOG(INFO) << "hot data enable false , lazy data will be empty ";
    return;
  }
  LOG(INFO) << "hot data enable true , lazy data will be dump ";
  valid_ = true;
  return;
}
void HotDataCenter::UpdateFunc() {
  running_.store(true);
  while (running_.load()) {
    std::this_thread::sleep_for(std::chrono::seconds(10));
    {
      std::lock_guard<std::mutex> lock(mtx_);
      std::string version;
      FindLastestUsefulVersionOnHdfs(hdfs_path_, &version, kFlagFileName);
      if (version != GetWriteBuffer()->version) {
        LOG(INFO) << "Find new version HotData verion : " << version
                  << " , Start to update";
      } else {
        continue;
      }
      std::string unit_path = base::FilePath(hdfs_path_)
                                  .Append(version)
                                  .Append(kUnitFileName)
                                  .ToString();
      LOG(INFO) << "unit Path" << unit_path;
      std::string photo_path = base::FilePath(hdfs_path_)
                                   .Append(version)
                                   .Append(kPhotoFileName)
                                   .ToString();
      LOG(INFO) << "photo Path" << photo_path;

      HDFSReader unit_reader(unit_path);
      HDFSReader photo_reader(photo_path);
      ::thread::ThreadPool pool{2};
      GetWriteBuffer()->photo_ids.clear();
      GetWriteBuffer()->unit_ids.clear();
      pool.AddTask(::NewCallback(this, &HotDataCenter::LoadData, &unit_reader,
                                 &(GetWriteBuffer()->unit_ids)));
      pool.AddTask(::NewCallback(this, &HotDataCenter::LoadData, &photo_reader,
                                 &(GetWriteBuffer()->photo_ids)));
      pool.JoinAll();
      LOG(INFO) << "version : " << version
                << " has photo_ids : " << GetWriteBuffer()->photo_ids.size()
                << " has unit_ids : " << GetWriteBuffer()->unit_ids.size();
      GetWriteBuffer()->version = version;
    }
  }
}

void HotDataCenter::LoadData(HDFSReader* reader,
                             absl::flat_hash_set<int64_t>* map) {
  std::string content;
  while (true) {
    reader->ReadLine(content);
    if (content.empty()) break;
    int64_t id = std::stoll(content);
    map->emplace(id);
  }
}
}  // namespace index_adapter
}  // namespace ks
