#pragma once
#include <stdint.h>
#include <memory>
#include <string>
#include <unordered_map>
#include "teams/ad/index_adapter/utils/hot_data/hot_data_loader.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace ks {
namespace index_adapter {

using HotDataLoaderPtr = std::shared_ptr<HotDataLoader>;
class AllSceneHotDataCenter {
 public:
  static AllSceneHotDataCenter* GetInstance() {
    static AllSceneHotDataCenter instance;
    return &instance;
  }
  void Init(const std::unordered_map<int32_t, std::string>& paths);
  void Start();

  // 输入当前制作版本
  // 检查双 buffer 最新版本
  // 使读版本最新
  // 校验制作版本与最新版本时间差
  // 时间差大于两小时失效
  void CheckStatus(const std::string dump_version);
  bool ProcessForFull(kuaishou::ad::AdInstance* ad_instance);

 private:
  AllSceneHotDataCenter() = default;
  ~AllSceneHotDataCenter();
  std::unordered_map<int32_t, HotDataLoaderPtr> hot_map_;
};
}  // namespace index_adapter
}  // namespace ks
