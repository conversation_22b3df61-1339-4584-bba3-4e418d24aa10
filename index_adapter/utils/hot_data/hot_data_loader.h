#pragma once
#include <stdint.h>
#include <atomic>
#include <mutex>
#include <string>
#include <thread>
#include "absl/container/flat_hash_set.h"

namespace ks {
namespace index_adapter {

class HDFSReader;

struct Data {
  std::string version;
  absl::flat_hash_set<int64_t> photo_ids;
  absl::flat_hash_set<int64_t> unit_ids;
};
class HotDataLoader {
 public:
  explicit HotDataLoader(const std::string& path);
  ~HotDataLoader();
  void Start();

  // 数据无效时默认为热点数据
  bool SearchUnit(int64_t id) {
    if (valid_) {
      return GetReadBuffer()->unit_ids.find(id) !=
             GetReadBuffer()->unit_ids.end();
    }
    return true;
  }
  bool SearchPhoto(int64_t id) {
    if (valid_) {
      return GetReadBuffer()->photo_ids.find(id) !=
             GetReadBuffer()->photo_ids.end();
    }
    return true;
  }
  // 输入当前制作版本
  // 检查双 buffer 最新版本
  // 使读版本最新
  // 校验制作版本与最新版本时间差
  // 时间差大于两小时失效
  void CheckStatus(const std::string dump_version);
  bool IsValid() { return valid_; }

 private:
  Data* GetWriteBuffer() { return &data_[write_index_]; }
  Data* GetReadBuffer() { return &data_[1 - write_index_]; }
  void SwapBuffer() { write_index_ = 1 - write_index_; }
  void UpdateFunc();
  void LoadData(HDFSReader* reader, absl::flat_hash_set<int64_t>* map);
  bool FindLastestUsefulVersionOnHdfs(const std::string& hdfs_path,
                                      std::string* lastest_version,
                                      const std::string& suc_file);
  std::atomic_bool running_{false};
  std::atomic_bool valid_{false};
  std::string hdfs_path_;
  std::mutex mtx_;  // 保证数据完整
  std::thread update_thread_;
  std::atomic_int write_index_{0};
  Data data_[2];
};
}  // namespace index_adapter
}  // namespace ks
