#pragma once

#include <memory>
#include <mutex>
#include <string>
#include <map>
#include <vector>
#include <utility>
#include <fstream>
#include <iostream>
#include "absl/strings/str_split.h"
#include "absl/strings/str_join.h"
#include "base/file/file_util.h"
#include "base/common/closure.h"
#include "base/thread/thread_pool.h"
#include "perfutil/perfutil.h"
#include "serving_base/util/hdfs.h"
#include "serving_base/hdfs_read/hdfs_file_util.h"
#include "serving_base/hdfs_read/hdfs_handle.h"

#include "teams/ad/index_builder/utils/md5_helper.h"
#include "base/time/timestamp.h"
namespace ks {
namespace index_adapter {
using ks::infra::PerfUtil;

static bool GetTableNameAndVersion(const std::string& local_path,
                            std::string* table_name, std::string* version) {
  std::vector<std::string> pieces =
      absl::StrSplit(local_path, "/", absl::SkipEmpty());
  if (pieces.size() < 2) {
    return false;
  }
  auto size = pieces.size();
  *table_name = pieces[size - 1];
  *version = pieces[size - 2];
  return true;
}

class HdfsUploader {
 public:
  void EnableUpload() {enable_upload_ = true;}
  void DisableUpload() {enable_upload_ = false;}

  void SetUploadThreadPool(thread::ThreadPool* pool) {
    upload_pool_ = pool;
    EnableUpload();
  }

  void AddUploadTask(std::string local_file, std::string hdfs_path) {
    if (!enable_upload_ || !upload_pool_) {
      return;
    }

    std::lock_guard<std::mutex> guard(lock_);
    upload_pool_->AddTask(::NewCallback(this, &HdfsUploader::PutFileToHdfs, local_file, hdfs_path));
  }

  void PutFileToHdfs(std::string local_file, std::string hdfs_path) {
    if (!enable_upload_) {
      return;
    }
    int start = time(nullptr);
    std::string hdfs_file;
    size_t pos = local_file.rfind('/');
    if (pos == local_file.npos) {
      hdfs_file = hdfs_path + "/" + local_file;
    } else {
      hdfs_file = hdfs_path + "/" + local_file.substr(pos + 1);
    }

    if (hadoop::HDFSExists(hdfs_file.c_str())) {
      LOG(INFO) << "dst hdfs_file: " << hdfs_file << " has been exists, first remove it";
      if (!hadoop::HDFSRmr(hdfs_file.c_str())) {
        LOG(ERROR) << "hadoop::HDFSRmr(" << hdfs_file << ") failed.";
      }
    }
    int upload_start = time(nullptr);
    LOG(INFO) << "start HDFSPut| " << local_file << " -> " << hdfs_file;
    int ret = hadoop::HDFSPut(local_file.c_str(), hdfs_file.c_str());
    if (ret == 0) {
      LOG(INFO) << "finish HDFSPut| " << local_file << " -> " << hdfs_file
                << " success. cost: " << (time(nullptr) - start) << "s";
    } else {
      LOG(ERROR) << "finish HDFSPut| " << local_file << " -> " << hdfs_file
                 << " failed. ret: " << ret;
    }
    int upload_cost = time(nullptr) - upload_start;
    std::string table_name, version;
    if (GetTableNameAndVersion(local_file, &table_name, &version)) {
      PerfUtil::SetLogStash(upload_cost, "ad.index_adapter",
                            "hdfs_upload_cost_time", table_name, version);
    }
  }
  void TouchLocalSuccess(const std::string& local_path) {
    if (!enable_upload_) {
      return;
    }
    std::string flag_file =
        base::FilePath(local_path).Append("_SUCCESS").ToString();
    if (!base::file_util::PathExists(flag_file)) {
      auto time = base::Time::FromInternalValue(base::GetTimestamp());
      std::string success = "_SUCCESS";
      if (base::file_util::WriteFile(flag_file, success.c_str(),
                                     success.size())) {
        LOG(INFO) << " local write(" << flag_file << ") success.";
      } else {
        LOG(ERROR) << "local write(" << flag_file << ") failed.";
      }
    }
  }
  void TouchSuccess(const std::string& hdfs_path) {
    if (!enable_upload_) {
      return;
    }
    std::string flag_file = hdfs_path + "/_SUCCESS";
    if (!hadoop::HDFSExists(flag_file.c_str())) {
      if (hdfs_.Touch(flag_file)) {
        LOG(INFO) << "hdfs_.Touch(" << flag_file << ") success.";
      } else {
        LOG(ERROR) << "hdfs_.Touch(" << flag_file << ") failed.";
      }
    }
  }

  void TouchLocalDumpDone(const std::string& local_path) {
    if (!enable_upload_) {
      return;
    }
    std::string flag_file =
        base::FilePath(local_path).Append("dump_done").ToString();
    if (!base::file_util::PathExists(flag_file)) {
      auto time = base::Time::FromInternalValue(base::GetTimestamp());
      std::string success = "dump_done";
      if (base::file_util::WriteFile(flag_file, success.c_str(),
                                     success.size())) {
        LOG(INFO) << " local write(" << flag_file << ") success.";
      } else {
        LOG(ERROR) << "local write(" << flag_file << ") failed.";
      }
    }
  }

  void TouchDumpDone(const std::string& hdfs_path) {
    if (!enable_upload_) {
      return;
    }
    std::string flag_file = hdfs_path + "/dump_done";
    if (!hadoop::HDFSExists(flag_file.c_str())) {
      if (hdfs_.Touch(flag_file)) {
        LOG(INFO) << "hdfs_.Touch(" << flag_file << ") success.";
      } else {
        LOG(ERROR) << "hdfs_.Touch(" << flag_file << ") failed.";
      }
    }
  }

  bool TouchPending(const std::string& pending_file) {
    if (!enable_upload_) {
      LOG(INFO) << "hdfs uploader is closed.";
      return false;
    }
    bool touch_result = hdfs_.Touch(pending_file);
    if (touch_result) {
      LOG(INFO) << "hdfs touch pending file " << pending_file << " succeeded.";
    } else {
      LOG(ERROR) << "hdfs touch pending file " << pending_file << " failed.";
    }
    return touch_result;
  }

  bool DeletePending(const std::string& pending_file) {
    if (!enable_upload_) {
      LOG(INFO) << "hdfs uploader is closed.";
      return false;
    }
    bool delete_result = hadoop::HDFSRmr(pending_file.c_str());
    if (delete_result) {
      LOG(INFO) << "hdfs delete pending file " << pending_file << " succeeded.";
    } else {
      LOG(ERROR) << "hdfs delete pending file " << pending_file << " failed.";
    }
    return delete_result;
  }

  bool DumpTimetExists(const std::string& hdfs_path) {
    std::string dump_timet_file = hdfs_path + "/dump_timet";
    return hadoop::HDFSExists(dump_timet_file.c_str());
  }

  bool Mkdir(const std::string& hdfs_path) {
    if (!enable_upload_) {
      LOG(INFO) << "hdfs uploader is closed.";
      return false;
    }
    if (!hadoop::HDFSExists(hdfs_path.c_str())) {
      if (!hadoop::HDFSMkdir(hdfs_path.c_str())) {
        LOG(ERROR) << "hadoop::HDFSMkdir(" << hdfs_path << ") failed.";
        return false;
      }
    }
    return true;
  }
  HdfsUploader() : hdfs_(hadoop::FLAGS_hadoop_namenode_ip,
      hadoop::FLAGS_hadoop_namenode_port, "ad") {}
  ~HdfsUploader() = default;

 private:
  bool enable_upload_ {false};
  thread::ThreadPool* upload_pool_ {nullptr};

  std::mutex lock_;

  base::HDFSWrapper hdfs_;
};

}  // namespace index_adapter
}  // namespace ks
