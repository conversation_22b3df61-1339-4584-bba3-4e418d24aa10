#include "teams/ad/index_adapter/utils/p2p/p2p_task.h"
#include <algorithm>
#include <map>
#include <string>
#include <thread>
#include <vector>

#include "serving_base/hdfs_read/hdfs_file_stream.h"
#include "serving_base/hdfs_read/hdfs_file_util.h"
#include "base/strings/string_split.h"
#include "base/thread/thread_pool.h"
#include "base/file/file_util.h"
#include "teams/ad/index_adapter/utils/hdfs_uploader.h"
#include "teams/ad/index_adapter/utils/hdfs_util/hdfs_wrapper.h"
#include "absl/strings/substitute.h"


namespace ks {
namespace index_adapter {

std::string GetLastValidName(const std::string& file_dir) {
  std::vector<std::string> slides;
  base::SplitString(file_dir, "/", &slides);
  while (!slides.empty()) {
    if (!slides.back().empty()) {
      return slides.back();
    } else {
      slides.pop_back();
    }
  }
  return std::string();
}

std::string GetlatestValidDir(const std::string& hdfs_path) {
  HDFSWrapper hdfs_wrapper;
  hdfs_wrapper.Initialize();

  std::vector<HadoopEntry> entries;
  if (hdfs_wrapper.ListSubDirectories(hdfs_path, &entries) != 0) {
    LOG(INFO) << "Get sub directories failed, hdfs_path:" << hdfs_path;
    return std::string();
  }
  // 根据创建时间，降序排列
  std::sort(entries.begin(), entries.end(), [](HadoopEntry& a, HadoopEntry& b) {
    return a.last_mod > b.last_mod;
  });
  for (const auto& entry : entries) {
    std::string success_file_dir = entry.full_name + "/_SUCCESS";
    if (hadoop::HDFSExists(success_file_dir.c_str())) {
      return entry.full_name;
    }
  }
  return std::string();
}

std::vector<std::string> GetFilesByDir(const std::string& hdfs_path) {
  std::vector<std::string> ret;
  std::vector<hadoop::HDFSPathInfo> hdfs_files;
  if (hadoop::HDFSListDirectory(hdfs_path.c_str(), &hdfs_files)) {
    for (const auto& hdfs_file : hdfs_files) {
      std::vector<std::string> slides;
      base::SplitString(hdfs_file.name, "/", &slides);
      if (slides.size() > 0 && slides.back() == "_SUCCESS") {
        continue;
      }
      ret.push_back(hdfs_file.name);
    }
  }
  return ret;
}

P2pTaskDetector::P2pTaskDetector(const std::string& hdfs_path) {
  file_dir_ = GetlatestValidDir(hdfs_path);
  std::vector<std::string> slides;
  base::SplitString(hdfs_path, "/", &slides);
  while (!slides.empty()) {
    if (!slides.back().empty()) {
      dir_name_ = slides.back();
      break;
    } else {
      slides.pop_back();
    }
  }
  p2p_files_ = GetFilesByDir(file_dir_);
}

bool P2pTaskDetector::IsValid() {
  return !file_dir_.empty() && !dir_name_.empty() && !p2p_files_.empty();
}

void P2pTaskDetector::PrintLog() {
  LOG(INFO) << "file_dir: " << file_dir_;
  LOG(INFO) << "dir_name: " << dir_name_;
  std::string files;
  for (const auto& file : p2p_files_) {
    files += file + ",";
  }
  if (!files.empty()) {
    files.pop_back();
  }
  LOG(INFO) << "p2p_files: " << files;
}

void P2pTaskManager::PrintLog() {
  for (auto& sn_pair : sn_local_path) {
    LOG(INFO) << "server_name: " << sn_pair.first;
    const auto local_path = sn_local_path[sn_pair.first];
    const auto update_path = sn_update_path[sn_pair.first];
    LOG(INFO) << "local_path: " << local_path;
    LOG(INFO) << "update_path: " << update_path;
    for (auto& task : sn_p2p_tasks) {
      task.PrintLog();
    }
  }
}

void P2pTaskManager::RegisterTask(const std::string& server_name,
                                  const std::string& local_dir,
                                  const std::string& update_dir) {
  sn_local_path[server_name] = local_dir;
  sn_update_path[server_name] = update_dir;

  sn_status[server_name] = true;
}

void P2pTaskManager::Init(const std::vector<std::string>& p2p_dirs) {
  sn_p2p_tasks.clear();
  sn_local_path.clear();
  sn_update_path.clear();
  sn_status.clear();
  for (auto& dir : p2p_dirs) {
    LOG(INFO) << dir;
    sn_p2p_tasks.emplace_back(dir);
  }
}

void P2pTaskManager::DoUploadTask() {
  thread::ThreadPool thread_pool_(5);
  for (auto& sn_pair : sn_local_path) {
    auto server_name = sn_pair.first;
    thread_pool_.AddTask(
        ::NewCallback(this, &P2pTaskManager::DoP2pTask, server_name));
  }
  thread_pool_.JoinAll();
}

void P2pTaskManager::DoP2pTask(std::string server_name) {
  thread::ThreadPool thread_pool_(10);
  std::string src_content;
  for (auto& p2p_task : sn_p2p_tasks) {
    const std::string local_dir =
        sn_local_path[server_name] + "/" + p2p_task.GetTaskName();
    mkdir(local_dir.c_str(), 0777);
    const std::string hdfs_dir =
        sn_update_path[server_name] + "/" + p2p_task.GetTaskName();
    HdfsUploader tmp_hdfs_uploader;
    tmp_hdfs_uploader.Mkdir(hdfs_dir);
    LOG(INFO) << "local_dir: " << local_dir << " hdfs_dir: " << hdfs_dir;
    if (!p2p_task.IsValid()) {
      LOG(INFO) << "p2p_task not valid: " << p2p_task.GetTaskName();
      sn_status[server_name] = false;
      continue;
    }
    for (auto& p2p_file : p2p_task.GetP2pFiles()) {
      const std::string file_name = GetLastValidName(p2p_file);
      const std::string src_hdfs_path = p2p_file;
      const std::string local_path = local_dir + "/" + file_name;
      const std::string dest_hdfs_path = hdfs_dir + "/" + file_name;
      thread_pool_.AddTask(::NewCallback(this, &P2pTaskManager::P2pTaskImp,
                                         server_name, src_hdfs_path, local_path,
                                         dest_hdfs_path));
    }
    // p2p 记录源地址
    src_content +=
        absl::Substitute("task_name:$0\tsrc_dir:$1\n", p2p_task.GetTaskName(),
                         p2p_task.GetSrcDir());
  }
  // p2p 记录上传
  {
    const std::string hdfs_path = sn_update_path[server_name] + "/src_dir";
    const std::string local_path = sn_local_path[server_name] + "/src_dir";
    base::file_util::WriteFile(local_path, src_content.c_str(),
                               src_content.size());
    if (0 != hadoop::HDFSPut(local_path.c_str(), hdfs_path.c_str())) {
      sn_status[server_name] = false;
    }
  }
  thread_pool_.JoinAll();
}

void P2pTaskManager::P2pTaskImp(std::string server_name,
                                std::string src_hdfs_path,
                                std::string local_path,
                                std::string dest_hdfs_path) {
  LOG(INFO) << "src_hdfs_path:" << src_hdfs_path
            << " local_path: " << local_path
            << "dest_hdfs_path: " << dest_hdfs_path;
  int ret = 0;
  ret = hadoop::HDFSGet(src_hdfs_path.c_str(), local_path.c_str());
  if (ret != 0) {
    sn_status[server_name] = false;
    return;
  }
  ret = hadoop::HDFSPut(local_path.c_str(), dest_hdfs_path.c_str());
  if (ret != 0) {
    sn_status[server_name] = false;
    return;
  }
}

bool P2pTaskManager::GetStatusByServerName(const std::string& server_name) {
  if (sn_status.count(server_name) > 0) {
    return sn_status[server_name];
  } else {
    return false;
  }
}

}  // namespace index_adapter
}  // namespace ks
