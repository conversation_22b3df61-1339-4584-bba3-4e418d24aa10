#pragma once
#include <map>
#include <string>
#include <vector>

#include "teams/ad/index_builder/utils/message_stream.h"
#include "teams/ad/index_adapter/utils/kconf/kconf.h"
#include "base/file/file_stream.h"
#include "teams/ad/index_builder/utils/unified_config_manager/unified_adapter_config.h"
namespace ks {
namespace index_adapter {

using ks::index_builder::MessageStreamMap;
using ks::index_builder::MessageStreamPtr;
using ks::index_builder::InstanceStreamPtr;
using ks::index_builder::InstanceStreamMap;

bool GetDumpInfoDetail(const std::string& hdfs_path, const std::string& version,
                       kuaishou::ad::tables::DumpInfo* dump_info);

bool FindLastestSuccessVersionOnHdfs(const std::string& hdfs_path,
                                     std::string* lastest_version,
                                     const std::string& suc_file);
bool FindLastestSuccessVersionOnKfs(const std::string& kfs_path,
                                    std::string* lastest_version,
                                    const std::string& suc_file);

bool GetAllFileOnPath(const std::string& hdfs_path, std::vector<std::string>* files);

/*
  保留 24 份有效数据，至多一次删除 20 份数据
*/
bool ClearHistoryFile(const std::string& hdfs_path);
/*
  清除本地历史文件，只保留 2 份最好
*/
void CleanLocalIndex(const std::string& output, int keep_num);

class StreamMapBase {
 public:
  explicit StreamMapBase(std::string type) : type_(type) {}
  StreamMapBase() {}
  virtual ~StreamMapBase() {}

  virtual bool CheckLastetValidVersionOfAdInstance(
    const std::string& hdfs_path, std::string* latest_version);

  virtual bool CheckLastetValidVersionOfAdInstanceOnKfs(
    const std::string& kfs_path, std::string* latest_version);

  virtual bool CheckLastetSuccessVersionOfAdInstance(
      const std::string& hdfs_path, std::string* latest_version);
  virtual bool CheckLastetSuccessVersionOfAdInstanceOnKfs(
      const std::string& kfs_path, std::string* latest_version);
  virtual kuaishou::ad::tables::DumpInfo GetTableDumpInfo() {
    return table_dump_info_;
  }
  virtual kuaishou::ad::tables::DumpInfo GetDumpInfo() { return dump_info_; }

  // if need, override
  virtual bool CheckLatestValidVersionOfInputDir(std::string* latest_version) {
    LOG_ASSERT(false) << "this should not be called";
    return true;
  }
  virtual bool CheckLatestReadyVersionOfInputDir(std::string* latest_version) {
    LOG_ASSERT(false) << "this should not be called";
    return true;
  }
  virtual bool CheckBenchmarkFiles(const std::string& version,
                                   kuaishou::ad::tables::DumpInfo* dump_info) {
    LOG_ASSERT(false) << "this should not be called";
    return true;
  }
  virtual bool GetDumpInfoByPath(const std::string& hdfs_path,
                                 const std::string& version,
                                 kuaishou::ad::tables::DumpInfo* dump_info) {
    LOG_ASSERT(false) << "this should not be called";
    return true;
  }
  virtual bool InitHdfsMessageStreamMap(const std::string& version,
                                        MessageStreamMap* stream_map) {
    LOG_ASSERT(false) << "this should not be called";
    return true;
  }
  // 输入是 index_adapter 数据
  virtual bool InitAdInstanceMessageStreamMap(const std::string& hdfs_path,
                                              InstanceStreamMap* stream_map) {
    LOG_ASSERT(false) << "this should not be called";
    return true;
  }

 protected:
  std::string type_;
  // consumer map init config
  kuaishou::ad::tables::DumpInfo table_dump_info_;
  // stream map init config
  kuaishou::ad::tables::DumpInfo dump_info_;
};

class StreamMapPb : public StreamMapBase {
 public:
  StreamMapPb() : StreamMapBase() {
    // adapter_config_ = Kconf::adapterConfig()->data();
    // auto stream_map_type = adapter_config_.stream_map_type();
    auto stream_map_type = index_builder::UnifiedAdapterConfigManager::GetInstanace()->GetStreamMapType();
    auto& stream_maps = Kconf::streamMapAdapter()->data();
    if (stream_maps.name2map().find(stream_map_type) != stream_maps.name2map().end()) {
      stream_map_ = stream_maps.name2map().at(stream_map_type);
    } else {
      LOG_ASSERT(false) << "stream_map_type: " << stream_map_type
                        << " not supported!";
    }
  }
  explicit StreamMapPb(const std::string& type) : StreamMapBase(type) {
    auto& stream_maps = Kconf::streamMapAdapter()->data();
    if (stream_maps.name2map().find(type) != stream_maps.name2map().end()) {
      stream_map_ = stream_maps.name2map().at(type);
    } else {
      LOG_ASSERT(false) << "stream_map_type: " << type << " not supported!";
    }
  }
  virtual ~StreamMapPb() {}
  bool CheckLatestValidVersionOfInputDir(std::string* latest_version) override;
  bool CheckLatestReadyVersionOfInputDir(std::string* latest_version) override;
  bool InitHdfsMessageStreamMap(const std::string& version,
                                MessageStreamMap* stream_map) override;
  bool CheckBenchmarkFiles(const std::string& version,
                           kuaishou::ad::tables::DumpInfo* dump_info) override;
  bool GetDumpInfoByPath(const std::string& hdfs_path,
                         const std::string& version,
                         kuaishou::ad::tables::DumpInfo* dump_info) override;

 private:
  bool FindUpdateTableConfig();
  AdapterConfig adapter_config_;
  StreamMap stream_map_;
};

class StreamMapPbOnKfs : public StreamMapBase {
 public:
  explicit StreamMapPbOnKfs(const std::string& type) : StreamMapBase(type) {
    auto& stream_maps = Kconf::streamMapAdapter()->data();
    if (stream_maps.name2map().find(type) != stream_maps.name2map().end()) {
      stream_map_ = stream_maps.name2map().at(type);
    } else {
      LOG_ASSERT(false) << "stream_map_type: " << type << " not supported!";
    }
  }
  virtual ~StreamMapPbOnKfs() {}
  bool CheckLatestValidVersionOfInputDir(std::string* latest_version) override;
  bool InitHdfsMessageStreamMap(const std::string& version,
                                MessageStreamMap* stream_map) override;
  bool CheckBenchmarkFiles(const std::string& version,
                           kuaishou::ad::tables::DumpInfo* dump_info) override;
  bool GetDumpInfoByPath(const std::string& hdfs_path,
                         const std::string& version,
                         kuaishou::ad::tables::DumpInfo* dump_info) override;

 private:
  bool FindUpdateTableConfig();
  StreamMap stream_map_;
};

class StreamMapTableLite : public StreamMapBase {
 public:
  StreamMapTableLite() : StreamMapBase() {}
  virtual ~StreamMapTableLite() {}
  bool InitAdInstanceMessageStreamMap(const std::string& hdfs_path,
                                      InstanceStreamMap* stream_map) override;
  // 添加 hdfs_path/version/dump_info 的 meta 信息到 dump_info
  bool GetDumpInfoByPath(const std::string& hdfs_path,
                         const std::string& version,
                         kuaishou::ad::tables::DumpInfo* dump_info) override;
};

class StreamMapTableLiteOnKfs : public StreamMapTableLite {
 public:
  StreamMapTableLiteOnKfs() : StreamMapTableLite() {}
  virtual ~StreamMapTableLiteOnKfs() {}
  bool InitAdInstanceMessageStreamMap(const std::string& kfs_path,
                                      InstanceStreamMap* stream_map) override;
  // 添加 hdfs_path/version/dump_info 的 meta 信息到 dump_info
  bool GetDumpInfoByPath(const std::string& hdfs_path,
                         const std::string& version,
                         kuaishou::ad::tables::DumpInfo* dump_info) override;
};

class StreamMapLiteIndex : public StreamMapTableLite {
 public:
  StreamMapLiteIndex() : StreamMapTableLite() {}
  virtual ~StreamMapLiteIndex() {}
  bool InitAdInstanceMessageStreamMap(const std::string& hdfs_path,
                                      InstanceStreamMap* stream_map) override;
};

class StreamMapLiteIndexOnKfs : public StreamMapTableLiteOnKfs {
 public:
  StreamMapLiteIndexOnKfs() : StreamMapTableLiteOnKfs() {}
  virtual ~StreamMapLiteIndexOnKfs() {}
  bool InitAdInstanceMessageStreamMap(const std::string& kfs_path,
                                      InstanceStreamMap* stream_map) override;
};

class StreamMapMakerFactory {
 public:
  static StreamMapMakerFactory* GetInstance() {
    static StreamMapMakerFactory instance;
    return &instance;
  }
  StreamMapBase* GetStreamMap() { return new StreamMapPb(); }
  StreamMapBase* GetStreamMapTableLite() { return new StreamMapTableLite(); }
  StreamMapBase* GetStreamMapTableLiteOnKfs() {
    return new StreamMapTableLiteOnKfs();
  }
  StreamMapBase* GetStreamMapPbOnKfs(const std::string& type) {
    return new StreamMapPbOnKfs(type);
  }
  StreamMapBase* GetStreamMapPb(const std::string& type) {
    return new StreamMapPb(type);
  }
  StreamMapBase* GetStreamMapLiteIndex() { return new StreamMapLiteIndex(); }
  StreamMapBase* GetStreamMapLiteIndexOnKfs() {
    return new StreamMapLiteIndexOnKfs();
  }
};

}  // namespace index_adapter
}  // namespace ks
