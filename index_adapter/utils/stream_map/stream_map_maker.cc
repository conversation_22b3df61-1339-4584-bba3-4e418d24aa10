#include "teams/ad/index_adapter/utils/stream_map/stream_map_maker.h"

#include <absl/time/clock.h>
#include <absl/time/time.h>
#include <algorithm>
#include <fstream>
#include <set>
#include <utility>
#include "abseil/absl/strings/strip.h"
#include "base/file/dir_reader_posix.h"
#include "base/file/file_path.h"
#include "base/file/file_stream.h"
#include "base/file/file_util.h"
#include "base/time/timestamp.h"
#include "perfutil/perfutil.h"
#include "serving_base/hdfs_read/hdfs_file_stream.h"
#include "serving_base/hdfs_read/hdfs_file_util.h"
#include "teams/ad/index_adapter/utils/hot_data/hot_data_center.h"
#include "teams/ad/index_builder/utils/hdfs_valid_checker/hdfs_valid_checker.h"
#include "teams/ad/index_builder/utils/kconf.h"
#include "teams/ad/index_builder/utils/table_config.h"
namespace ks {
namespace index_adapter {

using ks::index_builder::MessageStreamPtr;
using ks::index_builder::HdfsMessageStream;
using ks::index_builder::KfsMessageStream;
using ks::index_builder::TableConfigManager;
using ks::index_builder::InstanceStreamPtr;
using ks::index_builder::InstanceStreamMap;
using ks::index_builder::HdfsMessageStreamForAdInstance;
using ks::index_builder::HdfsMessageStreamOnKfsForAdInstance;
using kuaishou::ad::tables::DumpInfo;
using ks::index_builder::HdfsLiteIndexStream;
using ks::index_builder::HdfsLiteIndexStreamOnKfs;
using ks::index_builder::AdKconfUtil;
using ks::index_builder::GetPbNameByAdInstanceType;
using ks::infra::PerfUtil;

void CheckTableConfigCoverStatus(MessageStreamMap* input_stream_map) {
  std::set<std::string> table_name_set;
  std::set<std::string> miss_table_set;
  auto config = AdKconfUtil::tableConfig()->data();
  for (auto& item : config.table_dump_configs()) {
    table_name_set.insert(item.table_name());
  }
  for (const auto& item : *input_stream_map) {
    auto table_name = item.first;
    if (table_name_set.count(table_name) > 0) {
      PerfUtil::CountLogStash(1, "ad.index_adapter", "table_name_hit",
                              table_name);
      LOG(INFO) << table_name << " match";
    } else {
      PerfUtil::CountLogStash(1, "ad.index_adapter", "table_name_miss",
                              table_name);
      LOG(INFO) << table_name << " miss";
      miss_table_set.insert(table_name);
    }
  }
  // 不在 kconf 内的表不会读取
  for (auto table_name : miss_table_set) {
    input_stream_map->erase(table_name);
  }
}

bool GetDumpInfoDetail(const std::string& hdfs_path, const std::string& version,
                       kuaishou::ad::tables::DumpInfo* dump_info) {
  hadoop::HDFSFileStream hdfs_stream(hadoop::FLAGS_hadoop_namenode_ip.c_str(),
                                     hadoop::FLAGS_hadoop_namenode_port);
  std::string dump_info_file =
      base::FilePath(hdfs_path).Append(version).Append("dump_info").ToString();
  if (!hdfs_stream.Open(dump_info_file.c_str(), O_RDONLY)) {
    LOG(ERROR) << "hdfs_stream.Open(" << dump_info_file << ") failed";
    return false;
  }
  // 此处 1M 空间足以存放 dump_info 信息
  int BUF_SIZE = 1 << 20, offset = 0;
  std::vector<char> buf;
  buf.resize(BUF_SIZE);
  while (true) {
    int read_size = hdfs_stream.Read(buf.data() + offset, BUF_SIZE - offset);
    if (read_size == 0) {
      break;
    } else {
      offset += read_size;
    }
  }
  std::string content(buf.data(), offset);
  kuaishou::ad::tables::DumpInfo temp_dump_info;
  auto status =
      google::protobuf::util::JsonStringToMessage(content, &temp_dump_info);
  if (!status.ok()) {
    LOG(ERROR) << "JsonStringToMessage failed. error: "
               << status.error_message() << ", dump_info: " << content;
    return false;
  }
  for (auto& temp_info : temp_dump_info.info()) {
    auto* info = dump_info->add_info();
    info->CopyFrom(temp_info);
    info->set_file_name(base::FilePath(hdfs_path)
                            .Append(version)
                            .Append(temp_info.file_name())
                            .ToString());
    LOG(INFO) << "table_name: " << info->table_name()
              << " proto_name:" << info->proto_name()
              << " file_name: " << info->file_name();
  }
  return true;
}

bool FindLastestVersionOnHdfs(const std::string& hdfs_path,
                              std::string* lastest_version,
                              const std::string& suc_file) {
  if (lastest_version == nullptr) {
    return false;
  }
  std::vector<hadoop::HDFSPathInfo> hdfs_files;
  if (!hadoop::HDFSListDirectory(hdfs_path.c_str(), &hdfs_files)) {
    LOG(ERROR) << "hadoop::HDFSListDirectory(" << hdfs_path << ") failed.";
    return false;
  }
  std::sort(hdfs_files.begin(), hdfs_files.end(),
            [](const hadoop::HDFSPathInfo& a, const hadoop::HDFSPathInfo& b) {
              return a.type > b.type || (a.type == b.type && a.name > b.name);
            });
  if (hdfs_files[0].type != hadoop::kDirectory) {
    LOG(ERROR) << "Invalid latest path: " << hdfs_files[0].name
               << ", it's not a directory.";
    return false;
  }

  std::string path = hdfs_files[0].name;
  if (path.back() == '/') {
    path.pop_back();
  }
  std::string flag_file = path + "/" + suc_file;
  if (!hadoop::HDFSExists(flag_file.c_str())) {
    LOG(ERROR) << "flag_file: " << flag_file
               << " does not exist, maybe not prepared.";
    return false;
  }
  size_t pos = path.rfind('/');
  *lastest_version = path.substr(pos + 1);
  return true;
}

bool GetAllFileOnPath(const std::string& hdfs_path,
                      std::vector<std::string>* files) {
  std::vector<hadoop::HDFSPathInfo> hdfs_files;
  static std::string suc_flag = "_SUCCESS";
  if (!hadoop::HDFSListDirectory(hdfs_path.c_str(), &hdfs_files)) {
    LOG(ERROR) << "hadoop::HDFSListDirectory(" << hdfs_path << ") failed.";
    return false;
  } else {
    for (auto hdfs_file : hdfs_files) {
      std::vector<std::string> words =
          absl::StrSplit(hdfs_file.name, "/", absl::SkipEmpty());
      if (words.empty()) {
        continue;
      }
      if (words.back() == suc_flag) {
        continue;
      }
      std::string target_file =
          base::FilePath(hdfs_path).Append(words.back()).ToString();
      files->push_back(target_file);
      LOG(INFO) << target_file << " add to dest_files";
    }
  }
  return true;
}

bool FindLastestVersionOnKfs(const std::string& kfs_path,
                             std::string* lastest_version,
                             const std::string& suc_file) {
  if (lastest_version == nullptr) {
    return false;
  }
  std::vector<std::string> elems;
  std::string dot_dir = base::FilePath(kfs_path).Append(".").ToString();
  std::string dot_dot_dir = base::FilePath(kfs_path).Append("..").ToString();
  std::set<std::string> black_paths{dot_dir, dot_dot_dir};
  base::DirReaderPosix dir_reader(kfs_path.c_str());
  while (dir_reader.Next()) {
    auto curr =
        base::FilePath(kfs_path.c_str()).Append(dir_reader.name()).ToString();
    if (black_paths.count(curr) != 0) continue;
    elems.push_back(
        base::FilePath(kfs_path.c_str()).Append(dir_reader.name()).ToString());
  }
  if (elems.empty()) {
    LOG(INFO) << "Find No Version ";
    return false;
  }
  std::vector<std::pair<std::string, base::PlatformFileInfo>> infos;
  for (auto& elem : elems) {
    base::PlatformFileInfo temp_info;
    if (base::file_util::GetFileInfo(elem, &temp_info)) {
      infos.emplace_back(elem, temp_info);
    } else {
      LOG(ERROR) << "GetFileInfo [" << elem << "] failed";
    }
  }
  std::sort(infos.begin(), infos.end(),
            [](const std::pair<std::string, base::PlatformFileInfo>& a,
               const std::pair<std::string, base::PlatformFileInfo>& b) {
              return a.second.is_directory > b.second.is_directory ||
                     (a.second.is_directory == b.second.is_directory &&
                      a.first > b.first);
            });
  if (!infos[0].second.is_directory) {
    LOG(ERROR) << "Invalid latest path: " << infos[0].first
               << ", it's not a directory.";
    return false;
  }

  std::string path = infos[0].first;
  if (path.back() == '/') {
    path.pop_back();
  }
  std::string flag_file = path + "/" + suc_file;
  if (!base::file_util::PathExists(flag_file)) {
    LOG(ERROR) << "flag_file: " << flag_file
               << " does not exist, maybe not prepared.";
    return false;
  }
  size_t pos = path.rfind('/');
  *lastest_version = path.substr(pos + 1);
  return true;
}

bool FindLastestSuccessVersionOnHdfs(const std::string& hdfs_path,
                              std::string* lastest_version,
                              const std::string& suc_file) {
  if (lastest_version == nullptr) {
    return false;
  }
  std::vector<hadoop::HDFSPathInfo> hdfs_files;
  if (!hadoop::HDFSListDirectory(hdfs_path.c_str(), &hdfs_files)) {
    LOG(ERROR) << "hadoop::HDFSListDirectory(" << hdfs_path << ") failed.";
    return false;
  }
  std::sort(hdfs_files.begin(), hdfs_files.end(),
            [](const hadoop::HDFSPathInfo& a, const hadoop::HDFSPathInfo& b) {
              return a.type > b.type || (a.type == b.type && a.name > b.name);
            });
  for (auto hdfs_file : hdfs_files) {
    if (hdfs_file.type != hadoop::kDirectory) {
      LOG(ERROR) << "Invalid latest path: " << hdfs_file.name
                 << ", it's not a directory.";
      continue;
    }

    std::string path = hdfs_file.name;
    if (path.back() == '/') {
      path.pop_back();
    }
    std::string flag_file = path + "/" + suc_file;
    if (!hadoop::HDFSExists(flag_file.c_str())) {
      LOG(ERROR) << "flag_file: " << flag_file
                 << " does not exist, maybe not prepared.";
      continue;
    }
    size_t pos = path.rfind('/');
    *lastest_version = path.substr(pos + 1);
    return true;
  }
  return false;
}
bool FindLastestSuccessVersionOnKfs(const std::string& kfs_path,
                                    std::string* lastest_version,
                                    const std::string& suc_file) {
  if (lastest_version == nullptr) {
    return false;
  }
  std::vector<std::string> elems;
  base::DirReaderPosix dir_reader(kfs_path.c_str());
  std::string dot_dir = base::FilePath(kfs_path).Append(".").ToString();
  std::string dot_dot_dir = base::FilePath(kfs_path).Append("..").ToString();
  std::set<std::string> black_paths{dot_dir, dot_dot_dir};
  while (dir_reader.Next()) {
    auto curr =
        base::FilePath(kfs_path.c_str()).Append(dir_reader.name()).ToString();
    if (black_paths.count(curr) != 0) continue;
    elems.push_back(
        base::FilePath(kfs_path.c_str()).Append(dir_reader.name()).ToString());
  }
  if (elems.empty()) {
    LOG(INFO) << "Find No Version ";
    return false;
  }
  std::vector<std::pair<std::string, base::PlatformFileInfo>> infos;
  for (auto& elem : elems) {
    base::PlatformFileInfo temp_info;
    if (base::file_util::GetFileInfo(elem, &temp_info)) {
      infos.emplace_back(elem, temp_info);
    } else {
      LOG(ERROR) << "GetFileInfo [" << elem << "] failed";
    }
  }
  std::sort(infos.begin(), infos.end(),
            [](const std::pair<std::string, base::PlatformFileInfo>& a,
               const std::pair<std::string, base::PlatformFileInfo>& b) {
              return a.second.is_directory > b.second.is_directory ||
                     (a.second.is_directory == b.second.is_directory &&
                      a.first > b.first);
            });

  for (auto info : infos) {
    if (!info.second.is_directory) {
      LOG(ERROR) << "Invalid latest path: " << info.first
                 << ", it's not a directory.";
      continue;
    }

    std::string path = info.first;
    if (path.back() == '/') {
      path.pop_back();
    }
    std::string flag_file = path + "/" + suc_file;
    if (!base::file_util::PathExists(flag_file)) {
      LOG(ERROR) << "flag_file: " << flag_file
                 << " does not exist, maybe not prepared.";
      continue;
    }
    size_t pos = path.rfind('/');
    *lastest_version = path.substr(pos + 1);
    return true;
  }
  return false;
}

bool ClearHistoryFile(const std::string& hdfs_path) {
  std::vector<hadoop::HDFSPathInfo> hdfs_files;
  if (!hadoop::HDFSListDirectory(hdfs_path.c_str(), &hdfs_files)) {
    LOG(ERROR) << "hadoop::HDFSListDirectory(" << hdfs_path << ") failed.";
    return false;
  }
  std::sort(hdfs_files.begin(), hdfs_files.end(),
            [](const hadoop::HDFSPathInfo& a, const hadoop::HDFSPathInfo& b) {
              return a.type > b.type || (a.type == b.type && a.name > b.name);
            });
  static const int64_t reserved_num = 24;
  int64_t success_num = 0;
  int64_t delete_num = 0;
  for (auto hdfs_file : hdfs_files) {
    std::string path = hdfs_file.name;
    if (hdfs_file.type != hadoop::kDirectory) {
      LOG(ERROR) << "Invalid latest path: " << hdfs_file.name
                 << ", it's not a directory. Delete It!";
      LOG(INFO) << "Path: " << path << " delete!";
      bool ret = hadoop::HDFSRmr(path.c_str());
      delete_num++;
      if (delete_num > 20) {
        return true;
      }
      continue;
    }
    if (path.back() == '/') {
      path.pop_back();
    }
    std::string flag_file = path + "/_SUCCESS";
    if (!hadoop::HDFSExists(flag_file.c_str())) {
      LOG(ERROR) << "flag_file: " << flag_file
                 << " does not exist, maybe not prepared.";
      LOG(INFO) << "Path: " << path << " delete!";
      bool ret = hadoop::HDFSRmr(path.c_str());
      delete_num++;
      if (delete_num > 20) {
        return true;
      }
      continue;
    }
    success_num++;
    if (success_num > reserved_num) {
      bool ret = hadoop::HDFSRmr(path.c_str());
      delete_num++;
      if (delete_num > 20) {
        return true;
      }
      continue;
    }
  }
  return true;
}

bool StreamMapBase::CheckLastetValidVersionOfAdInstance(
    const std::string& hdfs_path, std::string* latest_version) {
  return FindLastestVersionOnHdfs(hdfs_path, latest_version, "_SUCCESS");
}

bool StreamMapBase::CheckLastetValidVersionOfAdInstanceOnKfs(
    const std::string& kfs_path, std::string* latest_version) {
  return FindLastestVersionOnKfs(kfs_path, latest_version, "_SUCCESS");
}


bool StreamMapBase::CheckLastetSuccessVersionOfAdInstance(
    const std::string& hdfs_path, std::string* latest_version) {
  return FindLastestSuccessVersionOnHdfs(hdfs_path, latest_version, "_SUCCESS");
}

bool StreamMapBase::CheckLastetSuccessVersionOfAdInstanceOnKfs(
    const std::string& kfs_path, std::string* latest_version) {
  return FindLastestSuccessVersionOnKfs(kfs_path, latest_version, "_SUCCESS");
}


bool StreamMapPb::CheckLatestReadyVersionOfInputDir(
    std::string* latest_version) {
  const auto& input_dirs = stream_map_.input_dirs();
  std::set<std::string> all_versions;
  for (auto& hdfs_path : input_dirs) {
    std::string lastest_version;
    if (FindLastestSuccessVersionOnHdfs(hdfs_path, &lastest_version, "dump_done")) {
      all_versions.insert(lastest_version);
    } else {
      LOG(INFO) << "hdfs: " << hdfs_path << " not prepared! fail...";
      return false;
    }
  }
  if (all_versions.size() != 1) {
    LOG(INFO) << "not all dir reach same version, fail!";
    return false;
  }

  std::string export_version = *(all_versions.rbegin());
  absl::Time t;
  std::string err;
  if (!absl::ParseTime("%Y-%m-%d_%H%M%S", export_version, absl::LocalTimeZone(),
                       &t, &err)) {
    LOG(ERROR) << "absl::ParseTime(" << export_version
               << ") failed. err: " << err;
    return false;
  }
  // 如果最新版本距离当前时间超过 10 小时，说明版本过老
  if (absl::ToUnixSeconds(absl::Now()) - absl::ToUnixSeconds(t) > 36000) {
    LOG(ERROR) << "The latest version: " << export_version
               << " is also too old.";
    return false;
  }
  *latest_version = export_version;
  return true;
}

bool StreamMapPb::CheckLatestValidVersionOfInputDir(
    std::string* latest_version) {
  const auto& input_dirs = stream_map_.input_dirs();
  std::set<std::string> all_versions;
  for (auto& hdfs_path : input_dirs) {
    std::string lastest_version;
    if (FindLastestVersionOnHdfs(hdfs_path, &lastest_version, "dump_done")) {
      all_versions.insert(lastest_version);
    } else {
      LOG(INFO) << "hdfs: " << hdfs_path << " not prepared! fail...";
      return false;
    }
  }
  if (all_versions.size() != 1) {
    LOG(INFO) << "not all dir reach same version, fail!";
    return false;
  }

  std::string export_version = *(all_versions.rbegin());
  absl::Time t;
  std::string err;
  if (!absl::ParseTime("%Y-%m-%d_%H%M%S", export_version, absl::LocalTimeZone(),
                       &t, &err)) {
    LOG(ERROR) << "absl::ParseTime(" << export_version
               << ") failed. err: " << err;
    return false;
  }
  // 如果最新版本距离当前时间超过 1.5 小时，说明版本过老
  if (absl::ToUnixSeconds(absl::Now()) - absl::ToUnixSeconds(t) > 7200) {
    LOG(ERROR) << "The latest version: " << export_version
               << " is also too old.";
    return false;
  }
  *latest_version = export_version;
  return true;
}

// 合并所有 dump_info
bool StreamMapPb::CheckBenchmarkFiles(
    const std::string& version, kuaishou::ad::tables::DumpInfo* dump_info) {
  const auto& input_dirs = stream_map_.input_dirs();
  const auto& forbid_input_hdfs = stream_map_.forbid_input_hdfs();
  // 分目录过滤需要不文件
  for (auto& hdfs_path : input_dirs) {
    const auto& map_it = forbid_input_hdfs.find(hdfs_path);
    std::set<std::string> filter_tables_names;
    if (map_it != forbid_input_hdfs.end()) {
      filter_tables_names.insert(forbid_input_hdfs.at(hdfs_path).tables().cbegin(),
                                 forbid_input_hdfs.at(hdfs_path).tables().cend());
    }
    LOG(INFO) << "dir: " << hdfs_path << " export forbid_dir";
    for (auto& filter_table : filter_tables_names) {
      LOG(INFO) << "dir: " << hdfs_path << " table_name: " << filter_table;
    }
    hadoop::HDFSFileStream hdfs_stream(hadoop::FLAGS_hadoop_namenode_ip.c_str(),
                                       hadoop::FLAGS_hadoop_namenode_port);
    std::string dump_info_file = hdfs_path + "/" + version + "/dump_info";

    if (!hdfs_stream.Open(dump_info_file.c_str(), O_RDONLY)) {
      LOG(ERROR) << "hdfs_stream.Open(" << dump_info_file << ") failed";
      return false;
    }
    // 此处 1M 空间足以存放 dump_info 信息
    int BUF_SIZE = 1 << 20, offset = 0;
    std::vector<char> buf;
    buf.resize(BUF_SIZE);
    while (true) {
      int read_size = hdfs_stream.Read(buf.data() + offset, BUF_SIZE - offset);
      if (read_size == 0) {
        break;
      } else {
        offset += read_size;
      }
    }
    std::string content(buf.data(), offset);
    kuaishou::ad::tables::DumpInfo temp_dump_info;
    auto status =
        google::protobuf::util::JsonStringToMessage(content, &temp_dump_info);
    if (!status.ok()) {
      LOG(ERROR) << "JsonStringToMessage failed. error: "
                 << status.error_message() << ", dump_info: " << content;
      return false;
    }
    for (auto& temp_info : temp_dump_info.info()) {
      if (filter_tables_names.count(temp_info.table_name()) > 0) {
        LOG(INFO) << "dir: " << hdfs_path
                  << " table_name: " << temp_info.table_name()
                  << " be filtered";
        continue;
      }
      auto* info = dump_info->add_info();
      info->CopyFrom(temp_info);
      info->set_file_name(hdfs_path + "/" + version + "/" +
                          temp_info.file_name());
      LOG(INFO) << "table_name: " << info->table_name()
                << " proto_name:" << info->proto_name()
                << " file_name: " << info->file_name();
    }
  }
  return true;
}

//
bool StreamMapPb::InitHdfsMessageStreamMap(const std::string& version,
                                           MessageStreamMap* stream_map) {
  // auto adapter_config = Kconf::adapterConfig()->data();
  auto& hot_data_path = index_builder::UnifiedAdapterConfigManager::GetInstanace()->GetHotDataPath();
  if (!hot_data_path.empty()) {
    HotDataCenter::GetInstance()->CheckStatus(version);
  }
  const auto& input_dirs = stream_map_.input_dirs();
  for (const auto& input_dir : input_dirs) {
    GetDumpInfoByPath(input_dir, version, &dump_info_);
  }
  LOG(INFO) << "total dump_info: " << dump_info_.ShortDebugString();

  FindUpdateTableConfig();

  LOG(INFO) << "table dump_info: " << table_dump_info_.ShortDebugString();

  if (table_dump_info_.info_size() == 0) {
    LOG(ERROR) << "CheckBenchmarkFiles failed";
    return false;
  }

  TableConfigManager::GetInstance()->InitByDumpInfo(table_dump_info_);
  ks::index_builder::HdfsValidChecker::GetInstance()->InitDumpInfo(dump_info_);

  const auto& redirections = stream_map_.redirections();
  for (auto& info : dump_info_.info()) {
    LOG(INFO) << "dump_info: " << info.ShortDebugString();
    MessageStreamPtr stream(
        new HdfsMessageStream(info.table_name(), info.file_name()));
    stream->SetBaseMessage(info.proto_name());
    auto type_name = ks::index_builder::GetEnumNameByPbName(info.proto_name());
    if (type_name.empty()) {
      LOG(INFO) << "proto_name: " << info.proto_name() << " has non table skip";
      continue;
    } else {
      LOG(INFO) << "type_name: " << type_name
                << " proto_name: " << info.proto_name();
    }
    (*stream_map)[info.table_name()].emplace_back(stream);

    // 重定向
    std::string table_name = info.table_name();
    if (redirections.count(table_name) > 0) {
      MessageStreamPtr stream(
          new HdfsMessageStream(redirections.at(table_name), info.file_name()));
      stream->SetBaseMessage(info.proto_name());
      (*stream_map)[redirections.at(table_name)].emplace_back(stream);
    }
    LOG(INFO) << "single_table dump_info: " << info.ShortDebugString();
  }
  CheckTableConfigCoverStatus(stream_map);
  return true;
}

bool StreamMapPbOnKfs::CheckLatestValidVersionOfInputDir(
    std::string* latest_version) {
  const auto& input_dirs = stream_map_.input_dirs();
  std::set<std::string> all_versions;
  for (auto& hdfs_path : input_dirs) {
    std::string lastest_version;
    if (FindLastestSuccessVersionOnKfs(hdfs_path, &lastest_version,
                                       "dump_done")) {
      all_versions.insert(lastest_version);
    } else {
      LOG(INFO) << "hdfs: " << hdfs_path << " not prepared! fail...";
      return false;
    }
  }
  if (all_versions.size() != 1) {
    LOG(INFO) << "not all dir reach same version, fail!";
    return false;
  }

  std::string export_version = *(all_versions.rbegin());
  absl::Time t;
  std::string err;
  if (!absl::ParseTime("%Y-%m-%d_%H%M%S", export_version, absl::LocalTimeZone(),
                       &t, &err)) {
    LOG(ERROR) << "absl::ParseTime(" << export_version
               << ") failed. err: " << err;
    return false;
  }
  // 如果最新版本距离当前时间超过 1.5 小时，说明版本过老
  if (absl::ToUnixSeconds(absl::Now()) - absl::ToUnixSeconds(t) > 7200) {
    LOG(ERROR) << "The latest version: " << export_version
               << " is also too old.";
    return false;
  }
  *latest_version = export_version;
  return true;
}

// 合并所有 dump_info
bool StreamMapPbOnKfs::CheckBenchmarkFiles(
    const std::string& version, kuaishou::ad::tables::DumpInfo* dump_info) {
  const auto& input_dirs = stream_map_.input_dirs();
  const auto& forbid_input_hdfs = stream_map_.forbid_input_hdfs();
  // 分目录过滤需要不文件
  for (auto& hdfs_path : input_dirs) {
    const auto& map_it = forbid_input_hdfs.find(hdfs_path);
    std::set<std::string> filter_tables_names;
    if (map_it != forbid_input_hdfs.end()) {
      filter_tables_names.insert(
          forbid_input_hdfs.at(hdfs_path).tables().cbegin(),
          forbid_input_hdfs.at(hdfs_path).tables().cend());
    }
    LOG(INFO) << "dir: " << hdfs_path << " export forbid_dir";
    for (auto& filter_table : filter_tables_names) {
      LOG(INFO) << "dir: " << hdfs_path << " table_name: " << filter_table;
    }

    std::string dump_info_file = hdfs_path + "/" + version + "/dump_info";
    base::FileStream kfs_stream;
    int flags = base::PLATFORM_FILE_OPEN | base::PLATFORM_FILE_READ;
    if (kfs_stream.Open(dump_info_file, flags) != 0) {
      LOG(ERROR) << "kfs_stream.Open(" << dump_info_file << ") failed";
      return false;
    }
    // 此处 1M 空间足以存放 dump_info 信息
    int BUF_SIZE = 1 << 20, offset = 0;
    std::vector<char> buf;
    buf.resize(BUF_SIZE);
    while (true) {
      int read_size =
          kfs_stream.Read(buf.data() + offset, BUF_SIZE - offset);
      if (read_size <= 0) {
        break;
      } else {
        offset += read_size;
      }
    }
    std::string content(buf.data(), offset);
    kuaishou::ad::tables::DumpInfo temp_dump_info;

    auto status =
        google::protobuf::util::JsonStringToMessage(content, &temp_dump_info);
    if (!status.ok()) {
      LOG(ERROR) << "JsonStringToMessage failed. error: "
                 << status.error_message() << ", dump_info: " << content;
      return false;
    }
    for (auto& temp_info : temp_dump_info.info()) {
      if (filter_tables_names.count(temp_info.table_name()) > 0) {
        LOG(INFO) << "dir: " << hdfs_path
                  << " table_name: " << temp_info.table_name()
                  << " be filtered";
        continue;
      }
      auto* info = dump_info->add_info();
      info->CopyFrom(temp_info);
      info->set_file_name(hdfs_path + "/" + version + "/" +
                          temp_info.file_name());
      LOG(INFO) << "table_name: " << info->table_name()
                << " proto_name:" << info->proto_name()
                << " file_name: " << info->file_name();
    }
  }
  return true;
}

//
bool StreamMapPbOnKfs::InitHdfsMessageStreamMap(const std::string& version,
                                                MessageStreamMap* stream_map) {
  const auto& input_dirs = stream_map_.input_dirs();
  for (const auto& input_dir : input_dirs) {
    GetDumpInfoByPath(input_dir, version, &dump_info_);
  }
  LOG(INFO) << "total dump_info: " << dump_info_.ShortDebugString();

  FindUpdateTableConfig();

  LOG(INFO) << "table dump_info: " << table_dump_info_.ShortDebugString();

  if (table_dump_info_.info_size() == 0) {
    LOG(ERROR) << "CheckBenchmarkFiles failed";
    return false;
  }

  TableConfigManager::GetInstance()->InitByDumpInfo(table_dump_info_);
  ks::index_builder::HdfsValidChecker::GetInstance()->InitDumpInfo(dump_info_);

  const auto& redirections = stream_map_.redirections();
  for (auto& info : dump_info_.info()) {
    LOG(INFO) << "dump_info: " << info.ShortDebugString();
    MessageStreamPtr stream(
        new KfsMessageStream(info.table_name(), info.file_name()));
    stream->SetBaseMessage(info.proto_name());
    auto type_name = ks::index_builder::GetEnumNameByPbName(info.proto_name());
    if (type_name.empty()) {
      LOG(INFO) << "proto_name: " << info.proto_name() << " has non table skip";
      continue;
    } else {
      LOG(INFO) << "type_name: " << type_name
                << " proto_name: " << info.proto_name();
    }
    (*stream_map)[info.table_name()].emplace_back(stream);

    // 重定向
    std::string table_name = info.table_name();
    if (redirections.count(table_name) > 0) {
      MessageStreamPtr stream(
          new KfsMessageStream(redirections.at(table_name), info.file_name()));
      stream->SetBaseMessage(info.proto_name());
      (*stream_map)[redirections.at(table_name)].emplace_back(stream);
    }
    LOG(INFO) << "single_table dump_info: " << info.ShortDebugString();
  }
  CheckTableConfigCoverStatus(stream_map);
  return true;
}

bool StreamMapPbOnKfs::FindUpdateTableConfig() {
  const auto& input_dump_info = dump_info_;
  auto* output_dump_info = &table_dump_info_;
  std::set<std::string> dup_table;
  // temp_dump_info
  for (const auto& info : input_dump_info.info()) {
    const auto& table_name = info.table_name();
    const auto& proto_name = info.proto_name();
    if (dup_table.count(table_name) > 0) {
      continue;
    }
    dup_table.insert(table_name);
    auto* add_info = output_dump_info->add_info();
    add_info->CopyFrom(info);
    add_info->clear_file_name();
    add_info->clear_record_num();
  }
  return true;
}

bool StreamMapPbOnKfs::GetDumpInfoByPath(
    const std::string& hdfs_path, const std::string& version,
    kuaishou::ad::tables::DumpInfo* dump_info) {
  const auto& forbid_input_hdfs = stream_map_.forbid_input_hdfs();
  const auto& map_it = forbid_input_hdfs.find(hdfs_path);
  std::set<std::string> filter_tables_names;
  if (map_it != forbid_input_hdfs.end()) {
    filter_tables_names.insert(
        forbid_input_hdfs.at(hdfs_path).tables().cbegin(),
        forbid_input_hdfs.at(hdfs_path).tables().cend());
  }
  LOG(INFO) << "dir: " << hdfs_path << " export forbid_dir";
  for (auto& filter_table : filter_tables_names) {
    LOG(INFO) << "dir: " << hdfs_path << " table_name: " << filter_table;
  }

  std::string dump_info_file = hdfs_path + "/" + version + "/dump_info";
  base::FileStream kfs_stream;
  int flags = base::PLATFORM_FILE_OPEN | base::PLATFORM_FILE_READ;
  if (kfs_stream.Open(dump_info_file, flags) != 0) {
    LOG(ERROR) << "kfs_stream.Open(" << dump_info_file << ") failed";
    return false;
  }
  // 此处 1M 空间足以存放 dump_info 信息
  int BUF_SIZE = 1 << 20, offset = 0;
  std::vector<char> buf;
  buf.resize(BUF_SIZE);
  while (true) {
    int read_size = kfs_stream.Read(buf.data() + offset, BUF_SIZE - offset);
    if (read_size <= 0) {
      break;
    } else {
      offset += read_size;
    }
  }
  std::string content(buf.data(), offset);
  kuaishou::ad::tables::DumpInfo temp_dump_info;

  auto status =
      google::protobuf::util::JsonStringToMessage(content, &temp_dump_info);
  if (!status.ok()) {
    LOG(ERROR) << "JsonStringToMessage failed. error: "
               << status.error_message() << ", dump_info: " << content;
    return false;
  }

  for (auto& temp_info : temp_dump_info.info()) {
    if (filter_tables_names.count(temp_info.table_name()) > 0) {
      LOG(INFO) << "dir: " << hdfs_path
                << " table_name: " << temp_info.table_name() << " be filtered";
      continue;
    }
    auto* info = dump_info->add_info();
    info->CopyFrom(temp_info);
    info->set_file_name(hdfs_path + "/" + version + "/" +
                        temp_info.file_name());
    LOG(INFO) << "table_name: " << info->table_name()
              << " proto_name:" << info->proto_name()
              << " file_name: " << info->file_name();
  }
  return true;
}

bool StreamMapTableLite::GetDumpInfoByPath(
    const std::string& hdfs_path, const std::string& version,
    kuaishou::ad::tables::DumpInfo* dump_info) {
  hadoop::HDFSFileStream hdfs_stream(hadoop::FLAGS_hadoop_namenode_ip.c_str(),
                                     hadoop::FLAGS_hadoop_namenode_port);
  std::string dump_info_file =
      base::FilePath(hdfs_path).Append(version).Append("dump_info").value();

  if (!hdfs_stream.Open(dump_info_file.c_str(), O_RDONLY)) {
    LOG(ERROR) << "hdfs_stream.Open(" << dump_info_file << ") failed";
    return false;
  }
  // 此处 1M 空间足以存放 dump_info 信息
  int BUF_SIZE = 1 << 20, offset = 0;
  std::vector<char> buf;
  buf.resize(BUF_SIZE);
  while (true) {
    int read_size = hdfs_stream.Read(buf.data() + offset, BUF_SIZE - offset);
    if (read_size == 0) {
      break;
    } else {
      offset += read_size;
    }
  }
  std::string content(buf.data(), offset);
  kuaishou::ad::tables::DumpInfo temp_dump_info;
  auto status =
      google::protobuf::util::JsonStringToMessage(content, &temp_dump_info);
  if (!status.ok()) {
    LOG(ERROR) << "JsonStringToMessage failed. error: "
               << status.error_message() << ", dump_info: " << content;
    return false;
  }
  for (auto& temp_info : temp_dump_info.info()) {
    auto* info = dump_info->add_info();
    info->CopyFrom(temp_info);
    info->set_file_name(base::FilePath(hdfs_path)
                            .Append(version)
                            .Append(temp_info.file_name())
                            .value());
    LOG(INFO) << "table_name: " << info->table_name()
              << " proto_name:" << info->proto_name()
              << " file_name: " << info->file_name();
  }
  return true;
}

bool StreamMapTableLite::InitAdInstanceMessageStreamMap(
    const std::string& hdfs_path, InstanceStreamMap* stream_map) {
  hadoop::HDFSFileStream hdfs_stream(hadoop::FLAGS_hadoop_namenode_ip.c_str(),
                                     hadoop::FLAGS_hadoop_namenode_port);
  std::string dump_info_file = base::FilePath(hdfs_path).Append("dump_info").ToString();
  if (!hdfs_stream.Open(dump_info_file.c_str(), O_RDONLY)) {
    LOG(ERROR) << "hdfs_stream.Open(" << dump_info_file << ") failed";
    return false;
  }
  // 此处 1M 空间足以存放 dump_info 信息
  int BUF_SIZE = 1 << 20, offset = 0;
  std::vector<char> buf;
  buf.resize(BUF_SIZE);
  while (true) {
    int read_size = hdfs_stream.Read(buf.data() + offset, BUF_SIZE - offset);
    if (read_size == 0) {
      break;
    } else {
      offset += read_size;
    }
  }
  std::string content(buf.data(), offset);
  kuaishou::ad::tables::DumpInfo temp_dump_info;
  auto status =
      google::protobuf::util::JsonStringToMessage(content, &temp_dump_info);
  if (!status.ok()) {
    LOG(ERROR) << "JsonStringToMessage failed. error: "
               << status.error_message() << ", dump_info: " << content;
    return false;
  }

  if (temp_dump_info.info_size() < 0) {
    LOG(ERROR) << "dump_info: " << dump_info_file << " empty! error";
    return false;
  }
  kuaishou::ad::tables::DumpInfo dump_info_with_concrete;
  for (auto info : temp_dump_info.info()) {
    auto table_name = info.table_name();
    auto hdfs_file = base::FilePath(hdfs_path).Append(info.file_name()).ToString();
    InstanceStreamPtr stream(
        new HdfsMessageStreamForAdInstance(table_name, hdfs_file));
    stream->SetBaseMessage(info.proto_name());
    (*stream_map)[table_name].push_back(stream);
    auto add_info = dump_info_with_concrete.add_info();
    *add_info = info;
    add_info->set_file_name(hdfs_file);
  }
  ks::index_builder::HdfsValidChecker::GetInstance()->InitDumpInfo(
      dump_info_with_concrete);
  LOG(INFO) << dump_info_with_concrete.ShortDebugString();
  LOG_ASSERT(stream_map->size() > 0) << "empty streammap get, error";
  return true;
}

bool StreamMapPb::GetDumpInfoByPath(const std::string& hdfs_path,
                                    const std::string& version,
                                    kuaishou::ad::tables::DumpInfo* dump_info) {
  const auto& forbid_input_hdfs = stream_map_.forbid_input_hdfs();
  const auto& map_it = forbid_input_hdfs.find(hdfs_path);
  std::set<std::string> filter_tables_names;
  if (map_it != forbid_input_hdfs.end()) {
    filter_tables_names.insert(
        forbid_input_hdfs.at(hdfs_path).tables().cbegin(),
        forbid_input_hdfs.at(hdfs_path).tables().cend());
  }
  LOG(INFO) << "dir: " << hdfs_path << " export forbid_dir";
  for (auto& filter_table : filter_tables_names) {
    LOG(INFO) << "dir: " << hdfs_path << " table_name: " << filter_table;
  }
  hadoop::HDFSFileStream hdfs_stream(hadoop::FLAGS_hadoop_namenode_ip.c_str(),
                                     hadoop::FLAGS_hadoop_namenode_port);
  std::string dump_info_file = hdfs_path + "/" + version + "/dump_info";

  if (!hdfs_stream.Open(dump_info_file.c_str(), O_RDONLY)) {
    LOG(ERROR) << "hdfs_stream.Open(" << dump_info_file << ") failed";
    return false;
  }
  // 此处修改为 16M 空间足以存放 dump_info 信息
  int BUF_SIZE = 1 << 24, offset = 0;
  std::vector<char> buf;
  buf.resize(BUF_SIZE);
  while (true) {
    int read_size = hdfs_stream.Read(buf.data() + offset, BUF_SIZE - offset);
    if (read_size == 0) {
      break;
    } else {
      offset += read_size;
    }
  }
  std::string content(buf.data(), offset);
  kuaishou::ad::tables::DumpInfo temp_dump_info;
  auto status =
      google::protobuf::util::JsonStringToMessage(content, &temp_dump_info);
  if (!status.ok()) {
    LOG(ERROR) << "JsonStringToMessage failed. error: "
               << status.error_message() << ", dump_info: " << content;
    return false;
  }
  for (auto& temp_info : temp_dump_info.info()) {
    if (filter_tables_names.count(temp_info.table_name()) > 0) {
      LOG(INFO) << "dir: " << hdfs_path
                << " table_name: " << temp_info.table_name() << " be filtered";
      continue;
    }
    auto* info = dump_info->add_info();
    info->CopyFrom(temp_info);
    info->set_file_name(hdfs_path + "/" + version + "/" +
                        temp_info.file_name());
    LOG(INFO) << "table_name: " << info->table_name()
              << " proto_name:" << info->proto_name()
              << " file_name: " << info->file_name();
  }
  return true;
}

bool StreamMapPb::FindUpdateTableConfig() {
  const auto& input_dump_info = dump_info_;
  auto* output_dump_info = &table_dump_info_;
  std::set<std::string> dup_table;
  // temp_dump_info
  for (const auto& info : input_dump_info.info()) {
    const auto& table_name = info.table_name();
    const auto& proto_name = info.proto_name();
    if (dup_table.count(table_name) > 0) {
      continue;
    }
    dup_table.insert(table_name);
    auto* add_info = output_dump_info->add_info();
    add_info->CopyFrom(info);
    add_info->clear_file_name();
    add_info->clear_record_num();
  }
  return true;
}

void CleanLocalIndex(const std::string& output, int keep_num) {
  using base::file_util::FileEnumerator;
  FileEnumerator file_enumerator(output, false, FileEnumerator::DIRECTORIES);
  std::vector<std::string> directories;
  for (auto path = file_enumerator.Next(); !path.value().empty();
      path = file_enumerator.Next()) {
    directories.push_back(path.value());
  }

  if (directories.size() < keep_num) {
    return;
  }

  std::sort(directories.begin(), directories.end(),
      [](const std::string& a, const std::string& b) -> bool {
    return a < b;
  });

  while (directories.size() > keep_num) {
    std::string&& delete_path = std::string(directories.front());
    LOG(INFO) << "delete_dir: " << delete_path;
    base::file_util::Delete(base::FilePath(delete_path), true);
    directories.erase(directories.begin());
  }
}


bool StreamMapTableLiteOnKfs::InitAdInstanceMessageStreamMap(
    const std::string& hdfs_path, InstanceStreamMap* stream_map) {
  std::string dump_info_file =
      base::FilePath(hdfs_path).Append("dump_info").ToString();
  base::FileStream kfs_stream;
  int flags = base::PLATFORM_FILE_OPEN | base::PLATFORM_FILE_READ;
  if (kfs_stream.Open(dump_info_file, flags) != 0) {
    LOG(ERROR) << "kfs_stream.Open(" << dump_info_file << ") failed";
    return false;
  }
  // 此处 1M 空间足以存放 dump_info 信息
  int BUF_SIZE = 1 << 20, offset = 0;
  std::vector<char> buf;
  buf.resize(BUF_SIZE);
  while (true) {
    int read_size = kfs_stream.Read(buf.data() + offset, BUF_SIZE - offset);
    if (read_size <= 0) {
      break;
    } else {
      offset += read_size;
    }
  }
  std::string content(buf.data(), offset);
  kuaishou::ad::tables::DumpInfo temp_dump_info;

  auto status =
      google::protobuf::util::JsonStringToMessage(content, &temp_dump_info);
  if (!status.ok()) {
    LOG(ERROR) << "JsonStringToMessage failed. error: "
               << status.error_message() << ", dump_info: " << content;
    return false;
  }
  if (temp_dump_info.info_size() < 0) {
    LOG(ERROR) << "dump_info: " << dump_info_file << " empty! error";
    return false;
  }

  kuaishou::ad::tables::DumpInfo dump_info_with_concrete;

  for (auto info : temp_dump_info.info()) {
    auto table_name = info.table_name();
    auto hdfs_file =
        base::FilePath(hdfs_path).Append(info.file_name()).ToString();
    InstanceStreamPtr stream(
        new HdfsMessageStreamOnKfsForAdInstance(table_name, hdfs_file));
    stream->SetBaseMessage(info.proto_name());
    (*stream_map)[table_name].push_back(stream);
    auto add_info = dump_info_with_concrete.add_info();
    *add_info = info;
    add_info->set_file_name(hdfs_file);
  }
  ks::index_builder::HdfsValidChecker::GetInstance()->InitDumpInfo(
      dump_info_with_concrete);
  LOG(INFO) << dump_info_with_concrete.ShortDebugString();
  LOG_ASSERT(stream_map->size() > 0) << "empty streammap get, error";
  return true;
}

bool StreamMapTableLiteOnKfs::GetDumpInfoByPath(
    const std::string& hdfs_path, const std::string& version,
    kuaishou::ad::tables::DumpInfo* dump_info) {
  std::string dump_info_file =
      base::FilePath(hdfs_path).Append(version).Append("dump_info").value();
  base::FileStream kfs_stream;
  int flags = base::PLATFORM_FILE_OPEN | base::PLATFORM_FILE_READ;
  if (kfs_stream.Open(dump_info_file, flags) != 0) {
    LOG(ERROR) << "kfs_stream.Open(" << dump_info_file << ") failed";
    return false;
  }
  // 此处 1M 空间足以存放 dump_info 信息
  int BUF_SIZE = 1 << 20, offset = 0;
  std::vector<char> buf;
  buf.resize(BUF_SIZE);
  while (true) {
    int read_size = kfs_stream.Read(buf.data() + offset, BUF_SIZE - offset);
    if (read_size <= 0) {
      break;
    } else {
      offset += read_size;
    }
  }
  std::string content(buf.data(), offset);
  kuaishou::ad::tables::DumpInfo temp_dump_info;

  auto status =
      google::protobuf::util::JsonStringToMessage(content, &temp_dump_info);
  if (!status.ok()) {
    LOG(ERROR) << "JsonStringToMessage failed. error: "
               << status.error_message() << ", dump_info: " << content;
    return false;
  }

  for (auto& temp_info : temp_dump_info.info()) {
    auto* info = dump_info->add_info();
    info->CopyFrom(temp_info);
    info->set_file_name(base::FilePath(hdfs_path)
                            .Append(version)
                            .Append(temp_info.file_name())
                            .value());
    LOG(INFO) << "table_name: " << info->table_name()
              << " proto_name:" << info->proto_name()
              << " file_name: " << info->file_name();
  }
  return true;
}

bool StreamMapLiteIndex::InitAdInstanceMessageStreamMap(
    const std::string& hdfs_path, InstanceStreamMap* stream_map) {
  hadoop::HDFSFileStream hdfs_stream(hadoop::FLAGS_hadoop_namenode_ip.c_str(),
                                     hadoop::FLAGS_hadoop_namenode_port);
  std::string dump_info_file =
      base::FilePath(hdfs_path).Append("dump_info").ToString();
  if (!hdfs_stream.Open(dump_info_file.c_str(), O_RDONLY)) {
    LOG(ERROR) << "hdfs_stream.Open(" << dump_info_file << ") failed";
    return false;
  }
  // 此处 1M 空间足以存放 dump_info 信息
  int BUF_SIZE = 1 << 20, offset = 0;
  std::vector<char> buf;
  buf.resize(BUF_SIZE);
  while (true) {
    int read_size = hdfs_stream.Read(buf.data() + offset, BUF_SIZE - offset);
    if (read_size == 0) {
      break;
    } else {
      offset += read_size;
    }
  }
  std::string content(buf.data(), offset);
  kuaishou::ad::tables::DumpInfo temp_dump_info;
  auto status =
      google::protobuf::util::JsonStringToMessage(content, &temp_dump_info);
  if (!status.ok()) {
    LOG(ERROR) << "JsonStringToMessage failed. error: "
               << status.error_message() << ", dump_info: " << content;
    return false;
  }

  if (temp_dump_info.info_size() < 0) {
    LOG(ERROR) << "dump_info: " << dump_info_file << " empty! error";
    return false;
  }
  kuaishou::ad::tables::DumpInfo dump_info_with_concrete;
  for (auto info : temp_dump_info.info()) {
    std::vector<std::string> real_files;
    for (auto file_info : info.files()) {
      real_files.push_back(file_info.file());
    }
    auto table_name = info.table_name();
    auto hdfs_file =
        base::FilePath(hdfs_path).Append(info.table_name()).ToString();
    auto* ori_ptr = new HdfsLiteIndexStream(table_name, hdfs_file);
    InstanceStreamPtr stream(ori_ptr);
    stream->SetBaseMessage(info.proto_name());
    ori_ptr->InitMultiFile(real_files);
    (*stream_map)[table_name].push_back(stream);
    auto add_info = dump_info_with_concrete.add_info();
    *add_info = info;
    add_info->set_file_name(hdfs_file);
  }
  ks::index_builder::HdfsValidChecker::GetInstance()->InitDumpInfo(
      dump_info_with_concrete);
  LOG(INFO) << dump_info_with_concrete.ShortDebugString();
  LOG_ASSERT(stream_map->size() > 0) << "empty streammap get, error";
  return true;
}

bool StreamMapLiteIndexOnKfs::InitAdInstanceMessageStreamMap(
    const std::string& hdfs_path, InstanceStreamMap* stream_map) {
  std::string dump_info_file =
      base::FilePath(hdfs_path).Append("dump_info").ToString();
  base::FileStream kfs_stream;
  int flags = base::PLATFORM_FILE_OPEN | base::PLATFORM_FILE_READ;
  if (kfs_stream.Open(dump_info_file, flags) != 0) {
    LOG(ERROR) << "kfs_stream.Open(" << dump_info_file << ") failed";
    return false;
  }
  // 此处 1M 空间足以存放 dump_info 信息
  int BUF_SIZE = 1 << 20, offset = 0;
  std::vector<char> buf;
  buf.resize(BUF_SIZE);
  while (true) {
    int read_size = kfs_stream.Read(buf.data() + offset, BUF_SIZE - offset);
    if (read_size <= 0) {
      break;
    } else {
      offset += read_size;
    }
  }
  std::string content(buf.data(), offset);
  kuaishou::ad::tables::DumpInfo temp_dump_info;

  auto status =
      google::protobuf::util::JsonStringToMessage(content, &temp_dump_info);
  if (!status.ok()) {
    LOG(ERROR) << "JsonStringToMessage failed. error: "
               << status.error_message() << ", dump_info: " << content;
    return false;
  }
  if (temp_dump_info.info_size() < 0) {
    LOG(ERROR) << "dump_info: " << dump_info_file << " empty! error";
    return false;
  }

  kuaishou::ad::tables::DumpInfo dump_info_with_concrete;

  for (auto info : temp_dump_info.info()) {
    auto table_name = info.table_name();
    std::vector<std::string> real_files;
    for (auto file_info : info.files()) {
      real_files.push_back(file_info.file());
    }
    auto hdfs_file =
        base::FilePath(hdfs_path).Append(info.table_name()).ToString();
    auto* ori_ptr = new HdfsLiteIndexStreamOnKfs(table_name, hdfs_file);
    InstanceStreamPtr stream(ori_ptr);
    stream->SetBaseMessage(info.proto_name());
    ori_ptr->InitMultiFile(real_files);
    (*stream_map)[table_name].push_back(stream);
    auto add_info = dump_info_with_concrete.add_info();
    *add_info = info;
    add_info->set_file_name(hdfs_file);
  }
  ks::index_builder::HdfsValidChecker::GetInstance()->InitDumpInfo(
      dump_info_with_concrete);
  LOG(INFO) << dump_info_with_concrete.ShortDebugString();
  LOG_ASSERT(stream_map->size() > 0) << "empty streammap get, error";
  return true;
}

}  // namespace index_adapter
}  // namespace ks
