#pragma once
#include <atomic>
#include <map>
#include <string>
#include <vector>
#include <mutex>
#include <utility>
#include <unordered_map>
#include "base/common/closure.h"
#include "base/thread/thread_pool.h"
#include "third_party/libcuckoo/cuckoohash_map.hh"
#include "perfutil/perfutil.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/meta.pb.h"

/*
  1.监控上一个成功的任务
  2.记录上一个成功任务的数据输出情况
  3.hdfs 卡住了,强制 core 重启
*/

namespace ks {
namespace index_adapter {

using TableRecordCountContainer = cuckoohash_map<std::string, int64>;
using ShardTableRecordCountContainer = std::unordered_map<int, TableRecordCountContainer>;
using TableFilterReasonContainer = cuckoohash_map<std::string, std::unordered_map<std::string, int64>>;
using ShardTableFilterReasonContainer = std::unordered_map<int, TableFilterReasonContainer>;

class PerfManager {
 public:
  static PerfManager* GetInstance() {
    static PerfManager instance;
    return &instance;
  }

  void Start();
  void Stop();

  void RegisterIndexMonitorTask(const std::string& prefix,
                                const std::string& hdfs_path);
  void UpdateTableRecord(const std::string& prefix,
                         const kuaishou::ad::tables::DumpInfo& dump_info);

  // 记录时间
  void UpdateBuildBeginTime(const std::string& prefix);
  void UpdateBuildEndTime(const std::string& prefix);

  void UpdateSnapShot(const std::string& file_name, const std::string& version);
  void UpdateLabelShot(const std::string& file_name, const std::string& version,
                       const std::string& type);

  // 初始化记录各个 builder 中间数据的容器
  void InitBuilderRecordContainer(int shard);
  void RecordOneForBuilderRead(int shard, const std::string& table, int cnt = 1);
  void RecordOneForBuilderValid(int shard, const std::string& table, int cnt = 1);
  void RecordOneForBuilderFilter(int shard, const std::string& table, const std::string& reason, int cnt = 1);
  void PerfLogRecordForBuilder(bool clear_after_perf = false);

 private:
  PerfManager() = default;
  ~PerfManager() = default;

  void RunImp();
  void CheckImp();
  std::mutex mtx_;
  std::mutex mtx_for_keep_alive_;
  // 各个表维度的打点
  std::map<std::string, kuaishou::ad::tables::DumpInfo> dump_info_map_;
  // 前后数量变化率打点
  std::map<std::string, std::vector<std::pair<std::string, double>>>
      diff_rate_map_;
  // 要检测的输出路径
  std::map<std::string, std::string> hdfs_paths_map_;
  std::atomic_bool run_{false};
  // creative_score snap 文件路径 -> 更新时间
  std::map<std::string, int64_t> dir2version_;
  // label 更新时间标签
  std::map<std::string, int64_t> label_dir2verion_;

  // 上次确认 hdfs 存活的时间
  int64_t last_checkpoint_in_ms_{0};

  ::thread::ThreadPool monitor_thread_pool_{3};

  cuckoohash_map<std::string, std::pair<bool, int64_t>> living_task_record_map_;

  std::mutex builder_record_mtx_;
  bool builder_record_ready{false};
  // builder 读取每个表数据情况
  ShardTableRecordCountContainer builder_table_record_read_;
  // builder 读取每个表数据有效情况
  ShardTableRecordCountContainer builder_table_record_valid_;
  // builder 读取每个表数据过滤情况
  ShardTableFilterReasonContainer builder_table_record_filter_;
};
}  // namespace index_adapter
}  // namespace ks
