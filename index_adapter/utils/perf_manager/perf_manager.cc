#include "teams/ad/index_adapter/utils/perf_manager/perf_manager.h"
#include <absl/time/clock.h>
#include <absl/time/time.h>
#include <functional>
#include <memory>
#include <mutex>
#include <string>
#include <utility>
#include "base/time/timestamp.h"
#include "glog/logging.h"
#include "perfutil/perfutil.h"
#include "teams/ad/engine_base/utils/deploy_variable.h"
#include "teams/ad/index_adapter/utils/kconf/kconf.h"
#include "teams/ad/index_builder/multiform_filter/common_tools.h"
#include "teams/ad/index_builder/base/index_read/file_wrapper.h"
#include "teams/ad/index_builder/base/index_read/index_wrapper.h"
#include "teams/ad/index_builder/utils/unified_config_manager/unified_adapter_config.h"

namespace ks {
namespace index_adapter {
using ks::infra::PerfUtil;

void PerfManager::Start() {
  bool start_once = run_.exchange(true);
  LOG_ASSERT(!start_once) << "PerfManager start twice!";

  monitor_thread_pool_.AddTask(::NewCallback(this, &PerfManager::RunImp));
  monitor_thread_pool_.AddTask(::NewCallback(this, &PerfManager::CheckImp));
}

void PerfManager::Stop() {
  run_.store(false);
  monitor_thread_pool_.JoinAll();
}

void PerfManager::RunImp() {
  std::shared_ptr<ks::index_builder::index_base::HdfsFileWrapper>
      hdfs_file_wrapper =
          std::make_shared<ks::index_builder::index_base::HdfsFileWrapper>();
  LOG(INFO) << "RunImp thread start";
  while (run_) {
    {
      std::lock_guard<std::mutex> lock_guard(mtx_);
      for (auto& item : hdfs_paths_map_) {
        std::string prefix = item.first;
        std::string hdfs_path = item.second;

        std::string version;
        if (!hdfs_file_wrapper->FindLastestSuccessVersion(
                hdfs_path, &version,
                ks::index_builder::index_base::TargetIndex().SuccessFlag())) {
          LOG(ERROR) << "hdfs_path: " << hdfs_path << " get version fail!";
          continue;
        }
        {
          last_checkpoint_in_ms_ = base::GetTimestamp() / 1000;
        }
        absl::Time t;
        std::string err;
        if (absl::ParseTime("%Y-%m-%d_%H%M", version, absl::LocalTimeZone(), &t,
                            &err)) {
          int64_t gap_sec =
              absl::ToUnixSeconds(absl::Now()) - absl::ToUnixSeconds(t);
          PerfUtil::SetLogStash((gap_sec / 60), "ad.index_adapter",
                                "from_last_update", prefix);
          LOG(INFO) << "point: " << (gap_sec / 60) << " prefix: " << prefix;
        } else {
          LOG(ERROR) << "hdfs_path: " << hdfs_path << " version: " << version
                     << " parse fail";
        }
      }
      for (auto& item : dump_info_map_) {
        std::string prefix = item.first;
        auto dump_info = item.second;

        for (auto info : dump_info.info()) {
          auto table_name = info.table_name();
          auto record_num = info.record_num();
          PerfUtil::SetLogStash(record_num, "ad.index_adapter", table_name,
                                prefix, "record_num");
          LOG(INFO) << "prefix: " << prefix << " table_name: " << table_name
                    << " record_num: " << record_num;
        }
      }
      for (auto& item : diff_rate_map_) {
        std::string prefix = item.first;
        const auto& record_vec = item.second;
        for (auto record : record_vec) {
          auto table_name = record.first;
          auto diff_rate = record.second;
          PerfUtil::SetLogStash(int64_t(diff_rate * 1000), "ad.index_adapter",
                                table_name, prefix, "diff_rate");
          LOG(INFO) << "prefix: " << prefix << " table_name: " << table_name
                    << " diff_rate: " << diff_rate;
        }
      }
      for (auto& kv : dir2version_) {
        auto dir = kv.first;
        auto version = kv.second;
        int64_t diff_sec = base::GetTimestamp() / 1000000;
        PerfUtil::SetLogStash(diff_sec - version, "ad.index_adapter",
                              "snapshot_latency", dir,
                              ks::index_builder::DeployVariable::GetKwsName(),
                              ks::index_builder::DeployVariable::GetPodName());
      }
    }
    for (auto& kv : label_dir2verion_) {
      auto dir = kv.first;
      auto version = kv.second;
      int64_t diff_sec = base::GetTimestamp() / 1000000;
      PerfUtil::SetLogStash(diff_sec - version, "ad.index_adapter",
                            "label_snapshot_latency", dir,
                            ks::index_builder::DeployVariable::GetKwsName(),
                            ks::index_builder::DeployVariable::GetPodName());
    }
    PerfLogRecordForBuilder(false);
    // 打点使用统一配置的情况
    auto* config_manager = index_builder::UnifiedAdapterConfigManager::GetInstanace();
    auto use_unified_adapter_config = config_manager->IsUseUnifiedAdapterConfig();
    if (use_unified_adapter_config) {
      PerfUtil::SetLogStash(1, "ad.index_builder", "hit_unified_adapter_config",
                            config_manager->GetKconfName(),
                            ks::engine_base::DeployVariable::GetKwsName(),
                            ks::engine_base::DeployVariable::GetStage());
    } else {
      PerfUtil::SetLogStash(1, "ad.index_builder", "miss_unified_adapter_config",
                            config_manager->GetKconfName(),
                            ks::engine_base::DeployVariable::GetKwsName(),
                            ks::engine_base::DeployVariable::GetStage());
    }
    sleep(60);
  }
  LOG(INFO) << "RunImp thread exit...";
}

void PerfManager::CheckImp() {
  while (run_) {
    std::function<bool(std::string, std::pair<bool, int64_t>)> traverse_func =
        [&](std::string prefix, const std::pair<bool, int64_t> value) {
          // 制作时间不得超过 2.5 个小时
          static const int64_t time_out = Kconf::taskBuilderLimit();  // ms

          if (value.first) {
            int64_t diff_ms = (base::GetTimestamp() - value.second) / 1000;
            PerfUtil::SetLogStash(
                diff_ms, "ad.index_adapter", "task_runtime", prefix,
                ks::index_builder::DeployVariable::GetKwsName(),
                ks::index_builder::DeployVariable::GetPodName());
            LOG(WARNING)
                << "prefix: " << prefix
                << " task begin at: " << (value.second / 1000)
                << ", time_out: " << diff_ms << " ms";
            LOG(INFO) << "prefix: " << prefix
                      << " task begin at: " << (value.second / 1000)
                      << ", running time: " << diff_ms << " ms";
          } else {
            LOG(INFO) << "prefix: " << prefix << " not running!";
          }
          return true;
        };
    for (auto& kv : living_task_record_map_.lock_table()) {
      traverse_func(kv.first, kv.second);
    }
    if (last_checkpoint_in_ms_ != 0) {
      int64_t now_in_ms = base::GetTimestamp() / 1000;
      // 上次检测时间 + 10 = 本次 deadline
      int64_t deadline_in_ms = last_checkpoint_in_ms_ +
                               (60 * 10 * 1000);  // 5 分钟 hdfs 检测失败 core
      if (deadline_in_ms <= now_in_ms) {
        PerfUtil::CountLogStash(
            1, "ad.index_adapter", "hdfs_connect_fail",
            ks::index_builder::DeployVariable::GetKwsName(),
            ks::index_builder::DeployVariable::GetPodName());
      }
      if (deadline_in_ms >= now_in_ms) {
        LOG(WARNING)
            << "hdfs connect fail exceed 10 min!!!";
      }
    }
    sleep(120);
  }
  LOG(INFO) << "CheckImp thread exit...";
}


void PerfManager::RegisterIndexMonitorTask(const std::string& prefix,
                                           const std::string& hdfs_path) {
  std::lock_guard<std::mutex> lock_guard(mtx_);
  hdfs_paths_map_[prefix] = hdfs_path;
}

static double GetDiffRate(int64_t size1, int64_t size2) {
  if (size1 == 0 || size2 == 0) {
    return 1.0;
  }
  double diff = size2 - size1;
  return std::abs(diff / size2);
}

void PerfManager::UpdateTableRecord(
    const std::string& prefix,
    const kuaishou::ad::tables::DumpInfo& dump_info) {
  LOG(INFO) << dump_info.ShortDebugString();
  std::lock_guard<std::mutex> lock_guard(mtx_);
  std::vector<std::pair<std::string, double>> diff_records;
  if (dump_info_map_.find(prefix) != dump_info_map_.end()) {
    auto old_dump_info = dump_info_map_[prefix];
    std::map<std::string, kuaishou::ad::tables::DumpData> table_name2info;
    for (const auto& info : old_dump_info.info()) {
      table_name2info[info.table_name()] = info;
    }
    for (const auto& info : dump_info.info()) {
      const auto table_name = info.table_name();
      if (table_name2info.find(table_name) == table_name2info.end()) {
        continue;
      }
      auto old_record_num = table_name2info[table_name].record_num();
      auto record_num = info.record_num();
      double diff_rate = GetDiffRate(old_record_num, record_num);
      diff_records.emplace_back(table_name, diff_rate);
    }
    diff_rate_map_[prefix] = diff_records;
  }
  dump_info_map_[prefix] = dump_info;
}

void PerfManager::UpdateBuildBeginTime(const std::string& prefix) {
  int64_t time_now = base::GetTimestamp();
  std::pair<bool, int64_t> status{true, time_now};
  {
    std::lock_guard<std::mutex> lock_guard(mtx_for_keep_alive_);
    living_task_record_map_.insert_or_assign(prefix, status);
  }
}

void PerfManager::UpdateBuildEndTime(const std::string& prefix) {
  int64_t time_now = base::GetTimestamp();
  std::pair<bool, int64_t> status{false, time_now};
  {
    std::lock_guard<std::mutex> lock_guard(mtx_for_keep_alive_);
    living_task_record_map_.insert_or_assign(prefix, status);
  }
}

void PerfManager::UpdateSnapShot(const std::string& file_name,
                                 const std::string& version) {
  std::lock_guard<std::mutex> lock_guard(mtx_for_keep_alive_);
  absl::Time t;
  std::string err;
  // format for example '2021-11-16_1553'
  if (absl::ParseTime("%Y-%m-%d_%H%M", version, absl::LocalTimeZone(), &t,
                      &err)) {
    auto seconds = absl::ToUnixSeconds(t);
    LOG(INFO) << file_name << " " << version << " seconds: " << seconds;
    dir2version_[file_name] = seconds;
  } else {
    LOG(INFO) << "version: " << version << " parse fail";
  }
}

void PerfManager::UpdateLabelShot(const std::string& file_name, const std::string& version,
                     const std::string& type) {
  std::lock_guard<std::mutex> lock_guard(mtx_for_keep_alive_);
  absl::Time t;
  std::string err;
  // format for example '20211223_2125'
  std::string tmp_version = version;
  tmp_version.insert(4, "_");
  if (absl::ParseTime("%Y_%m%d_%H%M", tmp_version, absl::LocalTimeZone(), &t,
                      &err)) {
    auto seconds = absl::ToUnixSeconds(t);
    LOG(INFO) << file_name << " " << type << " " << tmp_version
              << " seconds: " << seconds;
    label_dir2verion_[file_name + "_" + type] = seconds;
  } else {
    LOG(INFO) << "version: " << tmp_version << " parse fail";
  }
}

void PerfManager::InitBuilderRecordContainer(int shard) {
  LOG(INFO) << "InitBuilderRecordContainer:" << shard;
  std::lock_guard<std::mutex> lck(builder_record_mtx_);
  builder_table_record_read_[shard] = TableRecordCountContainer();
  builder_table_record_valid_[shard] = TableRecordCountContainer();
  builder_table_record_filter_[shard] = TableFilterReasonContainer();
}

void PerfManager::RecordOneForBuilderRead(int shard, const std::string& table, int cnt) {
  LOG(INFO) << "RecordOneForBuilderRead"
            << ", shard:" << shard
            << ", table:" << table
            << ", cnt:" << cnt;
  if (table.empty()) { return; }
  if (builder_table_record_read_.find(shard) == builder_table_record_read_.end()) { return; }
  builder_table_record_read_[shard].uprase_fn(table, [cnt](auto& value) {
    value += cnt;
    return false;
  }, cnt);
}

void PerfManager::RecordOneForBuilderValid(int shard, const std::string& table, int cnt) {
  LOG(INFO) << "RecordOneForBuilderValid"
            << ", shard:" << shard
            << ", table:" << table
            << ", cnt:" << cnt;
  if (table.empty()) { return; }
  if (builder_table_record_valid_.find(shard) == builder_table_record_valid_.end()) { return; }
  builder_table_record_valid_[shard].uprase_fn(table, [cnt](auto& value) {
    value += cnt;
    return false;
  }, cnt);
}

void PerfManager::RecordOneForBuilderFilter(int shard, const std::string& table,
                                            const std::string& reason, int cnt) {
  LOG(INFO) << "RecordOneForBuilderFilter"
            << ", shard:" << shard
            << ", table:" << table
            << ", reason" << reason
            << ", cnt:" << cnt;
  if (reason.empty() || table.empty()) { return; }
  if (builder_table_record_filter_.find(shard) == builder_table_record_filter_.end()) { return; }
  std::unordered_map<std::string, int64> filter_reason;
  filter_reason.emplace(reason, cnt);
  builder_table_record_filter_[shard].uprase_fn(table, [&](auto& value) {
    value[reason] += cnt;
    return false;
  }, filter_reason);
}

void PerfManager::PerfLogRecordForBuilder(bool clear_after_perf) {
  LOG(INFO) << "PerfLogRecordForBuilder, clear_after_perf:" << clear_after_perf;
  std::lock_guard<std::mutex> lck(builder_record_mtx_);
  auto perf = [&](ShardTableRecordCountContainer& container, const std::string& sub_tag) {
    for (auto& table : container) {
      auto lock_table = table.second.lock_table();
      for (const auto& item : lock_table) {
        PerfUtil::SetLogStash(item.second, "ad.index_adapter",
                              sub_tag, item.first, std::to_string(table.first));
      }
      if (clear_after_perf) { lock_table.clear(); }
      lock_table.unlock();
    }
  };
  perf(builder_table_record_read_, "table_build_read_num");
  perf(builder_table_record_valid_, "table_build_valid_num");
  for (auto& table : builder_table_record_filter_) {
    auto lock_table = table.second.lock_table();
    for (const auto& item : lock_table) {
      for (const auto reason_num : item.second) {
        PerfUtil::SetLogStash(reason_num.second, "ad.index_adapter",
                              "table_build_filter_num", item.first,
                              std::to_string(table.first), reason_num.first);
      }
    }
    if (clear_after_perf) {
      lock_table.clear();
    }
    lock_table.unlock();
  }
}

}  // namespace index_adapter
}  // namespace ks
