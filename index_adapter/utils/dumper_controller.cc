#include "teams/ad/index_adapter/utils/dumper_controller.h"

#include <algorithm>
#include <set>
#include <unordered_map>

#include "gflags/gflags.h"
#include "absl/strings/substitute.h"
#include "absl/time/clock.h"
#include "base/common/logging.h"
#include "base/common/stl_logging.h"
#include "base/encoding/base64.h"
#include "base/file/file_util.h"
#include "base/thread/thread_pool.h"
#include "ks/serving_util/dynamic_config.h"
#include "perfutil/perfutil.h"
#include "pub/src/base/time/timestamp.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/index_adapter/utils/consumer/message_consumer.h"
#include "teams/ad/index_adapter/utils/hot_data/hot_data_center.h"
#include "teams/ad/index_adapter/utils/hot_data/all_scene_hot_data_center.h"
#include "teams/ad/index_adapter/utils/kconf/kconf.h"
#include "teams/ad/index_adapter/utils/perf_manager/perf_manager.h"
#include "teams/ad/index_builder/utils/cache_loader/account_mark_no_smr.h"
#include "teams/ad/index_builder/utils/cache_loader/cache_common.h"
#include "teams/ad/index_builder/utils/extra_adapter/creative_adapter.h"
#include "teams/ad/index_builder/utils/kafka2hive/kafka2hive_manager.h"
#include "teams/ad/index_builder/utils/table_config.h"
#include "teams/ad/index_adapter/utils/adapter_stream_map.h"
#include "teams/ad/index_builder/utils/unified_config_manager/unified_adapter_config.h"
#include "teams/ad/index_builder/utils/extra_adapter/wt_creative_score_parser.h"

using ks::infra::PerfUtil;
using ks::index_builder::TableConfigManager;

DEFINE_bool(enable_exeunt_inactive_account, false, "enable exeunt inactive account");

namespace ks {
namespace index_adapter {

DataDumper::DataDumper() {}

bool DataDumper::Start() {
  int64_t start_ts = base::GetTimestamp();
  LOG(INFO) << "DataDumper start...";
  bool once_start = running_.exchange(true);
  if (once_start) {
    LOG_ASSERT(false) << "DataDumper start twice please check!!!";
  }
  const auto& adapter_config = Kconf::adapterConfig()->data();
  auto* config_manager = index_builder::UnifiedAdapterConfigManager::GetInstanace();
  config_manager->InitWithOldAdapterConfig(adapter_config);
  PerfManager::GetInstance()->Start();
  const auto& creative_adapter_key = config_manager->GetCreativeAdapterKey();
  auto use_aggr_data = config_manager->IsUseAggregationData();
  ks::index_builder::CacheCommonBegin(creative_adapter_key, use_aggr_data);
  ks::index_builder::WTCreativeScoreParser::GetInstance().Init(creative_adapter_key);

  table_conf_ = Kconf::tableConfig()->data();
  LOG(INFO) << table_conf_.ShortDebugString();
  TableConfigManager::GetInstance()->Init(table_conf_);
  ks::index_builder::Kafka2HiveManager::GetInstance()->Init();
  const auto& kafka2hive_key = config_manager->GetKafka2hiveKey();
  if (!kafka2hive_key.empty()) {
    ks::index_builder::Kafka2HiveManager::GetInstance()->Init(kafka2hive_key);
  }
  const auto& hot_data_path = config_manager->GetHotDataPath();
  if (!hot_data_path.empty()) {
    HotDataCenter::GetInstance()->Init(hot_data_path);
    HotDataCenter::GetInstance()->Start();
  }
  bool enable_ktable = config_manager->EnableKtableBuild();
  if (enable_ktable) {
    const auto& all_hot_data_paths = config_manager->GetAllHotDataPaths();
    AllSceneHotDataCenter::GetInstance()->Init(all_hot_data_paths);
    AllSceneHotDataCenter::GetInstance()->Start();
  }
  dump_thread_ = std::thread([this]() { DumpThread(); });
  point_thread_ = std::thread([this]() { PointThread(); });
  return true;
}

void DataDumper::BuilderRun(int64_t shard_no) {
  std::unique_ptr<Builder> builder;
  if (Kconf::enableDumpOnKfs()) {
    builder.reset(new KfsBuilder);
  } else {
    builder.reset(new Builder);
  }
  builder->SetSharedNo(shard_no);
  if (!builder->IsReady()) {
    LOG(INFO) << "shard_no: " << shard_no << ", builder not ready!";
    return;
  }
  if (!builder->Prepare()) {
    LOG(INFO) << "shard_no: " << shard_no << ", builder not prepared!";
    return;
  }
  if (!builder->BuilderRun()) {
    LOG(INFO) << "shard_no: " << shard_no << ", builder run error!";
    return;
  }
}

void DataDumper::Prepare() {
  das_version_.clear();
  stream_map_.clear();
  forbid_pb_set_.clear();
  std::shared_ptr<ks::index_builder::index_base::StreamMapBase> stream_map_maker_ptr;
  stream_map_maker_ptr = StreamMapMakerForAdapter::GetInstance().GetStreamMap();
  if (!stream_map_maker_ptr->CheckLastetValidVersion(&das_version_) ||
      das_version_.empty()) {
    LOG(INFO) << "Get Latest Das Version Fail, sleep 60s";
    return;
  }
  if (!stream_map_maker_ptr->InitMessageStreamMap(das_version_,
                                                      &stream_map_)) {
    LOG(INFO) << "Get Latest MessageStrem Fail, sleep 60s version: "
              << das_version_;
    return;
  }
  absl::Time t;
  std::string err;
  if (!absl::ParseTime("%Y-%m-%d_%H%M%S", das_version_, absl::LocalTimeZone(), &t, &err)) {
    LOG(ERROR) << "absl::ParseTime(" << das_version_ << ") failed. err: " << err;
    return;
  }
  int64_t ts = absl::ToUnixSeconds(t);
  if (ks::index_builder::AdKconfUtil::enableAdapterCreative()) {
    auto adapter_creative_server = ks::index_builder::AdapterForCreativeServer::GetInstance();
    adapter_creative_server->SetDasVersion(ts);
    adapter_creative_server->WaitForVersionReady(ts);
  }
  auto advanced_config = ks::ad::index_message_proxy::AdKconfUtil::adInstanceAdvanceConfig()->data();
  const auto& kconf_forbid_types = advanced_config.forbid_types();
  for (const auto& forbid_type : kconf_forbid_types) {
    forbid_pb_set_.insert(forbid_type);
  }

  // auto adapter_config = Kconf::adapterConfig()->data();
  const auto& tables = index_builder::UnifiedAdapterConfigManager::GetInstanace()->GetTables();
  // std::vector<std::string> table_names{tables.begin(), tables.end()};

  ks::index_builder::index_base::MessageStreamMap temp_stream_map;
  for (const auto& table : tables) {
    auto map_it = stream_map_.find(table);
    if (map_it != stream_map_.end()) {
      temp_stream_map.emplace(table, map_it->second);
    }
  }
  std::swap(temp_stream_map, stream_map_);
}

void DataDumper::SingleStreamRun(
    ks::index_builder::index_base::MessageStreamPtr hdfs_stream,
    std::vector<std::shared_ptr<Builder>> builder_ptr_vec) {
  auto proto_name = hdfs_stream->MessageName();
  std::vector<int> shard_no_vec;
  if (forbid_pb_set_.count(proto_name) == 0) {
    int read_cnt = 0;
    std::unordered_map<int, int> valid_cnt;
    std::unordered_map<int, std::unordered_map<std::string, int>> filter_cnt;
    // 先记录下有哪些分片
    for (auto& builder_ptr : builder_ptr_vec) {
      shard_no_vec.emplace_back(builder_ptr->GetShardId());
    }
    std::string filter_reason;
    while (hdfs_stream->Valid()) {
      read_cnt++;
      for (auto& builder_ptr : builder_ptr_vec) {
        const auto* ad_inst = hdfs_stream->NextAdInstance();
        auto ad_tmp = *ad_inst;
        builder_ptr->Consumer(&ad_tmp, &filter_reason);
        auto shard_no = builder_ptr->GetShardId();
        if (filter_reason.empty()) {
          valid_cnt[shard_no]++;
        } else {
          filter_cnt[shard_no][filter_reason]++;
        }
      }
    }
    for (auto shard_no : shard_no_vec) {
      PerfManager::GetInstance()->RecordOneForBuilderRead(shard_no, proto_name, read_cnt);
      PerfManager::GetInstance()->RecordOneForBuilderValid(shard_no, proto_name, valid_cnt[shard_no]);
      for (const auto& filter : filter_cnt[shard_no]) {
        PerfManager::GetInstance()->RecordOneForBuilderFilter(shard_no, proto_name,
                                                              filter.first, filter.second);
      }
    }
  }
  for (auto& builder_ptr : builder_ptr_vec) {
    builder_ptr->UnRegisterTablesOfProtoName(proto_name);
  }
}

void DataDumper::InitFlags() {
  FLAGS_enable_exeunt_inactive_account = Kconf::enableExeuntInactiveAccount();
}

void DataDumper::DumpThread() {
  LOG(INFO) << "DumpThread start...";
  while (running_) {
    std::this_thread::sleep_for(std::chrono::seconds{60});
    InitFlags();
    LOG(INFO) << "Try to dump data!";
    const auto& shard_no_vec = index_builder::UnifiedAdapterConfigManager::GetInstanace()->GetShardNo();
    // 分片
    if (shard_no_vec.size() > 0) {
      std::vector<std::shared_ptr<Builder>> builder_ptr_vec;
      Prepare();
      if (!IsValid()) {
        LOG(INFO) << "Das Data Not Ready Sleep 60s";
        continue;
      }
      for (auto shard_no : shard_no_vec) {
        std::shared_ptr<Builder> builder_ptr;
        if (Kconf::enableDumpOnKfs()) {
          builder_ptr.reset(new KfsBuilder);
        } else {
          builder_ptr.reset(new Builder);
        }
        PerfManager::GetInstance()->InitBuilderRecordContainer(shard_no);
        builder_ptr->SetSharedNo(shard_no);
        builder_ptr->SetDasVersion(das_version_);
        builder_ptr->SetStreamMap(stream_map_);
        if (!builder_ptr->IsReady()) {
          continue;
        }
        if (!builder_ptr->Prepare()) {
          LOG(INFO) << "shard_no: " << shard_no << ", builder not prepared!";
          continue;
        }
        builder_ptr_vec.push_back(builder_ptr);
      }
      if (builder_ptr_vec.empty()) {
        LOG(INFO) << "All Shard Ready, Sleep 60s";
        continue;
      }
      // dump data!

      int32_t max_thread_needed = 0;
      for (auto& vec : stream_map_) {
        max_thread_needed += vec.second.size();
      }
      if (!Kconf::enableMaxDumpThread()) {
        max_thread_needed = 200;
      }
      thread::ThreadPool dump_pool{max_thread_needed};
      for (auto hdfs_stream_vec : stream_map_) {
        for (ks::index_builder::index_base::MessageStreamPtr hdfs_stream_ptr : hdfs_stream_vec.second) {
          LOG(INFO) << "stream: " << hdfs_stream_ptr->DebugInfo()
                    << " begin to dump";
          dump_pool.AddTask(::NewCallback(this, &DataDumper::SingleStreamRun,
                                          hdfs_stream_ptr, builder_ptr_vec));
        }
      }
      dump_pool.JoinAll();
      for (auto& builder_ptr : builder_ptr_vec) {
        builder_ptr->Finish();
      }
      LOG(INFO) << "All Shard Dump Ready!";
    } else {  //  不分片处理
      int64_t shard_no = -1;
      PerfManager::GetInstance()->InitBuilderRecordContainer(shard_no);
      BuilderRun(shard_no);
    }
    PerfManager::GetInstance()->PerfLogRecordForBuilder(true);
  }
  LOG(INFO) << "DumpThread end...";
}

void DataDumper::PointThread() { LOG(INFO) << "Do nothing at present..."; }

void DataDumper::Stop() {
  running_.store(false);
  dump_thread_.join();
  point_thread_.join();
  PerfManager::GetInstance()->Stop();
}
}  // namespace index_adapter
}  // namespace ks
