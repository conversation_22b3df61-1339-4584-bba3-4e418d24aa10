#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_lsp_item_fea.dark
class ExtractLspLatestLiveSegmentInfoItemX7categoryValue : public FastFeatureNoPrefix {
 public:
  ExtractLspLatestLiveSegmentInfoItemX7categoryValue();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractLspLatestLiveSegmentInfoItemX7categoryValue);
};

REGISTER_EXTRACTOR(ExtractLspLatestLiveSegmentInfoItemX7categoryValue);
}  // namespace ad_algorithm
}  // namespace ks
