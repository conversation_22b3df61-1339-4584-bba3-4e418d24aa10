#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_combine_dense_auction_bid.dark
class ExtractCombineDenseOriginPriceNoPrefix : public FastFeature {
 public:
  ExtractCombineDenseOriginPriceNoPrefix();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractCombineDenseOriginPriceNoPrefix);
};

REGISTER_EXTRACTOR(ExtractCombineDenseOriginPriceNoPrefix);
}  // namespace ad_algorithm
}  // namespace ks
