/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_info_time_gap_value.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/user_hist_req_info.h"

namespace ks {
namespace ad_algorithm {

ExtractLspLatestLiveSegmentInfoTimeGapValue::ExtractLspLatestLiveSegmentInfoTimeGapValue()
    : FastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {}
void ExtractLspLatestLiveSegmentInfoTimeGapValue::Extract(const AdLog& adlog, size_t pos,
                                                          std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64_list(adlog.item(pos).ad_dsp_info().live_info().common_info_attr(), 5104672);
  auto x2 = get_adlog_time(adlog);
  auto x3 = get_lsp_seg_req_time_gap(x1, x2);
  add_feature_result(x3, result);
}

}  // namespace ad_algorithm
}  // namespace ks
