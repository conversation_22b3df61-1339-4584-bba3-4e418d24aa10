/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_inner_shelf_retrieval_tag.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

ExtractInnerShelfRetrievalTag::ExtractInnerShelfRetrievalTag() : FastFeature(FeatureType::DENSE_ITEM) {}
void ExtractInnerShelfRetrievalTag::Extract(const AdLog& adlog, size_t pos,
                                            std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_map_int32_int64(adlog.item(pos).rank_params());
  auto x3 = get_value_from_map(x1, 1523);
  auto x4 = cast_to_float(x3);
  add_feature_result(x4, 1, result);
}

}  // namespace ad_algorithm
}  // namespace ks
