/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_incentive_type.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/ad_huoke.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {

ExtractUserIncentiveType::ExtractUserIncentiveType() : FastFeatureNoPrefix(FeatureType::USER) {}
void ExtractUserIncentiveType::Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64(adlog.context().pos_id());
  auto x2 = get_adlog_int64(adlog.context().page_id());
  auto x3 = merge_incentive_type(x1, x2);
  add_feature_result(x3, result);
}

}  // namespace ad_algorithm
}  // namespace ks
