/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_l_r_sid_fir_gv_mix.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/merchant_match_sparse_action.h"

namespace ks {
namespace ad_algorithm {

ExtractMatchLRSidFirGvMix::ExtractMatchLRSidFirGvMix() : FastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {}
void ExtractMatchLRSidFirGvMix::Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64(adlog.item(pos).ad_dsp_info().live_info().common_info_attr(), 5101472);
  auto x2 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 5104947);
  auto x3 = get_match_target_sequence(x1, x2);
  add_feature_result(x3, result);
}

}  // namespace ad_algorithm
}  // namespace ks
