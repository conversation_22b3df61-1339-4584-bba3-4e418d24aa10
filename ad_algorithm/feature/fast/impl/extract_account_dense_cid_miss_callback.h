#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_account_dense_cid_miss_callback.dark
class ExtractAccountDenseCidMissCallback : public FastFeature {
 public:
  ExtractAccountDenseCidMissCallback();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractAccountDenseCidMissCallback);
};

REGISTER_EXTRACTOR(ExtractAccountDenseCidMissCallback);
}  // namespace ad_algorithm
}  // namespace ks
