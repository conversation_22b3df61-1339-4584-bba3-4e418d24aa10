/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_click_spu_level_match_dense.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/merge_list.h"
#include "teams/ad/ad_algorithm/fed/compute/realtime_ecom_action.h"

namespace ks {
namespace ad_algorithm {

ExtractURealTimeClickSpuLevelMatchDense::ExtractURealTimeClickSpuLevelMatchDense()
    : FastFeature(FeatureType::DENSE_COMBINE) {}
void ExtractURealTimeClickSpuLevelMatchDense::Extract(const AdLog& adlog, size_t pos,
                                                      std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 67115);
  auto x2 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 67118);
  auto x3 = get_adlog_time(adlog);
  auto x4 = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(),
                            CommonInfoAttr::ECOM_GOODS_INFO_SPU_ID);
  auto x7 = get_time_segment_cnt_log_value(x1, x2, x3, x4, 8, 30);
  auto x9 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 67123);
  auto x11 = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(),
                             CommonInfoAttr::ECOM_GOODS_INFO_MMU_B_CATEGORY1_ID);
  auto x14 = get_time_segment_cnt_log_value(x1, x9, x3, x11, 8, 30);
  auto x16 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 67124);
  auto x18 = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(),
                             CommonInfoAttr::ECOM_GOODS_INFO_MMU_B_CATEGORY2_ID);
  auto x21 = get_time_segment_cnt_log_value(x1, x16, x3, x18, 8, 30);
  auto x23 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 67125);
  auto x25 = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(),
                             CommonInfoAttr::ECOM_GOODS_INFO_MMU_B_CATEGORY3_ID);
  auto x28 = get_time_segment_cnt_log_value(x1, x23, x3, x25, 8, 30);
  auto x30 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 67126);
  auto x32 = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(),
                             CommonInfoAttr::ECOM_GOODS_INFO_MMU_B_CATEGORY4_ID);
  auto x35 = get_time_segment_cnt_log_value(x1, x30, x3, x32, 8, 30);
  auto x36 = merge_float_list_all(x7, x14, x21, x28, x35);
  add_feature_result(x36, 40, result);
}

}  // namespace ad_algorithm
}  // namespace ks
