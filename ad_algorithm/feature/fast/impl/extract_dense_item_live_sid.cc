/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_live_sid.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/merge_list.h"

namespace ks {
namespace ad_algorithm {

ExtractDenseItemLiveSid::ExtractDenseItemLiveSid() : FastFeature(FeatureType::DENSE_ITEM) {}
void ExtractDenseItemLiveSid::Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
  auto x1 =
      get_adlog_float(adlog.item(pos).label_info().label_info_attr(), LabelInfoCommonAttr::OUTER_LIVE_SID_0);
  auto x2 =
      get_adlog_float(adlog.item(pos).label_info().label_info_attr(), LabelInfoCommonAttr::OUTER_LIVE_SID_1);
  auto x3 =
      get_adlog_float(adlog.item(pos).label_info().label_info_attr(), LabelInfoCommonAttr::OUTER_LIVE_SID_2);
  auto x4 = merge_float_list_all(x1, x2, x3);
  add_feature_result(x4, 3, result);
}

}  // namespace ad_algorithm
}  // namespace ks
