#pragma once
#include <algorithm>
#include <string>
#include <vector>
#include "teams/ad/ad_algorithm/feature/fast/frame/common_info_attr_feature_factory.h"
#include "serving_base/jansson/json.h"

namespace ks {
namespace ad_algorithm {

class ExtractCommonDenseFloatListValue: public CommonInfoAttrFeature {
 public:
  ExtractCommonDenseFloatListValue(FeatureType type,
                                   ::kuaishou::ad::CommonInfoAttr::Name attr_name,
                                   FeaturePrefix prefix)
    : CommonInfoAttrFeature(type, attr_name, prefix) {
  }

  bool NeedSlot() override { return false; }

 protected:
  bool InitInternal(const base::Json& config) override {
    max_value_count_ = config.GetInt("max_value_count", max_value_count_);
    auto feature_name = config.GetString("feature_name", "");
    if (max_value_count_ <= 0) {
      LOG(FATAL) << "feature name: " << feature_name << " max value count must be bigger than 0, but is: " << max_value_count_;
    }

    return true;
  }

  void ExtractCommonAttr(const ::kuaishou::ad::CommonInfoAttr* attr,
                                 std::vector<ExtractResult>* result) override {
    const auto &values = attr->float_list_value();

    uint32_t value_count = std::min(max_value_count_, attr->float_list_value_size());

    for (uint32_t i = 0; i < value_count; ++i) {
      AddFeature(i, values[i], result);
    }

    // 补 0.
    if (value_count < max_value_count_) {
      for (uint32_t i = value_count; i < max_value_count_; i++) {
        AddFeature(i, 0.0, result);
      }
    }
  }

  bool CheckCommonAttr(const ::kuaishou::ad::CommonInfoAttr *attr,
                       const std::vector<ExtractResult> &result) override {
    if (result.size() != max_value_count_) {
      LOG(WARNING) << "result size " << result.size()
                   << " invalid, expected: " << max_value_count_;
      return false;
    }

    uint32_t value_count = std::min(max_value_count_, attr->float_list_value_size());

    for (size_t i = 0; i < value_count; i++) {
      if (result[i].value != attr->float_list_value(i)) {
        LOG(ERROR) << "value not same, i: " << i
                   << ", result[i].value: " << result[i].value
                   << ", attr->float_list_value(i): " << attr->float_list_value(i);
        return false;
      }
    }

    return true;
  }

 private:
  int32_t max_value_count_ = 0;
};

using ExtractCommonDenseFloatListValueNewType = ExtractCommonDenseFloatListValue;
REGISTER_COMMON_INFO_ATTR_EXTRACTOR(ExtractCommonDenseFloatListValue, "list<float>", "dense_value");

}  // namespace ad_algorithm
}  // namespace ks
