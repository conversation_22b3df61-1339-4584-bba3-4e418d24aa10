/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_id_conversion_cnt_6.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/realtime_ecom_action.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

ExtractAuthorIdConversionCnt6::ExtractAuthorIdConversionCnt6()
    : FastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {}
void ExtractAuthorIdConversionCnt6::Extract(const AdLog& adlog, size_t pos,
                                            std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_map_int64_int64(adlog.context().info_common_attr(),
                                      ContextInfoCommonAttr::AUTHOR_ID_CONVERSION_CNT_6);
  auto x2 = get_adlog_int64(adlog.item(pos).ad_dsp_info().photo_info().author_info().id());
  auto x3 = get_adlog_int64(adlog.item(pos).ad_dsp_info().live_info().author_info().id());
  auto x4 = get_first_non_zero(x2, x3);
  auto x5 = get_value_from_map(x1, x4);
  auto x7 = value_or(x5, 0);
  auto x8 = cast_to_int64(x7);
  add_feature_result(x8, result);
}

}  // namespace ad_algorithm
}  // namespace ks
