/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_mix_rank_unify_gpm.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/math.h"

namespace ks {
namespace ad_algorithm {

ExtractItemMixRankUnifyGpm::ExtractItemMixRankUnifyGpm() : FastFeature(FeatureType::DENSE_ITEM) {}
void ExtractItemMixRankUnifyGpm::Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_float(adlog.item(pos).item_common_attr(), ContextInfoCommonAttr::MIXRANK_UNIFY_GPM_NEW);
  auto x2 = cast_to_float(x1);
  auto x4 = div(x2, 1000000.0);
  add_feature_result(x4, 1, result);
}

}  // namespace ad_algorithm
}  // namespace ks
