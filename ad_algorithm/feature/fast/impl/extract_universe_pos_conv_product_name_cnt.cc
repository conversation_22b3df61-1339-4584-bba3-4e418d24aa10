/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_conv_product_name_cnt.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {

ExtractUniversePosConvProductNameCnt::ExtractUniversePosConvProductNameCnt()
    : FastFeatureNoPrefix(FeatureType::USER) {}
void ExtractUniversePosConvProductNameCnt::Extract(const AdLog& adlog, size_t pos,
                                                   std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64(adlog.context().info_common_attr(),
                            ContextInfoCommonAttr::UNIVERSE_POS_CONV_PRODUCT_NAME_CNT);
  add_feature_result(x1, result);
}

}  // namespace ad_algorithm
}  // namespace ks
