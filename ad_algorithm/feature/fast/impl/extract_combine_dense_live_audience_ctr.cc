/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_live_audience_ctr.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

ExtractCombineDenseLiveAudienceCtr::ExtractCombineDenseLiveAudienceCtr()
    : FastFeature(FeatureType::DENSE_COMBINE) {}
void ExtractCombineDenseLiveAudienceCtr::Extract(const AdLog& adlog, size_t pos,
                                                 std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_map_int64_float(adlog.context().info_common_attr(),
                                      ContextInfoCommonAttr::ADRANK_LIVE_AUDIENCE_CTR_LIST);
  auto x2 = get_adlog_int64(adlog.item(pos).ad_dsp_info().photo_info().id());
  auto x3 = get_value_from_map(x1, x2);
  auto x4 = cast_to_float(x3);
  add_feature_result(x4, 1, result);
}

}  // namespace ad_algorithm
}  // namespace ks
