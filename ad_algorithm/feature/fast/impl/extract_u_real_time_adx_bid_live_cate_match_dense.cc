/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_live_cate_match_dense.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/merge_list.h"
#include "teams/ad/ad_algorithm/fed/compute/realtime_ecom_action.h"

namespace ks {
namespace ad_algorithm {

ExtractURealTimeAdxBidLiveCateMatchDense::ExtractURealTimeAdxBidLiveCateMatchDense()
    : FastFeature(FeatureType::DENSE_COMBINE) {}
void ExtractURealTimeAdxBidLiveCateMatchDense::Extract(const AdLog& adlog, size_t pos,
                                                       std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 67081);
  auto x2 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 67089);
  auto x3 = get_adlog_time(adlog);
  auto x4 = get_adlog_int64_list(adlog.item(pos).ad_dsp_info().live_info().common_info_attr(),
                                 CommonInfoAttr::AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_1);
  auto x7 = get_time_segment_cnt_log_value_v3(x1, x2, x3, x4, 8, 200);
  auto x9 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 67090);
  auto x11 = get_adlog_int64_list(adlog.item(pos).ad_dsp_info().live_info().common_info_attr(),
                                  CommonInfoAttr::AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_2);
  auto x14 = get_time_segment_cnt_log_value_v3(x1, x9, x3, x11, 8, 200);
  auto x16 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 67091);
  auto x18 = get_adlog_int64_list(adlog.item(pos).ad_dsp_info().live_info().common_info_attr(),
                                  CommonInfoAttr::AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_3);
  auto x21 = get_time_segment_cnt_log_value_v3(x1, x16, x3, x18, 8, 200);
  auto x23 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 67092);
  auto x25 = get_adlog_int64_list(adlog.item(pos).ad_dsp_info().live_info().common_info_attr(),
                                  CommonInfoAttr::AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_4);
  auto x28 = get_time_segment_cnt_log_value_v3(x1, x23, x3, x25, 8, 200);
  auto x29 = merge_float_list_all(x7, x14, x21, x28);
  add_feature_result(x29, 32, result);
}

}  // namespace ad_algorithm
}  // namespace ks
