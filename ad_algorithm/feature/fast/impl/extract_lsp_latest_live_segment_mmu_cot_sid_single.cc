/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_mmu_cot_sid_single.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/user_hist_req_info.h"

namespace ks {
namespace ad_algorithm {

ExtractLspLatestLiveSegmentMmuCotSidSingle::ExtractLspLatestLiveSegmentMmuCotSidSingle()
    : FastFeatureNoPrefix(FeatureType::ITEM) {}
void ExtractLspLatestLiveSegmentMmuCotSidSingle::Extract(const AdLog& adlog, size_t pos,
                                                         std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64_list(adlog.item(pos).ad_dsp_info().live_info().common_info_attr(), 5104906);
  auto x2 = get_lsp_seg_cot_sid(x1);
  add_feature_result(x2, result);
}

}  // namespace ad_algorithm
}  // namespace ks
