/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_origin_price_no_prefix.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {

ExtractCombineDenseOriginPriceNoPrefix::ExtractCombineDenseOriginPriceNoPrefix()
    : FastFeature(FeatureType::DENSE_COMBINE) {}
void ExtractCombineDenseOriginPriceNoPrefix::Extract(const AdLog& adlog, size_t pos,
                                                     std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_float(adlog.item(pos).item_common_attr(), ContextInfoCommonAttr::DELIVERY_ORIGIN_PRICE);
  add_feature_result(x1, 1, result);
}

}  // namespace ad_algorithm
}  // namespace ks
