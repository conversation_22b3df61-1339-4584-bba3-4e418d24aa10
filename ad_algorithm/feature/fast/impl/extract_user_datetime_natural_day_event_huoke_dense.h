#pragma once
#include <string>
#include <vector>
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"


namespace ks {
namespace ad_algorithm {
class ExtractUserDatetimeNaturalDayEventHuokeDense : public FastFeature {
 public:
  ExtractUserDatetimeNaturalDayEventHuokeDense();

  virtual void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserDatetimeNaturalDayEventHuokeDense);
};

REGISTER_EXTRACTOR(ExtractUserDatetimeNaturalDayEventHuokeDense);

}  // namespace ad_algorithm
}  // namespace ks

