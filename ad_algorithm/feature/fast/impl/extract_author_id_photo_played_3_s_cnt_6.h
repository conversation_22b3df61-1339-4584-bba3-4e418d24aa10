#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature:
// teams/ad/ad_algorithm/dark_feature/impl/extract_context_chuangxin_author_industry_feature.dark
class ExtractAuthorIdPhotoPlayed3SCnt6 : public FastFeatureNoPrefix {
 public:
  ExtractAuthorIdPhotoPlayed3SCnt6();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractAuthorIdPhotoPlayed3SCnt6);
};

REGISTER_EXTRACTOR(ExtractAuthorIdPhotoPlayed3SCnt6);
}  // namespace ad_algorithm
}  // namespace ks
