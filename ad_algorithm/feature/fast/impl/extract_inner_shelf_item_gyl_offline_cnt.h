#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_inner_shelf_item_gyl_offline_cnt.dark
class ExtractInnerShelfItemGylOfflineCnt : public FastFeature {
 public:
  ExtractInnerShelfItemGylOfflineCnt();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractInnerShelfItemGylOfflineCnt);
};

REGISTER_EXTRACTOR(ExtractInnerShelfItemGylOfflineCnt);
}  // namespace ad_algorithm
}  // namespace ks
