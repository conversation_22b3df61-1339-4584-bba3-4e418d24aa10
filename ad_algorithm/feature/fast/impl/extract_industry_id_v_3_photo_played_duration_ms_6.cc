/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_id_v_3_photo_played_duration_ms_6.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

ExtractIndustryIdV3PhotoPlayedDurationMs6::ExtractIndustryIdV3PhotoPlayedDurationMs6()
    : FastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {}
void ExtractIndustryIdV3PhotoPlayedDurationMs6::Extract(const AdLog& adlog, size_t pos,
                                                        std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_map_int64_int64(adlog.context().info_common_attr(),
                                      ContextInfoCommonAttr::INDUSTRY_ID_V3_PHOTO_PLAYED_DURATION_MS_6);
  auto x2 = get_adlog_int64(adlog.item(pos).ad_dsp_info().creative().base().industry_id_v3());
  auto x3 = get_value_from_map(x1, x2);
  auto x5 = value_or(x3, 0);
  auto x6 = cast_to_int64(x5);
  add_feature_result(x6, result);
}

}  // namespace ad_algorithm
}  // namespace ks
