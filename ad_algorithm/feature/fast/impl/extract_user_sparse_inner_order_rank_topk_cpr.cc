/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_inner_order_rank_topk_cpr.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/rank_context_info.h"

namespace ks {
namespace ad_algorithm {

ExtractUserSparseInnerOrderRankTopkCpr::ExtractUserSparseInnerOrderRankTopkCpr()
    : FastFeatureNoPrefix(FeatureType::USER) {}
void ExtractUserSparseInnerOrderRankTopkCpr::Extract(const AdLog& adlog, size_t pos,
                                                     std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_map_int64_float(adlog.context().info_common_attr(),
                                      ContextInfoCommonAttr::ADRANK_INNER_MERGE_CVR_LIST);
  auto x2 = get_adlog_map_int64_float(adlog.context().info_common_attr(),
                                      ContextInfoCommonAttr::FRRANK_UESCORE_PCPR_LIST);
  auto x5 = get_topk_xtr_from_map(x1, x2, 50, 10000);
  add_feature_result(x5, result);
}

}  // namespace ad_algorithm
}  // namespace ks
