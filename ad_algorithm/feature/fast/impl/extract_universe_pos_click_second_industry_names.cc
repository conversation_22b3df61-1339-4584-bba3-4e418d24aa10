/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_click_second_industry_names.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/uv_action_seq_util.h"

namespace ks {
namespace ad_algorithm {

ExtractUniversePosClickSecondIndustryNames::ExtractUniversePosClickSecondIndustryNames()
    : FastFeatureNoPrefix(FeatureType::USER) {}
void ExtractUniversePosClickSecondIndustryNames::Extract(const AdLog& adlog, size_t pos,
                                                         std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_str(adlog.context().info_common_attr(),
                          ContextInfoCommonAttr::UNIVERSE_POS_CLICK_SECOND_INDUSTRY_NAMES);
  auto x2 = get_adlog_int64(adlog.context().info_common_attr(),
                            ContextInfoCommonAttr::UNIVERSE_POS_CLICK_PRODUCT_NAME_CNT);
  auto x4 = uv_split_string_string_list_2_int64(x1, x2, 3);
  add_feature_result(x4, result);
}

}  // namespace ad_algorithm
}  // namespace ks
