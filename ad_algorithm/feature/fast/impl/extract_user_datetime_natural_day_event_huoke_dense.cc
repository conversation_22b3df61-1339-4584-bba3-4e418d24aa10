#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_datetime_natural_day_event_huoke_dense.h"

namespace ks {
namespace ad_algorithm {

ExtractUserDatetimeNaturalDayEventHuokeDense::ExtractUserDatetimeNaturalDayEventHuokeDense()
    : FastFeature(FeatureType::DENSE_COMBINE) {}

void ExtractUserDatetimeNaturalDayEventHuokeDense::Extract(
    const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
  if (adlog.is_train()) {
    int64_t delivery_time = adlog.Get().time();
    const auto& item = adlog.item(pos);
    int idx = 0;
    int64_t pay_callback_t = 0;
    LOG(INFO) << "multi_label_fact_size=" << item.label_info().multi_label_fact_size();
    LOG(INFO) << "multi_label_occur_ts_size=" << item.label_info().multi_label_occur_ts_size();
    while (idx < item.label_info().multi_label_fact_size() &&
           idx < item.label_info().multi_label_occur_ts_size()) {
      const auto label = item.label_info().multi_label_fact(idx);
      const auto occur_ts = item.label_info().multi_label_occur_ts(idx);
      LOG(INFO) << "label : " << label << ", occur_ts : " << occur_ts;
      if (label == static_cast<int>(kuaishou::ad::AdCallbackLog::EVENT_EFFECTIVE_CUSTOMER_ACQUISITION) ||
            label == static_cast<int>(kuaishou::ad::AdCallbackLog::EVENT_PHONE_CARD_ACTIVATE)) {
        pay_callback_t = occur_ts * 10 < delivery_time ? occur_ts : occur_ts / 1000;
        break;
      }
      idx++;
    }

    LOG(INFO) << "pay_callback_t : " << pay_callback_t;
    int max_day = 7;
    if (pay_callback_t > 0) {
      time_t tick = (time_t)(delivery_time / 1000);
      struct tm tm = {0};
      struct tm* tm1 = localtime_r(&tick, &tm);
      if (tm1 == NULL) {
        return;
      }
      tm1->tm_hour = 0;
      tm1->tm_min = 0;
      tm1->tm_sec = 0;
      int64_t natural_day_t = mktime(tm1);

      LOG(INFO) << "natural_day_t : " << natural_day_t;

      for (int i = 0; i < max_day; ++i) {
        int64_t cur_natural_day_t = (i + 1) * 86400 + natural_day_t;
        if (cur_natural_day_t > pay_callback_t) {
          AddFeature(i, 1.0, result);
        } else {
          AddFeature(i, 0.0, result);
        }
      }
    } else {
      for (int i = 0; i < max_day; ++i) {
        AddFeature(i, 0.0, result);
      }
    }
  }
}

}  // namespace ad_algorithm
}  // namespace ks
