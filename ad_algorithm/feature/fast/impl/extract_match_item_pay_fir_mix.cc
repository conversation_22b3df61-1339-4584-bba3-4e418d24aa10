/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_item_pay_fir_mix.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/merchant_match_sparse_action.h"

namespace ks {
namespace ad_algorithm {

ExtractMatchItemPayFirMix::ExtractMatchItemPayFirMix() : FastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {}
void ExtractMatchItemPayFirMix::Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64(adlog.item(pos).ad_dsp_info().live_info().common_info_attr(),
                            CommonInfoAttr::LIVE_REALTIME_EXPLAINING_ITEM);
  auto x2 = get_adlog_int64_list(adlog.item(pos).ad_dsp_info().live_info().common_info_attr(),
                                 CommonInfoAttr::LIVE_REALTIME_YELLOWTROLLEY_ITEM_LIST);
  auto x3 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 5105656);
  auto x4 = get_match_target_list_sequence(x1, x2, x3);
  add_feature_result(x4, result);
}

}  // namespace ad_algorithm
}  // namespace ks
