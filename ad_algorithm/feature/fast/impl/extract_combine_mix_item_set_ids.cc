/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_item_set_ids.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_mix_item_set_features.h"

namespace ks {
namespace ad_algorithm {

ExtractCombineMixItemSetIds::ExtractCombineMixItemSetIds()
    : FastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {}
void ExtractCombineMixItemSetIds::Extract(const AdLog& adlog, size_t pos,
                                          std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64_list(adlog.context().info_common_attr(),
                                 ContextInfoCommonAttr::MIXRANK_SETITEM_STAT_FEA);
  auto x2 = get_adlog_int64_list(adlog.context().info_common_attr(),
                                 ContextInfoCommonAttr::MIXRANK_SETITEM_STAT_KEY);
  auto x3 = get_adlog_int64(adlog.item(pos).ad_dsp_info().creative().base().id());
  auto x4 = get_mix_item_set_features(x1, x2, x3);
  add_feature_result(x4, result);
}

}  // namespace ad_algorithm
}  // namespace ks
