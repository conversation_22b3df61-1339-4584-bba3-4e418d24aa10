#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_u_realtime_adx_live_match_spu_dense.dark
class ExtractURealTimeAdxBidLiveSpuMatchDense : public FastFeature {
 public:
  ExtractURealTimeAdxBidLiveSpuMatchDense();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractURealTimeAdxBidLiveSpuMatchDense);
};

REGISTER_EXTRACTOR(ExtractURealTimeAdxBidLiveSpuMatchDense);
}  // namespace ad_algorithm
}  // namespace ks
