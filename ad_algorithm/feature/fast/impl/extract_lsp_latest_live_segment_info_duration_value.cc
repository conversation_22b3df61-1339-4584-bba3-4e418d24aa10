/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_info_duration_value.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {

ExtractLspLatestLiveSegmentInfoDurationValue::ExtractLspLatestLiveSegmentInfoDurationValue()
    : FastFeatureNoPrefix(FeatureType::ITEM) {}
void ExtractLspLatestLiveSegmentInfoDurationValue::Extract(const AdLog& adlog, size_t pos,
                                                           std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64_list(adlog.item(pos).ad_dsp_info().live_info().common_info_attr(), 5104673);
  add_feature_result(x1, result);
}

}  // namespace ad_algorithm
}  // namespace ks
