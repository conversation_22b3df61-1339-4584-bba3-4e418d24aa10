#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_inner_shelf_item_bh_offline_cnt.dark
class ExtractInnerShelfItemBhOfflineCnt : public FastFeature {
 public:
  ExtractInnerShelfItemBhOfflineCnt();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractInnerShelfItemBhOfflineCnt);
};

REGISTER_EXTRACTOR(ExtractInnerShelfItemBhOfflineCnt);
}  // namespace ad_algorithm
}  // namespace ks
