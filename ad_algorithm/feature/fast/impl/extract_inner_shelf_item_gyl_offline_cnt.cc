/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_inner_shelf_item_gyl_offline_cnt.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/merge_list.h"
#include "teams/ad/ad_algorithm/fed/compute/realtime_ecom_action.h"

namespace ks {
namespace ad_algorithm {

ExtractInnerShelfItemGylOfflineCnt::ExtractInnerShelfItemGylOfflineCnt()
    : FastFeature(FeatureType::DENSE_ITEM) {}
void ExtractInnerShelfItemGylOfflineCnt::Extract(const AdLog& adlog, size_t pos,
                                                 std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(), 5104880);
  auto x2 = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(), 5104883);
  auto x3 = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(), 5104870);
  auto x4 = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(), 5104897);
  auto x5 = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(), 5104893);
  auto x6 = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(), 5104900);
  auto x7 = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(), 5104873);
  auto x8 = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(), 5104879);
  auto x9 = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(), 5104884);
  auto x10 = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(), 5104875);
  auto x11 = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(), 5104878);
  auto x12 = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(), 5104881);
  auto x13 = merge_int64_list_12(x1, x2, x3, x4, x5, x6, x7, x8, x9, x10, x11, x12);
  auto x14 = get_segment_cnt_log_value(x13);
  add_feature_result(x14, 12, result);
}

}  // namespace ad_algorithm
}  // namespace ks
