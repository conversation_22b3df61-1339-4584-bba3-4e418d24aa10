/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_query_quant_fix_seq.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_ad_search_quant_seq.h"
#include "teams/ad/ad_algorithm/fed/compute/math.h"

namespace ks {
namespace ad_algorithm {

ExtractSearchQueryQuantFixSeq::ExtractSearchQueryQuantFixSeq() : FastFeatureNoPrefix(FeatureType::USER) {}
ExtractSearchQueryQuantFixSeq::ExtractSearchQueryQuantFixSeq(size_t seq_index)
    : FastFeatureNoPrefix(FeatureType::USER) {
  seq_index_ = seq_index;
}
void ExtractSearchQueryQuantFixSeq::Extract(const AdLog& adlog, size_t pos,
                                            std::vector<ExtractResult>* result) {
  auto x1 =
      get_adlog_str(adlog.context().info_common_attr(), ContextInfoCommonAttr::SEARCH_QUERY_QUANTIZE_ID);
  auto x2 = get_ad_search_quant_seq(x1);
  auto x4 = get_nth_uint64(x2, seq_index_);
  add_feature_result(x4, result);
}

}  // namespace ad_algorithm
}  // namespace ks
