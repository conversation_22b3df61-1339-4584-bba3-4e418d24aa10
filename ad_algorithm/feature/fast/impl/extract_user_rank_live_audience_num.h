#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_combine_frrank_uescore_rank.dark
class ExtractUserRankLiveAudienceNum : public FastFeatureNoPrefix {
 public:
  ExtractUserRankLiveAudienceNum();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserRankLiveAudienceNum);
};

REGISTER_EXTRACTOR(ExtractUserRankLiveAudienceNum);
}  // namespace ad_algorithm
}  // namespace ks
