/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_gmv_list.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/realtime_ecom_action.h"

namespace ks {
namespace ad_algorithm {

ExtractURealTimeAdxClickGmvList::ExtractURealTimeAdxClickGmvList() : FastFeatureNoPrefix(FeatureType::USER) {}
void ExtractURealTimeAdxClickGmvList::Extract(const AdLog& adlog, size_t pos,
                                              std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 67129);
  auto x2 = get_segment_cnt_log_int(x1);
  add_feature_result(x2, result);
}

}  // namespace ad_algorithm
}  // namespace ks
