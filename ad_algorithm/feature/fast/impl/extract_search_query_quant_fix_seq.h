#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_search_query_quant_seq.dark
class ExtractSearchQueryQuantFixSeq : public FastFeatureNoPrefix {
 public:
  ExtractSearchQueryQuantFixSeq();
  explicit ExtractSearchQueryQuantFixSeq(size_t index);
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  size_t seq_index_ = 0;

  DISALLOW_COPY_AND_ASSIGN(ExtractSearchQueryQuantFixSeq);
};

REGISTER_SEQUENCE_EXTRACTOR(ExtractSearchQueryQuantFixSeq, 3);
}  // namespace ad_algorithm
}  // namespace ks
