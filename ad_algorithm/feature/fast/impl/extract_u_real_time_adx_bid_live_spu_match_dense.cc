/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_live_spu_match_dense.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/realtime_ecom_action.h"

namespace ks {
namespace ad_algorithm {

ExtractURealTimeAdxBidLiveSpuMatchDense::ExtractURealTimeAdxBidLiveSpuMatchDense()
    : FastFeature(FeatureType::DENSE_COMBINE) {}
void ExtractURealTimeAdxBidLiveSpuMatchDense::Extract(const AdLog& adlog, size_t pos,
                                                      std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 67081);
  auto x2 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 67084);
  auto x3 = get_adlog_time(adlog);
  auto x4 = get_adlog_int64(adlog.item(pos).ad_dsp_info().live_info().common_info_attr(),
                            CommonInfoAttr::LIVE_REALTIME_EXPLAINING_SPU);
  auto x5 = get_adlog_int64(adlog.item(pos).ad_dsp_info().live_info().common_info_attr(),
                            CommonInfoAttr::LIVE_REALTIME_YELLOWTROLLEY_SPU1);
  auto x6 = get_adlog_int64(adlog.item(pos).ad_dsp_info().live_info().common_info_attr(),
                            CommonInfoAttr::LIVE_REALTIME_YELLOWTROLLEY_SPU2);
  auto x9 = get_time_segment_cnt_log_value_v2(x1, x2, x3, x4, x5, x6, 8, 200);
  add_feature_result(x9, 8, result);
}

}  // namespace ad_algorithm
}  // namespace ks
