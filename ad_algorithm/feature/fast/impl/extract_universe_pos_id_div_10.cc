/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_id_div_10.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/uv_action_seq_util.h"

namespace ks {
namespace ad_algorithm {

ExtractUniversePosIdDiv10::ExtractUniversePosIdDiv10() : FastFeatureNoPrefix(FeatureType::USER) {}
void ExtractUniversePosIdDiv10::Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64(adlog.context().pos_id());
  auto x3 = div_sparse_id_op(x1, 1);
  add_feature_result(x3, result);
}

}  // namespace ad_algorithm
}  // namespace ks
