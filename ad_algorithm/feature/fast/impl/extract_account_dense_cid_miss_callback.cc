/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_account_dense_cid_miss_callback.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {

ExtractAccountDenseCidMissCallback::ExtractAccountDenseCidMissCallback()
    : FastFeature(FeatureType::DENSE_ITEM) {}
void ExtractAccountDenseCidMissCallback::Extract(const AdLog& adlog, size_t pos,
                                                 std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_float(adlog.item(pos).ad_dsp_info().common_info_attr(), 5104904);
  add_feature_result(x1, 1, result);
}

}  // namespace ad_algorithm
}  // namespace ks
