/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_last_pv_lose_other_max_score_no_prefix.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {

ExtractContextDenseLastPvLoseOtherMaxScoreNoPrefix::ExtractContextDenseLastPvLoseOtherMaxScoreNoPrefix()
    : FastFeature(FeatureType::DENSE_USER) {}
void ExtractContextDenseLastPvLoseOtherMaxScoreNoPrefix::Extract(const AdLog& adlog, size_t pos,
                                                                 std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_float(adlog.context().info_common_attr(),
                            ContextInfoCommonAttr::MIXRANK_LAST_PV_LOSE_OTHER_MAX_SCORE);
  add_feature_result(x1, 1, result);
}

}  // namespace ad_algorithm
}  // namespace ks
