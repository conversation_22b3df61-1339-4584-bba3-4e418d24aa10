#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_context_dense_last_pv_features.dark
class ExtractContextDenseLastPvAdMaxScoreNoPrefix : public FastFeature {
 public:
  ExtractContextDenseLastPvAdMaxScoreNoPrefix();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractContextDenseLastPvAdMaxScoreNoPrefix);
};

REGISTER_EXTRACTOR(ExtractContextDenseLastPvAdMaxScoreNoPrefix);
}  // namespace ad_algorithm
}  // namespace ks
