#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_combine_mix_item_set_ids.dark
class ExtractCombineMixItemSetIds : public FastFeatureNoPrefix {
 public:
  ExtractCombineMixItemSetIds();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractCombineMixItemSetIds);
};

REGISTER_EXTRACTOR(ExtractCombineMixItemSetIds);
}  // namespace ad_algorithm
}  // namespace ks
