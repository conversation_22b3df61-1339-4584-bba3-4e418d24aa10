#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature:
// teams/ad/ad_algorithm/dark_feature/impl/extract_u_realtime_adx_impression_click_bid_match_dense.dark
class ExtractURealTimeClickSpuLevelMatchDense : public FastFeature {
 public:
  ExtractURealTimeClickSpuLevelMatchDense();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractURealTimeClickSpuLevelMatchDense);
};

REGISTER_EXTRACTOR(ExtractURealTimeClickSpuLevelMatchDense);
}  // namespace ad_algorithm
}  // namespace ks
