/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_pcmtr_no_prefix.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/rank_context_info.h"

namespace ks {
namespace ad_algorithm {

ExtractCombineRankIndexUescorePcmtrNoPrefix::ExtractCombineRankIndexUescorePcmtrNoPrefix()
    : FastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {}
void ExtractCombineRankIndexUescorePcmtrNoPrefix::Extract(const AdLog& adlog, size_t pos,
                                                          std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_map_int64_float(adlog.context().info_common_attr(),
                                      ContextInfoCommonAttr::FRRANK_UESCORE_PCMTR_LIST);
  auto x2 = get_adlog_int64(adlog.item(pos).ad_dsp_info().photo_info().id());
  auto x3 = get_rank_index_from_map(x1, x2);
  add_feature_result(x3, result);
}

}  // namespace ad_algorithm
}  // namespace ks
