#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature:
// teams/ad/ad_algorithm/dark_feature/impl/extract_context_chuangxin_author_industry_feature.dark
class ExtractAuthorIdPhotoPlayedDurationMs6 : public FastFeatureNoPrefix {
 public:
  ExtractAuthorIdPhotoPlayedDurationMs6();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractAuthorIdPhotoPlayedDurationMs6);
};

REGISTER_EXTRACTOR(ExtractAuthorIdPhotoPlayedDurationMs6);
}  // namespace ad_algorithm
}  // namespace ks
