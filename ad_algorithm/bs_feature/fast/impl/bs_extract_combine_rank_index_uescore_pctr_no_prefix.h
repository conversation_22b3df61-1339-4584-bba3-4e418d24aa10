#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_combine_frrank_uescore_rank.dark
class BSExtractCombineRankIndexUescorePctrNoPrefix : public BSFastFeatureNoPrefix {
 public:
  BSExtractCombineRankIndexUescorePctrNoPrefix();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractCombineRankIndexUescorePctrNoPrefix);
};

REGISTER_BS_EXTRACTOR(BSExtractCombineRankIndexUescorePctrNoPrefix);
}  // namespace ad_algorithm
}  // namespace ks
