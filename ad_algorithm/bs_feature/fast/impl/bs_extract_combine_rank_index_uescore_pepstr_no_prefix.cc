/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_rank_index_uescore_pepstr_no_prefix.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/rank_context_info.h"

namespace ks {
namespace ad_algorithm {

BSExtractCombineRankIndexUescorePepstrNoPrefix::BSExtractCombineRankIndexUescorePepstrNoPrefix()
    : BSFastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_1211_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_1211_value);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_id);
}
void BSExtractCombineRankIndexUescorePepstrNoPrefix::Extract(const BSLog& bslog, size_t pos,
                                                             std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_map_int64_float<true>(*bs, BSFieldEnum::adlog_context_info_common_attr_key_1211_key,
                                            BSFieldEnum::adlog_context_info_common_attr_key_1211_value, pos);
  auto x2 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_id, pos);
  auto x3 = get_rank_index_from_map(x1, x2);
  add_feature_result(x3, result);
}

}  // namespace ad_algorithm
}  // namespace ks
