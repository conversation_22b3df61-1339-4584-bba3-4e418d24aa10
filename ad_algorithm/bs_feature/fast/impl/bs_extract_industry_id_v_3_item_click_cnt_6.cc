/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_industry_id_v_3_item_click_cnt_6.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

BSExtractIndustryIdV3ItemClickCnt6::BSExtractIndustryIdV3ItemClickCnt6()
    : BSFastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_1487_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_1487_value);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3);
}
void BSExtractIndustryIdV3ItemClickCnt6::Extract(const BSLog& bslog, size_t pos,
                                                 std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_map_int64_int64<true>(*bs, BSFieldEnum::adlog_context_info_common_attr_key_1487_key,
                                            BSFieldEnum::adlog_context_info_common_attr_key_1487_value, pos);
  auto x2 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3, pos);
  auto x3 = get_value_from_map(x1, x2);
  auto x5 = value_or(x3, 0);
  auto x6 = cast_to_int64(x5);
  add_feature_result(x6, result);
}

}  // namespace ad_algorithm
}  // namespace ks
