#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature:
// teams/ad/ad_algorithm/dark_feature/impl/extract_context_chuangxin_author_industry_feature.dark
class BSExtractIndustryIdV3CoverShowCnt6 : public BSFastFeatureNoPrefix {
 public:
  BSExtractIndustryIdV3CoverShowCnt6();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractIndustryIdV3CoverShowCnt6);
};

REGISTER_BS_EXTRACTOR(BSExtractIndustryIdV3CoverShowCnt6);
}  // namespace ad_algorithm
}  // namespace ks
