#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_lsp_item_fea.dark
class BSExtractLspLatestLiveSegmentMmuEmbV1 : public BSFastFeature {
 public:
  BSExtractLspLatestLiveSegmentMmuEmbV1();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractLspLatestLiveSegmentMmuEmbV1);
};

REGISTER_BS_EXTRACTOR(BSExtractLspLatestLiveSegmentMmuEmbV1);
}  // namespace ad_algorithm
}  // namespace ks
