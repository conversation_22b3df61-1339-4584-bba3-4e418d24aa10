/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_outerprm_cvr_top_layer_embedding.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {

BSExtractItemDenseOuterPrmCvrTopLayerEmbedding::BSExtractItemDenseOuterPrmCvrTopLayerEmbedding()
    : BSFastFeature(FeatureType::DENSE_COMBINE) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_item_common_attr_key_1524);
}
void BSExtractItemDenseOuterPrmCvrTopLayerEmbedding::Extract(const BSLog& bslog, size_t pos,
                                                  std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_float_list(
      *bs, BSFieldEnum::adlog_item_item_common_attr_key_1524, pos);
  add_feature_result(x1, 256, result);
}

}  // namespace ad_algorithm
}  // namespace ks
