#pragma once

#include <cstdio>
#include <string>
#include <functional>
#include <vector>
#include <iostream>
#include <unordered_map>
#include "base/file/file_util.h"
#include "base/strings/string_printf.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_nn/service/kconf_util.h"
#include "teams/ad/ad_algorithm/feature_interface/fast_feature_interface.h"

namespace ks {
namespace ad_algorithm {

class DupFeatureNames {
 public:
  static DupFeatureNames& Instance() {
    static DupFeatureNames dup_feature_names;
    return dup_feature_names;
  }

  void add_feature_name(const std::string& feature_name) { feature_names_.emplace_back(feature_name); }
  const std::vector<std::string>& feature_names() const { return feature_names_; }

 private:
  DupFeatureNames() = default;
  std::vector<std::string> feature_names_;
};

template <typename T, typename NameValueT>
using CommonAttrFeatureCreatorTmpl = std::function<T* (FeatureType, NameValueT, FeaturePrefix)>;
template <typename NameValueT>
using NameValueParseFunc = std::function<bool (const std::string&, NameValueT* name)>;
// CommonInfoAttr feature 脚本化 feature 管理相关逻辑
// 假定如下:
// 1. 每个基础实现 feature 通过 attr 的类型和操作两个维度来注册
// 2. 每个基础 feature 实现可以通过不同的参数实例化为具体的 feature，
//    实例化后的 feature 可以配置到 feature file 中使用
// 3. 每个基础 feature 的必选参数为 feature_type, attr_name, prefix，可以指定其他必选参数
// 4. 基础 feature 实例化需要的信息统一配置在一个配置文件中，Factory 通过读取配置文件实例化具体 feature
template <typename T, typename NameValueT>
class CommonInfoAttrFeatureFactoryTmpl {
 public:
  static CommonInfoAttrFeatureFactoryTmpl<T, NameValueT> &Instance() {
    static CommonInfoAttrFeatureFactoryTmpl<T, NameValueT> factory;
    return factory;
  }
  ~CommonInfoAttrFeatureFactoryTmpl() {}

  // 根据配置文件初始化 feature 实例化逻辑
  bool Init();

  // 返回实例化的 feature，供 feature file 解析逻辑使用
  T* Create(const std::string &name) {
    const auto &iter = features_.find(name);
    if (iter == features_.end()) {
      LOG(WARNING) << "feature " << name << " not found in common info features";
      return nullptr;
    }

    LOG(INFO) << "feature " << name << " found in common info features";
    return iter->second;
  }

  // meta feature 注册管理相关逻辑
  std::string GetFeatureName(const std::string &attr_type, const std::string &op) {
    return attr_type + "_" + op;
  }
  void RegisterMetaFeature(const std::string &attr_type, const std::string &op,
                           CommonAttrFeatureCreatorTmpl<T, NameValueT> creator) {
    auto name = GetFeatureName(attr_type, op);

    const auto &iter = meta_features_.find(name);
    if (iter != meta_features_.end()) {
      DupFeatureNames::Instance().add_feature_name(name);
      return;
    }

    meta_features_[name] = creator;
    // std::cerr << "meta feature " << name << " registered in common info meta features" << std::endl;
  }
  CommonAttrFeatureCreatorTmpl<T, NameValueT>
  GetMetaFeature(const std::string &attr_type, const std::string &op) {
    auto name = GetFeatureName(attr_type, op);
    const auto &iter = meta_features_.find(name);
    if (iter == meta_features_.end()) {
      LOG(WARNING) << "meta feature " << name << " not found in common info meta features";
      return nullptr;
    }
    return iter->second;
  }
  template <typename CLS>
  class Register {
   public:
    Register(const std::string &attr_type, const std::string &op) {
      auto creator = [](FeatureType type, NameValueT attr_name, FeaturePrefix prefix) {
        return new CLS(type, attr_name, prefix);
      };
      CommonInfoAttrFeatureFactoryTmpl<T, NameValueT>::Instance().
        RegisterMetaFeature(attr_type, op, creator);
    }
  };

  void SetNamePrefix(const std::string& prefix) { name_prefix_ = prefix; }
  void SetNameParseFunc(NameValueParseFunc<NameValueT> name_parse_func) {
    name_parse_func_ = name_parse_func;
  }

  const std::unordered_map<std::string, T*>& features() const { return features_; }

 private:
  CommonInfoAttrFeatureFactoryTmpl() {
  }

 private:
  std::unordered_map<std::string, CommonAttrFeatureCreatorTmpl<T, NameValueT>> meta_features_;
  std::unordered_map<std::string, T*> features_;
  NameValueT attr_name_;
  NameValueParseFunc<NameValueT> name_parse_func_ = nullptr;
  std::string name_prefix_ = "";
  FeaturePrefix feature_prefix_;
  bool is_item_feature = false;

  DISALLOW_COPY_AND_ASSIGN(CommonInfoAttrFeatureFactoryTmpl);
};

template <typename T, typename NameValueT>
bool CommonInfoAttrFeatureFactoryTmpl<T, NameValueT>::Init() {
  if (name_parse_func_ == nullptr) {
    LOG(FATAL) << "name_parse_func is a nullptr";
    return false;
  }

  auto config = ks::ad_nn::PredictServiceKconfUtil::adPredictFeatureInstanceList()->data;
  auto features = config->Get("features");
  if (features == nullptr || !features->IsArray()) {
    LOG(FATAL) << "no 'features' json array found in config";
    return false;
  }

  for (auto conf : features->array()) {
    auto attr_type = conf->GetString("attr_type", "");
    auto attr_name_str = conf->GetString("attr_name", "");
    auto op = conf->GetString("op", "");
    auto feature_name = conf->GetString("feature_name", "");
    if (!name_prefix_.empty()) {
      feature_name = base::StringPrintf("%s%s", name_prefix_.c_str(), feature_name.c_str());
    }
    auto feature_type_str = conf->GetString("feature_type", "");
    auto feature_type = GetFeatureTypeFromName(feature_type_str);
    auto feature_prefix = conf->GetInt("feature_prefix", 0);
    NameValueT attr_name;
    if (attr_type.empty() || attr_name_str.empty() || op.empty() || feature_name.empty()) {
      LOG(ERROR) << "feature: " << feature_name << " invalid empty attr_type/attr_name/op/feature_name found";
      continue;
    }
    if (!name_parse_func_(attr_name_str, &attr_name)) {
      LOG(ERROR) << "feature: " << feature_name << " invalid attr name " << attr_name_str << " found";
      continue;
    }

    if (feature_type == FeatureType::UNKNOWN) {
      LOG(ERROR) << "feature: " << feature_name << " invalid feature type " << feature_type_str << " found";
      continue;
    }
    if (features_.count(feature_name) > 0) {
      LOG(ERROR) << "duplicate feature name " << feature_name << " found, only first will be kept";
      continue;
    }
    auto create_func = GetMetaFeature(attr_type, op);
    if (!create_func) {
      LOG(ERROR) << "no meta feature found for attr type " << attr_type << " and op " << op
                   << " feature name: " << feature_name;
      continue;
    }
    auto feature = create_func(feature_type, attr_name, static_cast<FeaturePrefix>(feature_prefix));
    if (!feature->Init(*conf)) {
      LOG(ERROR) << "common attr feature " << feature_name << " init failed";
      continue;
    }
    features_[feature_name] = feature;

    LOG(INFO) << "common info attr feature " << feature_name << " register succeeds";
  }

  const auto& dup_feature_names = DupFeatureNames::Instance().feature_names();
  if (dup_feature_names.size() > 0) {
    LOG(FATAL) << "find duplicate feature_names: " << absl::StrJoin(dup_feature_names, " , ");
    return false;
  }

  LOG(INFO) << "common info attr factory init succeeds";
  return true;
}

}  // namespace ad_algorithm
}  // namespace ks
