using_item_filter("InnerQcpxPhotoCouponAmountSampleAllAddpageFilter",
    "adlog.context.sub_page_id",
    "adlog.item.type",
    "adlog.item.ad_dsp_info.campaign.base.type",
    "adlog.item.ad_dsp_info.creative.base.live_creative_type",
    "adlog.item.ad_dsp_info.unit.base.ocpc_action_type:int64",
    "adlog.item.rank_params",
    1108,
    get_value_from_map,
    cast_to_float,
    "adlog.item.rank_params",
    1107,
    get_value_from_map,
    cast_to_float,
    "adlog.item.rank_params",
    1224,
    get_value_from_map,
    cast_to_float,
    "adlog.item.rank_params",
    1225,
    get_value_from_map,
    cast_to_float,
    "adlog.item.rank_params",
    1127,
    get_value_from_map,
    cast_to_int64,
    inner_qcpx_photo_coupon_amount_sample_all_addpage_filter
);