using_label_extractor("ExtractMtlMixBonusCutFixLabelExtractorNew",
                      "adlog.item.label_info.item_impression:bool",
                      "adlog.item.label_info.live_played_started:bool",
                      "adlog.item.label_info.standard_live_played_started:bool",
                      "adlog.item.label_info.label_info_attr.key:1055:float",
                      mtl_mix_bonus_cut_fix_label_extractor_new);

using_label_extractor("ExtractMtlMixBonusCutFixLabelExtractorV2",
                      "adlog.item.label_info.item_impression:bool",
                      "adlog.item.label_info.live_played_started:bool",
                      "adlog.item.label_info.standard_live_played_started:bool",
                      "adlog.item.label_info.label_info_attr.key:1055:float",
                      "adlog.item.cpm:int64",
                      mtl_mix_bonus_cut_fix_label_extractor_v2);