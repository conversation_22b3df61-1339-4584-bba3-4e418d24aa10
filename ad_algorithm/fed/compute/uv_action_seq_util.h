#pragma once

#include <sys/types.h>
#include <cstdint>
#include <string_view>
#include <vector>
#include "absl/types/optional.h"
#include "absl/strings/string_view.h"

namespace ks {
namespace ad_algorithm {

// 截断行为序列到最大长度
// 参数:
// seq: 行为序列
// max_len: 序列最大保留长度
// 返回值： 截断后的行为序列
std::vector<int64_t> get_seq_with_max_len(
    absl::Span<const int64_t> seq,
    int max_len);

// hard search 输入某一维度行为序列和指定 key 得到 mask 向量
// 参数:
// seq: 行为序列
// key: hard search 的 key (int64_t 类型)
// max_len: 序列最大保留长度
// 返回值： float 类型 mask 向量
std::vector<float> hard_search_seq_with_single_key(
    absl::Span<const int64_t> seq,
    absl::optional<int64_t> key,
    int max_len);

// hard search 输入某一维度行为序列和指定 key 得到 mask 向量
// 参数:
// seq: 行为序列
// key: hard search 的 key (字符串类型)
// max_len: 序列最大保留长度
// 返回值： float 类型 mask 向量
std::vector<float> hard_search_seq_with_single_key(
    absl::Span<const int64_t> seq,
    absl::optional<absl::string_view> key,
    int max_len);

// hard search 输入某一维度行为序列和指定 key 得到 mask 向量
// 参数:
// seq: 行为序列
// key: hard search 的 key (字符串类型)
// max_len: 序列最大保留长度
// 返回值： float 类型 mask 向量
std::vector<float> hard_search_seq_with_string_single_key(
    absl::Span<const int64_t> seq,
    absl::optional<absl::string_view> key,
    int max_len);

// 序列最大填充合并
// 参数:
// seq1: 序列 1, 优先合并
// seq2: 序列 2
// seq1_max_len: seq1 最大保留长度
// max_len: 合并后序列最大长度
// 返回值: 合并后的序列
std::vector<int64_t> merge_two_seq_with_max_len(
    absl::Span<const int64_t> seq1,
    absl::Span<const int64_t> seq2,
    int seq1_max_len,
    int max_len);

// 序列指定长度合并
// 参数:
// seq1: 序列 1, 优先合并
// seq2: 序列 2
// seq1_max_len: seq1 最大保留长度
// seq2_max_len: seq2 最大保留长度
// 返回值: 合并后的序列
std::vector<int64_t> merge_two_seq_with_two_max_len(
    absl::Span<const int64_t> seq1,
    absl::Span<const int64_t> seq2,
    int seq1_max_len,
    int seq2_max_len);

// 联盟实时序列提取
// 参数:
// seq: 用户行为 value 序列
// medium_id_seq: 用户行为 medium 序列, 用于区别联盟与非联盟
// timestamp_seq: 用户行为时间戳序列, 单位毫秒
// max_len: 返回序列最大保留长度
// current_timestamp: 日志当前时间戳, 单位毫秒
// timestamp_delta: 时间戳间隔
// 返回值: 满足 为联盟, 且距离日志当前时间戳间隔小于 timestamp_delta 的 seq 序列
std::vector<int64_t> get_universe_seq_with_max_len_and_timestamp(
    absl::Span<const int64_t> seq,
    absl::Span<const int64_t> medium_id_seq,
    absl::Span<const int64_t> timestamp_seq,
    int max_len,
    int64_t current_timestamp,
    int64_t timestamp_delta);

// 联盟实时 hash 序列提取
// 参数:
// seq: 用户行为 value 序列
// medium_id_seq: 用户行为 medium 序列, 用于区别联盟与非联盟
// timestamp_seq: 用户行为时间戳序列, 单位毫秒
// max_len: 返回序列最大保留长度
// current_timestamp: 日志当前时间戳, 单位毫秒
// timestamp_delta: 时间戳间隔
// 返回值: 满足 为联盟, 且距离日志当前时间戳间隔小于 timestamp_delta 的 seq (hash 后) 序列
std::vector<int64_t> get_universe_hash_seq_with_max_len_and_timestamp(
    absl::Span<const int64_t> seq,
    absl::Span<const int64_t> medium_id_seq,
    absl::Span<const int64_t> timestamp_seq,
    int max_len,
    int64_t current_timestamp,
    int64_t timestamp_delta);

// 联盟实时 hour 序列提取
// 参数:
// medium_id_seq: 用户行为 medium 序列, 用于区别联盟与非联盟
// timestamp_seq: 用户行为时间戳序列, 单位毫秒
// max_len: 返回序列最大保留长度
// current_timestamp: 日志当前时间戳, 单位毫秒
// timestamp_delta: 时间戳间隔
// 返回值: 满足 为联盟, 且距离日志当前时间戳间隔小于 timestamp_delta 的小时序列
std::vector<int64_t> get_universe_hour_seq_with_max_len_and_timestamp(
    absl::Span<const int64_t> medium_id_seq,
    absl::Span<const int64_t> timestamp_seq,
    int max_len,
    int64_t current_timestamp,
    int64_t timestamp_delta);

// 联盟实时 date-diff 序列提取
// 参数:
// medium_id_seq: 用户行为 medium 序列, 用于区别联盟与非联盟
// timestamp_seq: 用户行为时间戳序列, 单位毫秒
// max_len: 返回序列最大保留长度
// current_timestamp: 日志当前时间戳, 单位毫秒
// timestamp_delta: 时间戳间隔
// 返回值: 满足 为联盟, 且距离日志当前时间戳间隔小于 timestamp_delta 的日期间隔序列
std::vector<int64_t> get_universe_date_diff_seq_with_max_len_and_timestamp(
    absl::Span<const int64_t> medium_id_seq,
    absl::Span<const int64_t> timestamp_seq,
    int max_len,
    int64_t current_timestamp,
    int64_t timestamp_delta);

// 联盟实时 log2 minute-diff 序列提取
// 参数:
// medium_id_seq: 用户行为 medium 序列, 用于区别联盟与非联盟
// timestamp_seq: 用户行为时间戳序列, 单位毫秒
// max_len: 返回序列最大保留长度
// current_timestamp: 日志当前时间戳, 单位毫秒
// timestamp_delta: 时间戳间隔
// 返回值: 满足 为联盟, 且距离日志当前时间戳间隔小于 timestamp_delta, 分钟级别的 log2 时间间隔序列
std::vector<int64_t> get_universe_log2_min_diff_seq_with_max_len_and_timestamp(
    absl::Span<const int64_t> medium_id_seq,
    absl::Span<const int64_t> timestamp_seq,
    int max_len,
    int64_t current_timestamp,
    int64_t timestamp_delta);

// 转换 date-diff 序列为 log2 minute-diff 序列
// 参数:
// date_diff_seq: 用户行为日期间隔序列
// 返回值: 分钟级别的 log2 时间间隔序列
std::vector<int64_t> convert_date_diff_to_log2_min_diff(
    absl::Span<const int64_t> date_diff_seq);

// 聚合 3 种联盟实验桶 tag
// 参数:
// exp1: 实验组 1
// exp2: 实验组 2
// exp3: 实验组 3
// 返回值: 聚合后的 tag 列表
std::vector<float> merge_universe_3_exp_tags(
    absl::optional<int64_t> universe_exp1,
    absl::optional<int64_t> universe_exp2,
    absl::optional<int64_t> universe_exp3);

// 转化数字字符串为 int64_t 类型
// 参数:
// exp1: 待转化的数字字符串
// 返回值: int64_t 类型整数
int64_t universe_cast_string_to_int64(
    absl::optional<absl::string_view> string_view_int_value
);

// 设置输入默认值为 float 1.0
// 参数：
// factor: 样本权重
// 返回值: float 类型浮点数
float sample_weight_default(
    absl::optional<float> factor
);

// 处理行为语义 id 序列 (默认为 3 级层次编码), 包括 前缀编码 长度对齐 等操作
// 参数:
// action_semantic_id_list: 原始行为语义 id 序列
// action_num: 行为数量
// 返回值: 经过 前缀编码 长度对齐 等操作之后的序列
std::vector<int64_t> process_action_semantic_id_list(
    absl::Span<const int64_t> action_semantic_id_list,
    absl::optional<int64_t> action_num);

// 将 string 形式的 int64 类型列表按分割符分割
// 参数:
// string_style_list: string 形式的 int64 类型列表
// list_len: 列表长度
// segment: 分割符
// 返回值： int64 类型的列表
std::vector<int64_t> uv_split_string_int64_list_2_int64(
    absl::optional<absl::string_view> string_style_list,
    absl::optional<int64_t> list_len,
    int segment_dot_num);

// 将 string 形式的 string 类型列表按分割符分割, 并 hash 到 id
// 参数:
// string_style_list: string 形式的 string 类型列表
// list_len: 列表长度
// segment: 分割符
// 返回值： int64 类型的列表
std::vector<int64_t> uv_split_string_string_list_2_int64(
    absl::optional<absl::string_view> string_style_list,
    absl::optional<int64_t> list_len,
    int segment_dot_num);

// 对 sparse_id 进行整除, 用于去掉 pos_id 低位等场景
// 参数:
// sparse_id: 输入 int64 类型 sparse_id
// low_bit_num: 去掉的低位数量
// 返回值: 去掉低位后的 sparse_id
int64_t div_sparse_id_op(
    absl::optional<int64_t> sparse_id,
    int low_bit_num);
}   // namespace ad_algorithm
}  // namespace ks
