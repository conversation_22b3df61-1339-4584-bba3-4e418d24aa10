#include "teams/ad/ad_algorithm/fed/compute/item_filter/search_data_subpageid_filter.h"

#include "teams/ad/ad_base/src/math/random/random.h"
#include "absl/types/optional.h"

namespace ks {
namespace ad_algorithm {

bool search_data_subpageid_filter(int64_t sub_page_id) {
  if (sub_page_id == 10014001 || sub_page_id == 11014001 || \
  sub_page_id == 100018489 || sub_page_id == 1000184890) {
    return true;
  }
  return false;
}

}  // namespace ad_algorithm
}  // namespace ks
