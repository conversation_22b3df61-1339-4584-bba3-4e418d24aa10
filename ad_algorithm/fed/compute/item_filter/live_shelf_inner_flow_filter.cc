#include "teams/ad/ad_algorithm/fed/compute/item_filter/live_shelf_inner_flow_filter.h"

namespace ks {
namespace ad_algorithm {

bool live_shelf_inner_flow_filter(absl::optional<int64_t> pos_id) {
  std::unordered_set<int64_t> mall_buyer_home_pos_id_set{386, 387, 23929, 23930};
  kuaishou::ad::AdLog ad_log;
  std::string enter_action = ad_log.client_ad_log().universe_client_ad_log().enter_action();
  if (pos_id.has_value() && !enter_action.empty()) {
    if (mall_buyer_home_pos_id_set.find(pos_id.value()) != mall_buyer_home_pos_id_set.end() &&
        (enter_action.compare("2") == 0 || enter_action.compare("3") == 0)) {
      return false;
    }
  }
  return true;
}

}  // namespace ad_algorithm
}  // namespace ks
