#include "teams/ad/ad_algorithm/fed/compute/item_filter/inner_qcpx_photo_coupon_amount_sample_all_addpage_filter.h"
#include "base/strings/string_split.h"
#include "absl/strings/numbers.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/ad_base/src/math/random/random.h"
namespace ks {
namespace ad_algorithm {
bool inner_qcpx_photo_coupon_amount_sample_all_addpage_filter(int64_t sub_page_id,
                                    int64_t item_type,
                                    int64_t campaign_type,
                                    int64_t live_creative_type,
                                    int64_t ocpc_action_type,
                                    double coupon_amt,
                                    double coupon_threshold,
                                    double discount_rate,
                                    double discount_cap,
                                    int64_t rct_tag) {
  // 场景
  bool check_sub_page = false;
  // 新增单列关注页 100013100 和 100016771
  if (sub_page_id == 11001001 || sub_page_id == 10011001 || sub_page_id == 10002001
    || sub_page_id == 100012194 || sub_page_id == 100022411 || sub_page_id == 10008001
    || sub_page_id == 100013100 || sub_page_id == 100016771) {
    check_sub_page = true;
  }

  // 广告队列
  bool check_ad_type = false;
  if (item_type == 1 ||
      item_type == 17) {
    check_ad_type = true;
  }

  // 物料类型
  bool check_item = false;
  if (campaign_type == 13) {
    check_item = true;
  }

  // 优化目标
  bool check_ocpx_type = false;
  if (ocpc_action_type == 395 ||
    ocpc_action_type == 944 ||
    ocpc_action_type == 192 ) {
    check_ocpx_type = true;
  }

  // 满减券信息
  bool check_coupon = false;
  if ((coupon_amt > 0 && coupon_threshold > 0)) {
    check_coupon = true;
  }

  // 模型发券 & 随机发券
  // 20: 满减券随机发券；32: 满减 & 折扣策略发券；21: 折扣随机发券
  bool check_mulamt_coupon = false;
  if (rct_tag == 20 || rct_tag == 32) {
    check_mulamt_coupon = true;
  }

  // 随机不发券流量
  bool check_random_no_coupon = false;
  // 10 & 11: 随机不发券
  if (rct_tag == 10 || rct_tag == 11) {
    // 随机不发券流量
    check_random_no_coupon = true;
  }

  // 判断策略不发券流量
  bool check_model_no_coupon = false;
  // 31: 策略不发券
  if (rct_tag == 31) {
    check_model_no_coupon = true;
  }

  return check_sub_page && check_ad_type && check_item &&
    check_ocpx_type && ((check_coupon && check_mulamt_coupon) ||
    check_random_no_coupon || check_model_no_coupon);
}
}  // namespace ad_algorithm
}  // namespace ks
