#include "teams/ad/ad_algorithm/fed/compute/item_filter/search_qcpx_coupon_amt_filter.h"
#include "base/strings/string_split.h"
#include "absl/strings/numbers.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
namespace ks {
namespace ad_algorithm {
bool search_qcpx_coupon_amt_filter(double coupon_amt,
                                   double coupon_threshold) {
  // 券信息
  bool check_coupon = false;
  if (coupon_amt > 0 && coupon_threshold > 0) {
    check_coupon = true;
  }
  return check_coupon;
}
}  // namespace ad_algorithm
}  // namespace ks
