#pragma once
#include <cstdint>
#include <string>
#include "absl/types/optional.h"
namespace ks {
namespace ad_algorithm {
// 用于 kaiworks 内循环短带样本筛选
// 功能: 筛选满减券所有样本 + 全部随机不发券 + 策略不发券
bool inner_qcpx_photo_coupon_amount_sample_all_addpage_filter(int64_t sub_page_id,
                                    int64_t item_type,
                                    int64_t campaign_type,
                                    int64_t live_creative_type,
                                    int64_t ocpc_action_type,
                                    double coupon_amt,
                                    double coupon_threshold,
                                    double discount_rate,
                                    double discount_cap,
                                    int64_t rct_tag);
}  // namespace ad_algorithm
}  // namespace ks
