#pragma once
#include <string>
#include "absl/types/optional.h"
namespace ks {
namespace ad_algorithm {
  int fanstop_play_with_clk_msg_label_extractor(
    int32_t item_type, int32_t ad_campaign_type, int64_t duration_ms_dsp, int64_t duration_ms_fanstop,
    bool is_negative, const absl::optional<bool>& AD_MERCHANT_FOLLOW,
    const absl::optional<bool>& AD_FANS_TOP_FOLLOW, const absl::optional<bool>& LIKE,
    const absl::optional<bool>& AD_PHOTO_COMMENT, const absl::optional<bool>& MESSAGE_SENT,
    const absl::optional<bool>& CLICK, const absl::optional<int64_t>& PLAYED_TIME);
}  // namespace ad_algorithm
}  // namespace ks
