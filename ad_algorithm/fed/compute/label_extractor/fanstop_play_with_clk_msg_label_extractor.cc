#include "teams/ad/ad_algorithm/fed/compute/label_extractor/fanstop_play_with_clk_msg_label_extractor.h"
#include "teams/ad/ad_algorithm/log_preprocess/ad_model_kconf_util.h"
namespace ks {
namespace ad_algorithm {
int fanstop_play_with_clk_msg_label_extractor(
    int32_t item_type, int32_t ad_campaign_type, int64_t duration_ms_dsp, int64_t duration_ms_fanstop,
    bool is_negative, const absl::optional<bool>& AD_MERCHANT_FOLLOW,
    const absl::optional<bool>& AD_FANS_TOP_FOLLOW, const absl::optional<bool>& LIKE,
    const absl::optional<bool>& AD_PHOTO_COMMENT, const absl::optional<bool>& MESSAGE_SENT,
    const absl::optional<bool>& CLICK, const absl::optional<int64_t>& PLAYED_TIME) {
  int label = 0;
  bool flash_sepecial_flag = false;
  bool play_label_flag = false;
  if (!is_negative) {
    // 专速推判断
    if (ad_campaign_type == 13 || ad_campaign_type == 14) {
      flash_sepecial_flag = true;
    }
    std::string duration_bucket = "";
    float duration_s = 0;
    int64_t played_time = PLAYED_TIME.value_or(0);
    if (duration_ms_dsp > 0) {
        duration_s = (float) (duration_ms_dsp);
    } else if (duration_ms_fanstop > 0) {
        duration_s = (float) (duration_ms_fanstop);
    }
    if (duration_s <= 14650) {
      duration_bucket = "0-14s";
    } else if (duration_s <= 22166) {
      duration_bucket = "14-22s";
    } else if (duration_s <= 31100) {
      duration_bucket = "22-31s";
    } else if (duration_s <= 41633) {
      duration_bucket = "31-41s";
    } else if (duration_s <= 57166) {
      duration_bucket = "41-57s";
    } else if (duration_s <= 77163) {
      duration_bucket = "57-77s";
    } else if (duration_s <= 120333) {
      duration_bucket = "77-120s";
    } else {
      duration_bucket = "120to";
    }
    const auto fanstop_watch_time_distribution = AdModelKconfUtil::FanstopWatchTimeDistribution();
    auto fanstop_watch_time_distribution_iter = fanstop_watch_time_distribution->find(duration_bucket);
    if (fanstop_watch_time_distribution_iter != fanstop_watch_time_distribution->end()) {
      float threshold = (float) (fanstop_watch_time_distribution_iter->second);
      if (played_time >= threshold) {
        play_label_flag = true;
      }
    }
    // 专速推
    if (flash_sepecial_flag) {
      label |= 1;
    }
    // 第二位为 p5s 的 label
    if (play_label_flag) {
      label |= 2;
    }
    //涨粉
    if (AD_MERCHANT_FOLLOW.value_or(0) || AD_FANS_TOP_FOLLOW.value_or(0)) {
      label |= 4;
    }
    //点赞
    if (LIKE.value_or(0)) {
      label |= 8;
    }
    //评论
    if (AD_PHOTO_COMMENT.value_or(0)) {
      label |= 16;
    }
    //消息
    if (MESSAGE_SENT.value_or(0)) {
      label |= 32;
    }
    if (CLICK.value_or(0)) {
      label |= 64;
    }
  }
  return label;
  }
}  // namespace ad_algorithm
}  // namespace ks
