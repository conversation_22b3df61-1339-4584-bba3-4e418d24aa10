#pragma once

#include <cstdint>
#include <optional>
#include <string>
#include <unordered_set>
#include <utility>

#include "absl/strings/string_view.h"
#include "absl/types/optional.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

/// inner_live_pay_fastemit_label_extractor 在 inner_live_pay_refund_label_extractor 的基础上，添加
/// fast emit 窗口的支付和退间 label
///
/// 根据 传入 label 的 true/false 给对应位置置 1
///
/// 参数:
/// - ad_campaign_type: int64_t, 区分直播场景
/// - mix_flag_val: bool, 区分前后链路
/// - ad_merchat_follow: bool, 电商曝光一定时间后的关注为涨粉
/// - ad_fans_top_follow: bool, 粉条服务端的关注事件
/// - goods_view: bool, 电商的商品浏览行为
/// - event_order_paied: bool, 订单付款成功
/// - order_paid_cnt: int64_t, 直接支付次数
/// - mix_front_order_paid_cnt: int64_t, 混合流中只存储前链路(5-20 分钟)的订单直接支付次数
/// - order_paid_cnt_v2: int64_t, ROAS_ORDER_PAID_CNT 多品归因支付次数
/// - mix_front_order_paid_cnt_v2: int64_t, MIX_FRONT_ ROAS_ORDER_PAID_CNT 混合流中只存储前链路(5-20
/// 分钟)的短视频订单直接支付次数
/// - live_like: bool, 直播间点赞
/// - live_shop_cart_click: bool, 直播购物车点击
/// - live_shop_link_jump: bool, 直播购物车链接跳转
/// - standard_live_play_started: bool, 标准直播开始播放
/// - live_play_started: bool, 进入直播间开始直播
/// - item_type: int64_t, item_type
/// - live_creative_type: int64_t, live_creative_type
/// - is_living: bool, is_living
/// - sub_page_id: int64_t, sub_page_id
/// - app_id: string_view, app_id
/// - receive_coupon: bool, 用户是否领券
/// - live_comment: bool, 直播评论
/// - order_imp: bool, 订单确认页页面曝光
/// - order_submit_click: bool, 订单提交按钮点击
/// - pos_id: int64_t, pos_id
/// - product_buy_click: bool, 立即购买按钮点击
/// - on_foreground: bool, 从广告切换至后台
/// - goods_view_content: bool, 商品详情页-页面曝光
/// - live_share: bool, 直播间分享
/// - live_reward: bool, 直播间打赏
/// - live_played_mean: bool, 直播播放时间到达统计平均数时间(当前 200s)
/// - event_order_refund_apply_success: bool, 退单申请成功
///
/// 返回值:
/// - label: int32_t。
int32_t inner_live_pay_fastemit_label_extractor(
    absl::optional<int64_t> ad_campaign_type, absl::optional<int64_t> mix_flag_val,
    absl::optional<bool> ad_merchat_follow, absl::optional<bool> ad_fans_top_follow,
    absl::optional<bool> goods_view, absl::optional<bool> event_order_paied,
    absl::optional<int64_t> order_paid_cnt, absl::optional<int64_t> mix_front_order_paid_cnt,
    absl::optional<int64_t> order_paid_cnt_v2, absl::optional<int64_t> mix_front_order_paid_cnt_v2,
    absl::optional<bool> live_like, absl::optional<bool> live_shop_cart_click,
    absl::optional<bool> live_shop_link_jump, absl::optional<bool> standard_live_play_started,
    absl::optional<bool> live_play_started, absl::optional<int64_t> item_type,
    absl::optional<int64_t> live_creative_type, absl::optional<bool> is_living,
    absl::optional<int64_t> sub_page_id, absl::optional<absl::string_view> app_id,
    absl::optional<bool> receive_coupon, absl::optional<bool> live_comment, absl::optional<bool> order_imp,
    absl::optional<bool> order_submit_click, absl::optional<int64_t> pos_id,
    absl::optional<bool> product_buy_click, absl::optional<bool> on_foreground,
    absl::optional<bool> goods_view_content, absl::optional<bool> live_share,
    absl::optional<int64_t> live_reward_cnt, absl::optional<bool> live_played_mean,
    absl::optional<bool> event_order_refund_apply_success);

/// inner_live_pay_fastemit_label_extractor2
///
/// 根据 传入 label 的 true/false 给对应位置置 1, 新增 live_played_mean
///
/// 参数:
/// - ad_campaign_type: int64_t, 区分直播场景
/// - mix_flag_val: bool, 区分前后链路
/// - ad_merchat_follow: bool, 电商曝光一定时间后的关注为涨粉
/// - ad_fans_top_follow: bool, 粉条服务端的关注事件
/// - goods_view: bool, 电商的商品浏览行为
/// - event_order_paied: bool, 订单付款成功
/// - order_paid_cnt: int64_t, 直接支付次数
/// - mix_front_order_paid_cnt: int64_t, 混合流中只存储前链路(5-20 分钟)的订单直接支付次数
/// - order_paid_cnt_v2: int64_t, ROAS_ORDER_PAID_CNT 多品归因支付次数
/// - mix_front_order_paid_cnt_v2: int64_t, MIX_FRONT_ ROAS_ORDER_PAID_CNT 混合流中只存储前链路(5-20
/// 分钟)的短视频订单直接支付次数
/// - live_like: bool, 直播间点赞
/// - live_shop_cart_click: bool, 直播购物车点击
/// - live_shop_link_jump: bool, 直播购物车链接跳转
/// - standard_live_play_started: bool, 标准直播开始播放
/// - live_play_started: bool, 进入直播间开始直播
/// - item_type: int64_t, item_type
/// - live_creative_type: int64_t, live_creative_type
/// - is_living: bool, is_living
/// - sub_page_id: int64_t, sub_page_id
/// - app_id: string_view, app_id
/// - receive_coupon: bool, 用户是否领券
/// - live_comment: bool, 直播评论
/// - order_imp: bool, 订单确认页页面曝光
/// - order_submit_click: bool, 订单提交按钮点击
/// - pos_id: int64_t, pos_id
/// - product_buy_click: bool, 立即购买按钮点击
/// - on_foreground: bool, 从广告切换至后台
/// - goods_view_content: bool, 商品详情页-页面曝光
/// - live_share: bool, 直播间分享
/// - live_reward: bool, 直播间打赏
/// - live_played_mean: bool, 直播播放时间到达统计平均数时间(当前 200s)
///
/// 返回值:
/// - label: int32_t
int32_t inner_live_pay_fastemit_label_extractor2(
    absl::optional<int64_t> ad_campaign_type, absl::optional<int64_t> mix_flag_val,
    absl::optional<bool> ad_merchat_follow, absl::optional<bool> ad_fans_top_follow,
    absl::optional<bool> goods_view, absl::optional<bool> event_order_paied,
    absl::optional<int64_t> order_paid_cnt, absl::optional<int64_t> mix_front_order_paid_cnt,
    absl::optional<int64_t> order_paid_cnt_v2, absl::optional<int64_t> mix_front_order_paid_cnt_v2,
    absl::optional<bool> live_like, absl::optional<bool> live_shop_cart_click,
    absl::optional<bool> live_shop_link_jump, absl::optional<bool> standard_live_play_started,
    absl::optional<bool> live_play_started, absl::optional<int64_t> item_type,
    absl::optional<int64_t> live_creative_type, absl::optional<bool> is_living,
    absl::optional<int64_t> sub_page_id, absl::optional<absl::string_view> app_id,
    absl::optional<bool> receive_coupon, absl::optional<bool> live_comment, absl::optional<bool> order_imp,
    absl::optional<bool> order_submit_click, absl::optional<int64_t> pos_id,
    absl::optional<bool> product_buy_click, absl::optional<bool> on_foreground,
    absl::optional<bool> goods_view_content, absl::optional<bool> live_share,
    absl::optional<int64_t> live_reward_cnt, absl::optional<bool> live_played_mean);
}  // namespace ad_algorithm
}  // namespace ks
