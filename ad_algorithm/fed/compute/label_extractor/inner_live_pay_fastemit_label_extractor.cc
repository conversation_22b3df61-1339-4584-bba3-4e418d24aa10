#include "teams/ad/ad_algorithm/fed/compute/label_extractor/inner_live_pay_fastemit_label_extractor.h"

namespace ks {
namespace ad_algorithm {

int32_t inner_live_pay_fastemit_label_extractor(
    absl::optional<int64_t> ad_campaign_type, absl::optional<int64_t> mix_flag_val,
    absl::optional<bool> ad_merchat_follow, absl::optional<bool> ad_fans_top_follow,
    absl::optional<bool> goods_view, absl::optional<bool> event_order_paied,
    absl::optional<int64_t> order_paid_cnt, absl::optional<int64_t> mix_front_order_paid_cnt,
    absl::optional<int64_t> order_paid_cnt_v2, absl::optional<int64_t> mix_front_order_paid_cnt_v2,
    absl::optional<bool> live_like, absl::optional<bool> live_shop_cart_click,
    absl::optional<bool> live_shop_link_jump, absl::optional<bool> standard_live_play_started,
    absl::optional<bool> live_play_started, absl::optional<int64_t> item_type,
    absl::optional<int64_t> live_creative_type, absl::optional<bool> is_living,
    absl::optional<int64_t> sub_page_id, absl::optional<absl::string_view> app_id,
    absl::optional<bool> receive_coupon, absl::optional<bool> live_comment, absl::optional<bool> order_imp,
    absl::optional<bool> order_submit_click, absl::optional<int64_t> pos_id,
    absl::optional<bool> product_buy_click, absl::optional<bool> on_foreground,
    absl::optional<bool> goods_view_content, absl::optional<bool> live_share,
    absl::optional<int64_t> live_reward_cnt, absl::optional<bool> live_played_mean,
    absl::optional<bool> event_order_refund_apply_success) {
  int32_t label = 0;
  // 表示是否是后链路数据
  if (mix_flag_val == 3 || mix_flag_val == 4) {
    label |= 1;
  }
  // follow
  if (ad_merchat_follow || ad_fans_top_follow) {
    label |= 2;
  }
  // gs
  if (goods_view) {
    label |= 4;
  }
  // pay
  if (event_order_paied) {
    label |= 8;
  }
  // 默认使用直播 paycnt
  // 下单数( 2h 流的话则是总的下单数)
  auto iter_total = order_paid_cnt;
  // 混合流前链路下单数
  auto iter_front = mix_front_order_paid_cnt;
  // 如果是短视频推广计划，则使用短视频直播态 paycnt
  if (ad_campaign_type == 13 || ad_campaign_type == 17 || ad_campaign_type == 18 || ad_campaign_type == 20) {
    // 下单数(多品归因，为解决短视频 paycnt 被单品归因限制)
    iter_total = order_paid_cnt_v2;
    // 混合流前链路下单数(同上)
    iter_front = mix_front_order_paid_cnt_v2;
  }
  // 使用 5 个 bit 来表示 order_paid_cnt
  if (iter_total.has_value()) {
    uint64_t order_paid_cnt_val = iter_total.value();
    if (order_paid_cnt_val > 31) {
      order_paid_cnt_val = 31;
    }
    // [16, 512) 即 [2^3, 2^9)
    label |= (order_paid_cnt_val << 4);
    if (order_paid_cnt_val >= 1) {
      label |= 8;
    }
  }

  // mix_front_order_paid_cnt use 5bit
  if (iter_front.has_value()) {
    uint64_t order_paid_cnt_val = iter_front.value();
    if (order_paid_cnt_val > 31) {
      order_paid_cnt_val = 31;
    }
    // [512, 16384) 即 [2^9, 2^14)
    label |= (order_paid_cnt_val << 9);
    if (order_paid_cnt_val >= 1) {
      label |= 8;
    }
  }
  if (live_like) {
    label |= (1 << 14);
  }
  if (live_shop_cart_click) {
    label |= (1 << 15);
  }
  if (live_shop_link_jump) {
    label |= (1 << 16);
  }

  // 判断是否是转化目标的负样本(进人)
  if (standard_live_play_started) {
    // 标准直播开始播放
    label |= (1 << 17);
  } else if (live_play_started) {
    // 进入直播间开始直播
    bool std_lps = false;
    if (item_type == bs::ItemType::AD_DSP) {
      // 直播类型用于区分直播和短视频
      if (live_creative_type != 1) {
        std_lps = true;
      }
    } else if (item_type == bs::ItemType::FANS_TOP) {
      if (is_living) {
        std_lps = true;
      }
    } else if (item_type == bs::ItemType::AD_BRAND) {
      std_lps = true;
    }
    if (std_lps) {
      label |= (1 << 17);
    }
  }
  // 100021875/16948 是内流
  if (sub_page_id == 100011292 || sub_page_id == 100011503 || sub_page_id == 100012061 ||
      sub_page_id == 100018338 || sub_page_id == 100021875 ||
      (sub_page_id.has_value() && AdModelKconfUtil::inspireLiveSubpageConf() != nullptr &&
       AdModelKconfUtil::inspireLiveSubpageConf()->count(sub_page_id.value()))) {
    label |= (1 << 18);
  }
  if (app_id != "kuaishou" && app_id != "kuaishou_nebula") {
    label |= (1 << 18);
  }
  if (item_type == bs::ItemType::AD_BRAND) {
    label |= (1 << 18);
  }
  if (receive_coupon) {
    label |= (1 << 19);
  }
  if (live_comment) {
    label |= (1 << 20);
  }
  if (order_imp) {
    label |= (1 << 21);
  }
  if (order_submit_click) {
    label |= (1 << 22);
  }
  static const std::unordered_set<int64_t> key_pos_set{27, 108, 2574, 5, 283, 284, 9198, 3904};
  if (pos_id.has_value() && key_pos_set.find(pos_id.value()) == key_pos_set.end()) {
    label |= (1 << 23);
  }
  if (product_buy_click) {
    label |= (1 << 24);
  }
  // 判断慢流 fast_emit 窗口 label, mix_flag_val 新增 3 支付触发，4 退间触发
  if (mix_flag_val == 3) {
    label |= (1 << 25);
  }
  if (goods_view_content) {
    label |= (1 << 26);
  }
  if (live_share) {
    label |= (1 << 27);
  }
  if (live_reward_cnt >= 1) {
    label |= (1 << 28);
  }
  if (live_played_mean) {
    label |= (1 << 29);
  }
  if (event_order_refund_apply_success) {
    label |= (1 << 30);
  }
  return label;
}

// V2 版本, fast emit
int32_t inner_live_pay_fastemit_label_extractor2(
    absl::optional<int64_t> ad_campaign_type, absl::optional<int64_t> mix_flag_val,
    absl::optional<bool> ad_merchat_follow, absl::optional<bool> ad_fans_top_follow,
    absl::optional<bool> goods_view, absl::optional<bool> event_order_paied,
    absl::optional<int64_t> order_paid_cnt, absl::optional<int64_t> mix_front_order_paid_cnt,
    absl::optional<int64_t> order_paid_cnt_v2, absl::optional<int64_t> mix_front_order_paid_cnt_v2,
    absl::optional<bool> live_like, absl::optional<bool> live_shop_cart_click,
    absl::optional<bool> live_shop_link_jump, absl::optional<bool> standard_live_play_started,
    absl::optional<bool> live_play_started, absl::optional<int64_t> item_type,
    absl::optional<int64_t> live_creative_type, absl::optional<bool> is_living,
    absl::optional<int64_t> sub_page_id, absl::optional<absl::string_view> app_id,
    absl::optional<bool> receive_coupon, absl::optional<bool> live_comment, absl::optional<bool> order_imp,
    absl::optional<bool> order_submit_click, absl::optional<int64_t> pos_id,
    absl::optional<bool> product_buy_click, absl::optional<bool> on_foreground,
    absl::optional<bool> goods_view_content, absl::optional<bool> live_share,
    absl::optional<int64_t> live_reward_cnt, absl::optional<bool> live_played_mean) {
  int32_t label = 0;
  // 表示是否是后链路数据
  if (mix_flag_val != 2) {
    label |= 1;
  }
  // follow
  if (ad_merchat_follow || ad_fans_top_follow) {
    label |= 2;
  }
  // gs
  if (goods_view) {
    label |= 4;
  }
  // pay
  if (event_order_paied) {
    label |= 8;
  }
  // 默认使用直播 paycnt
  // 下单数( 2h 流的话则是总的下单数)
  auto iter_total = order_paid_cnt;
  // 混合流前链路下单数
  auto iter_front = mix_front_order_paid_cnt;
  // 如果是短视频推广计划，则使用短视频直播态 paycnt
  if (ad_campaign_type == 13 || ad_campaign_type == 17 || ad_campaign_type == 18 || ad_campaign_type == 20) {
    // 下单数(多品归因，为解决短视频 paycnt 被单品归因限制)
    iter_total = order_paid_cnt_v2;
    // 混合流前链路下单数(同上)
    iter_front = mix_front_order_paid_cnt_v2;
  }
  // 使用 5 个 bit 来表示 order_paid_cnt
  if (iter_total.has_value()) {
    uint64_t order_paid_cnt_val = iter_total.value();
    if (order_paid_cnt_val > 31) {
      order_paid_cnt_val = 31;
    }
    // [16, 512) 即 [2^3, 2^9)
    label |= (order_paid_cnt_val << 4);
    if (order_paid_cnt_val >= 1) {
      label |= 8;
    }
  }

  // mix_front_order_paid_cnt use 5bit
  if (iter_front.has_value()) {
    uint64_t order_paid_cnt_val = iter_front.value();
    if (order_paid_cnt_val > 31) {
      order_paid_cnt_val = 31;
    }
    // [512, 16384) 即 [2^9, 2^14)
    label |= (order_paid_cnt_val << 9);
    if (order_paid_cnt_val >= 1) {
      label |= 8;
    }
  }
  if (live_like) {
    label |= (1 << 14);
  }
  if (live_shop_cart_click) {
    label |= (1 << 15);
  }
  if (live_shop_link_jump) {
    label |= (1 << 16);
  }

  // 判断是否是转化目标的负样本(进人)
  if (standard_live_play_started) {
    // 标准直播开始播放
    label |= (1 << 17);
  } else if (live_play_started) {
    // 进入直播间开始直播
    bool std_lps = false;
    if (item_type == bs::ItemType::AD_DSP) {
      // 直播类型用于区分直播和短视频
      if (live_creative_type != 1) {
        std_lps = true;
      }
    } else if (item_type == bs::ItemType::FANS_TOP) {
      if (is_living) {
        std_lps = true;
      }
    } else if (item_type == bs::ItemType::AD_BRAND) {
      std_lps = true;
    }
    if (std_lps) {
      label |= (1 << 17);
    }
  }
  // 100021875/16948 是内流
  if (sub_page_id == 100011292 || sub_page_id == 100011503 || sub_page_id == 100012061 ||
      sub_page_id == 100018338 || sub_page_id == 100021875 ||
      (sub_page_id.has_value() && AdModelKconfUtil::inspireLiveSubpageConf() != nullptr &&
       AdModelKconfUtil::inspireLiveSubpageConf()->count(sub_page_id.value()))) {
    label |= (1 << 18);
  }
  if (app_id != "kuaishou" && app_id != "kuaishou_nebula") {
    label |= (1 << 18);
  }
  if (item_type == bs::ItemType::AD_BRAND) {
    label |= (1 << 18);
  }
  if (receive_coupon) {
    label |= (1 << 19);
  }
  if (live_comment) {
    label |= (1 << 20);
  }
  if (order_imp) {
    label |= (1 << 21);
  }
  if (order_submit_click) {
    label |= (1 << 22);
  }
  static const std::unordered_set<int64_t> key_pos_set{27, 108, 2574, 5, 283, 284, 9198, 3904};
  if (pos_id.has_value() && key_pos_set.find(pos_id.value()) == key_pos_set.end()) {
    label |= (1 << 23);
  }
  if (product_buy_click) {
    label |= (1 << 24);
  }
  if (mix_flag_val == 3) {
    label |= (1 << 25);
  }
  if (goods_view_content) {
    label |= (1 << 26);
  }
  if (live_share) {
    label |= (1 << 27);
  }
  if (live_reward_cnt >= 1) {
    label |= (1 << 28);
  }
  if (live_played_mean) {
    label |= (1 << 29);
  }

  return label;
}

}  // namespace ad_algorithm
}  // namespace ks
