#pragma once

#include <cstdint>
#include <string>

#include "absl/types/optional.h"

namespace ks {
namespace ad_algorithm {

/// mtl_mix_bonus_cut_fix_label_extractor_v2
///
/// 根据是否曝光以及 bonus 确认多任务 label
///
/// 参数:
/// - item_impression: bool, 为 true 或 1
/// - live_played_started: bool, 为 true 或 1
/// - standard_live_played_started: bool, 为 true 或 1
/// - bonus: float
/// - cpm: int64_t
///
/// 返回值:
/// - label: int32_t。
int32_t mtl_mix_bonus_cut_fix_label_extractor_v2(bool item_impression, bool live_played_started, bool standard_live_played_started, absl::optional<float> op_bonus, absl::optional<int64_t> op_cpm);  //NOLINT

}  // namespace ad_algorithm
}  // namespace ks
