#include "teams/ad/ad_algorithm/fed/compute/label_extractor/mtl_mix_bonus_cut_fix_label_extractor_v2.h"

namespace ks {
namespace ad_algorithm {

int32_t mtl_mix_bonus_cut_fix_label_extractor_v2(bool item_impression, bool live_played_started, bool standard_live_played_started, absl::optional<float> op_bonus, absl::optional<int64_t> op_cpm) {  //NOLINT
  int32_t label = 0, task_mask = 0;
  bool imp = item_impression || live_played_started || standard_live_played_started;
  const double eps = 1e-8;
  float bonus = op_bonus.value_or(0.0);
  int64_t cpm_original = op_cpm.value_or(0);
  float cpm = cpm_original * 1.0 / 1e6;
  float bonus_ratio = 0.0;
  if (cpm_original > 0) {
    bonus_ratio = bonus / cpm;
  }
  if (cpm_original <= 0) {  // 处理 cpm 小于等于 0 的异常值
    if (bonus > eps) {
      // 10000000000, 属于 task[10] 样本
      task_mask = 1024;
      if (imp) {
        // 10000000000, 样本 label 为正
        label = 1024;
      } else {
        // 00000000000
        label = 0;
      }
    } else {
      // 00000000001, 属于 task[0] 样本
      task_mask = 1;
      if (imp) {
        // 00000000001, 样本 label 为正
        label = 1;
      } else {
        // 00000000000
        label = 0;
      }
    }
  } else if (bonus_ratio < eps) {  // 相当于 bonus 为 0 的样本
    // 00000000001, 属于 task[0] 样本
    task_mask = 1;
    if (imp) {
      // 00000000001, task[0] 样本 label 为正
      label = 1;
    } else {
      // 00000000000
      label = 0;
    }
  } else if (bonus_ratio <= 0.101) {
    // 00000000010
    task_mask = 2;
    if (imp) {
      // 00000000010
      label = 2;
    } else {
      // 00000000000
      label = 0;
    }
  } else if (bonus_ratio <= 0.201) {
    // 00000000100
    task_mask = 4;
    if (imp) {
      // 00000000100
      label = 4;
    } else {
      // 00000000000
      label = 0;
    }
  } else if (bonus_ratio <= 0.301) {
    // 00000001000
    task_mask = 8;
    if (imp) {
      // 00000001000
      label = 8;
    } else {
      // 00000000000
      label = 0;
    }
  } else if (bonus_ratio <= 0.401) {
    // 00000010000
    task_mask = 16;
    if (imp) {
      // 00000010000
      label = 16;
    } else {
      // 00000000000
      label = 0;
    }
  } else if (bonus_ratio <= 0.501) {
    // 00000100000
    task_mask = 32;
    if (imp) {
      // 00000100000
      label = 32;
    } else {
      // 00000000000
      label = 0;
    }
  } else if (bonus_ratio <= 0.601) {
    // 00001000000
    task_mask = 64;
    if (imp) {
      // 00001000000
      label = 64;
    } else {
      // 00000000000
      label = 0;
    }
  } else if (bonus_ratio <= 0.701) {
    // 00010000000
    task_mask = 128;
    if (imp) {
      // 00010000000
      label = 128;
    } else {
      // 00000000000
      label = 0;
    }
  } else if (bonus_ratio <= 0.801) {
    // 00100000000
    task_mask = 256;
    if (imp) {
      // 00100000000
      label = 256;
    } else {
      // 00000000000
      label = 0;
    }
  } else if (bonus_ratio <= 0.901) {
    // 01000000000
    task_mask = 512;
    if (imp) {
      // 01000000000
      label = 512;
    } else {
      // 00000000000
      label = 0;
    }
  } else {
    // 10000000000
    task_mask = 1024;
    if (imp) {
      // 10000000000
      label = 1024;
    } else {
      // 00000000000
      label = 0;
    }
  }

  return (task_mask << 11) | label;
}

}  // namespace ad_algorithm
}  // namespace ks
