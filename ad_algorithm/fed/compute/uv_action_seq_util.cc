#include <stdio.h>
#include <sys/types.h>
#include <cstdint>
#include <string>
#include <string_view>
#include <algorithm>
#include <cmath>
#include "absl/strings/numbers.h"
#include "absl/strings/string_view.h"
#include "base/strings/string_split.h"
#include "absl/types/span.h"
#include "base/hash_function/city.h"
#include "glog/logging.h"
#include "base/time/timestamp.h"
#include "teams/ad/ad_algorithm/fed/compute/uv_action_seq_util.h"

namespace ks {
namespace ad_algorithm {

std::vector<int64_t> get_seq_with_max_len(
    absl::Span<const int64_t> seq,
    int max_len) {
    std::vector<int64_t> result;
    result.reserve(max_len);
    for (int i = 0; i < seq.size() && i < max_len; i++) {
        result.push_back(seq[i]);
    }
    return result;
}

std::vector<float> hard_search_seq_with_single_key(
  absl::Span<const int64_t> seq,
  absl::optional<int64_t> key,
  int max_len) {
    std::vector<float> result;
    result.reserve(max_len);
    ino64_t flag = key.value_or(-2);
    for (int i = 0; i < seq.size() && i < max_len; i++) {
      if (seq[i] == flag) {
        result.push_back(1.0);
      } else {
        result.push_back(0.0);
      }
    }
    return result;
}

std::vector<float> hard_search_seq_with_single_key(
  absl::Span<const int64_t> seq,
  absl::optional<absl::string_view> key,
  int max_len) {
    std::vector<float> result;
    result.reserve(max_len);
    std::string string_key{key.value_or("未命中")};
    int64_t flag = base::CityHash64(string_key.data(), string_key.size()) % 2147483647;
    for (int i = 0; i < seq.size() && i < max_len; i++) {
      if (seq[i] == flag) {
        result.push_back(1.0);
      } else {
        result.push_back(1.0);
      }
    }
    return result;
}

std::vector<float> hard_search_seq_with_string_single_key(
  absl::Span<const int64_t> seq,
  absl::optional<absl::string_view> key,
  int max_len) {
    std::vector<float> result;
    result.reserve(max_len);
    std::string string_key{key.value_or("未命中")};
    const int64_t hash_base = 2147483647;
    // key 为字符类型时, 在 CityHash64 的基础上, 再对 (2 的 32 次方减 1) 求余
    int64_t flag = base::CityHash64(string_key.data(), string_key.size()) % hash_base;
    for (int i = 0; i < seq.size() && i < max_len; i++) {
      if (seq[i] == flag) {
        result.push_back(1.0);
      } else {
        result.push_back(0.0);
      }
    }
    return result;
}

std::vector<int64_t> merge_two_seq_with_max_len(
  absl::Span<const int64_t> seq1,
  absl::Span<const int64_t> seq2,
  int seq1_max_len,
  int max_len) {
    std::vector<int64_t> result;
    result.reserve(max_len);
    for (int i = 0; i < seq1.size() && i < seq1_max_len; i++) {
      result.push_back(seq1[i]);
    }
    int current_len = result.size();
    for (int i = 0; i < seq2.size() && (i + current_len) < max_len; i++) {
      result.push_back(seq2[i]);
    }
    return result;
}

std::vector<int64_t> merge_two_seq_with_two_max_len(
  absl::Span<const int64_t> seq1,
  absl::Span<const int64_t> seq2,
  int seq1_max_len,
  int seq2_max_len) {
    std::vector<int64_t> result;
    result.reserve(seq1_max_len + seq2_max_len);
    for (int i = 0; i < seq1.size() && i < seq1_max_len; i++) {
      result.push_back(seq1[i]);
    }
    for (int i = 0; i < seq2.size() && i < seq2_max_len; i++) {
      result.push_back(seq2[i]);
    }
    return result;
}

std::vector<int64_t> get_universe_seq_with_max_len_and_timestamp(
    absl::Span<const int64_t> seq,
    absl::Span<const int64_t> medium_id_seq,
    absl::Span<const int64_t> timestamp_seq,
    int max_len,
    int64_t current_timestamp,
    int64_t timestamp_delta) {
    std::vector<int64_t> result;
    result.reserve(max_len);
    int64_t last_timestamp = current_timestamp - timestamp_delta;
    if (seq.size() > 0 && seq.size() == medium_id_seq.size() && seq.size() == timestamp_seq.size()) {
      for (int i = seq.size()-1; i >= 0 && result.size() < max_len; i--) {
        if (timestamp_seq[i] > last_timestamp) {
          if (medium_id_seq[i] == 2 || medium_id_seq[i] == 4) {
            result.push_back(seq[i]);
          }
        }
      }
    }
    return result;
}

std::vector<int64_t> get_universe_hash_seq_with_max_len_and_timestamp(
    absl::Span<const int64_t> seq,
    absl::Span<const int64_t> medium_id_seq,
    absl::Span<const int64_t> timestamp_seq,
    int max_len,
    int64_t current_timestamp,
    int64_t timestamp_delta) {
    std::vector<int64_t> result;
    result.reserve(max_len);
    int64_t last_timestamp = current_timestamp - timestamp_delta;
    if (seq.size() > 0 && seq.size() == medium_id_seq.size() && seq.size() == timestamp_seq.size()) {
      for (int i = seq.size()-1; i >= 0 && result.size() < max_len; i--) {
        if (timestamp_seq[i] > last_timestamp) {
          if (medium_id_seq[i] == 2 || medium_id_seq[i] == 4) {
            result.push_back(seq[i] % 2147483647);
          }
        }
      }
    }
    return result;
}

std::vector<int64_t> get_universe_hour_seq_with_max_len_and_timestamp(
    absl::Span<const int64_t> medium_id_seq,
    absl::Span<const int64_t> timestamp_seq,
    int max_len,
    int64_t current_timestamp,
    int64_t timestamp_delta) {
    std::vector<int64_t> result;
    result.reserve(max_len);
    int64_t last_timestamp = current_timestamp - timestamp_delta;
    char datehour[100];
    time_t tick;
    struct tm tm = { 0 };
    struct tm* tm1;
    if (timestamp_seq.size() > 0 && timestamp_seq.size() == medium_id_seq.size()) {
      for (int i = timestamp_seq.size()-1; i >= 0 && result.size() < max_len; i--) {
        if (timestamp_seq[i] > last_timestamp) {
          if (medium_id_seq[i] == 2 || medium_id_seq[i] == 4) {
            tick = (time_t)(timestamp_seq[i]/1000);
            tm1 = localtime_r(&tick, &tm);
            if (tm1 == NULL) {
              break;
            }
            strftime(datehour, sizeof(datehour), "%H", &tm);
            // hour 统一为 0 - 2300 格式
            result.push_back(atoi(datehour) * 100);
          }
        }
      }
    }
    return result;
}

std::vector<int64_t> get_universe_date_diff_seq_with_max_len_and_timestamp(
    absl::Span<const int64_t> medium_id_seq,
    absl::Span<const int64_t> timestamp_seq,
    int max_len,
    int64_t current_timestamp,
    int64_t timestamp_delta) {
    std::vector<int64_t> result;
    result.reserve(max_len);
    int64_t last_timestamp = current_timestamp - timestamp_delta;
    int64_t one_day_timestamp = 86400000;
    if (timestamp_seq.size() > 0 && timestamp_seq.size() == medium_id_seq.size()) {
      for (int i = timestamp_seq.size()-1; i >= 0 && result.size() < max_len; i--) {
        if (timestamp_seq[i] > last_timestamp) {
          if (medium_id_seq[i] == 2 || medium_id_seq[i] == 4) {
            result.push_back((current_timestamp - timestamp_seq[i]) / one_day_timestamp);
          }
        }
      }
    }
    return result;
}

std::vector<int64_t> get_universe_log2_min_diff_seq_with_max_len_and_timestamp(
    absl::Span<const int64_t> medium_id_seq,
    absl::Span<const int64_t> timestamp_seq,
    int max_len,
    int64_t current_timestamp,
    int64_t timestamp_delta) {
    std::vector<int64_t> result;
    result.reserve(max_len);
    int64_t last_timestamp = current_timestamp - timestamp_delta;
    int64_t one_minute_timestamp = 60000;
    double minutes = 1.0;
    if (timestamp_seq.size() > 0 && timestamp_seq.size() == medium_id_seq.size()) {
      for (int i = timestamp_seq.size()-1; i >= 0 && result.size() < max_len; i--) {
        if (timestamp_seq[i] > last_timestamp) {
          if (medium_id_seq[i] == 2 || medium_id_seq[i] == 4) {
            minutes = static_cast<double>((current_timestamp - timestamp_seq[i]) / one_minute_timestamp);
            result.push_back(static_cast<int64_t>(std::log2(std::max(1.0, minutes))));
          }
        }
      }
    }
    return result;
}

std::vector<int64_t> convert_date_diff_to_log2_min_diff(
    absl::Span<const int64_t> date_diff_seq) {
    std::vector<int64_t> result;
    int64_t one_day_minutes = 1440;
    int seq_len = date_diff_seq.size();
    result.reserve(seq_len);
    double minutes = 1.0;
    for (int i = 0; i < seq_len; i++) {
        minutes = std::max(1.0, static_cast<double>(date_diff_seq[i] * one_day_minutes));
        result.push_back(static_cast<int64_t>(std::log2(minutes)));
    }
    return result;
}

std::vector<float> merge_universe_3_exp_tags(
    absl::optional<int64_t> universe_exp1,
    absl::optional<int64_t> universe_exp2,
    absl::optional<int64_t> universe_exp3) {
    // std::vector<float> result;
    std::vector<float> result(3, 0.0);
    result[0] = static_cast<float>(universe_exp1.value_or(-1));
    result[1] = static_cast<float>(universe_exp2.value_or(-1));
    result[2] = static_cast<float>(universe_exp3.value_or(-1));
    return result;
}

int64_t universe_cast_string_to_int64(
    absl::optional<absl::string_view> string_view_int_value
) {
    int64_t int_value = 0;
    bool success = absl::SimpleAtoi(string_view_int_value.value_or("0"), &int_value);
    return int_value;
}

float sample_weight_default(
  absl::optional<float> factor
) {
  float result;
  result = static_cast<float>(factor.value_or(1.0));
  if (result < 1.0) {
    result = 1.0;
  }
  return result;
}

std::vector<int64_t> process_action_semantic_id_list(
  absl::Span<const int64_t> action_semantic_id_list,
  absl::optional<int64_t> action_num) {
    std::vector<int64_t> result;
    int64_t target_action_num = action_num.value_or(0);
    // 每个行为对应 3 级语义 ID, 不同级别 ID 增加前缀作为区分标记
    for (int i = 0; i < action_semantic_id_list.size() && i < 3 * target_action_num; i++) {
      if (i % 3 == 0) {
        result.push_back(action_semantic_id_list[i]);
      }
      if (i % 3 == 1) {
        result.push_back(action_semantic_id_list[i] + 10000);
      }
      if (i % 3 == 2) {
        result.push_back(action_semantic_id_list[i] + 20000);
      }
    }
    // 不够长度继续补默认值
    for (int i = action_semantic_id_list.size(); i < 3 * target_action_num; i++) {
      result.push_back(-1);
    }
    return result;
}

std::vector<int64_t> uv_split_string_int64_list_2_int64(
  absl::optional<absl::string_view> string_style_list,
  absl::optional<int64_t> list_len,
  int segment_dot_num) {
    std::vector<int64_t> result;
    std::string original_string_list{string_style_list.value_or("")};
    list_len = list_len.value_or(0);
    // 输入 list_len 小于等于 0, 或 string_style_list 为空字符串时返回空列表
    if (list_len <= 0 || original_string_list.empty()) {
      return result;
    }
    // 构造逗号分割符, 最少为一个逗号
    segment_dot_num = std::max(segment_dot_num, 1);
    std::string segment_str(segment_dot_num, ',');
    // 根据分隔符进行字符串分割
    std::vector<std::string> string_result;
    base::SplitStringUsingSubstr(original_string_list, segment_str, &string_result);
    // 字符形式数据转为整数类型
    int64_t temp_value = 0;
    for (int i = 0; i < list_len && i < string_result.size(); i ++) {
      try {
        temp_value = std::stoll(string_result[i]);
      } catch (const std::exception) {
        temp_value = -1;
        LOG(ERROR) << "parse int64_t failed in uv_split_string_int64_list_2_int64 !";
      }
      result.push_back(temp_value);
    }
    return result;
}

std::vector<int64_t> uv_split_string_string_list_2_int64(
  absl::optional<absl::string_view> string_style_list,
  absl::optional<int64_t> list_len,
  int segment_dot_num) {
    std::vector<int64_t> result;
    std::string original_string_list{string_style_list.value_or("")};
    list_len = list_len.value_or(0);
    // 输入 list_len 小于等于 0, 或 string_style_list 为空字符串时返回空列表
    if (list_len <= 0 || original_string_list.empty()) {
      return result;
    }
    // 构造逗号分割符, 最少为一个逗号
    segment_dot_num = std::max(segment_dot_num, 1);
    std::string segment_str(segment_dot_num, ',');
    // 根据分隔符进行字符串分割
    std::vector<std::string> string_result;
    base::SplitStringUsingSubstr(original_string_list, segment_str, &string_result);
    // 字符形式数据 hash 为整数类型
    int64_t temp_value = 0;
    for (int i = 0; i < list_len && i < string_result.size(); i ++) {
      temp_value = base::CityHash64(string_result[i].data(), string_result[i].size()) % 2147483647;
      result.push_back(temp_value);
    }
    return result;
}

int64_t div_sparse_id_op(
  absl::optional<int64_t> sparse_id,
  int low_bit_num) {
    int64_t result = sparse_id.value_or(0);
    if (low_bit_num <= 0) {
      return result;
    }
    for (int i = 0; i < low_bit_num; i++) {
      result = result / 10;
    }
    return result;
}
}  // namespace ad_algorithm
}  // namespace ks
