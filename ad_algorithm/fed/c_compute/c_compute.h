#pragma once

// Automatically generated file, do not edit!

#include <algorithm>
#include <set>
#include <string>
#include <unordered_map>
#include <vector>

#include "absl/strings/str_join.h"
#include "absl/strings/string_view.h"
#include "absl/types/optional.h"
#include "absl/types/span.h"
#include "teams/ad/ad_algorithm/feature_interface/fast_feature_interface.h"
#include "teams/ad/ad_algorithm/fed/c_adt/c_adt.h"
#include "teams/ad/ad_algorithm/fed/compute/action.h"
#include "teams/ad/ad_algorithm/fed/compute/action_compare.h"
#include "teams/ad/ad_algorithm/fed/compute/action_list_op.h"
#include "teams/ad/ad_algorithm/fed/compute/ad_meta_list.h"
#include "teams/ad/ad_algorithm/fed/compute/ad_sim_list.h"
#include "teams/ad/ad_algorithm/fed/compute/buyer_effective_type.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/comp_scene_flag.h"
#include "teams/ad/ad_algorithm/fed/compute/complete_seq_list_with_value.h"
#include "teams/ad/ad_algorithm/fed/compute/ecom_campaign_type.h"
#include "teams/ad/ad_algorithm/fed/compute/ecom_coupon_price_action.h"
#include "teams/ad/ad_algorithm/fed/compute/ecom_live_coldstart_action.h"
#include "teams/ad/ad_algorithm/fed/compute/edu_action.h"
#include "teams/ad/ad_algorithm/fed/compute/esp_qcpx_cvr_compute.h"
#include "teams/ad/ad_algorithm/fed/compute/fans_num_bucket.h"
#include "teams/ad/ad_algorithm/fed/compute/fans_tag.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/float_list_add_flag.h"
#include "teams/ad/ad_algorithm/fed/compute/follow_ocpx_action.h"
#include "teams/ad/ad_algorithm/fed/compute/generate_model_name_combine.h"
#include "teams/ad/ad_algorithm/fed/compute/gesture_type_client_volume.h"
#include "teams/ad/ad_algorithm/fed/compute/get_ad_queue_type.h"
#include "teams/ad/ad_algorithm/fed/compute/get_dense_ple.h"
#include "teams/ad/ad_algorithm/fed/compute/get_diff_value_and_merge.h"
#include "teams/ad/ad_algorithm/fed/compute/get_from_kconf_map.h"
#include "teams/ad/ad_algorithm/fed/compute/get_info_from_yellow_car_list.h"
#include "teams/ad/ad_algorithm/fed/compute/get_item_card_scene_flag.h"
#include "teams/ad/ad_algorithm/fed/compute/get_realtime_action_list_full.h"
#include "teams/ad/ad_algorithm/fed/compute/get_realtime_cross_feature.h"
#include "teams/ad/ad_algorithm/fed/compute/gmv_round.h"
#include "teams/ad/ad_algorithm/fed/compute/health_industry_info.h"
#include "teams/ad/ad_algorithm/fed/compute/im_author_msg_seperate.h"
#include "teams/ad/ad_algorithm/fed/compute/industry_v5_match_with_time_function.h"
#include "teams/ad/ad_algorithm/fed/compute/inherit_id_from_kconf_map.h"
#include "teams/ad/ad_algorithm/fed/compute/item_creative_type.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/simple_item_filter.h"
#include "teams/ad/ad_algorithm/fed/compute/kpin_bucket.h"
#include "teams/ad/ad_algorithm/fed/compute/label_extractor/simple_label_extractor.h"
#include "teams/ad/ad_algorithm/fed/compute/live_item_feature.h"
#include "teams/ad/ad_algorithm/fed/compute/local_sample_type.h"
#include "teams/ad/ad_algorithm/fed/compute/math.h"
#include "teams/ad/ad_algorithm/fed/compute/merchant_match_sparse_action.h"
#include "teams/ad/ad_algorithm/fed/compute/merchant_price.h"
#include "teams/ad/ad_algorithm/fed/compute/merchant_product_goods_info.h"
#include "teams/ad/ad_algorithm/fed/compute/merge_list.h"
#include "teams/ad/ad_algorithm/fed/compute/ocpc_type_filter_func.h"
#include "teams/ad/ad_algorithm/fed/compute/offline_user_seq_pooling_global_merge.h"
#include "teams/ad/ad_algorithm/fed/compute/one_hot.h"
#include "teams/ad/ad_algorithm/fed/compute/outer_loop_flag.h"
#include "teams/ad/ad_algorithm/fed/compute/rank_candidates_list_info.h"
#include "teams/ad/ad_algorithm/fed/compute/realtime_ecom_action.h"
#include "teams/ad/ad_algorithm/fed/compute/recall_pdct_ltr.h"
#include "teams/ad/ad_algorithm/fed/compute/search_source.h"
#include "teams/ad/ad_algorithm/fed/compute/segments.h"
#include "teams/ad/ad_algorithm/fed/compute/single_product_flag.h"
#include "teams/ad/ad_algorithm/fed/compute/single_product_price.h"
#include "teams/ad/ad_algorithm/fed/compute/split_int_to_vec.h"
#include "teams/ad/ad_algorithm/fed/compute/stay_action.h"
#include "teams/ad/ad_algorithm/fed/compute/tag_center.h"
#include "teams/ad/ad_algorithm/fed/compute/universe_extract_def.h"
#include "teams/ad/ad_algorithm/fed/compute/universe_parse_feature.h"
#include "teams/ad/ad_algorithm/fed/compute/user_5r.h"
#include "teams/ad/ad_algorithm/fed/compute/user_active_sum_bucket.h"
#include "teams/ad/ad_algorithm/fed/compute/user_hist_req_info.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"
#include "teams/ad/ad_algorithm/fed/compute/uv_action_seq_util.h"
#include "teams/ad/ad_algorithm/fed/compute/zed_account_filter_func.h"
#include "teams/ad/ad_algorithm/fed/compute/rank_context_info.h"

namespace ks {
namespace ad_algorithm {

extern "C" {

inline void c_get_diff_value_and_merge(void* first_timestamp_list_void_ptr, void* first_photo_list_void_ptr,
                                       void* first_industry_list_void_ptr,
                                       void* second_timestamp_list_void_ptr, void* second_photo_list_void_ptr,    // NOLINT
                                       void* second_industry_list_void_ptr, int32_t process_time,
                                       void* res_void_ptr) {
  auto* first_timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(first_timestamp_list_void_ptr);
  if (KS_UNLIKELY(first_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* first_photo_list_ptr = static_cast<absl::Span<const int64_t>*>(first_photo_list_void_ptr);
  if (KS_UNLIKELY(first_photo_list_ptr == nullptr)) {
    return;
  }

  auto* first_industry_list_ptr = static_cast<absl::Span<const int64_t>*>(first_industry_list_void_ptr);
  if (KS_UNLIKELY(first_industry_list_ptr == nullptr)) {
    return;
  }

  auto* second_timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(second_timestamp_list_void_ptr);
  if (KS_UNLIKELY(second_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* second_photo_list_ptr = static_cast<absl::Span<const int64_t>*>(second_photo_list_void_ptr);
  if (KS_UNLIKELY(second_photo_list_ptr == nullptr)) {
    return;
  }

  auto* second_industry_list_ptr = static_cast<absl::Span<const int64_t>*>(second_industry_list_void_ptr);
  if (KS_UNLIKELY(second_industry_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_diff_value_and_merge(*first_timestamp_list_ptr, *first_photo_list_ptr,
                                      *first_industry_list_ptr, *second_timestamp_list_ptr,
                                      *second_photo_list_ptr, *second_industry_list_ptr, process_time);
}

inline void c_find_product_id_in_base_url(void* s_void_ptr, void* res_void_ptr) {
  auto* s_ptr = static_cast<absl::string_view*>(s_void_ptr);
  if (KS_UNLIKELY(s_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = find_product_id_in_base_url(*s_ptr);
}

inline void c_get_second_list_value(void* result_void_ptr, void* res_void_ptr) {
  auto* result_ptr = static_cast<std::vector<std::vector<int64_t>>*>(result_void_ptr);
  if (KS_UNLIKELY(result_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_second_list_value(*result_ptr);
}

inline void c_get_first_list_value(void* result_void_ptr, void* res_void_ptr) {
  auto* result_ptr = static_cast<std::vector<std::vector<int64_t>>*>(result_void_ptr);
  if (KS_UNLIKELY(result_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_first_list_value(*result_ptr);
}

inline void c_get_diff_value_from_two_list(
    void* first_timestamp_list_void_ptr, void* first_photo_list_void_ptr, void* first_industry_list_void_ptr,
    void* second_timestamp_list_void_ptr, void* second_photo_list_void_ptr,
    void* second_industry_list_void_ptr, int32_t process_time, int32_t period, void* res_void_ptr) {
  auto* first_timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(first_timestamp_list_void_ptr);
  if (KS_UNLIKELY(first_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* first_photo_list_ptr = static_cast<absl::Span<const int64_t>*>(first_photo_list_void_ptr);
  if (KS_UNLIKELY(first_photo_list_ptr == nullptr)) {
    return;
  }

  auto* first_industry_list_ptr = static_cast<absl::Span<const int64_t>*>(first_industry_list_void_ptr);
  if (KS_UNLIKELY(first_industry_list_ptr == nullptr)) {
    return;
  }

  auto* second_timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(second_timestamp_list_void_ptr);
  if (KS_UNLIKELY(second_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* second_photo_list_ptr = static_cast<absl::Span<const int64_t>*>(second_photo_list_void_ptr);
  if (KS_UNLIKELY(second_photo_list_ptr == nullptr)) {
    return;
  }

  auto* second_industry_list_ptr = static_cast<absl::Span<const int64_t>*>(second_industry_list_void_ptr);
  if (KS_UNLIKELY(second_industry_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<std::vector<int64_t>>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_diff_value_from_two_list(
      *first_timestamp_list_ptr, *first_photo_list_ptr, *first_industry_list_ptr, *second_timestamp_list_ptr,
      *second_photo_list_ptr, *second_industry_list_ptr, process_time, period);
}

inline void c_get_ecom_action_time_segment_v2(void* timestamp_list_void_ptr, void* action_list_void_ptr,
                                              void* target_id_list_void_ptr, bool whole, int64_t adlog_time,
                                              int64_t target_id, size_t bucket_size, size_t max_cnt,
                                              void* res_void_ptr) {
  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* target_id_list_ptr = static_cast<absl::Span<const int64_t>*>(target_id_list_void_ptr);
  if (KS_UNLIKELY(target_id_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_ecom_action_time_segment_v2(*timestamp_list_ptr, *action_list_ptr, *target_id_list_ptr,
                                             whole, adlog_time, target_id, bucket_size, max_cnt);
}

inline void c_get_slice_ecom_cot_fea(void* fea_value_list_void_ptr, int64_t start_pos, int64_t length,
                                     void* res_void_ptr) {
  auto* fea_value_list_ptr = static_cast<absl::Span<const float>*>(fea_value_list_void_ptr);
  if (KS_UNLIKELY(fea_value_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_slice_ecom_cot_fea(*fea_value_list_ptr, start_pos, length);
}

inline int64_t c_get_kpin_live_recruit_in_show_user_cnt_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_live_recruit_in_show_user_cnt_bucket(*raw_input_ptr);
}

inline size_t c_inherit_id_from_kconf_map(int64_t photo_id, bool is_train) {
  return inherit_id_from_kconf_map(photo_id, is_train);
}

inline void c_get_gap_seq_with_target_hit_combine_v2(void* action_list_void_ptr,
                                                     void* timestamp_list_void_ptr, void* target_id_void_ptr,
                                                     void* target_id_v2_void_ptr, int64_t timestamp,
                                                     void* res_void_ptr) {
  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* target_id_ptr = static_cast<absl::optional<int64_t>*>(target_id_void_ptr);
  if (KS_UNLIKELY(target_id_ptr == nullptr)) {
    return;
  }

  auto* target_id_v2_ptr = static_cast<absl::optional<int64_t>*>(target_id_v2_void_ptr);
  if (KS_UNLIKELY(target_id_v2_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_gap_seq_with_target_hit_combine_v2(*action_list_ptr, *timestamp_list_ptr, *target_id_ptr,
                                                    *target_id_v2_ptr, timestamp);
}

inline int64_t c_get_client_info_combine(void* client_info_value_void_ptr, int64_t pos_id,
                                         void* interactive_form_void_ptr, void* platform_void_ptr) {
  auto* client_info_value_ptr = static_cast<absl::optional<int64_t>*>(client_info_value_void_ptr);
  if (KS_UNLIKELY(client_info_value_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* interactive_form_ptr = static_cast<absl::optional<int64_t>*>(interactive_form_void_ptr);
  if (KS_UNLIKELY(interactive_form_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* platform_ptr = static_cast<absl::string_view*>(platform_void_ptr);
  if (KS_UNLIKELY(platform_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_client_info_combine(*client_info_value_ptr, pos_id, *interactive_form_ptr, *platform_ptr);
}

inline void c_get_first_list(void* result_void_ptr, void* res_void_ptr) {
  auto* result_ptr = static_cast<std::vector<std::vector<int64_t>>*>(result_void_ptr);
  if (KS_UNLIKELY(result_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_first_list(*result_ptr);
}

inline void c_get_ad_meta_action_list(void* features_void_ptr, void* res_void_ptr) {
  auto* features_ptr = static_cast<absl::Span<const int64_t>*>(features_void_ptr);
  if (KS_UNLIKELY(features_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_ad_meta_action_list(*features_ptr);
}

inline float c_calc_certain_avg_val_dense(void* val_list_void_ptr, int32_t start, int32_t step,
                                          int32_t max_len) {
  auto* val_list_ptr = static_cast<absl::Span<const int64_t>*>(val_list_void_ptr);
  if (KS_UNLIKELY(val_list_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return calc_certain_avg_val_dense(*val_list_ptr, start, step, max_len);
}

inline void c_get_avg_price(void* price_list_void_ptr, void* res_void_ptr) {
  auto* price_list_ptr = static_cast<absl::Span<const int64_t>*>(price_list_void_ptr);
  if (KS_UNLIKELY(price_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_avg_price(*price_list_ptr);
}

inline void c_get_user_cross_item_scores(void* timestamp_list_void_ptr, void* action_list0_void_ptr,
                                         void* action_list1_void_ptr, int64_t adlog_time,
                                         void* target_ids0_void_ptr, void* target_id_void_ptr,
                                         void* target_ids1_void_ptr, int64_t t_author_id,
                                         void* scores0_void_ptr, void* scores1_void_ptr,
                                         void* scores2_void_ptr, void* scores3_void_ptr,
                                         void* scores4_void_ptr, void* scores5_void_ptr, void* res_void_ptr) {    // NOLINT
  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* action_list0_ptr = static_cast<absl::Span<const int64_t>*>(action_list0_void_ptr);
  if (KS_UNLIKELY(action_list0_ptr == nullptr)) {
    return;
  }

  auto* action_list1_ptr = static_cast<absl::Span<const int64_t>*>(action_list1_void_ptr);
  if (KS_UNLIKELY(action_list1_ptr == nullptr)) {
    return;
  }

  auto* target_ids0_ptr = static_cast<absl::Span<const int64_t>*>(target_ids0_void_ptr);
  if (KS_UNLIKELY(target_ids0_ptr == nullptr)) {
    return;
  }

  auto* target_id_ptr = static_cast<absl::optional<int64_t>*>(target_id_void_ptr);
  if (KS_UNLIKELY(target_id_ptr == nullptr)) {
    return;
  }

  auto* target_ids1_ptr = static_cast<absl::Span<const int64_t>*>(target_ids1_void_ptr);
  if (KS_UNLIKELY(target_ids1_ptr == nullptr)) {
    return;
  }

  auto* scores0_ptr = static_cast<absl::Span<const int64_t>*>(scores0_void_ptr);
  if (KS_UNLIKELY(scores0_ptr == nullptr)) {
    return;
  }

  auto* scores1_ptr = static_cast<absl::Span<const int64_t>*>(scores1_void_ptr);
  if (KS_UNLIKELY(scores1_ptr == nullptr)) {
    return;
  }

  auto* scores2_ptr = static_cast<absl::Span<const int64_t>*>(scores2_void_ptr);
  if (KS_UNLIKELY(scores2_ptr == nullptr)) {
    return;
  }

  auto* scores3_ptr = static_cast<absl::Span<const int64_t>*>(scores3_void_ptr);
  if (KS_UNLIKELY(scores3_ptr == nullptr)) {
    return;
  }

  auto* scores4_ptr = static_cast<absl::Span<const int64_t>*>(scores4_void_ptr);
  if (KS_UNLIKELY(scores4_ptr == nullptr)) {
    return;
  }

  auto* scores5_ptr = static_cast<absl::Span<const int64_t>*>(scores5_void_ptr);
  if (KS_UNLIKELY(scores5_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_cross_item_scores(*timestamp_list_ptr, *action_list0_ptr, *action_list1_ptr, adlog_time,    // NOLINT
                                        *target_ids0_ptr, *target_id_ptr, *target_ids1_ptr, t_author_id,
                                        *scores0_ptr, *scores1_ptr, *scores2_ptr, *scores3_ptr, *scores4_ptr,
                                        *scores5_ptr);
}

inline void c_get_user_hist_page_num_ordered_cross_pos_id(void* page_num_list_void_ptr,
                                                          void* cur_page_num_void_ptr, void* pos_id_void_ptr,
                                                          void* res_void_ptr) {
  auto* page_num_list_ptr = static_cast<absl::Span<const int64_t>*>(page_num_list_void_ptr);
  if (KS_UNLIKELY(page_num_list_ptr == nullptr)) {
    return;
  }

  auto* cur_page_num_ptr = static_cast<absl::optional<int64_t>*>(cur_page_num_void_ptr);
  if (KS_UNLIKELY(cur_page_num_ptr == nullptr)) {
    return;
  }

  auto* pos_id_ptr = static_cast<absl::optional<int64_t>*>(pos_id_void_ptr);
  if (KS_UNLIKELY(pos_id_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_hist_page_num_ordered_cross_pos_id(*page_num_list_ptr, *cur_page_num_ptr, *pos_id_ptr);
}

inline int64_t c_get_first_value_in_list(void* result_void_ptr) {
  auto* result_ptr = static_cast<std::vector<int64_t>*>(result_void_ptr);
  if (KS_UNLIKELY(result_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_first_value_in_list(*result_ptr);
}

inline void c_get_user_hist_page_num_succ_gap_ordered_span_i64__optional_i64(void* x1_void_ptr,
                                                                             void* x2_void_ptr,
                                                                             void* res_void_ptr) {
  auto* x1_ptr = static_cast<absl::Span<const int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* x2_ptr = static_cast<absl::optional<int64_t>*>(x2_void_ptr);
  if (KS_UNLIKELY(x2_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_hist_page_num_succ_gap_ordered(*x1_ptr, *x2_ptr);
}

inline int64_t c_calc_avg_val(void* val1_void_ptr, void* val2_void_ptr, void* coef1_void_ptr,
                              void* coef2_void_ptr) {
  auto* val1_ptr = static_cast<absl::optional<int64_t>*>(val1_void_ptr);
  if (KS_UNLIKELY(val1_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* val2_ptr = static_cast<absl::optional<int64_t>*>(val2_void_ptr);
  if (KS_UNLIKELY(val2_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* coef1_ptr = static_cast<absl::optional<float>*>(coef1_void_ptr);
  if (KS_UNLIKELY(coef1_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* coef2_ptr = static_cast<absl::optional<float>*>(coef2_void_ptr);
  if (KS_UNLIKELY(coef2_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return calc_avg_val(*val1_ptr, *val2_ptr, *coef1_ptr, *coef2_ptr);
}

inline void c_get_specific_room_pattern_action_lst_merge(
    void* fanstop_action_list_void_ptr, void* fanstop_timestamp_list_void_ptr,
    void* fanstop_room_pattern_list_void_ptr, void* dsp_action_list_void_ptr,
    void* dsp_timestamp_list_void_ptr, void* dsp_room_pattern_list_void_ptr, int64_t adlog_time,
    void* room_pattern_type_void_ptr, void* res_void_ptr) {
  auto* fanstop_action_list_ptr = static_cast<absl::Span<const int64_t>*>(fanstop_action_list_void_ptr);
  if (KS_UNLIKELY(fanstop_action_list_ptr == nullptr)) {
    return;
  }

  auto* fanstop_timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(fanstop_timestamp_list_void_ptr);    // NOLINT
  if (KS_UNLIKELY(fanstop_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* fanstop_room_pattern_list_ptr =
      static_cast<absl::Span<const int64_t>*>(fanstop_room_pattern_list_void_ptr);
  if (KS_UNLIKELY(fanstop_room_pattern_list_ptr == nullptr)) {
    return;
  }

  auto* dsp_action_list_ptr = static_cast<absl::Span<const int64_t>*>(dsp_action_list_void_ptr);
  if (KS_UNLIKELY(dsp_action_list_ptr == nullptr)) {
    return;
  }

  auto* dsp_timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(dsp_timestamp_list_void_ptr);
  if (KS_UNLIKELY(dsp_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* dsp_room_pattern_list_ptr = static_cast<absl::Span<const int64_t>*>(dsp_room_pattern_list_void_ptr);
  if (KS_UNLIKELY(dsp_room_pattern_list_ptr == nullptr)) {
    return;
  }

  auto* room_pattern_type_ptr = static_cast<RoomPatternType*>(room_pattern_type_void_ptr);
  if (KS_UNLIKELY(room_pattern_type_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_specific_room_pattern_action_lst_merge(*fanstop_action_list_ptr, *fanstop_timestamp_list_ptr,    // NOLINT
                                                        *fanstop_room_pattern_list_ptr, *dsp_action_list_ptr,
                                                        *dsp_timestamp_list_ptr, *dsp_room_pattern_list_ptr,
                                                        adlog_time, *room_pattern_type_ptr);
}

inline void c_get_p2l_sparse_feature_use_product_id_int64(int64_t campaign_type, int64_t live_creative_type,
                                                          void* base_url_void_ptr,
                                                          void* live_info_data_void_ptr, void* res_void_ptr) {    // NOLINT
  auto* base_url_ptr = static_cast<absl::string_view*>(base_url_void_ptr);
  if (KS_UNLIKELY(base_url_ptr == nullptr)) {
    return;
  }

  auto* live_info_data_ptr = static_cast<absl::optional<int64_t>*>(live_info_data_void_ptr);
  if (KS_UNLIKELY(live_info_data_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_p2l_sparse_feature_use_product_id_int64(campaign_type, live_creative_type, *base_url_ptr,
                                                         *live_info_data_ptr);
}

inline void c_seq_list_completion_dense(void* seq_list_void_ptr, int32_t seq_max_len, float default_value,
                                        void* res_void_ptr) {
  auto* seq_list_ptr = static_cast<absl::Span<const int64_t>*>(seq_list_void_ptr);
  if (KS_UNLIKELY(seq_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = seq_list_completion_dense(*seq_list_ptr, seq_max_len, default_value);
}

inline void c_get_match_detail_with_gift_price(void* user_gift_action_author_list_void_ptr,
                                               void* user_gift_price_list_void_ptr,
                                               void* user_action_timestamp_list_void_ptr, int64_t target_id,
                                               int64_t adlog_timestamp, void* res_void_ptr) {
  auto* user_gift_action_author_list_ptr =
      static_cast<absl::Span<const int64_t>*>(user_gift_action_author_list_void_ptr);
  if (KS_UNLIKELY(user_gift_action_author_list_ptr == nullptr)) {
    return;
  }

  auto* user_gift_price_list_ptr = static_cast<absl::Span<const int64_t>*>(user_gift_price_list_void_ptr);
  if (KS_UNLIKELY(user_gift_price_list_ptr == nullptr)) {
    return;
  }

  auto* user_action_timestamp_list_ptr =
      static_cast<absl::Span<const int64_t>*>(user_action_timestamp_list_void_ptr);
  if (KS_UNLIKELY(user_action_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_match_detail_with_gift_price(*user_gift_action_author_list_ptr, *user_gift_price_list_ptr,
                                              *user_action_timestamp_list_ptr, target_id, adlog_timestamp);
}

inline void c_price_level_map_list(void* attr_list_void_ptr, void* res_void_ptr) {
  auto* attr_list_ptr = static_cast<absl::Span<const int64_t>*>(attr_list_void_ptr);
  if (KS_UNLIKELY(attr_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = price_level_map_list(*attr_list_ptr);
}

inline void c_get_feature_info_bitwise(void* feature_info_void_ptr, int32_t start_bit, int32_t bit_length,
                                       void* res_void_ptr) {
  auto* feature_info_ptr = static_cast<absl::Span<const int64_t>*>(feature_info_void_ptr);
  if (KS_UNLIKELY(feature_info_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_feature_info_bitwise(*feature_info_ptr, start_bit, bit_length);
}

inline bool c_is_p2l_type(int64_t campaign_type, int64_t live_creative_type) {
  return is_p2l_type(campaign_type, live_creative_type);
}

inline void c_get_live_pay_labels_end(void* ad_merchat_follow_void_ptr, void* ad_fans_top_follow_void_ptr,
                                      void* goods_view_void_ptr, void* live_like_void_ptr,
                                      void* live_shop_cart_click_void_ptr, void* live_shop_link_jump_void_ptr,    // NOLINT
                                      void* live_p3s_void_ptr, void* live_p1m_void_ptr,
                                      void* live_pmedian_void_ptr, void* live_pmean_void_ptr,
                                      void* live_comment_void_ptr, void* rec_coupon_void_ptr,
                                      void* std_live_play_seconds_void_ptr,
                                      void* mix_front_order_paid_cnt_void_ptr, void* res_void_ptr) {
  auto* ad_merchat_follow_ptr = static_cast<absl::optional<int64_t>*>(ad_merchat_follow_void_ptr);
  if (KS_UNLIKELY(ad_merchat_follow_ptr == nullptr)) {
    return;
  }

  auto* ad_fans_top_follow_ptr = static_cast<absl::optional<int64_t>*>(ad_fans_top_follow_void_ptr);
  if (KS_UNLIKELY(ad_fans_top_follow_ptr == nullptr)) {
    return;
  }

  auto* goods_view_ptr = static_cast<absl::optional<int64_t>*>(goods_view_void_ptr);
  if (KS_UNLIKELY(goods_view_ptr == nullptr)) {
    return;
  }

  auto* live_like_ptr = static_cast<absl::optional<int64_t>*>(live_like_void_ptr);
  if (KS_UNLIKELY(live_like_ptr == nullptr)) {
    return;
  }

  auto* live_shop_cart_click_ptr = static_cast<absl::optional<int64_t>*>(live_shop_cart_click_void_ptr);
  if (KS_UNLIKELY(live_shop_cart_click_ptr == nullptr)) {
    return;
  }

  auto* live_shop_link_jump_ptr = static_cast<absl::optional<int64_t>*>(live_shop_link_jump_void_ptr);
  if (KS_UNLIKELY(live_shop_link_jump_ptr == nullptr)) {
    return;
  }

  auto* live_p3s_ptr = static_cast<absl::optional<int64_t>*>(live_p3s_void_ptr);
  if (KS_UNLIKELY(live_p3s_ptr == nullptr)) {
    return;
  }

  auto* live_p1m_ptr = static_cast<absl::optional<int64_t>*>(live_p1m_void_ptr);
  if (KS_UNLIKELY(live_p1m_ptr == nullptr)) {
    return;
  }

  auto* live_pmedian_ptr = static_cast<absl::optional<int64_t>*>(live_pmedian_void_ptr);
  if (KS_UNLIKELY(live_pmedian_ptr == nullptr)) {
    return;
  }

  auto* live_pmean_ptr = static_cast<absl::optional<int64_t>*>(live_pmean_void_ptr);
  if (KS_UNLIKELY(live_pmean_ptr == nullptr)) {
    return;
  }

  auto* live_comment_ptr = static_cast<absl::optional<int64_t>*>(live_comment_void_ptr);
  if (KS_UNLIKELY(live_comment_ptr == nullptr)) {
    return;
  }

  auto* rec_coupon_ptr = static_cast<absl::optional<int64_t>*>(rec_coupon_void_ptr);
  if (KS_UNLIKELY(rec_coupon_ptr == nullptr)) {
    return;
  }

  auto* std_live_play_seconds_ptr = static_cast<absl::optional<int64_t>*>(std_live_play_seconds_void_ptr);
  if (KS_UNLIKELY(std_live_play_seconds_ptr == nullptr)) {
    return;
  }

  auto* mix_front_order_paid_cnt_ptr =
      static_cast<absl::optional<int64_t>*>(mix_front_order_paid_cnt_void_ptr);
  if (KS_UNLIKELY(mix_front_order_paid_cnt_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_pay_labels_end(*ad_merchat_follow_ptr, *ad_fans_top_follow_ptr, *goods_view_ptr,
                                     *live_like_ptr, *live_shop_cart_click_ptr, *live_shop_link_jump_ptr,
                                     *live_p3s_ptr, *live_p1m_ptr, *live_pmedian_ptr, *live_pmean_ptr,
                                     *live_comment_ptr, *rec_coupon_ptr, *std_live_play_seconds_ptr,
                                     *mix_front_order_paid_cnt_ptr);
}

inline void c_get_action_a_no_action_b_list(void* user_action_a_list_void_ptr,
                                            void* user_action_b_list_void_ptr, void* res_void_ptr) {
  auto* user_action_a_list_ptr = static_cast<absl::Span<const int64_t>*>(user_action_a_list_void_ptr);
  if (KS_UNLIKELY(user_action_a_list_ptr == nullptr)) {
    return;
  }

  auto* user_action_b_list_ptr = static_cast<absl::Span<const int64_t>*>(user_action_b_list_void_ptr);
  if (KS_UNLIKELY(user_action_b_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_action_a_no_action_b_list(*user_action_a_list_ptr, *user_action_b_list_ptr);
}

inline void c_get_successive_time_gap_cross_pos_id_platform(void* time_v_void_ptr, int64_t cur_timestamp,
                                                            void* pos_id_void_ptr, void* platform_void_ptr,
                                                            void* res_void_ptr) {
  auto* time_v_ptr = static_cast<absl::Span<const int64_t>*>(time_v_void_ptr);
  if (KS_UNLIKELY(time_v_ptr == nullptr)) {
    return;
  }

  auto* pos_id_ptr = static_cast<absl::optional<int64_t>*>(pos_id_void_ptr);
  if (KS_UNLIKELY(pos_id_ptr == nullptr)) {
    return;
  }

  auto* platform_ptr = static_cast<absl::string_view*>(platform_void_ptr);
  if (KS_UNLIKELY(platform_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr =
      get_successive_time_gap_cross_pos_id_platform(*time_v_ptr, cur_timestamp, *pos_id_ptr, *platform_ptr);
}

inline float c_calc_avg_val_dense(void* val1_void_ptr, void* val2_void_ptr, void* coef1_void_ptr,
                                  void* coef2_void_ptr) {
  auto* val1_ptr = static_cast<absl::optional<int64_t>*>(val1_void_ptr);
  if (KS_UNLIKELY(val1_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* val2_ptr = static_cast<absl::optional<int64_t>*>(val2_void_ptr);
  if (KS_UNLIKELY(val2_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* coef1_ptr = static_cast<absl::optional<float>*>(coef1_void_ptr);
  if (KS_UNLIKELY(coef1_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* coef2_ptr = static_cast<absl::optional<float>*>(coef2_void_ptr);
  if (KS_UNLIKELY(coef2_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return calc_avg_val_dense(*val1_ptr, *val2_ptr, *coef1_ptr, *coef2_ptr);
}

inline void
c_merge_float_list_with_limit_f32__f32__f32__f32__f32__f32__f32__f32__f32__f32__f32__f32__f32__f32__f32__f32(
    float x13, float x26, float x39, float x52, float x65, float x78, float x91, float x104, float x117,
    float x130, float x143, float x156, float x169, float x182, float x195, float x208, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_with_limit(x13, x26, x39, x52, x65, x78, x91, x104, x117, x130, x143, x156,
                                         x169, x182, x195, x208);
}

inline void c_add_feature_result_vector_i64__vector_extractresult(void* x7_void_ptr, void* result_void_ptr) {
  auto* x7_ptr = static_cast<std::vector<int64_t>*>(x7_void_ptr);
  if (KS_UNLIKELY(x7_ptr == nullptr)) {
    return;
  }

  auto* result_ptr = static_cast<std::vector<ExtractResult>*>(result_void_ptr);
  if (KS_UNLIKELY(result_ptr == nullptr)) {
    return;
  }

  return add_feature_result(*x7_ptr, result_ptr);
}

inline void c_add_feature_result_i64__vector_extractresult(int64_t x3, void* result_void_ptr) {
  auto* result_ptr = static_cast<std::vector<ExtractResult>*>(result_void_ptr);
  if (KS_UNLIKELY(result_ptr == nullptr)) {
    return;
  }

  return add_feature_result(x3, result_ptr);
}

inline void c_add_feature_result_size__vector_extractresult(size_t x4, void* result_void_ptr) {
  auto* result_ptr = static_cast<std::vector<ExtractResult>*>(result_void_ptr);
  if (KS_UNLIKELY(result_ptr == nullptr)) {
    return;
  }

  return add_feature_result(x4, result_ptr);
}

inline void c_add_feature_result_optional_f32__i32__vector_extractresult(void* x1_void_ptr, int32_t size,
                                                                         void* result_void_ptr) {
  auto* x1_ptr = static_cast<absl::optional<float>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* result_ptr = static_cast<std::vector<ExtractResult>*>(result_void_ptr);
  if (KS_UNLIKELY(result_ptr == nullptr)) {
    return;
  }

  return add_feature_result(*x1_ptr, size, result_ptr);
}

inline void c_add_feature_result_optional_i64__vector_extractresult(void* x1_void_ptr,
                                                                    void* result_void_ptr) {
  auto* x1_ptr = static_cast<absl::optional<int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* result_ptr = static_cast<std::vector<ExtractResult>*>(result_void_ptr);
  if (KS_UNLIKELY(result_ptr == nullptr)) {
    return;
  }

  return add_feature_result(*x1_ptr, result_ptr);
}

inline void c_add_feature_result_vector_ui64__vector_extractresult(void* x5_void_ptr, void* result_void_ptr) {    // NOLINT
  auto* x5_ptr = static_cast<std::vector<uint64_t>*>(x5_void_ptr);
  if (KS_UNLIKELY(x5_ptr == nullptr)) {
    return;
  }

  auto* result_ptr = static_cast<std::vector<ExtractResult>*>(result_void_ptr);
  if (KS_UNLIKELY(result_ptr == nullptr)) {
    return;
  }

  return add_feature_result(*x5_ptr, result_ptr);
}

inline int64_t c_get_brand_behaviour_cnt_span_i64__span_i64__i64__i32(void* x1_void_ptr, void* x2_void_ptr,
                                                                      int64_t x4, int32_t int_arg3) {
  auto* x1_ptr = static_cast<absl::Span<const int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* x2_ptr = static_cast<absl::Span<const int64_t>*>(x2_void_ptr);
  if (KS_UNLIKELY(x2_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_brand_behaviour_cnt(*x1_ptr, *x2_ptr, x4, int_arg3);
}

inline void c_get_specific_room_pattern_action_lst(void* action_list_void_ptr, void* timestamp_list_void_ptr,
                                                   void* room_pattern_list_void_ptr, int64_t adlog_time,
                                                   void* room_pattern_type_void_ptr, void* res_void_ptr) {
  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* room_pattern_list_ptr = static_cast<absl::Span<const int64_t>*>(room_pattern_list_void_ptr);
  if (KS_UNLIKELY(room_pattern_list_ptr == nullptr)) {
    return;
  }

  auto* room_pattern_type_ptr = static_cast<RoomPatternType*>(room_pattern_type_void_ptr);
  if (KS_UNLIKELY(room_pattern_type_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_specific_room_pattern_action_lst(*action_list_ptr, *timestamp_list_ptr,
                                                  *room_pattern_list_ptr, adlog_time, *room_pattern_type_ptr);    // NOLINT
}

inline void c_get_live_discount_ratio(void* discount_list_void_ptr, void* res_void_ptr) {
  auto* discount_list_ptr = static_cast<absl::Span<const float>*>(discount_list_void_ptr);
  if (KS_UNLIKELY(discount_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_discount_ratio(*discount_list_ptr);
}

inline void c_get_ecom_campaign_flag(int64_t campaign_type, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_ecom_campaign_flag(campaign_type);
}

inline void c_get_target_id_list(void* target_id_list_void_ptr, int64_t max_cnt, void* res_void_ptr) {
  auto* target_id_list_ptr = static_cast<absl::Span<const int64_t>*>(target_id_list_void_ptr);
  if (KS_UNLIKELY(target_id_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_target_id_list(*target_id_list_ptr, max_cnt);
}

inline void c_get_recent_avg_gmv(void* gmv_list_void_ptr, void* paycnt_list_void_ptr, void* res_void_ptr) {
  auto* gmv_list_ptr = static_cast<absl::Span<const int64_t>*>(gmv_list_void_ptr);
  if (KS_UNLIKELY(gmv_list_ptr == nullptr)) {
    return;
  }

  auto* paycnt_list_ptr = static_cast<absl::Span<const int64_t>*>(paycnt_list_void_ptr);
  if (KS_UNLIKELY(paycnt_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_recent_avg_gmv(*gmv_list_ptr, *paycnt_list_ptr);
}

inline int64_t c_calc_bid_level(void* bid_list_void_ptr, int32_t tag) {
  auto* bid_list_ptr = static_cast<absl::Span<const int64_t>*>(bid_list_void_ptr);
  if (KS_UNLIKELY(bid_list_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return calc_bid_level(*bid_list_ptr, tag);
}

inline int64_t c_value_or_optional_i64__i32(void* x1_void_ptr, int32_t int_arg1) {
  auto* x1_ptr = static_cast<absl::optional<int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return value_or(*x1_ptr, int_arg1);
}

inline void c_get_mix_rank_ecom_reco_context_match_info(void* item_types_void_ptr, void* author_ids_void_ptr,
                                                        void* t_author_id_void_ptr, void* item_ids_void_ptr,
                                                        void* t_item_id_void_ptr, void* res_void_ptr) {
  auto* item_types_ptr = static_cast<absl::Span<const int64_t>*>(item_types_void_ptr);
  if (KS_UNLIKELY(item_types_ptr == nullptr)) {
    return;
  }

  auto* author_ids_ptr = static_cast<absl::Span<const int64_t>*>(author_ids_void_ptr);
  if (KS_UNLIKELY(author_ids_ptr == nullptr)) {
    return;
  }

  auto* t_author_id_ptr = static_cast<absl::optional<int64_t>*>(t_author_id_void_ptr);
  if (KS_UNLIKELY(t_author_id_ptr == nullptr)) {
    return;
  }

  auto* item_ids_ptr = static_cast<absl::Span<const int64_t>*>(item_ids_void_ptr);
  if (KS_UNLIKELY(item_ids_ptr == nullptr)) {
    return;
  }

  auto* t_item_id_ptr = static_cast<absl::optional<int64_t>*>(t_item_id_void_ptr);
  if (KS_UNLIKELY(t_item_id_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_mix_rank_ecom_reco_context_match_info(*item_types_ptr, *author_ids_ptr, *t_author_id_ptr,
                                                       *item_ids_ptr, *t_item_id_ptr);
}

inline void c_get_diff_coupon_cnt(void* target_list_void_ptr, void* type_list_void_ptr,
                                  void* source_list_void_ptr, void* res_void_ptr) {
  auto* target_list_ptr = static_cast<absl::Span<const int64_t>*>(target_list_void_ptr);
  if (KS_UNLIKELY(target_list_ptr == nullptr)) {
    return;
  }

  auto* type_list_ptr = static_cast<absl::Span<const int64_t>*>(type_list_void_ptr);
  if (KS_UNLIKELY(type_list_ptr == nullptr)) {
    return;
  }

  auto* source_list_ptr = static_cast<absl::Span<const int64_t>*>(source_list_void_ptr);
  if (KS_UNLIKELY(source_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_diff_coupon_cnt(*target_list_ptr, *type_list_ptr, *source_list_ptr);
}

inline int64_t c_get_ecom_ocpc_action_type(int64_t ocpc_action_type) {
  return get_ecom_ocpc_action_type(ocpc_action_type);
}

inline int64_t c_get_kpin_live_recruit_in_show_cnt_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_live_recruit_in_show_cnt_bucket(*raw_input_ptr);
}

inline void c_get_photo_combine_features_app_stringview__size__ui64__optional_i64__optional_i64__stringview(
    void* x1_void_ptr, size_t x4, uint64_t x5, void* x6_void_ptr, void* x7_void_ptr, void* x8_void_ptr,
    void* res_void_ptr) {
  auto* x1_ptr = static_cast<absl::string_view*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* x6_ptr = static_cast<absl::optional<int64_t>*>(x6_void_ptr);
  if (KS_UNLIKELY(x6_ptr == nullptr)) {
    return;
  }

  auto* x7_ptr = static_cast<absl::optional<int64_t>*>(x7_void_ptr);
  if (KS_UNLIKELY(x7_ptr == nullptr)) {
    return;
  }

  auto* x8_ptr = static_cast<absl::string_view*>(x8_void_ptr);
  if (KS_UNLIKELY(x8_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_photo_combine_features_app(*x1_ptr, x4, x5, *x6_ptr, *x7_ptr, *x8_ptr);
}

inline void c_get_mix_rank_context_match_info(void* item_types_void_ptr, void* author_ids_void_ptr,
                                              void* t_author_id_void_ptr, void* item_ids_void_ptr,
                                              void* t_item_id_void_ptr, void* res_void_ptr) {
  auto* item_types_ptr = static_cast<absl::Span<const int64_t>*>(item_types_void_ptr);
  if (KS_UNLIKELY(item_types_ptr == nullptr)) {
    return;
  }

  auto* author_ids_ptr = static_cast<absl::Span<const int64_t>*>(author_ids_void_ptr);
  if (KS_UNLIKELY(author_ids_ptr == nullptr)) {
    return;
  }

  auto* t_author_id_ptr = static_cast<absl::optional<int64_t>*>(t_author_id_void_ptr);
  if (KS_UNLIKELY(t_author_id_ptr == nullptr)) {
    return;
  }

  auto* item_ids_ptr = static_cast<absl::Span<const int64_t>*>(item_ids_void_ptr);
  if (KS_UNLIKELY(item_ids_ptr == nullptr)) {
    return;
  }

  auto* t_item_id_ptr = static_cast<absl::optional<int64_t>*>(t_item_id_void_ptr);
  if (KS_UNLIKELY(t_item_id_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_mix_rank_context_match_info(*item_types_ptr, *author_ids_ptr, *t_author_id_ptr,
                                             *item_ids_ptr, *t_item_id_ptr);
}

inline void c_generate_rank_candidates_list_feature(void* rank_candidates_void_ptr,
                                                    void* prerank_scores_void_ptr, void* ad_types_void_ptr,
                                                    void* llsid_void_ptr, int32_t start_bit,
                                                    int32_t bit_length, int32_t is_outer,
                                                    int32_t ad_list_info, int32_t sort_type, int32_t max_cnt,
                                                    void* res_void_ptr) {
  auto* rank_candidates_ptr = static_cast<absl::Span<const int64_t>*>(rank_candidates_void_ptr);
  if (KS_UNLIKELY(rank_candidates_ptr == nullptr)) {
    return;
  }

  auto* prerank_scores_ptr = static_cast<absl::Span<const int64_t>*>(prerank_scores_void_ptr);
  if (KS_UNLIKELY(prerank_scores_ptr == nullptr)) {
    return;
  }

  auto* ad_types_ptr = static_cast<absl::Span<const int64_t>*>(ad_types_void_ptr);
  if (KS_UNLIKELY(ad_types_ptr == nullptr)) {
    return;
  }

  auto* llsid_ptr = static_cast<absl::optional<int64_t>*>(llsid_void_ptr);
  if (KS_UNLIKELY(llsid_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = generate_rank_candidates_list_feature(*rank_candidates_ptr, *prerank_scores_ptr, *ad_types_ptr,
                                                   *llsid_ptr, start_bit, bit_length, is_outer, ad_list_info,
                                                   sort_type, max_cnt);
}

inline void c_get_second_list(void* result_void_ptr, void* res_void_ptr) {
  auto* result_ptr = static_cast<std::vector<std::vector<int64_t>>*>(result_void_ptr);
  if (KS_UNLIKELY(result_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_second_list(*result_ptr);
}

inline void c_outer_loop_flag(void* interactive_form_void_ptr, void* sub_page_id_void_ptr,
                              void* ad_campaign_type_void_ptr, void* ocpc_action_type_void_ptr,
                              void* industry_id_void_ptr, void* item_type_void_ptr, void* res_void_ptr) {
  auto* interactive_form_ptr = static_cast<absl::optional<int64_t>*>(interactive_form_void_ptr);
  if (KS_UNLIKELY(interactive_form_ptr == nullptr)) {
    return;
  }

  auto* sub_page_id_ptr = static_cast<absl::optional<int64_t>*>(sub_page_id_void_ptr);
  if (KS_UNLIKELY(sub_page_id_ptr == nullptr)) {
    return;
  }

  auto* ad_campaign_type_ptr = static_cast<absl::optional<int64_t>*>(ad_campaign_type_void_ptr);
  if (KS_UNLIKELY(ad_campaign_type_ptr == nullptr)) {
    return;
  }

  auto* ocpc_action_type_ptr = static_cast<absl::optional<int64_t>*>(ocpc_action_type_void_ptr);
  if (KS_UNLIKELY(ocpc_action_type_ptr == nullptr)) {
    return;
  }

  auto* industry_id_ptr = static_cast<absl::optional<int64_t>*>(industry_id_void_ptr);
  if (KS_UNLIKELY(industry_id_ptr == nullptr)) {
    return;
  }

  auto* item_type_ptr = static_cast<absl::optional<int64_t>*>(item_type_void_ptr);
  if (KS_UNLIKELY(item_type_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = outer_loop_flag(*interactive_form_ptr, *sub_page_id_ptr, *ad_campaign_type_ptr,
                             *ocpc_action_type_ptr, *industry_id_ptr, *item_type_ptr);
}

inline void c_get_universe_date_diff_seq_with_max_len_and_timestamp(
    void* medium_id_seq_void_ptr, void* timestamp_seq_void_ptr, int32_t max_len, int64_t current_timestamp,
    int64_t timestamp_delta, void* res_void_ptr) {
  auto* medium_id_seq_ptr = static_cast<absl::Span<const int64_t>*>(medium_id_seq_void_ptr);
  if (KS_UNLIKELY(medium_id_seq_ptr == nullptr)) {
    return;
  }

  auto* timestamp_seq_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_seq_void_ptr);
  if (KS_UNLIKELY(timestamp_seq_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_universe_date_diff_seq_with_max_len_and_timestamp(
      *medium_id_seq_ptr, *timestamp_seq_ptr, max_len, current_timestamp, timestamp_delta);
}

inline void c_get_mix_rank_seq_id_x_pos_id(void* seq_ids_void_ptr, int64_t pos_id, void* res_void_ptr) {
  auto* seq_ids_ptr = static_cast<absl::Span<const int64_t>*>(seq_ids_void_ptr);
  if (KS_UNLIKELY(seq_ids_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_mix_rank_seq_id_x_pos_id(*seq_ids_ptr, pos_id);
}

inline void c_get_mix_rank_context_info(void* item_types_void_ptr, void* cxt_infos_void_ptr, bool only_ecom,
                                        void* res_void_ptr) {
  auto* item_types_ptr = static_cast<absl::Span<const int64_t>*>(item_types_void_ptr);
  if (KS_UNLIKELY(item_types_ptr == nullptr)) {
    return;
  }

  auto* cxt_infos_ptr = static_cast<absl::Span<const int64_t>*>(cxt_infos_void_ptr);
  if (KS_UNLIKELY(cxt_infos_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_mix_rank_context_info(*item_types_ptr, *cxt_infos_ptr, only_ecom);
}

inline int32_t c_value_or_i32__i32(int32_t x7, int32_t int_arg1) { return value_or(x7, int_arg1); }

inline void c_get_mix_rank_page_avg_item_time(void* time_v_void_ptr, void* seg_len_void_ptr, int64_t cur_time,    // NOLINT
                                              void* res_void_ptr) {
  auto* time_v_ptr = static_cast<absl::Span<const int64_t>*>(time_v_void_ptr);
  if (KS_UNLIKELY(time_v_ptr == nullptr)) {
    return;
  }

  auto* seg_len_ptr = static_cast<absl::Span<const int64_t>*>(seg_len_void_ptr);
  if (KS_UNLIKELY(seg_len_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_mix_rank_page_avg_item_time(*time_v_ptr, *seg_len_ptr, cur_time);
}

inline void c_get_wifi_bucket(void* wifi_value_void_ptr, void* res_void_ptr) {
  auto* wifi_value_ptr = static_cast<absl::optional<float>*>(wifi_value_void_ptr);
  if (KS_UNLIKELY(wifi_value_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_wifi_bucket(*wifi_value_ptr);
}

inline float c_get_single_product_price(void* price_list_void_ptr) {
  auto* price_list_ptr = static_cast<absl::Span<const int64_t>*>(price_list_void_ptr);
  if (KS_UNLIKELY(price_list_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return get_single_product_price(*price_list_ptr);
}

inline void c_account_conv_hack_pay_filter(int64_t account_id, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = account_conv_hack_pay_filter(account_id);
}

inline int32_t c_get_action_type_deep_old(uint64_t action_type) {
  return get_action_type_deep_old(action_type);
}

inline void c_get_hist_req_time_gap_ordered(void* time_v_void_ptr, int64_t cur_timestamp,
                                            void* res_void_ptr) {
  auto* time_v_ptr = static_cast<absl::Span<const int64_t>*>(time_v_void_ptr);
  if (KS_UNLIKELY(time_v_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_hist_req_time_gap_ordered(*time_v_ptr, cur_timestamp);
}

inline int64_t c_get_minimum_last_gap_timestamp_merge(void* room_pattern_list_dsp_void_ptr,
                                                      void* timestamp_list_dsp_void_ptr,
                                                      void* room_pattern_list_fanstop_void_ptr,
                                                      void* timestamp_list_fanstop_void_ptr,
                                                      int64_t adlog_timestamp,
                                                      void* room_pattern_type_void_ptr) {
  auto* room_pattern_list_dsp_ptr = static_cast<absl::Span<const int64_t>*>(room_pattern_list_dsp_void_ptr);
  if (KS_UNLIKELY(room_pattern_list_dsp_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* timestamp_list_dsp_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_dsp_void_ptr);
  if (KS_UNLIKELY(timestamp_list_dsp_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* room_pattern_list_fanstop_ptr =
      static_cast<absl::Span<const int64_t>*>(room_pattern_list_fanstop_void_ptr);
  if (KS_UNLIKELY(room_pattern_list_fanstop_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* timestamp_list_fanstop_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_fanstop_void_ptr);    // NOLINT
  if (KS_UNLIKELY(timestamp_list_fanstop_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* room_pattern_type_ptr = static_cast<RoomPatternType*>(room_pattern_type_void_ptr);
  if (KS_UNLIKELY(room_pattern_type_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_minimum_last_gap_timestamp_merge(*room_pattern_list_dsp_ptr, *timestamp_list_dsp_ptr,
                                              *room_pattern_list_fanstop_ptr, *timestamp_list_fanstop_ptr,
                                              adlog_timestamp, *room_pattern_type_ptr);
}

inline void c_get_user_hist_page_num_succ_cross_pos_id_platform(void* page_num_list_void_ptr,
                                                                void* cur_page_num_void_ptr,
                                                                void* pos_id_void_ptr,
                                                                void* platform_void_ptr, void* res_void_ptr) {    // NOLINT
  auto* page_num_list_ptr = static_cast<absl::Span<const int64_t>*>(page_num_list_void_ptr);
  if (KS_UNLIKELY(page_num_list_ptr == nullptr)) {
    return;
  }

  auto* cur_page_num_ptr = static_cast<absl::optional<int64_t>*>(cur_page_num_void_ptr);
  if (KS_UNLIKELY(cur_page_num_ptr == nullptr)) {
    return;
  }

  auto* pos_id_ptr = static_cast<absl::optional<int64_t>*>(pos_id_void_ptr);
  if (KS_UNLIKELY(pos_id_ptr == nullptr)) {
    return;
  }

  auto* platform_ptr = static_cast<absl::string_view*>(platform_void_ptr);
  if (KS_UNLIKELY(platform_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_hist_page_num_succ_cross_pos_id_platform(*page_num_list_ptr, *cur_page_num_ptr,
                                                               *pos_id_ptr, *platform_ptr);
}

inline void c_get_user_hist_page_num_ordered_cross_platform(void* page_num_list_void_ptr,
                                                            void* cur_page_num_void_ptr,
                                                            void* platform_void_ptr, void* res_void_ptr) {
  auto* page_num_list_ptr = static_cast<absl::Span<const int64_t>*>(page_num_list_void_ptr);
  if (KS_UNLIKELY(page_num_list_ptr == nullptr)) {
    return;
  }

  auto* cur_page_num_ptr = static_cast<absl::optional<int64_t>*>(cur_page_num_void_ptr);
  if (KS_UNLIKELY(cur_page_num_ptr == nullptr)) {
    return;
  }

  auto* platform_ptr = static_cast<absl::string_view*>(platform_void_ptr);
  if (KS_UNLIKELY(platform_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr =
      get_user_hist_page_num_ordered_cross_platform(*page_num_list_ptr, *cur_page_num_ptr, *platform_ptr);
}

inline void c_get_successive_time_gap_cross_pos_id(void* time_v_void_ptr, int64_t cur_timestamp,
                                                   void* pos_id_void_ptr, void* res_void_ptr) {
  auto* time_v_ptr = static_cast<absl::Span<const int64_t>*>(time_v_void_ptr);
  if (KS_UNLIKELY(time_v_ptr == nullptr)) {
    return;
  }

  auto* pos_id_ptr = static_cast<absl::optional<int64_t>*>(pos_id_void_ptr);
  if (KS_UNLIKELY(pos_id_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_successive_time_gap_cross_pos_id(*time_v_ptr, cur_timestamp, *pos_id_ptr);
}

inline void c_get_yellow_cart_goods_price(
    void* yellow_cart_goods_list_transaction_price_void_ptr, void* yellow_cart_goods_list_tag_price_void_ptr,
    void* live_author_price_24h_void_ptr, void* live_author_price_3d_void_ptr,
    void* live_author_price_14d_void_ptr, void* live_author_ocpc_roi_price_7d_void_ptr,
    void* live_author_ocpc_roas_price_7d_void_ptr, void* ocpc_action_type_void_ptr, void* top_n_void_ptr,
    void* res_void_ptr) {
  auto* yellow_cart_goods_list_transaction_price_ptr =
      static_cast<absl::Span<const int64_t>*>(yellow_cart_goods_list_transaction_price_void_ptr);
  if (KS_UNLIKELY(yellow_cart_goods_list_transaction_price_ptr == nullptr)) {
    return;
  }

  auto* yellow_cart_goods_list_tag_price_ptr =
      static_cast<absl::Span<const int64_t>*>(yellow_cart_goods_list_tag_price_void_ptr);
  if (KS_UNLIKELY(yellow_cart_goods_list_tag_price_ptr == nullptr)) {
    return;
  }

  auto* live_author_price_24h_ptr = static_cast<absl::optional<float>*>(live_author_price_24h_void_ptr);
  if (KS_UNLIKELY(live_author_price_24h_ptr == nullptr)) {
    return;
  }

  auto* live_author_price_3d_ptr = static_cast<absl::optional<float>*>(live_author_price_3d_void_ptr);
  if (KS_UNLIKELY(live_author_price_3d_ptr == nullptr)) {
    return;
  }

  auto* live_author_price_14d_ptr = static_cast<absl::optional<float>*>(live_author_price_14d_void_ptr);
  if (KS_UNLIKELY(live_author_price_14d_ptr == nullptr)) {
    return;
  }

  auto* live_author_ocpc_roi_price_7d_ptr =
      static_cast<absl::optional<float>*>(live_author_ocpc_roi_price_7d_void_ptr);
  if (KS_UNLIKELY(live_author_ocpc_roi_price_7d_ptr == nullptr)) {
    return;
  }

  auto* live_author_ocpc_roas_price_7d_ptr =
      static_cast<absl::optional<float>*>(live_author_ocpc_roas_price_7d_void_ptr);
  if (KS_UNLIKELY(live_author_ocpc_roas_price_7d_ptr == nullptr)) {
    return;
  }

  auto* ocpc_action_type_ptr = static_cast<absl::optional<int64_t>*>(ocpc_action_type_void_ptr);
  if (KS_UNLIKELY(ocpc_action_type_ptr == nullptr)) {
    return;
  }

  auto* top_n_ptr = static_cast<absl::optional<int64_t>*>(top_n_void_ptr);
  if (KS_UNLIKELY(top_n_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_yellow_cart_goods_price(
      *yellow_cart_goods_list_transaction_price_ptr, *yellow_cart_goods_list_tag_price_ptr,
      *live_author_price_24h_ptr, *live_author_price_3d_ptr, *live_author_price_14d_ptr,
      *live_author_ocpc_roi_price_7d_ptr, *live_author_ocpc_roas_price_7d_ptr, *ocpc_action_type_ptr,
      *top_n_ptr);
}

inline void c_get_hist_req_time_gap_cross_platform(void* hist_req_timestamp_list_void_ptr,
                                                   int64_t cur_timestamp, void* platform_void_ptr,
                                                   void* res_void_ptr) {
  auto* hist_req_timestamp_list_ptr =
      static_cast<absl::Span<const int64_t>*>(hist_req_timestamp_list_void_ptr);
  if (KS_UNLIKELY(hist_req_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* platform_ptr = static_cast<absl::string_view*>(platform_void_ptr);
  if (KS_UNLIKELY(platform_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_hist_req_time_gap_cross_platform(*hist_req_timestamp_list_ptr, cur_timestamp, *platform_ptr);    // NOLINT
}

inline int64_t c_get_price_bin(int64_t price) { return get_price_bin(price); }

inline int64_t c_get_kpin_recruit_card_show_cnt_90d_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_recruit_card_show_cnt_90d_bucket(*raw_input_ptr);
}

inline void c_filter_list_by_time_diff_thresh_idx(void* target_list_void_ptr, void* timestamp_list_void_ptr,
                                                  size_t max_seq_len, int64_t adlog_time, int64_t diff_thresh,    // NOLINT
                                                  bool cut_back, void* res_void_ptr) {
  auto* target_list_ptr = static_cast<absl::Span<const int64_t>*>(target_list_void_ptr);
  if (KS_UNLIKELY(target_list_ptr == nullptr)) {
    return;
  }

  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = filter_list_by_time_diff_thresh_idx(*target_list_ptr, *timestamp_list_ptr, max_seq_len,
                                                 adlog_time, diff_thresh, cut_back);
}

inline int32_t c_get_time_idx_max_3m(int64_t time_diff, size_t bucket_size) {
  return get_time_idx_max_3m(time_diff, bucket_size);
}

inline void c_get_hist_req_time_gap_cross_pos_id(void* hist_req_timestamp_list_void_ptr,
                                                 int64_t cur_timestamp, void* pos_id_void_ptr,
                                                 void* res_void_ptr) {
  auto* hist_req_timestamp_list_ptr =
      static_cast<absl::Span<const int64_t>*>(hist_req_timestamp_list_void_ptr);
  if (KS_UNLIKELY(hist_req_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* pos_id_ptr = static_cast<absl::optional<int64_t>*>(pos_id_void_ptr);
  if (KS_UNLIKELY(pos_id_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_hist_req_time_gap_cross_pos_id(*hist_req_timestamp_list_ptr, cur_timestamp, *pos_id_ptr);
}

inline size_t c_get_from_kconf_sdpa_map(int64_t account_id, void* default_sdpa_name_void_ptr, bool is_train) {    // NOLINT
  auto* default_sdpa_name_ptr = static_cast<absl::optional<int64_t>*>(default_sdpa_name_void_ptr);
  if (KS_UNLIKELY(default_sdpa_name_ptr == nullptr)) {
    return static_cast<size_t>(0);
  }

  return get_from_kconf_sdpa_map(account_id, *default_sdpa_name_ptr, is_train);
}

inline void c_get_hist_req_sub_page_id_ordered(void* hist_sub_page_ids_void_ptr, void* res_void_ptr) {
  auto* hist_sub_page_ids_ptr = static_cast<absl::Span<const int64_t>*>(hist_sub_page_ids_void_ptr);
  if (KS_UNLIKELY(hist_sub_page_ids_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_hist_req_sub_page_id_ordered(*hist_sub_page_ids_ptr);
}

inline int64_t c_price_level_map(void* attr_void_ptr) {
  auto* attr_ptr = static_cast<absl::optional<int64_t>*>(attr_void_ptr);
  if (KS_UNLIKELY(attr_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return price_level_map(*attr_ptr);
}

inline void c_get_successive_time_gap(void* time_v_void_ptr, int64_t cur_timestamp, void* res_void_ptr) {
  auto* time_v_ptr = static_cast<absl::Span<const int64_t>*>(time_v_void_ptr);
  if (KS_UNLIKELY(time_v_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_successive_time_gap(*time_v_ptr, cur_timestamp);
}

inline void c_add_feature_result_ui64__vector_extractresult(uint64_t x1, void* result_void_ptr) {
  auto* result_ptr = static_cast<std::vector<ExtractResult>*>(result_void_ptr);
  if (KS_UNLIKELY(result_ptr == nullptr)) {
    return;
  }

  return add_feature_result(x1, result_ptr);
}

inline void c_get_hist_req_time_gap(void* hist_req_timestamp_list_void_ptr, int64_t cur_timestamp,
                                    void* res_void_ptr) {
  auto* hist_req_timestamp_list_ptr =
      static_cast<absl::Span<const int64_t>*>(hist_req_timestamp_list_void_ptr);
  if (KS_UNLIKELY(hist_req_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_hist_req_time_gap(*hist_req_timestamp_list_ptr, cur_timestamp);
}

inline void c_get_successive_time_gap_cross_platform(void* time_v_void_ptr, int64_t cur_timestamp,
                                                     void* platform_void_ptr, void* res_void_ptr) {
  auto* time_v_ptr = static_cast<absl::Span<const int64_t>*>(time_v_void_ptr);
  if (KS_UNLIKELY(time_v_ptr == nullptr)) {
    return;
  }

  auto* platform_ptr = static_cast<absl::string_view*>(platform_void_ptr);
  if (KS_UNLIKELY(platform_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_successive_time_gap_cross_platform(*time_v_ptr, cur_timestamp, *platform_ptr);
}

inline void c_get_live_realtime_acc_cnt2(void* play_time_void_ptr, void* live_acc_std_lps_cnt_void_ptr,
                                         void* live_acc_pay_cnt_void_ptr,
                                         void* photo_acc_std_lps_cnt_void_ptr,
                                         void* photo_acc_pay_cnt_void_ptr, void* res_void_ptr) {
  auto* play_time_ptr = static_cast<absl::optional<int64_t>*>(play_time_void_ptr);
  if (KS_UNLIKELY(play_time_ptr == nullptr)) {
    return;
  }

  auto* live_acc_std_lps_cnt_ptr = static_cast<absl::optional<int64_t>*>(live_acc_std_lps_cnt_void_ptr);
  if (KS_UNLIKELY(live_acc_std_lps_cnt_ptr == nullptr)) {
    return;
  }

  auto* live_acc_pay_cnt_ptr = static_cast<absl::optional<int64_t>*>(live_acc_pay_cnt_void_ptr);
  if (KS_UNLIKELY(live_acc_pay_cnt_ptr == nullptr)) {
    return;
  }

  auto* photo_acc_std_lps_cnt_ptr = static_cast<absl::optional<int64_t>*>(photo_acc_std_lps_cnt_void_ptr);
  if (KS_UNLIKELY(photo_acc_std_lps_cnt_ptr == nullptr)) {
    return;
  }

  auto* photo_acc_pay_cnt_ptr = static_cast<absl::optional<int64_t>*>(photo_acc_pay_cnt_void_ptr);
  if (KS_UNLIKELY(photo_acc_pay_cnt_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_realtime_acc_cnt2(*play_time_ptr, *live_acc_std_lps_cnt_ptr, *live_acc_pay_cnt_ptr,
                                        *photo_acc_std_lps_cnt_ptr, *photo_acc_pay_cnt_ptr);
}

inline void c_one_hot_i64__i32(int64_t x4, int32_t int_arg1, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = one_hot(x4, int_arg1);
}

inline void c_one_hot_optional_i64__i32(void* x4_void_ptr, int32_t int_arg1, void* res_void_ptr) {
  auto* x4_ptr = static_cast<absl::optional<int64_t>*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = one_hot(*x4_ptr, int_arg1);
}

inline void
c_merge_float_list_all_vector_f32__vector_f32__vector_f32__vector_f32__vector_f32__vector_f32__vector_f32__vector_f32(    // NOLINT
    void* x4_void_ptr, void* x8_void_ptr, void* x12_void_ptr, void* x16_void_ptr, void* x20_void_ptr,
    void* x24_void_ptr, void* x28_void_ptr, void* x32_void_ptr, void* res_void_ptr) {
  auto* x4_ptr = static_cast<std::vector<float>*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return;
  }

  auto* x8_ptr = static_cast<std::vector<float>*>(x8_void_ptr);
  if (KS_UNLIKELY(x8_ptr == nullptr)) {
    return;
  }

  auto* x12_ptr = static_cast<std::vector<float>*>(x12_void_ptr);
  if (KS_UNLIKELY(x12_ptr == nullptr)) {
    return;
  }

  auto* x16_ptr = static_cast<std::vector<float>*>(x16_void_ptr);
  if (KS_UNLIKELY(x16_ptr == nullptr)) {
    return;
  }

  auto* x20_ptr = static_cast<std::vector<float>*>(x20_void_ptr);
  if (KS_UNLIKELY(x20_ptr == nullptr)) {
    return;
  }

  auto* x24_ptr = static_cast<std::vector<float>*>(x24_void_ptr);
  if (KS_UNLIKELY(x24_ptr == nullptr)) {
    return;
  }

  auto* x28_ptr = static_cast<std::vector<float>*>(x28_void_ptr);
  if (KS_UNLIKELY(x28_ptr == nullptr)) {
    return;
  }

  auto* x32_ptr = static_cast<std::vector<float>*>(x32_void_ptr);
  if (KS_UNLIKELY(x32_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr =
      merge_float_list_all(*x4_ptr, *x8_ptr, *x12_ptr, *x16_ptr, *x20_ptr, *x24_ptr, *x28_ptr, *x32_ptr);
}

inline void c_get_mix_rank_ecom_reco_context_info(void* item_types_void_ptr, void* is_lives_void_ptr,
                                                  void* item_ids_void_ptr, bool keep_live,
                                                  void* res_void_ptr) {
  auto* item_types_ptr = static_cast<absl::Span<const int64_t>*>(item_types_void_ptr);
  if (KS_UNLIKELY(item_types_ptr == nullptr)) {
    return;
  }

  auto* is_lives_ptr = static_cast<absl::Span<const int64_t>*>(is_lives_void_ptr);
  if (KS_UNLIKELY(is_lives_ptr == nullptr)) {
    return;
  }

  auto* item_ids_ptr = static_cast<absl::Span<const int64_t>*>(item_ids_void_ptr);
  if (KS_UNLIKELY(item_ids_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_mix_rank_ecom_reco_context_info(*item_types_ptr, *is_lives_ptr, *item_ids_ptr, keep_live);
}

inline void c_get_realtime_user_attr_item_attr_list_cross(void* item_attr_list_void_ptr,
                                                          void* user_attr_1_void_ptr,
                                                          void* user_attr_2_void_ptr,
                                                          void* user_attr_3_void_ptr, void* res_void_ptr) {
  auto* item_attr_list_ptr = static_cast<absl::Span<const int64_t>*>(item_attr_list_void_ptr);
  if (KS_UNLIKELY(item_attr_list_ptr == nullptr)) {
    return;
  }

  auto* user_attr_1_ptr = static_cast<absl::string_view*>(user_attr_1_void_ptr);
  if (KS_UNLIKELY(user_attr_1_ptr == nullptr)) {
    return;
  }

  auto* user_attr_2_ptr = static_cast<absl::string_view*>(user_attr_2_void_ptr);
  if (KS_UNLIKELY(user_attr_2_ptr == nullptr)) {
    return;
  }

  auto* user_attr_3_ptr = static_cast<absl::optional<const absl::string_view>*>(user_attr_3_void_ptr);
  if (KS_UNLIKELY(user_attr_3_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_realtime_user_attr_item_attr_list_cross(*item_attr_list_ptr, *user_attr_1_ptr,
                                                         *user_attr_2_ptr, *user_attr_3_ptr);
}

inline void c_get_realtime_target_id_match_ratio(void* attr_list_void_ptr, int64_t target_id,
                                                 void* res_void_ptr) {
  auto* attr_list_ptr = static_cast<absl::Span<const int64_t>*>(attr_list_void_ptr);
  if (KS_UNLIKELY(attr_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_realtime_target_id_match_ratio(*attr_list_ptr, target_id);
}

inline void c_merge_float_list_all_vector_f32__vector_f32__vector_f32(void* x7_void_ptr, void* x14_void_ptr,
                                                                      void* x23_void_ptr,
                                                                      void* res_void_ptr) {
  auto* x7_ptr = static_cast<std::vector<float>*>(x7_void_ptr);
  if (KS_UNLIKELY(x7_ptr == nullptr)) {
    return;
  }

  auto* x14_ptr = static_cast<std::vector<float>*>(x14_void_ptr);
  if (KS_UNLIKELY(x14_ptr == nullptr)) {
    return;
  }

  auto* x23_ptr = static_cast<std::vector<float>*>(x23_void_ptr);
  if (KS_UNLIKELY(x23_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_all(*x7_ptr, *x14_ptr, *x23_ptr);
}

inline void c_merge_float_list_all_vector_f32__vector_f32__vector_f32__vector_f32(
    void* x2_void_ptr, void* x4_void_ptr, void* x6_void_ptr, void* x8_void_ptr, void* res_void_ptr) {
  auto* x2_ptr = static_cast<std::vector<float>*>(x2_void_ptr);
  if (KS_UNLIKELY(x2_ptr == nullptr)) {
    return;
  }

  auto* x4_ptr = static_cast<std::vector<float>*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return;
  }

  auto* x6_ptr = static_cast<std::vector<float>*>(x6_void_ptr);
  if (KS_UNLIKELY(x6_ptr == nullptr)) {
    return;
  }

  auto* x8_ptr = static_cast<std::vector<float>*>(x8_void_ptr);
  if (KS_UNLIKELY(x8_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_all(*x2_ptr, *x4_ptr, *x6_ptr, *x8_ptr);
}

inline void
c_merge_float_list_all_vector_f32__vector_f32__vector_f32__vector_f32__vector_f32__vector_f32__vector_f32(
    void* x5_void_ptr, void* x10_void_ptr, void* x15_void_ptr, void* x20_void_ptr, void* x25_void_ptr,
    void* x30_void_ptr, void* x35_void_ptr, void* res_void_ptr) {
  auto* x5_ptr = static_cast<std::vector<float>*>(x5_void_ptr);
  if (KS_UNLIKELY(x5_ptr == nullptr)) {
    return;
  }

  auto* x10_ptr = static_cast<std::vector<float>*>(x10_void_ptr);
  if (KS_UNLIKELY(x10_ptr == nullptr)) {
    return;
  }

  auto* x15_ptr = static_cast<std::vector<float>*>(x15_void_ptr);
  if (KS_UNLIKELY(x15_ptr == nullptr)) {
    return;
  }

  auto* x20_ptr = static_cast<std::vector<float>*>(x20_void_ptr);
  if (KS_UNLIKELY(x20_ptr == nullptr)) {
    return;
  }

  auto* x25_ptr = static_cast<std::vector<float>*>(x25_void_ptr);
  if (KS_UNLIKELY(x25_ptr == nullptr)) {
    return;
  }

  auto* x30_ptr = static_cast<std::vector<float>*>(x30_void_ptr);
  if (KS_UNLIKELY(x30_ptr == nullptr)) {
    return;
  }

  auto* x35_ptr = static_cast<std::vector<float>*>(x35_void_ptr);
  if (KS_UNLIKELY(x35_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_all(*x5_ptr, *x10_ptr, *x15_ptr, *x20_ptr, *x25_ptr, *x30_ptr, *x35_ptr);
}

inline int64_t c_define_series_type(int64_t allow_bigcard, int64_t sub_page_id) {
  return define_series_type(allow_bigcard, sub_page_id);
}

inline int64_t c_add(int64_t n, int64_t m) { return add(n, m); }

inline float c_check_account_selection(int64_t account_id) { return check_account_selection(account_id); }

inline void c_merge_float_list_all_vector_f32__vector_f32__vector_f32__vector_f32__vector_f32(
    void* x4_void_ptr, void* x10_void_ptr, void* x14_void_ptr, void* x18_void_ptr, void* x22_void_ptr,
    void* res_void_ptr) {
  auto* x4_ptr = static_cast<std::vector<float>*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return;
  }

  auto* x10_ptr = static_cast<std::vector<float>*>(x10_void_ptr);
  if (KS_UNLIKELY(x10_ptr == nullptr)) {
    return;
  }

  auto* x14_ptr = static_cast<std::vector<float>*>(x14_void_ptr);
  if (KS_UNLIKELY(x14_ptr == nullptr)) {
    return;
  }

  auto* x18_ptr = static_cast<std::vector<float>*>(x18_void_ptr);
  if (KS_UNLIKELY(x18_ptr == nullptr)) {
    return;
  }

  auto* x22_ptr = static_cast<std::vector<float>*>(x22_void_ptr);
  if (KS_UNLIKELY(x22_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_all(*x4_ptr, *x10_ptr, *x14_ptr, *x18_ptr, *x22_ptr);
}

inline void c_merge_float_list_all_f32__f32__f32(float x3, float x6, float x9, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_all(x3, x6, x9);
}

inline void c_merge_float_list_all_f32__f32__f32__f32__f32__f32__f32__f32__f32__f32__f32__f32(
    float x4, float x8, float x12, float x16, float x20, float x24, float x28, float x32, float x36,
    float x40, float x44, float x48, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_all(x4, x8, x12, x16, x20, x24, x28, x32, x36, x40, x44, x48);
}

inline void c_merge_float_list_all_ui64__ui64__i64__i64__ui64__optional_i64__optional_i64__optional_i64(
    uint64_t x1, uint64_t x2, int64_t x3, int64_t x5, uint64_t x6, void* x7_void_ptr, void* x8_void_ptr,
    void* x9_void_ptr, void* res_void_ptr) {
  auto* x7_ptr = static_cast<absl::optional<int64_t>*>(x7_void_ptr);
  if (KS_UNLIKELY(x7_ptr == nullptr)) {
    return;
  }

  auto* x8_ptr = static_cast<absl::optional<int64_t>*>(x8_void_ptr);
  if (KS_UNLIKELY(x8_ptr == nullptr)) {
    return;
  }

  auto* x9_ptr = static_cast<absl::optional<int64_t>*>(x9_void_ptr);
  if (KS_UNLIKELY(x9_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_all(x1, x2, x3, x5, x6, *x7_ptr, *x8_ptr, *x9_ptr);
}

inline void c_merge_float_list_all_f32__f32(float x2, float x4, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_all(x2, x4);
}

inline void c_merge_float_list_all_vector_f32(void* x5_void_ptr, void* res_void_ptr) {
  auto* x5_ptr = static_cast<std::vector<float>*>(x5_void_ptr);
  if (KS_UNLIKELY(x5_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_all(*x5_ptr);
}

inline void c_merge_float_list_all_f32__f32__f32__f32__f32(float x2, float x4, float x6, float x8, float x10,
                                                           void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_all(x2, x4, x6, x8, x10);
}

inline void c_merge_float_list_all_vector_f32__vector_f32__vector_f32__vector_f32__vector_f32__vector_f32(
    void* x2_void_ptr, void* x4_void_ptr, void* x6_void_ptr, void* x8_void_ptr, void* x10_void_ptr,
    void* x12_void_ptr, void* res_void_ptr) {
  auto* x2_ptr = static_cast<std::vector<float>*>(x2_void_ptr);
  if (KS_UNLIKELY(x2_ptr == nullptr)) {
    return;
  }

  auto* x4_ptr = static_cast<std::vector<float>*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return;
  }

  auto* x6_ptr = static_cast<std::vector<float>*>(x6_void_ptr);
  if (KS_UNLIKELY(x6_ptr == nullptr)) {
    return;
  }

  auto* x8_ptr = static_cast<std::vector<float>*>(x8_void_ptr);
  if (KS_UNLIKELY(x8_ptr == nullptr)) {
    return;
  }

  auto* x10_ptr = static_cast<std::vector<float>*>(x10_void_ptr);
  if (KS_UNLIKELY(x10_ptr == nullptr)) {
    return;
  }

  auto* x12_ptr = static_cast<std::vector<float>*>(x12_void_ptr);
  if (KS_UNLIKELY(x12_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_all(*x2_ptr, *x4_ptr, *x6_ptr, *x8_ptr, *x10_ptr, *x12_ptr);
}

inline void
c_merge_float_list_all_f32__f32__f32__f32__f32__f32__f32__f32__f32__f32__f32__f32__f32__f32__f32__f32(
    float x2, float x4, float x5, float x6, float x10, float x14, float x18, float x22, float x26, float x30,
    float x34, float x38, float x42, float x46, float x50, float x54, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_all(x2, x4, x5, x6, x10, x14, x18, x22, x26, x30, x34, x38, x42, x46, x50, x54);    // NOLINT
}

inline void c_get_ts_list_dense_ple_helper(int64_t current_ts, void* ts_list_void_ptr, void* bins_void_ptr,
                                           size_t max_len, void* res_void_ptr) {
  auto* ts_list_ptr = static_cast<absl::Span<const int64_t>*>(ts_list_void_ptr);
  if (KS_UNLIKELY(ts_list_ptr == nullptr)) {
    return;
  }

  auto* bins_ptr = static_cast<std::vector<float>*>(bins_void_ptr);
  if (KS_UNLIKELY(bins_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_ts_list_dense_ple_helper(current_ts, *ts_list_ptr, *bins_ptr, max_len);
}

inline void c_get_active_sum_bucket(void* active_sum_time_void_ptr, void* res_void_ptr) {
  auto* active_sum_time_ptr = static_cast<absl::optional<int64_t>*>(active_sum_time_void_ptr);
  if (KS_UNLIKELY(active_sum_time_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_active_sum_bucket(*active_sum_time_ptr);
}

inline float c_cast_to_float_with_default_f32__i32(float x7, int32_t int_arg1) {
  return cast_to_float_with_default(x7, int_arg1);
}

inline float c_cast_to_float_with_default_optional_f32__i32(void* x28_void_ptr, int32_t int_arg1) {
  auto* x28_ptr = static_cast<absl::optional<float>*>(x28_void_ptr);
  if (KS_UNLIKELY(x28_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return cast_to_float_with_default(*x28_ptr, int_arg1);
}

inline int64_t c_get_two_value_ratio_bucket(int64_t ele1, int64_t ele2) {
  return get_two_value_ratio_bucket(ele1, ele2);
}

inline void c_get_user_pay_ability(void* order_times_void_ptr, void* total_fee_void_ptr,
                                   void* mid_price_void_ptr, void* res_void_ptr) {
  auto* order_times_ptr = static_cast<absl::optional<int64_t>*>(order_times_void_ptr);
  if (KS_UNLIKELY(order_times_ptr == nullptr)) {
    return;
  }

  auto* total_fee_ptr = static_cast<absl::optional<int64_t>*>(total_fee_void_ptr);
  if (KS_UNLIKELY(total_fee_ptr == nullptr)) {
    return;
  }

  auto* mid_price_ptr = static_cast<absl::optional<int64_t>*>(mid_price_void_ptr);
  if (KS_UNLIKELY(mid_price_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_pay_ability(*order_times_ptr, *total_fee_ptr, *mid_price_ptr);
}

inline void c_cast_to_float_vector_i64(void* x6_void_ptr, void* res_void_ptr) {
  auto* x6_ptr = static_cast<std::vector<int64_t>*>(x6_void_ptr);
  if (KS_UNLIKELY(x6_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = cast_to_float(*x6_ptr);
}

inline void c_cast_to_float_span_i64(void* x1_void_ptr, void* res_void_ptr) {
  auto* x1_ptr = static_cast<absl::Span<const int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = cast_to_float(*x1_ptr);
}

inline void c_get_user_hist_page_num_gap_ordered_span_i64__optional_i64(void* x1_void_ptr, void* x2_void_ptr,
                                                                        void* res_void_ptr) {
  auto* x1_ptr = static_cast<absl::Span<const int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* x2_ptr = static_cast<absl::optional<int64_t>*>(x2_void_ptr);
  if (KS_UNLIKELY(x2_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_hist_page_num_gap_ordered(*x1_ptr, *x2_ptr);
}

inline float c_cast_to_float_optional_i64(void* x1_void_ptr) {
  auto* x1_ptr = static_cast<absl::optional<int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return cast_to_float(*x1_ptr);
}

inline float c_cast_to_float_i32(int32_t x1) { return cast_to_float(x1); }

inline int64_t c_get_first_non_zero(int64_t a, int64_t b) { return get_first_non_zero(a, b); }

inline float c_get_two_value_ratio(int64_t ele1, int64_t ele2) { return get_two_value_ratio(ele1, ele2); }

inline float c_cast_to_float_ui64(uint64_t x1) { return cast_to_float(x1); }

inline void
c_combine_industry_action_diff_list_span_i64__span_i64__span_i64__span_i64__span_i64__span_i64__span_i64__span_i64__span_i64__span_i64__span_i64__span_i64__span_i64__span_i64__span_i64__span_i64__span_i64__span_i64__span_i64__span_i64__ui64__ui64(    // NOLINT
    void* x1_void_ptr, void* x2_void_ptr, void* x3_void_ptr, void* x4_void_ptr, void* x5_void_ptr,
    void* x6_void_ptr, void* x7_void_ptr, void* x8_void_ptr, void* x9_void_ptr, void* x10_void_ptr,
    void* x11_void_ptr, void* x12_void_ptr, void* x13_void_ptr, void* x14_void_ptr, void* x15_void_ptr,
    void* x16_void_ptr, void* x17_void_ptr, void* x18_void_ptr, void* x19_void_ptr, void* x20_void_ptr,
    uint64_t x21, uint64_t x22, void* res_void_ptr) {
  auto* x1_ptr = static_cast<absl::Span<const int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* x2_ptr = static_cast<absl::Span<const int64_t>*>(x2_void_ptr);
  if (KS_UNLIKELY(x2_ptr == nullptr)) {
    return;
  }

  auto* x3_ptr = static_cast<absl::Span<const int64_t>*>(x3_void_ptr);
  if (KS_UNLIKELY(x3_ptr == nullptr)) {
    return;
  }

  auto* x4_ptr = static_cast<absl::Span<const int64_t>*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return;
  }

  auto* x5_ptr = static_cast<absl::Span<const int64_t>*>(x5_void_ptr);
  if (KS_UNLIKELY(x5_ptr == nullptr)) {
    return;
  }

  auto* x6_ptr = static_cast<absl::Span<const int64_t>*>(x6_void_ptr);
  if (KS_UNLIKELY(x6_ptr == nullptr)) {
    return;
  }

  auto* x7_ptr = static_cast<absl::Span<const int64_t>*>(x7_void_ptr);
  if (KS_UNLIKELY(x7_ptr == nullptr)) {
    return;
  }

  auto* x8_ptr = static_cast<absl::Span<const int64_t>*>(x8_void_ptr);
  if (KS_UNLIKELY(x8_ptr == nullptr)) {
    return;
  }

  auto* x9_ptr = static_cast<absl::Span<const int64_t>*>(x9_void_ptr);
  if (KS_UNLIKELY(x9_ptr == nullptr)) {
    return;
  }

  auto* x10_ptr = static_cast<absl::Span<const int64_t>*>(x10_void_ptr);
  if (KS_UNLIKELY(x10_ptr == nullptr)) {
    return;
  }

  auto* x11_ptr = static_cast<absl::Span<const int64_t>*>(x11_void_ptr);
  if (KS_UNLIKELY(x11_ptr == nullptr)) {
    return;
  }

  auto* x12_ptr = static_cast<absl::Span<const int64_t>*>(x12_void_ptr);
  if (KS_UNLIKELY(x12_ptr == nullptr)) {
    return;
  }

  auto* x13_ptr = static_cast<absl::Span<const int64_t>*>(x13_void_ptr);
  if (KS_UNLIKELY(x13_ptr == nullptr)) {
    return;
  }

  auto* x14_ptr = static_cast<absl::Span<const int64_t>*>(x14_void_ptr);
  if (KS_UNLIKELY(x14_ptr == nullptr)) {
    return;
  }

  auto* x15_ptr = static_cast<absl::Span<const int64_t>*>(x15_void_ptr);
  if (KS_UNLIKELY(x15_ptr == nullptr)) {
    return;
  }

  auto* x16_ptr = static_cast<absl::Span<const int64_t>*>(x16_void_ptr);
  if (KS_UNLIKELY(x16_ptr == nullptr)) {
    return;
  }

  auto* x17_ptr = static_cast<absl::Span<const int64_t>*>(x17_void_ptr);
  if (KS_UNLIKELY(x17_ptr == nullptr)) {
    return;
  }

  auto* x18_ptr = static_cast<absl::Span<const int64_t>*>(x18_void_ptr);
  if (KS_UNLIKELY(x18_ptr == nullptr)) {
    return;
  }

  auto* x19_ptr = static_cast<absl::Span<const int64_t>*>(x19_void_ptr);
  if (KS_UNLIKELY(x19_ptr == nullptr)) {
    return;
  }

  auto* x20_ptr = static_cast<absl::Span<const int64_t>*>(x20_void_ptr);
  if (KS_UNLIKELY(x20_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = combine_industry_action_diff_list(
      *x1_ptr, *x2_ptr, *x3_ptr, *x4_ptr, *x5_ptr, *x6_ptr, *x7_ptr, *x8_ptr, *x9_ptr, *x10_ptr, *x11_ptr,
      *x12_ptr, *x13_ptr, *x14_ptr, *x15_ptr, *x16_ptr, *x17_ptr, *x18_ptr, *x19_ptr, *x20_ptr, x21, x22);
}

inline void c_get_live_id_vanilla(int64_t live_id, bool is_train, void* play_duration_void_ptr,
                                  void* res_void_ptr) {
  auto* play_duration_ptr = static_cast<absl::optional<int64_t>*>(play_duration_void_ptr);
  if (KS_UNLIKELY(play_duration_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_id_vanilla(live_id, is_train, *play_duration_ptr);
}

inline int64_t c_combine_3_i64__i64__i64(int64_t x3, int64_t x6, int64_t x9) { return combine_3(x3, x6, x9); }    // NOLINT

inline int64_t c_combine_3_optional_i64__optional_i64__optional_i64(void* x1_void_ptr, void* x2_void_ptr,
                                                                    void* x4_void_ptr) {
  auto* x1_ptr = static_cast<absl::optional<int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* x2_ptr = static_cast<absl::optional<int64_t>*>(x2_void_ptr);
  if (KS_UNLIKELY(x2_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* x4_ptr = static_cast<absl::optional<int64_t>*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return combine_3(*x1_ptr, *x2_ptr, *x4_ptr);
}

inline void c_get_user_author_unique_bins(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_author_unique_bins();
}

inline void c_get_live_realtime_extreme_price(void* gmv_list_void_ptr, void* sku_cnt_list_void_ptr,
                                              void* timestamp_list_void_ptr, void* res_void_ptr) {
  auto* gmv_list_ptr = static_cast<absl::Span<const int64_t>*>(gmv_list_void_ptr);
  if (KS_UNLIKELY(gmv_list_ptr == nullptr)) {
    return;
  }

  auto* sku_cnt_list_ptr = static_cast<absl::Span<const int64_t>*>(sku_cnt_list_void_ptr);
  if (KS_UNLIKELY(sku_cnt_list_ptr == nullptr)) {
    return;
  }

  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_realtime_extreme_price(*gmv_list_ptr, *sku_cnt_list_ptr, *timestamp_list_ptr);
}

inline float c_flt_eps() { return flt_eps(); }

inline int64_t c_min(int64_t n, int64_t m) { return min(n, m); }

inline int32_t c_get_time_idx(uint64_t time_diff, size_t bucket_size) {
  return get_time_idx(time_diff, bucket_size);
}

inline void c_get_live_yellow_car_len_bins(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_yellow_car_len_bins();
}

inline int64_t c_mod(int64_t n, int64_t m) { return mod(n, m); }

inline int64_t c_get_search_source(int64_t entry_source, void* from_page_void_ptr) {
  auto* from_page_ptr = static_cast<absl::optional<int64_t>*>(from_page_void_ptr);
  if (KS_UNLIKELY(from_page_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_search_source(entry_source, *from_page_ptr);
}

inline void c_get_merchant_matched_target_id_list(void* matched_target_id_list_void_ptr,
                                                  void* target_id_list_void_ptr, int64_t matched_cnt,
                                                  void* res_void_ptr) {
  auto* matched_target_id_list_ptr = static_cast<std::vector<int64_t>*>(matched_target_id_list_void_ptr);
  if (KS_UNLIKELY(matched_target_id_list_ptr == nullptr)) {
    return;
  }

  auto* target_id_list_ptr = static_cast<std::vector<int64_t>*>(target_id_list_void_ptr);
  if (KS_UNLIKELY(target_id_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr =
      get_merchant_matched_target_id_list(*matched_target_id_list_ptr, *target_id_list_ptr, matched_cnt);
}

inline void c_get_cnt_diff_with_target_hit_combine(void* action_list_void_ptr, void* timestamp_list_void_ptr,
                                                   int64_t target_id, int64_t timestamp, void* res_void_ptr) {    // NOLINT
  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr =
      get_cnt_diff_with_target_hit_combine(*action_list_ptr, *timestamp_list_ptr, target_id, timestamp);
}

inline void c_get_diff_action_from_two_list(
    void* first_timestamp_list_void_ptr, void* first_photo_list_void_ptr, void* first_industry_list_void_ptr,
    void* first_ocpc_list_void_ptr, void* second_timestamp_list_void_ptr, void* second_photo_list_void_ptr,
    void* second_industry_list_void_ptr, void* second_ocpc_list_void_ptr, int32_t process_time,
    int32_t period, void* res_void_ptr) {
  auto* first_timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(first_timestamp_list_void_ptr);
  if (KS_UNLIKELY(first_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* first_photo_list_ptr = static_cast<absl::Span<const int64_t>*>(first_photo_list_void_ptr);
  if (KS_UNLIKELY(first_photo_list_ptr == nullptr)) {
    return;
  }

  auto* first_industry_list_ptr = static_cast<absl::Span<const int64_t>*>(first_industry_list_void_ptr);
  if (KS_UNLIKELY(first_industry_list_ptr == nullptr)) {
    return;
  }

  auto* first_ocpc_list_ptr = static_cast<absl::Span<const int64_t>*>(first_ocpc_list_void_ptr);
  if (KS_UNLIKELY(first_ocpc_list_ptr == nullptr)) {
    return;
  }

  auto* second_timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(second_timestamp_list_void_ptr);
  if (KS_UNLIKELY(second_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* second_photo_list_ptr = static_cast<absl::Span<const int64_t>*>(second_photo_list_void_ptr);
  if (KS_UNLIKELY(second_photo_list_ptr == nullptr)) {
    return;
  }

  auto* second_industry_list_ptr = static_cast<absl::Span<const int64_t>*>(second_industry_list_void_ptr);
  if (KS_UNLIKELY(second_industry_list_ptr == nullptr)) {
    return;
  }

  auto* second_ocpc_list_ptr = static_cast<absl::Span<const int64_t>*>(second_ocpc_list_void_ptr);
  if (KS_UNLIKELY(second_ocpc_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<std::vector<int64_t>>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_diff_action_from_two_list(
      *first_timestamp_list_ptr, *first_photo_list_ptr, *first_industry_list_ptr, *first_ocpc_list_ptr,
      *second_timestamp_list_ptr, *second_photo_list_ptr, *second_industry_list_ptr, *second_ocpc_list_ptr,
      process_time, period);
}

inline size_t c_combine_fans_tag(void* fanstag_list_void_ptr) {
  auto* fanstag_list_ptr = static_cast<std::vector<int64_t>*>(fanstag_list_void_ptr);
  if (KS_UNLIKELY(fanstag_list_ptr == nullptr)) {
    return static_cast<size_t>(0);
  }

  return combine_fans_tag(*fanstag_list_ptr);
}

inline void c_get_mix_rank_ecom_context_info(void* item_types_void_ptr, void* is_lives_void_ptr,
                                             void* item_ids_void_ptr, bool keep_live, void* res_void_ptr) {
  auto* item_types_ptr = static_cast<absl::Span<const int64_t>*>(item_types_void_ptr);
  if (KS_UNLIKELY(item_types_ptr == nullptr)) {
    return;
  }

  auto* is_lives_ptr = static_cast<absl::Span<const int64_t>*>(is_lives_void_ptr);
  if (KS_UNLIKELY(is_lives_ptr == nullptr)) {
    return;
  }

  auto* item_ids_ptr = static_cast<absl::Span<const int64_t>*>(item_ids_void_ptr);
  if (KS_UNLIKELY(item_ids_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_mix_rank_ecom_context_info(*item_types_ptr, *is_lives_ptr, *item_ids_ptr, keep_live);
}

inline void c_get_user_live_avg_price(void* cost_list_void_ptr, void* price_list_void_ptr,
                                      void* discount_list_void_ptr, void* order_cnt_list_void_ptr,
                                      void* res_void_ptr) {
  auto* cost_list_ptr = static_cast<absl::Span<const float>*>(cost_list_void_ptr);
  if (KS_UNLIKELY(cost_list_ptr == nullptr)) {
    return;
  }

  auto* price_list_ptr = static_cast<absl::Span<const float>*>(price_list_void_ptr);
  if (KS_UNLIKELY(price_list_ptr == nullptr)) {
    return;
  }

  auto* discount_list_ptr = static_cast<absl::Span<const float>*>(discount_list_void_ptr);
  if (KS_UNLIKELY(discount_list_ptr == nullptr)) {
    return;
  }

  auto* order_cnt_list_ptr = static_cast<absl::Span<const int64_t>*>(order_cnt_list_void_ptr);
  if (KS_UNLIKELY(order_cnt_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr =
      get_user_live_avg_price(*cost_list_ptr, *price_list_ptr, *discount_list_ptr, *order_cnt_list_ptr);
}

inline void c_get_rnd_photo_live_emb(void* arr_photo_void_ptr, void* arr_live_void_ptr, void* res_void_ptr) {
  auto* arr_photo_ptr = static_cast<absl::Span<const float>*>(arr_photo_void_ptr);
  if (KS_UNLIKELY(arr_photo_ptr == nullptr)) {
    return;
  }

  auto* arr_live_ptr = static_cast<absl::Span<const float>*>(arr_live_void_ptr);
  if (KS_UNLIKELY(arr_live_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_rnd_photo_live_emb(*arr_photo_ptr, *arr_live_ptr);
}

inline void c_get_nth_uint64_span_i64__size(void* x1_void_ptr, size_t seq_index_, void* res_void_ptr) {
  auto* x1_ptr = static_cast<absl::Span<const int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_nth_uint64(*x1_ptr, seq_index_);
}

inline void c_get_nth_uint64_vector_i64__size(void* x3_void_ptr, size_t seq_index_, void* res_void_ptr) {
  auto* x3_ptr = static_cast<std::vector<int64_t>*>(x3_void_ptr);
  if (KS_UNLIKELY(x3_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_nth_uint64(*x3_ptr, seq_index_);
}

inline void c_get_live_combine_realtime_price_comm(void* cost_list_void_ptr, void* industry_list_void_ptr,
                                                   void* author_cost_list_void_ptr,
                                                   void* author_industry_list_void_ptr, void* res_void_ptr) {
  auto* cost_list_ptr = static_cast<absl::Span<const float>*>(cost_list_void_ptr);
  if (KS_UNLIKELY(cost_list_ptr == nullptr)) {
    return;
  }

  auto* industry_list_ptr = static_cast<absl::Span<const int64_t>*>(industry_list_void_ptr);
  if (KS_UNLIKELY(industry_list_ptr == nullptr)) {
    return;
  }

  auto* author_cost_list_ptr = static_cast<absl::Span<const int64_t>*>(author_cost_list_void_ptr);
  if (KS_UNLIKELY(author_cost_list_ptr == nullptr)) {
    return;
  }

  auto* author_industry_list_ptr = static_cast<absl::Span<const int64_t>*>(author_industry_list_void_ptr);
  if (KS_UNLIKELY(author_industry_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_combine_realtime_price_comm(*cost_list_ptr, *industry_list_ptr, *author_cost_list_ptr,
                                                  *author_industry_list_ptr);
}

inline int32_t c_get_time_idx_longterm(uint64_t time_diff, size_t bucket_size) {
  return get_time_idx_longterm(time_diff, bucket_size);
}

inline void c_get_car_second_industry_id_hash_set(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::set<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_car_second_industry_id_hash_set();
}

inline void c_get_final_live_id_list(int64_t live_id, void* live_ids_void_ptr, void* res_void_ptr) {
  auto* live_ids_ptr = static_cast<absl::Span<const int64_t>*>(live_ids_void_ptr);
  if (KS_UNLIKELY(live_ids_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_final_live_id_list(live_id, *live_ids_ptr);
}

inline void c_add_value_by_time(void* short_list_void_ptr, void* long_list_void_ptr,
                                void* attr_value_list_void_ptr, int32_t max_len, void* res_void_ptr) {
  auto* short_list_ptr = static_cast<absl::Span<const int64_t>*>(short_list_void_ptr);
  if (KS_UNLIKELY(short_list_ptr == nullptr)) {
    return;
  }

  auto* long_list_ptr = static_cast<absl::Span<const int64_t>*>(long_list_void_ptr);
  if (KS_UNLIKELY(long_list_ptr == nullptr)) {
    return;
  }

  auto* attr_value_list_ptr = static_cast<absl::Span<const int64_t>*>(attr_value_list_void_ptr);
  if (KS_UNLIKELY(attr_value_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = add_value_by_time(*short_list_ptr, *long_list_ptr, *attr_value_list_ptr, max_len);
}

inline void c_seq_list_input_with_sparse_id_v1(void* industry_tag_void_ptr, void* is_playable_void_ptr,
                                               void* ad_style_void_ptr, void* cooperation_mode_void_ptr,
                                               void* material_feature_type_void_ptr, void* res_void_ptr) {
  auto* industry_tag_ptr = static_cast<absl::Span<const int64_t>*>(industry_tag_void_ptr);
  if (KS_UNLIKELY(industry_tag_ptr == nullptr)) {
    return;
  }

  auto* is_playable_ptr = static_cast<absl::Span<const int64_t>*>(is_playable_void_ptr);
  if (KS_UNLIKELY(is_playable_ptr == nullptr)) {
    return;
  }

  auto* ad_style_ptr = static_cast<absl::Span<const int64_t>*>(ad_style_void_ptr);
  if (KS_UNLIKELY(ad_style_ptr == nullptr)) {
    return;
  }

  auto* cooperation_mode_ptr = static_cast<absl::Span<const int64_t>*>(cooperation_mode_void_ptr);
  if (KS_UNLIKELY(cooperation_mode_ptr == nullptr)) {
    return;
  }

  auto* material_feature_type_ptr = static_cast<absl::Span<const int64_t>*>(material_feature_type_void_ptr);
  if (KS_UNLIKELY(material_feature_type_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = seq_list_input_with_sparse_id_v1(*industry_tag_ptr, *is_playable_ptr, *ad_style_ptr,
                                              *cooperation_mode_ptr, *material_feature_type_ptr);
}

inline void c_get_time_segment_cnt_log_value(void* timestamp_list_void_ptr, void* action_list_void_ptr,
                                             int64_t adlog_time, int64_t target_id, size_t bucket_size,
                                             size_t max_cnt, void* res_void_ptr) {
  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_time_segment_cnt_log_value(*timestamp_list_ptr, *action_list_ptr, adlog_time, target_id,
                                            bucket_size, max_cnt);
}

inline void c_get_live_author_atv_grade_prob(void* live_author_30d_atv_seq_void_ptr, void* res_void_ptr) {
  auto* live_author_30d_atv_seq_ptr = static_cast<absl::Span<const float>*>(live_author_30d_atv_seq_void_ptr);    // NOLINT
  if (KS_UNLIKELY(live_author_30d_atv_seq_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_author_atv_grade_prob(*live_author_30d_atv_seq_ptr);
}

inline void c_merge_two_seq_with_two_max_len(void* seq1_void_ptr, void* seq2_void_ptr, int32_t seq1_max_len,
                                             int32_t seq2_max_len, void* res_void_ptr) {
  auto* seq1_ptr = static_cast<absl::Span<const int64_t>*>(seq1_void_ptr);
  if (KS_UNLIKELY(seq1_ptr == nullptr)) {
    return;
  }

  auto* seq2_ptr = static_cast<absl::Span<const int64_t>*>(seq2_void_ptr);
  if (KS_UNLIKELY(seq2_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_two_seq_with_two_max_len(*seq1_ptr, *seq2_ptr, seq1_max_len, seq2_max_len);
}

inline void c_get_live_realtime_acc_fea(void* live_acc_delivery_cnt_void_ptr,
                                        void* live_acc_std_lps_cnt_void_ptr, void* live_acc_pay_cnt_void_ptr,
                                        void* photo_acc_delivery_cnt_void_ptr,
                                        void* photo_acc_std_lps_cnt_void_ptr,
                                        void* photo_acc_pay_cnt_void_ptr, void* upload_time_void_ptr,
                                        void* cur_time_void_ptr, void* res_void_ptr) {
  auto* live_acc_delivery_cnt_ptr = static_cast<absl::optional<int64_t>*>(live_acc_delivery_cnt_void_ptr);
  if (KS_UNLIKELY(live_acc_delivery_cnt_ptr == nullptr)) {
    return;
  }

  auto* live_acc_std_lps_cnt_ptr = static_cast<absl::optional<int64_t>*>(live_acc_std_lps_cnt_void_ptr);
  if (KS_UNLIKELY(live_acc_std_lps_cnt_ptr == nullptr)) {
    return;
  }

  auto* live_acc_pay_cnt_ptr = static_cast<absl::optional<int64_t>*>(live_acc_pay_cnt_void_ptr);
  if (KS_UNLIKELY(live_acc_pay_cnt_ptr == nullptr)) {
    return;
  }

  auto* photo_acc_delivery_cnt_ptr = static_cast<absl::optional<int64_t>*>(photo_acc_delivery_cnt_void_ptr);
  if (KS_UNLIKELY(photo_acc_delivery_cnt_ptr == nullptr)) {
    return;
  }

  auto* photo_acc_std_lps_cnt_ptr = static_cast<absl::optional<int64_t>*>(photo_acc_std_lps_cnt_void_ptr);
  if (KS_UNLIKELY(photo_acc_std_lps_cnt_ptr == nullptr)) {
    return;
  }

  auto* photo_acc_pay_cnt_ptr = static_cast<absl::optional<int64_t>*>(photo_acc_pay_cnt_void_ptr);
  if (KS_UNLIKELY(photo_acc_pay_cnt_ptr == nullptr)) {
    return;
  }

  auto* upload_time_ptr = static_cast<absl::optional<int64_t>*>(upload_time_void_ptr);
  if (KS_UNLIKELY(upload_time_ptr == nullptr)) {
    return;
  }

  auto* cur_time_ptr = static_cast<absl::optional<int64_t>*>(cur_time_void_ptr);
  if (KS_UNLIKELY(cur_time_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr =
      get_live_realtime_acc_fea(*live_acc_delivery_cnt_ptr, *live_acc_std_lps_cnt_ptr, *live_acc_pay_cnt_ptr,
                                *photo_acc_delivery_cnt_ptr, *photo_acc_std_lps_cnt_ptr,
                                *photo_acc_pay_cnt_ptr, *upload_time_ptr, *cur_time_ptr);
}

inline void c_merge_two_seq_with_max_len(void* seq1_void_ptr, void* seq2_void_ptr, int32_t seq1_max_len,
                                         int32_t max_len, void* res_void_ptr) {
  auto* seq1_ptr = static_cast<absl::Span<const int64_t>*>(seq1_void_ptr);
  if (KS_UNLIKELY(seq1_ptr == nullptr)) {
    return;
  }

  auto* seq2_ptr = static_cast<absl::Span<const int64_t>*>(seq2_void_ptr);
  if (KS_UNLIKELY(seq2_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_two_seq_with_max_len(*seq1_ptr, *seq2_ptr, seq1_max_len, max_len);
}

inline void c_get_live_pay_labels_front(void* ad_merchat_follow_void_ptr, void* ad_fans_top_follow_void_ptr,
                                        void* goods_view_void_ptr, void* live_like_void_ptr,
                                        void* live_shop_cart_click_void_ptr,
                                        void* live_shop_link_jump_void_ptr, void* live_p3s_void_ptr,
                                        void* live_p1m_void_ptr, void* live_pmedian_void_ptr,
                                        void* live_pmean_void_ptr, void* live_comment_void_ptr,
                                        void* rec_coupon_void_ptr, void* std_live_play_seconds_void_ptr,
                                        void* res_void_ptr) {
  auto* ad_merchat_follow_ptr = static_cast<absl::optional<int64_t>*>(ad_merchat_follow_void_ptr);
  if (KS_UNLIKELY(ad_merchat_follow_ptr == nullptr)) {
    return;
  }

  auto* ad_fans_top_follow_ptr = static_cast<absl::optional<int64_t>*>(ad_fans_top_follow_void_ptr);
  if (KS_UNLIKELY(ad_fans_top_follow_ptr == nullptr)) {
    return;
  }

  auto* goods_view_ptr = static_cast<absl::optional<int64_t>*>(goods_view_void_ptr);
  if (KS_UNLIKELY(goods_view_ptr == nullptr)) {
    return;
  }

  auto* live_like_ptr = static_cast<absl::optional<int64_t>*>(live_like_void_ptr);
  if (KS_UNLIKELY(live_like_ptr == nullptr)) {
    return;
  }

  auto* live_shop_cart_click_ptr = static_cast<absl::optional<int64_t>*>(live_shop_cart_click_void_ptr);
  if (KS_UNLIKELY(live_shop_cart_click_ptr == nullptr)) {
    return;
  }

  auto* live_shop_link_jump_ptr = static_cast<absl::optional<int64_t>*>(live_shop_link_jump_void_ptr);
  if (KS_UNLIKELY(live_shop_link_jump_ptr == nullptr)) {
    return;
  }

  auto* live_p3s_ptr = static_cast<absl::optional<int64_t>*>(live_p3s_void_ptr);
  if (KS_UNLIKELY(live_p3s_ptr == nullptr)) {
    return;
  }

  auto* live_p1m_ptr = static_cast<absl::optional<int64_t>*>(live_p1m_void_ptr);
  if (KS_UNLIKELY(live_p1m_ptr == nullptr)) {
    return;
  }

  auto* live_pmedian_ptr = static_cast<absl::optional<int64_t>*>(live_pmedian_void_ptr);
  if (KS_UNLIKELY(live_pmedian_ptr == nullptr)) {
    return;
  }

  auto* live_pmean_ptr = static_cast<absl::optional<int64_t>*>(live_pmean_void_ptr);
  if (KS_UNLIKELY(live_pmean_ptr == nullptr)) {
    return;
  }

  auto* live_comment_ptr = static_cast<absl::optional<int64_t>*>(live_comment_void_ptr);
  if (KS_UNLIKELY(live_comment_ptr == nullptr)) {
    return;
  }

  auto* rec_coupon_ptr = static_cast<absl::optional<int64_t>*>(rec_coupon_void_ptr);
  if (KS_UNLIKELY(rec_coupon_ptr == nullptr)) {
    return;
  }

  auto* std_live_play_seconds_ptr = static_cast<absl::optional<int64_t>*>(std_live_play_seconds_void_ptr);
  if (KS_UNLIKELY(std_live_play_seconds_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_pay_labels_front(*ad_merchat_follow_ptr, *ad_fans_top_follow_ptr, *goods_view_ptr,
                                       *live_like_ptr, *live_shop_cart_click_ptr, *live_shop_link_jump_ptr,
                                       *live_p3s_ptr, *live_p1m_ptr, *live_pmedian_ptr, *live_pmean_ptr,
                                       *live_comment_ptr, *rec_coupon_ptr, *std_live_play_seconds_ptr);
}

inline void c_get_live_cover_vanilla_stringview__stringview__bool__optional_i64(void* x1_void_ptr,
                                                                                void* x2_void_ptr, bool x3,
                                                                                void* x4_void_ptr,
                                                                                void* res_void_ptr) {
  auto* x1_ptr = static_cast<absl::string_view*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* x2_ptr = static_cast<absl::string_view*>(x2_void_ptr);
  if (KS_UNLIKELY(x2_ptr == nullptr)) {
    return;
  }

  auto* x4_ptr = static_cast<absl::optional<int64_t>*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_cover_vanilla(*x1_ptr, *x2_ptr, x3, *x4_ptr);
}

inline float c_div(float n, float m) { return div(n, m); }

inline void c_get_live_author_shop_types(void* shop_type_void_ptr, void* score_type_void_ptr,
                                         void* res_void_ptr) {
  auto* shop_type_ptr = static_cast<absl::optional<int64_t>*>(shop_type_void_ptr);
  if (KS_UNLIKELY(shop_type_ptr == nullptr)) {
    return;
  }

  auto* score_type_ptr = static_cast<absl::optional<int64_t>*>(score_type_void_ptr);
  if (KS_UNLIKELY(score_type_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_author_shop_types(*shop_type_ptr, *score_type_ptr);
}

inline int64_t c_get_kpin_first_recruit_photo_deliver_time_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_first_recruit_photo_deliver_time_bucket(*raw_input_ptr);
}

inline void c_get_dense_ple_helper(float val, void* bins_void_ptr, void* res_void_ptr) {
  auto* bins_ptr = static_cast<std::vector<float>*>(bins_void_ptr);
  if (KS_UNLIKELY(bins_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_dense_ple_helper(val, *bins_ptr);
}

inline void c_get_live_highlight(void* total_watch_uv_list_void_ptr, void* total_paycnt_void_ptr,
                                 void* res_void_ptr) {
  auto* total_watch_uv_list_ptr = static_cast<absl::Span<const int64_t>*>(total_watch_uv_list_void_ptr);
  if (KS_UNLIKELY(total_watch_uv_list_ptr == nullptr)) {
    return;
  }

  auto* total_paycnt_ptr = static_cast<absl::Span<const int64_t>*>(total_paycnt_void_ptr);
  if (KS_UNLIKELY(total_paycnt_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_highlight(*total_watch_uv_list_ptr, *total_paycnt_ptr);
}

inline void c_get_useful_live_id(int64_t live_id, void* live_ids_void_ptr, void* cost_list_void_ptr,
                                 void* res_void_ptr) {
  auto* live_ids_ptr = static_cast<absl::Span<const int64_t>*>(live_ids_void_ptr);
  if (KS_UNLIKELY(live_ids_ptr == nullptr)) {
    return;
  }

  auto* cost_list_ptr = static_cast<absl::Span<const int64_t>*>(cost_list_void_ptr);
  if (KS_UNLIKELY(cost_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_useful_live_id(live_id, *live_ids_ptr, *cost_list_ptr);
}

inline void c_get_play_duration_dense(void* play_time_void_ptr, void* res_void_ptr) {
  auto* play_time_ptr = static_cast<absl::optional<int64_t>*>(play_time_void_ptr);
  if (KS_UNLIKELY(play_time_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_play_duration_dense(*play_time_ptr);
}

inline void c_get_author_live_exp_cnt_bins(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_author_live_exp_cnt_bins();
}

inline void c_get_live_author_attraction(void* svr_rank_list_void_ptr, void* cxr_rank_list_void_ptr,
                                         void* price_rank_list_void_ptr, void* discount_rank_list_void_ptr,
                                         void* res_void_ptr) {
  auto* svr_rank_list_ptr = static_cast<absl::Span<const int64_t>*>(svr_rank_list_void_ptr);
  if (KS_UNLIKELY(svr_rank_list_ptr == nullptr)) {
    return;
  }

  auto* cxr_rank_list_ptr = static_cast<absl::Span<const int64_t>*>(cxr_rank_list_void_ptr);
  if (KS_UNLIKELY(cxr_rank_list_ptr == nullptr)) {
    return;
  }

  auto* price_rank_list_ptr = static_cast<absl::Span<const int64_t>*>(price_rank_list_void_ptr);
  if (KS_UNLIKELY(price_rank_list_ptr == nullptr)) {
    return;
  }

  auto* discount_rank_list_ptr = static_cast<absl::Span<const int64_t>*>(discount_rank_list_void_ptr);
  if (KS_UNLIKELY(discount_rank_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_author_attraction(*svr_rank_list_ptr, *cxr_rank_list_ptr, *price_rank_list_ptr,
                                        *discount_rank_list_ptr);
}

inline void c_get_slice_llm_fea(void* fea_value_list_void_ptr, int64_t start_pos, int64_t length,
                                void* res_void_ptr) {
  auto* fea_value_list_ptr = static_cast<absl::Span<const int64_t>*>(fea_value_list_void_ptr);
  if (KS_UNLIKELY(fea_value_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_slice_llm_fea(*fea_value_list_ptr, start_pos, length);
}

inline void c_get_universe_hash_seq_with_max_len_and_timestamp(void* seq_void_ptr,
                                                               void* medium_id_seq_void_ptr,
                                                               void* timestamp_seq_void_ptr, int32_t max_len,
                                                               int64_t current_timestamp,
                                                               int64_t timestamp_delta, void* res_void_ptr) {
  auto* seq_ptr = static_cast<absl::Span<const int64_t>*>(seq_void_ptr);
  if (KS_UNLIKELY(seq_ptr == nullptr)) {
    return;
  }

  auto* medium_id_seq_ptr = static_cast<absl::Span<const int64_t>*>(medium_id_seq_void_ptr);
  if (KS_UNLIKELY(medium_id_seq_ptr == nullptr)) {
    return;
  }

  auto* timestamp_seq_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_seq_void_ptr);
  if (KS_UNLIKELY(timestamp_seq_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_universe_hash_seq_with_max_len_and_timestamp(
      *seq_ptr, *medium_id_seq_ptr, *timestamp_seq_ptr, max_len, current_timestamp, timestamp_delta);
}

inline void c_get_random_uniform_int(int64_t n, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_random_uniform_int(n);
}

inline void c_get_live_author_score(void* shop_score_void_ptr, void* shop_score_rank_void_ptr,
                                    void* master_score_void_ptr, void* master_score_rank_void_ptr,
                                    void* res_void_ptr) {
  auto* shop_score_ptr = static_cast<absl::optional<int64_t>*>(shop_score_void_ptr);
  if (KS_UNLIKELY(shop_score_ptr == nullptr)) {
    return;
  }

  auto* shop_score_rank_ptr = static_cast<absl::optional<int64_t>*>(shop_score_rank_void_ptr);
  if (KS_UNLIKELY(shop_score_rank_ptr == nullptr)) {
    return;
  }

  auto* master_score_ptr = static_cast<absl::optional<int64_t>*>(master_score_void_ptr);
  if (KS_UNLIKELY(master_score_ptr == nullptr)) {
    return;
  }

  auto* master_score_rank_ptr = static_cast<absl::optional<int64_t>*>(master_score_rank_void_ptr);
  if (KS_UNLIKELY(master_score_rank_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr =
      get_live_author_score(*shop_score_ptr, *shop_score_rank_ptr, *master_score_ptr, *master_score_rank_ptr);    // NOLINT
}

inline void c_get_match_detail_fix(void* user_action_content_list_void_ptr,
                                   void* user_action_timestamp_list_void_ptr, int64_t target_id,
                                   int64_t adlog_timestamp, void* res_void_ptr) {
  auto* user_action_content_list_ptr =
      static_cast<absl::Span<const int64_t>*>(user_action_content_list_void_ptr);
  if (KS_UNLIKELY(user_action_content_list_ptr == nullptr)) {
    return;
  }

  auto* user_action_timestamp_list_ptr =
      static_cast<absl::Span<const int64_t>*>(user_action_timestamp_list_void_ptr);
  if (KS_UNLIKELY(user_action_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_match_detail_fix(*user_action_content_list_ptr, *user_action_timestamp_list_ptr, target_id,
                                  adlog_timestamp);
}

inline void c_get_user_seq(void* action_list_void_ptr, void* timestamp_list_void_ptr, int64_t timestamp,
                           void* res_void_ptr) {
  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_seq(*action_list_ptr, *timestamp_list_ptr, timestamp);
}

inline float c_cast_to_float_optional_f32(void* x3_void_ptr) {
  auto* x3_ptr = static_cast<absl::optional<float>*>(x3_void_ptr);
  if (KS_UNLIKELY(x3_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return cast_to_float(*x3_ptr);
}

inline void c_get_hist_req_time_gap_cross_pos_id_platform(void* hist_req_timestamp_list_void_ptr,
                                                          int64_t cur_timestamp, void* pos_id_void_ptr,
                                                          void* platform_void_ptr, void* res_void_ptr) {
  auto* hist_req_timestamp_list_ptr =
      static_cast<absl::Span<const int64_t>*>(hist_req_timestamp_list_void_ptr);
  if (KS_UNLIKELY(hist_req_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* pos_id_ptr = static_cast<absl::optional<int64_t>*>(pos_id_void_ptr);
  if (KS_UNLIKELY(pos_id_ptr == nullptr)) {
    return;
  }

  auto* platform_ptr = static_cast<absl::string_view*>(platform_void_ptr);
  if (KS_UNLIKELY(platform_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_hist_req_time_gap_cross_pos_id_platform(*hist_req_timestamp_list_ptr, cur_timestamp,
                                                         *pos_id_ptr, *platform_ptr);
}

inline void c_parse_gmv_bucket_count(void* raw_bucket_mean_list_void_ptr, void* res_void_ptr) {
  auto* raw_bucket_mean_list_ptr = static_cast<absl::Span<const int64_t>*>(raw_bucket_mean_list_void_ptr);
  if (KS_UNLIKELY(raw_bucket_mean_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = parse_gmv_bucket_count(*raw_bucket_mean_list_ptr);
}

inline int64_t c_get_kpin_deliver_cnt_60d_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_deliver_cnt_60d_bucket(*raw_input_ptr);
}

inline int64_t c_get_kpin_recruit_card_show_cnt_30d_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_recruit_card_show_cnt_30d_bucket(*raw_input_ptr);
}

inline void c_get_user_live_total_cost(void* cost_list_void_ptr, void* price_list_void_ptr,
                                       void* discount_list_void_ptr, void* order_cnt_list_void_ptr,
                                       void* res_void_ptr) {
  auto* cost_list_ptr = static_cast<absl::Span<const float>*>(cost_list_void_ptr);
  if (KS_UNLIKELY(cost_list_ptr == nullptr)) {
    return;
  }

  auto* price_list_ptr = static_cast<absl::Span<const float>*>(price_list_void_ptr);
  if (KS_UNLIKELY(price_list_ptr == nullptr)) {
    return;
  }

  auto* discount_list_ptr = static_cast<absl::Span<const float>*>(discount_list_void_ptr);
  if (KS_UNLIKELY(discount_list_ptr == nullptr)) {
    return;
  }

  auto* order_cnt_list_ptr = static_cast<absl::Span<const int64_t>*>(order_cnt_list_void_ptr);
  if (KS_UNLIKELY(order_cnt_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr =
      get_user_live_total_cost(*cost_list_ptr, *price_list_ptr, *discount_list_ptr, *order_cnt_list_ptr);
}

inline int64_t c_get_cur_page_num_cross_pos_id(void* cur_page_num_void_ptr, void* pos_id_void_ptr) {
  auto* cur_page_num_ptr = static_cast<absl::optional<int64_t>*>(cur_page_num_void_ptr);
  if (KS_UNLIKELY(cur_page_num_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* pos_id_ptr = static_cast<absl::optional<int64_t>*>(pos_id_void_ptr);
  if (KS_UNLIKELY(pos_id_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_cur_page_num_cross_pos_id(*cur_page_num_ptr, *pos_id_ptr);
}

inline float c_get_gpm_paycnt(void* x_void_ptr) {
  auto* x_ptr = static_cast<absl::optional<int64_t>*>(x_void_ptr);
  if (KS_UNLIKELY(x_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return get_gpm_paycnt(*x_ptr);
}

inline void c_seq_list_input_with_sparse_hard_search(
    void* industry_tag_void_ptr, void* is_playable_void_ptr, void* ad_style_void_ptr,
    void* cooperation_mode_void_ptr, void* material_feature_type_void_ptr, void* industry_tag_target_void_ptr,    // NOLINT
    void* is_playable_target_void_ptr, void* ad_style_target_void_ptr, void* cooperation_mode_target_void_ptr,    // NOLINT
    void* material_feature_type_target_void_ptr, void* res_void_ptr) {
  auto* industry_tag_ptr = static_cast<absl::Span<const int64_t>*>(industry_tag_void_ptr);
  if (KS_UNLIKELY(industry_tag_ptr == nullptr)) {
    return;
  }

  auto* is_playable_ptr = static_cast<absl::Span<const int64_t>*>(is_playable_void_ptr);
  if (KS_UNLIKELY(is_playable_ptr == nullptr)) {
    return;
  }

  auto* ad_style_ptr = static_cast<absl::Span<const int64_t>*>(ad_style_void_ptr);
  if (KS_UNLIKELY(ad_style_ptr == nullptr)) {
    return;
  }

  auto* cooperation_mode_ptr = static_cast<absl::Span<const int64_t>*>(cooperation_mode_void_ptr);
  if (KS_UNLIKELY(cooperation_mode_ptr == nullptr)) {
    return;
  }

  auto* material_feature_type_ptr = static_cast<absl::Span<const int64_t>*>(material_feature_type_void_ptr);
  if (KS_UNLIKELY(material_feature_type_ptr == nullptr)) {
    return;
  }

  auto* industry_tag_target_ptr =
      static_cast<absl::optional<absl::string_view>*>(industry_tag_target_void_ptr);
  if (KS_UNLIKELY(industry_tag_target_ptr == nullptr)) {
    return;
  }

  auto* is_playable_target_ptr = static_cast<absl::optional<int64_t>*>(is_playable_target_void_ptr);
  if (KS_UNLIKELY(is_playable_target_ptr == nullptr)) {
    return;
  }

  auto* ad_style_target_ptr = static_cast<absl::optional<int64_t>*>(ad_style_target_void_ptr);
  if (KS_UNLIKELY(ad_style_target_ptr == nullptr)) {
    return;
  }

  auto* cooperation_mode_target_ptr = static_cast<absl::optional<int64_t>*>(cooperation_mode_target_void_ptr);    // NOLINT
  if (KS_UNLIKELY(cooperation_mode_target_ptr == nullptr)) {
    return;
  }

  auto* material_feature_type_target_ptr =
      static_cast<absl::optional<int64_t>*>(material_feature_type_target_void_ptr);
  if (KS_UNLIKELY(material_feature_type_target_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = seq_list_input_with_sparse_hard_search(
      *industry_tag_ptr, *is_playable_ptr, *ad_style_ptr, *cooperation_mode_ptr, *material_feature_type_ptr,
      *industry_tag_target_ptr, *is_playable_target_ptr, *ad_style_target_ptr, *cooperation_mode_target_ptr,
      *material_feature_type_target_ptr);
}

inline void c_get_play_duration(void* play_time_void_ptr, void* res_void_ptr) {
  auto* play_time_ptr = static_cast<absl::optional<int64_t>*>(play_time_void_ptr);
  if (KS_UNLIKELY(play_time_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_play_duration(*play_time_ptr);
}

inline void c_get_coupons_value_stat(void* value_list_void_ptr, void* res_void_ptr) {
  auto* value_list_ptr = static_cast<absl::Span<const int64_t>*>(value_list_void_ptr);
  if (KS_UNLIKELY(value_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_coupons_value_stat(*value_list_ptr);
}

inline float c_cast_to_float_with_default_optional_i64__i32(void* x1_void_ptr, int32_t int_arg1) {
  auto* x1_ptr = static_cast<absl::optional<int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return cast_to_float_with_default(*x1_ptr, int_arg1);
}

inline void c_get_list_length(void* attr_list_void_ptr, void* res_void_ptr) {
  auto* attr_list_ptr = static_cast<absl::Span<const int64_t>*>(attr_list_void_ptr);
  if (KS_UNLIKELY(attr_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_list_length(*attr_list_ptr);
}

inline void c_parse_gmv_bucket_dist(void* raw_bucket_mean_list_void_ptr, void* res_void_ptr) {
  auto* raw_bucket_mean_list_ptr = static_cast<absl::Span<const int64_t>*>(raw_bucket_mean_list_void_ptr);
  if (KS_UNLIKELY(raw_bucket_mean_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = parse_gmv_bucket_dist(*raw_bucket_mean_list_ptr);
}

inline void c_get_condition_author_list(void* author_list_void_ptr, void* paycnt_list_void_ptr,
                                        int64_t condition, void* res_void_ptr) {
  auto* author_list_ptr = static_cast<absl::Span<const int64_t>*>(author_list_void_ptr);
  if (KS_UNLIKELY(author_list_ptr == nullptr)) {
    return;
  }

  auto* paycnt_list_ptr = static_cast<absl::Span<const int64_t>*>(paycnt_list_void_ptr);
  if (KS_UNLIKELY(paycnt_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_condition_author_list(*author_list_ptr, *paycnt_list_ptr, condition);
}

inline void c_log_10_cast_int(float x, void* res_void_ptr) {
  auto* res_ptr = static_cast<absl::optional<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = log_10_cast_int(x);
}

inline void c_get_live_author_cg_rank(void* author_category_list_void_ptr, void* author_rank_list_void_ptr,
                                      void* res_void_ptr) {
  auto* author_category_list_ptr = static_cast<absl::Span<const int64_t>*>(author_category_list_void_ptr);
  if (KS_UNLIKELY(author_category_list_ptr == nullptr)) {
    return;
  }

  auto* author_rank_list_ptr = static_cast<absl::Span<const int64_t>*>(author_rank_list_void_ptr);
  if (KS_UNLIKELY(author_rank_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_author_cg_rank(*author_category_list_ptr, *author_rank_list_ptr);
}

inline void c_get_condition_author_list_len(void* paycnt_list_void_ptr, void* res_void_ptr) {
  auto* paycnt_list_ptr = static_cast<absl::Span<const int64_t>*>(paycnt_list_void_ptr);
  if (KS_UNLIKELY(paycnt_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_condition_author_list_len(*paycnt_list_ptr);
}

inline int64_t c_get_item_creative_type(int64_t campaign_type, int64_t live_creative_type) {
  return get_item_creative_type(campaign_type, live_creative_type);
}

inline int64_t c_get_kpin_deliver_cnt_90d_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_deliver_cnt_90d_bucket(*raw_input_ptr);
}

inline void c_get_realtime_action_list_full(void* timestamp_list_void_ptr, void* photo_id_list_void_ptr,
                                            void* account_id_list_void_ptr,
                                            void* industry_v3_id_list_void_ptr,
                                            void* product_name_list_void_ptr, int64_t adlog_time,
                                            void* res_void_ptr) {
  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* photo_id_list_ptr = static_cast<absl::Span<const int64_t>*>(photo_id_list_void_ptr);
  if (KS_UNLIKELY(photo_id_list_ptr == nullptr)) {
    return;
  }

  auto* account_id_list_ptr = static_cast<absl::Span<const int64_t>*>(account_id_list_void_ptr);
  if (KS_UNLIKELY(account_id_list_ptr == nullptr)) {
    return;
  }

  auto* industry_v3_id_list_ptr = static_cast<absl::Span<const int64_t>*>(industry_v3_id_list_void_ptr);
  if (KS_UNLIKELY(industry_v3_id_list_ptr == nullptr)) {
    return;
  }

  auto* product_name_list_ptr = static_cast<absl::Span<const int64_t>*>(product_name_list_void_ptr);
  if (KS_UNLIKELY(product_name_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_realtime_action_list_full(*timestamp_list_ptr, *photo_id_list_ptr, *account_id_list_ptr,
                                           *industry_v3_id_list_ptr, *product_name_list_ptr, adlog_time);
}

inline void c_get_industry_filter_realtime_action_list_full(
    void* timestamp_list_void_ptr, void* photo_id_list_void_ptr, void* account_id_list_void_ptr,
    void* industry_v3_id_list_void_ptr, void* product_name_list_void_ptr, int64_t adlog_time,
    int64_t target_id, void* res_void_ptr) {
  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* photo_id_list_ptr = static_cast<absl::Span<const int64_t>*>(photo_id_list_void_ptr);
  if (KS_UNLIKELY(photo_id_list_ptr == nullptr)) {
    return;
  }

  auto* account_id_list_ptr = static_cast<absl::Span<const int64_t>*>(account_id_list_void_ptr);
  if (KS_UNLIKELY(account_id_list_ptr == nullptr)) {
    return;
  }

  auto* industry_v3_id_list_ptr = static_cast<absl::Span<const int64_t>*>(industry_v3_id_list_void_ptr);
  if (KS_UNLIKELY(industry_v3_id_list_ptr == nullptr)) {
    return;
  }

  auto* product_name_list_ptr = static_cast<absl::Span<const int64_t>*>(product_name_list_void_ptr);
  if (KS_UNLIKELY(product_name_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_industry_filter_realtime_action_list_full(*timestamp_list_ptr, *photo_id_list_ptr,
                                                           *account_id_list_ptr, *industry_v3_id_list_ptr,
                                                           *product_name_list_ptr, adlog_time, target_id);
}

inline float c_get_period_gmv_trend_dense(void* gmv_list_void_ptr, void* paycnt_list_void_ptr, int32_t start,
                                          int32_t duration) {
  auto* gmv_list_ptr = static_cast<absl::Span<const int64_t>*>(gmv_list_void_ptr);
  if (KS_UNLIKELY(gmv_list_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* paycnt_list_ptr = static_cast<absl::Span<const int64_t>*>(paycnt_list_void_ptr);
  if (KS_UNLIKELY(paycnt_list_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return get_period_gmv_trend_dense(*gmv_list_ptr, *paycnt_list_ptr, start, duration);
}

inline void c_get_kpin_identity_func(void* raw_input_void_ptr, void* res_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_kpin_identity_func(*raw_input_ptr);
}

inline int64_t c_combine_f32__i64(float x3, int64_t x5) { return combine(x3, x5); }

inline void c_get_context_candidate_info_top_n_span_i64(void* x1_void_ptr, void* res_void_ptr) {
  auto* x1_ptr = static_cast<absl::Span<const int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_context_candidate_info_top_n(*x1_ptr);
}

inline void c_get_real_view_combine_cg_rank(void* user_category_list_void_ptr, void* user_ts_list_void_ptr,
                                            int64_t adlog_ts, void* author_category_list_void_ptr,
                                            void* author_rank_list_void_ptr, void* res_void_ptr) {
  auto* user_category_list_ptr = static_cast<absl::Span<const int64_t>*>(user_category_list_void_ptr);
  if (KS_UNLIKELY(user_category_list_ptr == nullptr)) {
    return;
  }

  auto* user_ts_list_ptr = static_cast<absl::Span<const int64_t>*>(user_ts_list_void_ptr);
  if (KS_UNLIKELY(user_ts_list_ptr == nullptr)) {
    return;
  }

  auto* author_category_list_ptr = static_cast<absl::Span<const int64_t>*>(author_category_list_void_ptr);
  if (KS_UNLIKELY(author_category_list_ptr == nullptr)) {
    return;
  }

  auto* author_rank_list_ptr = static_cast<absl::Span<const int64_t>*>(author_rank_list_void_ptr);
  if (KS_UNLIKELY(author_rank_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_real_view_combine_cg_rank(*user_category_list_ptr, *user_ts_list_ptr, adlog_ts,
                                           *author_category_list_ptr, *author_rank_list_ptr);
}

inline void c_get_time_gap_group(void* timestamp_list_void_ptr, int64_t adlog_timestamp, int32_t dividend,
                                 void* res_void_ptr) {
  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_time_gap_group(*timestamp_list_ptr, adlog_timestamp, dividend);
}

inline void c_ods_item_comment_match_target_item(
    void* item_ids0_void_ptr, void* item_ids1_void_ptr, void* car_item_ids_void_ptr,
    void* explain_item_id_void_ptr, void* hist_item_ids_void_ptr, void* label_cat_ids_void_ptr,
    void* label_spu_ids_void_ptr, void* label_brand_ids_void_ptr, void* label_x7_level1_void_ptr,
    void* label_x7_level2_void_ptr, void* label_x7_level3_void_ptr, void* label_x7_level4_void_ptr,
    void* car_cat_ids_void_ptr, void* car_spu_ids_void_ptr, void* car_brand_ids_void_ptr,
    void* car_x7_level1_void_ptr, void* car_x7_level2_void_ptr, void* car_x7_level3_void_ptr,
    void* car_x7_level4_void_ptr, void* explain_cat_id_void_ptr, void* explain_spu_id_void_ptr,
    void* explain_brand_id_void_ptr, void* hist_cat_ids_void_ptr, void* hist_spu_ids_void_ptr,
    void* hist_brand_ids_void_ptr, void* res_void_ptr) {
  auto* item_ids0_ptr = static_cast<absl::Span<const int64_t>*>(item_ids0_void_ptr);
  if (KS_UNLIKELY(item_ids0_ptr == nullptr)) {
    return;
  }

  auto* item_ids1_ptr = static_cast<absl::Span<const int64_t>*>(item_ids1_void_ptr);
  if (KS_UNLIKELY(item_ids1_ptr == nullptr)) {
    return;
  }

  auto* car_item_ids_ptr = static_cast<absl::Span<const int64_t>*>(car_item_ids_void_ptr);
  if (KS_UNLIKELY(car_item_ids_ptr == nullptr)) {
    return;
  }

  auto* explain_item_id_ptr = static_cast<absl::optional<int64_t>*>(explain_item_id_void_ptr);
  if (KS_UNLIKELY(explain_item_id_ptr == nullptr)) {
    return;
  }

  auto* hist_item_ids_ptr = static_cast<absl::Span<const int64_t>*>(hist_item_ids_void_ptr);
  if (KS_UNLIKELY(hist_item_ids_ptr == nullptr)) {
    return;
  }

  auto* label_cat_ids_ptr = static_cast<absl::Span<const int64_t>*>(label_cat_ids_void_ptr);
  if (KS_UNLIKELY(label_cat_ids_ptr == nullptr)) {
    return;
  }

  auto* label_spu_ids_ptr = static_cast<absl::Span<const int64_t>*>(label_spu_ids_void_ptr);
  if (KS_UNLIKELY(label_spu_ids_ptr == nullptr)) {
    return;
  }

  auto* label_brand_ids_ptr = static_cast<absl::Span<const int64_t>*>(label_brand_ids_void_ptr);
  if (KS_UNLIKELY(label_brand_ids_ptr == nullptr)) {
    return;
  }

  auto* label_x7_level1_ptr = static_cast<absl::Span<const int64_t>*>(label_x7_level1_void_ptr);
  if (KS_UNLIKELY(label_x7_level1_ptr == nullptr)) {
    return;
  }

  auto* label_x7_level2_ptr = static_cast<absl::Span<const int64_t>*>(label_x7_level2_void_ptr);
  if (KS_UNLIKELY(label_x7_level2_ptr == nullptr)) {
    return;
  }

  auto* label_x7_level3_ptr = static_cast<absl::Span<const int64_t>*>(label_x7_level3_void_ptr);
  if (KS_UNLIKELY(label_x7_level3_ptr == nullptr)) {
    return;
  }

  auto* label_x7_level4_ptr = static_cast<absl::Span<const int64_t>*>(label_x7_level4_void_ptr);
  if (KS_UNLIKELY(label_x7_level4_ptr == nullptr)) {
    return;
  }

  auto* car_cat_ids_ptr = static_cast<absl::Span<const int64_t>*>(car_cat_ids_void_ptr);
  if (KS_UNLIKELY(car_cat_ids_ptr == nullptr)) {
    return;
  }

  auto* car_spu_ids_ptr = static_cast<absl::Span<const int64_t>*>(car_spu_ids_void_ptr);
  if (KS_UNLIKELY(car_spu_ids_ptr == nullptr)) {
    return;
  }

  auto* car_brand_ids_ptr = static_cast<absl::Span<const int64_t>*>(car_brand_ids_void_ptr);
  if (KS_UNLIKELY(car_brand_ids_ptr == nullptr)) {
    return;
  }

  auto* car_x7_level1_ptr = static_cast<absl::Span<const int64_t>*>(car_x7_level1_void_ptr);
  if (KS_UNLIKELY(car_x7_level1_ptr == nullptr)) {
    return;
  }

  auto* car_x7_level2_ptr = static_cast<absl::Span<const int64_t>*>(car_x7_level2_void_ptr);
  if (KS_UNLIKELY(car_x7_level2_ptr == nullptr)) {
    return;
  }

  auto* car_x7_level3_ptr = static_cast<absl::Span<const int64_t>*>(car_x7_level3_void_ptr);
  if (KS_UNLIKELY(car_x7_level3_ptr == nullptr)) {
    return;
  }

  auto* car_x7_level4_ptr = static_cast<absl::Span<const int64_t>*>(car_x7_level4_void_ptr);
  if (KS_UNLIKELY(car_x7_level4_ptr == nullptr)) {
    return;
  }

  auto* explain_cat_id_ptr = static_cast<absl::optional<int64_t>*>(explain_cat_id_void_ptr);
  if (KS_UNLIKELY(explain_cat_id_ptr == nullptr)) {
    return;
  }

  auto* explain_spu_id_ptr = static_cast<absl::optional<int64_t>*>(explain_spu_id_void_ptr);
  if (KS_UNLIKELY(explain_spu_id_ptr == nullptr)) {
    return;
  }

  auto* explain_brand_id_ptr = static_cast<absl::optional<int64_t>*>(explain_brand_id_void_ptr);
  if (KS_UNLIKELY(explain_brand_id_ptr == nullptr)) {
    return;
  }

  auto* hist_cat_ids_ptr = static_cast<absl::Span<const int64_t>*>(hist_cat_ids_void_ptr);
  if (KS_UNLIKELY(hist_cat_ids_ptr == nullptr)) {
    return;
  }

  auto* hist_spu_ids_ptr = static_cast<absl::Span<const int64_t>*>(hist_spu_ids_void_ptr);
  if (KS_UNLIKELY(hist_spu_ids_ptr == nullptr)) {
    return;
  }

  auto* hist_brand_ids_ptr = static_cast<absl::Span<const int64_t>*>(hist_brand_ids_void_ptr);
  if (KS_UNLIKELY(hist_brand_ids_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = ods_item_comment_match_target_item(
      *item_ids0_ptr, *item_ids1_ptr, *car_item_ids_ptr, *explain_item_id_ptr, *hist_item_ids_ptr,
      *label_cat_ids_ptr, *label_spu_ids_ptr, *label_brand_ids_ptr, *label_x7_level1_ptr,
      *label_x7_level2_ptr, *label_x7_level3_ptr, *label_x7_level4_ptr, *car_cat_ids_ptr, *car_spu_ids_ptr,
      *car_brand_ids_ptr, *car_x7_level1_ptr, *car_x7_level2_ptr, *car_x7_level3_ptr, *car_x7_level4_ptr,
      *explain_cat_id_ptr, *explain_spu_id_ptr, *explain_brand_id_ptr, *hist_cat_ids_ptr, *hist_spu_ids_ptr,
      *hist_brand_ids_ptr);
}

inline void c_find_cate_set_by_mmu_explaining_id(void* cart_list_id_void_ptr, void* cate_list_void_ptr,
                                                 int64_t explaining_id, int64_t mmu_explaining_id,
                                                 int32_t goods_max_num, void* res_void_ptr) {
  auto* cart_list_id_ptr = static_cast<absl::Span<const int64_t>*>(cart_list_id_void_ptr);
  if (KS_UNLIKELY(cart_list_id_ptr == nullptr)) {
    return;
  }

  auto* cate_list_ptr = static_cast<absl::Span<const int64_t>*>(cate_list_void_ptr);
  if (KS_UNLIKELY(cate_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::set<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = find_cate_set_by_mmu_explaining_id(*cart_list_id_ptr, *cate_list_ptr, explaining_id,
                                                mmu_explaining_id, goods_max_num);
}

inline void c_add_feature_result_vector_f32__i32__vector_extractresult(void* x7_void_ptr, int32_t size,
                                                                       void* result_void_ptr) {
  auto* x7_ptr = static_cast<std::vector<float>*>(x7_void_ptr);
  if (KS_UNLIKELY(x7_ptr == nullptr)) {
    return;
  }

  auto* result_ptr = static_cast<std::vector<ExtractResult>*>(result_void_ptr);
  if (KS_UNLIKELY(result_ptr == nullptr)) {
    return;
  }

  return add_feature_result(*x7_ptr, size, result_ptr);
}

inline int64_t c_get_bucket_from_segments(void* res_value_void_ptr, void* segments_void_ptr) {
  auto* res_value_ptr = static_cast<absl::optional<int64_t>*>(res_value_void_ptr);
  if (KS_UNLIKELY(res_value_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* segments_ptr = static_cast<std::vector<int64_t>*>(segments_void_ptr);
  if (KS_UNLIKELY(segments_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_bucket_from_segments(*res_value_ptr, *segments_ptr);
}

inline uint64_t c_get_last_time(void* cate_timestamp_list_void_ptr, int64_t adlog_timestamp) {
  auto* cate_timestamp_list_ptr = static_cast<std::vector<uint64_t>*>(cate_timestamp_list_void_ptr);
  if (KS_UNLIKELY(cate_timestamp_list_ptr == nullptr)) {
    return static_cast<uint64_t>(0);
  }

  return get_last_time(*cate_timestamp_list_ptr, adlog_timestamp);
}

inline int64_t c_get_user_buyer_effective_type(void* buyer_effective_type_void_ptr) {
  auto* buyer_effective_type_ptr =
      static_cast<absl::optional<absl::string_view>*>(buyer_effective_type_void_ptr);
  if (KS_UNLIKELY(buyer_effective_type_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_user_buyer_effective_type(*buyer_effective_type_ptr);
}

inline int64_t c_get_kpin_live_recruit_out_show_cnt_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_live_recruit_out_show_cnt_bucket(*raw_input_ptr);
}

inline int64_t c_get_last_gap_timestamp(void* room_pattern_list_void_ptr, void* timestamp_list_void_ptr,
                                        int64_t adlog_timestamp, int64_t room_pattern_type_value) {
  auto* room_pattern_list_ptr = static_cast<absl::Span<const int64_t>*>(room_pattern_list_void_ptr);
  if (KS_UNLIKELY(room_pattern_list_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_last_gap_timestamp(*room_pattern_list_ptr, *timestamp_list_ptr, adlog_timestamp,
                                room_pattern_type_value);
}

inline void c_get_comp_scene_flag(int64_t page_id, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_comp_scene_flag(page_id);
}

inline int64_t c_last_gap_time_segement(int64_t last_gap_timestamp) {
  return last_gap_time_segement(last_gap_timestamp);
}

inline float c_get_list_length_scalar(void* attr_list_void_ptr) {
  auto* attr_list_ptr = static_cast<absl::Span<const int64_t>*>(attr_list_void_ptr);
  if (KS_UNLIKELY(attr_list_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return get_list_length_scalar(*attr_list_ptr);
}

inline void c_get_cnt_diff_with_target_hit_combine_v2(void* action_list_void_ptr,
                                                      void* timestamp_list_void_ptr, void* target_id_void_ptr,    // NOLINT
                                                      void* target_id_v2_void_ptr, int64_t timestamp,
                                                      void* res_void_ptr) {
  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* target_id_ptr = static_cast<absl::optional<int64_t>*>(target_id_void_ptr);
  if (KS_UNLIKELY(target_id_ptr == nullptr)) {
    return;
  }

  auto* target_id_v2_ptr = static_cast<absl::optional<int64_t>*>(target_id_v2_void_ptr);
  if (KS_UNLIKELY(target_id_v2_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_cnt_diff_with_target_hit_combine_v2(*action_list_ptr, *timestamp_list_ptr, *target_id_ptr,
                                                     *target_id_v2_ptr, timestamp);
}

inline void c_get_realtime_target_list_match_cnt_v2(void* attr_list_void_ptr, void* target_list_void_ptr,
                                                    void* res_void_ptr) {
  auto* attr_list_ptr = static_cast<absl::Span<const int64_t>*>(attr_list_void_ptr);
  if (KS_UNLIKELY(attr_list_ptr == nullptr)) {
    return;
  }

  auto* target_list_ptr = static_cast<absl::Span<const int64_t>*>(target_list_void_ptr);
  if (KS_UNLIKELY(target_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_realtime_target_list_match_cnt_v2(*attr_list_ptr, *target_list_ptr);
}

inline void c_list_to_set(void* list_void_ptr, void* res_void_ptr) {
  auto* list_ptr = static_cast<absl::Span<const int64_t>*>(list_void_ptr);
  if (KS_UNLIKELY(list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::set<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = list_to_set(*list_ptr);
}

inline void c_get_ocpc_filter_realtime_action_list_full(
    void* timestamp_list_void_ptr, void* photo_id_list_void_ptr, void* account_id_list_void_ptr,
    void* industry_v3_id_list_void_ptr, void* product_name_list_void_ptr, void* ocpc_action_list_void_ptr,
    int64_t adlog_time, int64_t target_id_1, int64_t target_id_2, void* res_void_ptr) {
  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* photo_id_list_ptr = static_cast<absl::Span<const int64_t>*>(photo_id_list_void_ptr);
  if (KS_UNLIKELY(photo_id_list_ptr == nullptr)) {
    return;
  }

  auto* account_id_list_ptr = static_cast<absl::Span<const int64_t>*>(account_id_list_void_ptr);
  if (KS_UNLIKELY(account_id_list_ptr == nullptr)) {
    return;
  }

  auto* industry_v3_id_list_ptr = static_cast<absl::Span<const int64_t>*>(industry_v3_id_list_void_ptr);
  if (KS_UNLIKELY(industry_v3_id_list_ptr == nullptr)) {
    return;
  }

  auto* product_name_list_ptr = static_cast<absl::Span<const int64_t>*>(product_name_list_void_ptr);
  if (KS_UNLIKELY(product_name_list_ptr == nullptr)) {
    return;
  }

  auto* ocpc_action_list_ptr = static_cast<absl::Span<const int64_t>*>(ocpc_action_list_void_ptr);
  if (KS_UNLIKELY(ocpc_action_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_ocpc_filter_realtime_action_list_full(
      *timestamp_list_ptr, *photo_id_list_ptr, *account_id_list_ptr, *industry_v3_id_list_ptr,
      *product_name_list_ptr, *ocpc_action_list_ptr, adlog_time, target_id_1, target_id_2);
}

inline void c_optional_value_or_float(void* x_void_ptr, void* y_void_ptr, void* res_void_ptr) {
  auto* x_ptr = static_cast<absl::optional<float>*>(x_void_ptr);
  if (KS_UNLIKELY(x_ptr == nullptr)) {
    return;
  }

  auto* y_ptr = static_cast<absl::optional<float>*>(y_void_ptr);
  if (KS_UNLIKELY(y_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = optional_value_or_float(*x_ptr, *y_ptr);
}

inline void c_add_feature_result_span_f32__i32__vector_extractresult(void* x1_void_ptr, int32_t size,
                                                                     void* result_void_ptr) {
  auto* x1_ptr = static_cast<absl::Span<const float>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* result_ptr = static_cast<std::vector<ExtractResult>*>(result_void_ptr);
  if (KS_UNLIKELY(result_ptr == nullptr)) {
    return;
  }

  return add_feature_result(*x1_ptr, size, result_ptr);
}

inline float c_log_10_or_default(float x, float default_v) { return log_10_or_default(x, default_v); }

inline float c_get_max_gmv_v2(int64_t max, void* pay_cnt_opt_void_ptr, void* week_gmv_opt_void_ptr) {
  auto* pay_cnt_opt_ptr = static_cast<absl::optional<int64_t>*>(pay_cnt_opt_void_ptr);
  if (KS_UNLIKELY(pay_cnt_opt_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* week_gmv_opt_ptr = static_cast<absl::optional<float>*>(week_gmv_opt_void_ptr);
  if (KS_UNLIKELY(week_gmv_opt_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return get_max_gmv_v2(max, *pay_cnt_opt_ptr, *week_gmv_opt_ptr);
}

inline void c_get_gmv_buckets_lining10(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_gmv_buckets_lining10();
}

inline bool c_simple_item_type_filter(int32_t item_type) { return simple_item_type_filter(item_type); }

inline void c_merchant_im_message_match_target_item(void* author_ids0_void_ptr, void* author_ids1_void_ptr,
                                                    void* author_id_void_ptr, void* res_void_ptr) {
  auto* author_ids0_ptr = static_cast<absl::Span<const int64_t>*>(author_ids0_void_ptr);
  if (KS_UNLIKELY(author_ids0_ptr == nullptr)) {
    return;
  }

  auto* author_ids1_ptr = static_cast<absl::Span<const int64_t>*>(author_ids1_void_ptr);
  if (KS_UNLIKELY(author_ids1_ptr == nullptr)) {
    return;
  }

  auto* author_id_ptr = static_cast<absl::optional<int64_t>*>(author_id_void_ptr);
  if (KS_UNLIKELY(author_id_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merchant_im_message_match_target_item(*author_ids0_ptr, *author_ids1_ptr, *author_id_ptr);
}

inline void c_get_mathched_target_id(int64_t live_author_id, int64_t photo_author_id, int64_t live_id,
                                     void* x7_level_2_list_void_ptr, void* x7_level_3_list_void_ptr,
                                     void* spu_id_list_void_ptr, int64_t target_type, void* res_void_ptr) {
  auto* x7_level_2_list_ptr = static_cast<absl::Span<const int64_t>*>(x7_level_2_list_void_ptr);
  if (KS_UNLIKELY(x7_level_2_list_ptr == nullptr)) {
    return;
  }

  auto* x7_level_3_list_ptr = static_cast<absl::Span<const int64_t>*>(x7_level_3_list_void_ptr);
  if (KS_UNLIKELY(x7_level_3_list_ptr == nullptr)) {
    return;
  }

  auto* spu_id_list_ptr = static_cast<absl::Span<const int64_t>*>(spu_id_list_void_ptr);
  if (KS_UNLIKELY(spu_id_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_mathched_target_id(live_author_id, photo_author_id, live_id, *x7_level_2_list_ptr,
                                    *x7_level_3_list_ptr, *spu_id_list_ptr, target_type);
}

inline void c_get_diff_action_from_two_list_combine_industry(
    void* first_timestamp_list_void_ptr, void* first_photo_list_void_ptr, void* first_industry_list_void_ptr,
    void* first_product_list_void_ptr, void* second_timestamp_list_void_ptr, void* second_photo_list_void_ptr,    // NOLINT
    void* second_industry_list_void_ptr, void* second_product_list_void_ptr, int32_t process_time,
    int32_t period, int32_t target_industry, void* res_void_ptr) {
  auto* first_timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(first_timestamp_list_void_ptr);
  if (KS_UNLIKELY(first_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* first_photo_list_ptr = static_cast<absl::Span<const int64_t>*>(first_photo_list_void_ptr);
  if (KS_UNLIKELY(first_photo_list_ptr == nullptr)) {
    return;
  }

  auto* first_industry_list_ptr = static_cast<absl::Span<const int64_t>*>(first_industry_list_void_ptr);
  if (KS_UNLIKELY(first_industry_list_ptr == nullptr)) {
    return;
  }

  auto* first_product_list_ptr = static_cast<absl::Span<const int64_t>*>(first_product_list_void_ptr);
  if (KS_UNLIKELY(first_product_list_ptr == nullptr)) {
    return;
  }

  auto* second_timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(second_timestamp_list_void_ptr);
  if (KS_UNLIKELY(second_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* second_photo_list_ptr = static_cast<absl::Span<const int64_t>*>(second_photo_list_void_ptr);
  if (KS_UNLIKELY(second_photo_list_ptr == nullptr)) {
    return;
  }

  auto* second_industry_list_ptr = static_cast<absl::Span<const int64_t>*>(second_industry_list_void_ptr);
  if (KS_UNLIKELY(second_industry_list_ptr == nullptr)) {
    return;
  }

  auto* second_product_list_ptr = static_cast<absl::Span<const int64_t>*>(second_product_list_void_ptr);
  if (KS_UNLIKELY(second_product_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<std::vector<int64_t>>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_diff_action_from_two_list_combine_industry(
      *first_timestamp_list_ptr, *first_photo_list_ptr, *first_industry_list_ptr, *first_product_list_ptr,
      *second_timestamp_list_ptr, *second_photo_list_ptr, *second_industry_list_ptr, *second_product_list_ptr,    // NOLINT
      process_time, period, target_industry);
}

inline void c_get_successive_time_gap_ordered(void* time_v_void_ptr, int64_t cur_timestamp,
                                              void* res_void_ptr) {
  auto* time_v_ptr = static_cast<absl::Span<const int64_t>*>(time_v_void_ptr);
  if (KS_UNLIKELY(time_v_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_successive_time_gap_ordered(*time_v_ptr, cur_timestamp);
}

inline void c_merge_int64_list_3_i64__i64__i64(int64_t x3, int64_t x6, int64_t x9, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_int64_list_3(x3, x6, x9);
}

inline void c_get_photo_sparse_feature_ui64__i32__optional_f32(uint64_t x1, int32_t x2, void* x8_void_ptr,
                                                               void* res_void_ptr) {
  auto* x8_ptr = static_cast<absl::optional<float>*>(x8_void_ptr);
  if (KS_UNLIKELY(x8_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_photo_sparse_feature(x1, x2, *x8_ptr);
}

inline void c_get_value_from_map_protomapview_ui64_bool__ui64(void* x4_void_ptr, uint64_t x2,
                                                              void* res_void_ptr) {
  auto* x4_ptr = static_cast<ProtoMapView<uint64_t, bool>*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<bool>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_value_from_map(*x4_ptr, x2);
}

inline int64_t c_get_day_state(void* state_vector_void_ptr) {
  auto* state_vector_ptr = static_cast<std::vector<int64_t>*>(state_vector_void_ptr);
  if (KS_UNLIKELY(state_vector_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_day_state(*state_vector_ptr);
}

inline void c_get_live_combine_ind_price_comm(void* cost_list_void_ptr, void* industry_list_void_ptr,
                                              void* author_cost_list_void_ptr,
                                              void* author_industry_list_void_ptr, void* res_void_ptr) {
  auto* cost_list_ptr = static_cast<absl::Span<const float>*>(cost_list_void_ptr);
  if (KS_UNLIKELY(cost_list_ptr == nullptr)) {
    return;
  }

  auto* industry_list_ptr = static_cast<absl::Span<const int64_t>*>(industry_list_void_ptr);
  if (KS_UNLIKELY(industry_list_ptr == nullptr)) {
    return;
  }

  auto* author_cost_list_ptr = static_cast<absl::Span<const float>*>(author_cost_list_void_ptr);
  if (KS_UNLIKELY(author_cost_list_ptr == nullptr)) {
    return;
  }

  auto* author_industry_list_ptr = static_cast<absl::Span<const int64_t>*>(author_industry_list_void_ptr);
  if (KS_UNLIKELY(author_industry_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_combine_ind_price_comm(*cost_list_ptr, *industry_list_ptr, *author_cost_list_ptr,
                                             *author_industry_list_ptr);
}

inline int64_t c_everyday_deep_conv_type(int64_t ocpc_action_type, int64_t deep_conv_type) {
  return everyday_deep_conv_type(ocpc_action_type, deep_conv_type);
}

inline void c_merge_universe_3_exp_tags(void* universe_exp1_void_ptr, void* universe_exp2_void_ptr,
                                        void* universe_exp3_void_ptr, void* res_void_ptr) {
  auto* universe_exp1_ptr = static_cast<absl::optional<int64_t>*>(universe_exp1_void_ptr);
  if (KS_UNLIKELY(universe_exp1_ptr == nullptr)) {
    return;
  }

  auto* universe_exp2_ptr = static_cast<absl::optional<int64_t>*>(universe_exp2_void_ptr);
  if (KS_UNLIKELY(universe_exp2_ptr == nullptr)) {
    return;
  }

  auto* universe_exp3_ptr = static_cast<absl::optional<int64_t>*>(universe_exp3_void_ptr);
  if (KS_UNLIKELY(universe_exp3_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_universe_3_exp_tags(*universe_exp1_ptr, *universe_exp2_ptr, *universe_exp3_ptr);
}

inline float c_get_local_sample_type(int64_t campaign_type, int64_t ocpc_action_type) {
  return get_local_sample_type(campaign_type, ocpc_action_type);
}

inline int64_t c_get_fans_num_bucket(void* fans_num_void_ptr) {
  auto* fans_num_ptr = static_cast<absl::optional<int64_t>*>(fans_num_void_ptr);
  if (KS_UNLIKELY(fans_num_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_fans_num_bucket(*fans_num_ptr);
}

inline void c_float_10x_to_int(void* v_void_ptr, void* res_void_ptr) {
  auto* v_ptr = static_cast<absl::optional<float>*>(v_void_ptr);
  if (KS_UNLIKELY(v_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = float_10x_to_int(*v_ptr);
}

inline float c_get_split_gmv(void* opt_gmv_void_ptr, uint64_t industry_id, float author_price) {
  auto* opt_gmv_ptr = static_cast<absl::optional<float>*>(opt_gmv_void_ptr);
  if (KS_UNLIKELY(opt_gmv_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return get_split_gmv(*opt_gmv_ptr, industry_id, author_price);
}

inline size_t c_get_from_kconf_map(int64_t account_id, void* default_product_name_void_ptr, bool is_train) {
  auto* default_product_name_ptr = static_cast<absl::string_view*>(default_product_name_void_ptr);
  if (KS_UNLIKELY(default_product_name_ptr == nullptr)) {
    return static_cast<size_t>(0);
  }

  return get_from_kconf_map(account_id, *default_product_name_ptr, is_train);
}

inline int32_t c_calc_avg_time(void* time_v_void_ptr) {
  auto* time_v_ptr = static_cast<std::vector<uint64_t>*>(time_v_void_ptr);
  if (KS_UNLIKELY(time_v_ptr == nullptr)) {
    return static_cast<int32_t>(0);
  }

  return calc_avg_time(*time_v_ptr);
}

inline int64_t c_get_user_id_by_u_type_optional_stringview__ui64__i32(void* x1_void_ptr, uint64_t x2,
                                                                      int32_t int_arg2) {
  auto* x1_ptr = static_cast<absl::optional<absl::string_view>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_user_id_by_u_type(*x1_ptr, x2, int_arg2);
}

inline void c_merge_float_list_all_vector_f32__vector_f32(void* x2_void_ptr, void* x4_void_ptr,
                                                          void* res_void_ptr) {
  auto* x2_ptr = static_cast<std::vector<float>*>(x2_void_ptr);
  if (KS_UNLIKELY(x2_ptr == nullptr)) {
    return;
  }

  auto* x4_ptr = static_cast<std::vector<float>*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_all(*x2_ptr, *x4_ptr);
}

inline float c_flt_epsilon() { return flt_epsilon(); }

inline float c_get_match_value_by_target_id(void* values_void_ptr, void* candidate_ids_void_ptr,
                                            int64_t target_id) {
  auto* values_ptr = static_cast<absl::Span<const float>*>(values_void_ptr);
  if (KS_UNLIKELY(values_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* candidate_ids_ptr = static_cast<absl::Span<const int64_t>*>(candidate_ids_void_ptr);
  if (KS_UNLIKELY(candidate_ids_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return get_match_value_by_target_id(*values_ptr, *candidate_ids_ptr, target_id);
}

inline void c_merge_int64_list_all_optional_i64__vector_i64__optional_i64(void* x4_void_ptr,
                                                                          void* x9_void_ptr,
                                                                          void* x13_void_ptr,
                                                                          void* res_void_ptr) {
  auto* x4_ptr = static_cast<absl::optional<int64_t>*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return;
  }

  auto* x9_ptr = static_cast<std::vector<int64_t>*>(x9_void_ptr);
  if (KS_UNLIKELY(x9_ptr == nullptr)) {
    return;
  }

  auto* x13_ptr = static_cast<absl::optional<int64_t>*>(x13_void_ptr);
  if (KS_UNLIKELY(x13_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_int64_list_all(*x4_ptr, *x9_ptr, *x13_ptr);
}

inline void c_get_edu_zhonglaonian_second_industry_id_hash_set(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::set<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_edu_zhonglaonian_second_industry_id_hash_set();
}

inline int64_t c_combine_i64__ui64(int64_t x1, uint64_t x2) { return combine(x1, x2); }

inline int64_t c_get_brand_behaviour_cnt_span_i64__vector_i64__i64__i32(void* x1_void_ptr, void* x3_void_ptr,
                                                                        int64_t x5, int32_t int_arg3) {
  auto* x1_ptr = static_cast<absl::Span<const int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* x3_ptr = static_cast<std::vector<int64_t>*>(x3_void_ptr);
  if (KS_UNLIKELY(x3_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_brand_behaviour_cnt(*x1_ptr, *x3_ptr, x5, int_arg3);
}

inline int64_t c_city_hash64_stringview(void* x4_void_ptr) {
  auto* x4_ptr = static_cast<absl::string_view*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return city_hash64(*x4_ptr);
}

inline void c_get_ecom_campaign_type(int64_t campaign_type, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_ecom_campaign_type(campaign_type);
}

inline void c_get_fangchan_second_industry_id_v2(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::set<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_fangchan_second_industry_id_v2();
}

inline float c_get_quantile_gmv(void* gmv_list_void_ptr, double q, void* pay_cnt_opt_void_ptr,
                                void* week_gmv_opt_void_ptr) {
  auto* gmv_list_ptr = static_cast<absl::Span<const int64_t>*>(gmv_list_void_ptr);
  if (KS_UNLIKELY(gmv_list_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* pay_cnt_opt_ptr = static_cast<absl::optional<int64_t>*>(pay_cnt_opt_void_ptr);
  if (KS_UNLIKELY(pay_cnt_opt_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* week_gmv_opt_ptr = static_cast<absl::optional<float>*>(week_gmv_opt_void_ptr);
  if (KS_UNLIKELY(week_gmv_opt_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return get_quantile_gmv(*gmv_list_ptr, q, *pay_cnt_opt_ptr, *week_gmv_opt_ptr);
}

inline float c_cast_to_float_f32(float x12) { return cast_to_float(x12); }

inline void c_get_ad_meta_dense_action_list(void* features_void_ptr, void* res_void_ptr) {
  auto* features_ptr = static_cast<absl::Span<const int64_t>*>(features_void_ptr);
  if (KS_UNLIKELY(features_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_ad_meta_dense_action_list(*features_ptr);
}

inline int64_t c_get_cur_page_num_cross_pos_id_platform(void* cur_page_num_void_ptr, void* pos_id_void_ptr,
                                                        void* platform_void_ptr) {
  auto* cur_page_num_ptr = static_cast<absl::optional<int64_t>*>(cur_page_num_void_ptr);
  if (KS_UNLIKELY(cur_page_num_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* pos_id_ptr = static_cast<absl::optional<int64_t>*>(pos_id_void_ptr);
  if (KS_UNLIKELY(pos_id_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* platform_ptr = static_cast<absl::string_view*>(platform_void_ptr);
  if (KS_UNLIKELY(platform_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_cur_page_num_cross_pos_id_platform(*cur_page_num_ptr, *pos_id_ptr, *platform_ptr);
}

inline void c_get_longterm_target_id_match_cnt_with_ts_max_3m(void* ts_list_void_ptr, int64_t adlog_ts,
                                                              void* attr_list_void_ptr, int64_t target_id,
                                                              size_t bucket_size, void* res_void_ptr) {
  auto* ts_list_ptr = static_cast<absl::Span<const int64_t>*>(ts_list_void_ptr);
  if (KS_UNLIKELY(ts_list_ptr == nullptr)) {
    return;
  }

  auto* attr_list_ptr = static_cast<absl::Span<const int64_t>*>(attr_list_void_ptr);
  if (KS_UNLIKELY(attr_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_longterm_target_id_match_cnt_with_ts_max_3m(*ts_list_ptr, adlog_ts, *attr_list_ptr,
                                                             target_id, bucket_size);
}

inline int64_t c_get_digit_in_place(void* price_void_ptr, int64_t place) {
  auto* price_ptr = static_cast<absl::optional<int64_t>*>(price_void_ptr);
  if (KS_UNLIKELY(price_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_digit_in_place(*price_ptr, place);
}

inline int64_t c_city_hash64_string(void* x10_void_ptr) {
  auto* x10_ptr = static_cast<std::string*>(x10_void_ptr);
  if (KS_UNLIKELY(x10_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return city_hash64(*x10_ptr);
}

inline float c_get_min_gmv_v2(int64_t min, void* pay_cnt_opt_void_ptr, void* week_gmv_opt_void_ptr) {
  auto* pay_cnt_opt_ptr = static_cast<absl::optional<int64_t>*>(pay_cnt_opt_void_ptr);
  if (KS_UNLIKELY(pay_cnt_opt_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* week_gmv_opt_ptr = static_cast<absl::optional<float>*>(week_gmv_opt_void_ptr);
  if (KS_UNLIKELY(week_gmv_opt_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return get_min_gmv_v2(min, *pay_cnt_opt_ptr, *week_gmv_opt_ptr);
}

inline void c_get_action_list_count(void* vec_void_ptr, void* res_void_ptr) {
  auto* vec_ptr = static_cast<absl::Span<const int64_t>*>(vec_void_ptr);
  if (KS_UNLIKELY(vec_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_action_list_count(*vec_ptr);
}

inline void c_get_universe_seq_with_max_len_and_timestamp(void* seq_void_ptr, void* medium_id_seq_void_ptr,
                                                          void* timestamp_seq_void_ptr, int32_t max_len,
                                                          int64_t current_timestamp, int64_t timestamp_delta,
                                                          void* res_void_ptr) {
  auto* seq_ptr = static_cast<absl::Span<const int64_t>*>(seq_void_ptr);
  if (KS_UNLIKELY(seq_ptr == nullptr)) {
    return;
  }

  auto* medium_id_seq_ptr = static_cast<absl::Span<const int64_t>*>(medium_id_seq_void_ptr);
  if (KS_UNLIKELY(medium_id_seq_ptr == nullptr)) {
    return;
  }

  auto* timestamp_seq_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_seq_void_ptr);
  if (KS_UNLIKELY(timestamp_seq_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_universe_seq_with_max_len_and_timestamp(*seq_ptr, *medium_id_seq_ptr, *timestamp_seq_ptr,
                                                         max_len, current_timestamp, timestamp_delta);
}

inline int64_t c_get_kpin_recruit_photo_deliver_cnt_td_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_recruit_photo_deliver_cnt_td_bucket(*raw_input_ptr);
}

inline void c_get_ts_list_bins_helper(int64_t current_ts, void* ts_list_void_ptr, void* bins_void_ptr,
                                      size_t max_len, void* res_void_ptr) {
  auto* ts_list_ptr = static_cast<absl::Span<const int64_t>*>(ts_list_void_ptr);
  if (KS_UNLIKELY(ts_list_ptr == nullptr)) {
    return;
  }

  auto* bins_ptr = static_cast<std::vector<float>*>(bins_void_ptr);
  if (KS_UNLIKELY(bins_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_ts_list_bins_helper(current_ts, *ts_list_ptr, *bins_ptr, max_len);
}

inline void c_get_p2l_sparse_feature_ui64__i32__stringview__optional_f32__optional_f32(
    uint64_t x1, int32_t x2, void* x7_void_ptr, void* x8_void_ptr, void* x3_void_ptr, void* res_void_ptr) {
  auto* x7_ptr = static_cast<absl::string_view*>(x7_void_ptr);
  if (KS_UNLIKELY(x7_ptr == nullptr)) {
    return;
  }

  auto* x8_ptr = static_cast<absl::optional<float>*>(x8_void_ptr);
  if (KS_UNLIKELY(x8_ptr == nullptr)) {
    return;
  }

  auto* x3_ptr = static_cast<absl::optional<float>*>(x3_void_ptr);
  if (KS_UNLIKELY(x3_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_p2l_sparse_feature(x1, x2, *x7_ptr, *x8_ptr, *x3_ptr);
}

inline void c_get_live_combine_price_comm(void* cost_list_void_ptr, void* author_cost_list_void_ptr,
                                          void* res_void_ptr) {
  auto* cost_list_ptr = static_cast<absl::Span<const float>*>(cost_list_void_ptr);
  if (KS_UNLIKELY(cost_list_ptr == nullptr)) {
    return;
  }

  auto* author_cost_list_ptr = static_cast<absl::Span<const float>*>(author_cost_list_void_ptr);
  if (KS_UNLIKELY(author_cost_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_combine_price_comm(*cost_list_ptr, *author_cost_list_ptr);
}

inline int64_t c_get_match_msg_cnt_by_author_id(void* msgs_cnt_list_void_ptr, int64_t author_id) {
  auto* msgs_cnt_list_ptr = static_cast<absl::Span<const int64_t>*>(msgs_cnt_list_void_ptr);
  if (KS_UNLIKELY(msgs_cnt_list_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_match_msg_cnt_by_author_id(*msgs_cnt_list_ptr, author_id);
}

inline void c_cast_to_int64_list_span_f32(void* x2_void_ptr, void* res_void_ptr) {
  auto* x2_ptr = static_cast<absl::Span<const float>*>(x2_void_ptr);
  if (KS_UNLIKELY(x2_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = cast_to_int64_list(*x2_ptr);
}

inline void c_get_lt_gs_ts_bins_detail(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_lt_gs_ts_bins_detail();
}

inline void c_seq_list_completion_sparse_string(void* seq_list_void_ptr, int32_t seq_max_len,
                                                void* res_void_ptr) {
  auto* seq_list_ptr = static_cast<absl::Span<const int64_t>*>(seq_list_void_ptr);
  if (KS_UNLIKELY(seq_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = seq_list_completion_sparse_string(*seq_list_ptr, seq_max_len);
}

inline float c_get_author_realtime_atv(void* live_author_price_24h_void_ptr,
                                       void* live_author_price_3d_void_ptr,
                                       void* live_author_price_14d_void_ptr,
                                       void* live_realtime_total_gmv_void_ptr,
                                       void* live_realtime_total_order_num_void_ptr) {
  auto* live_author_price_24h_ptr = static_cast<absl::optional<float>*>(live_author_price_24h_void_ptr);
  if (KS_UNLIKELY(live_author_price_24h_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* live_author_price_3d_ptr = static_cast<absl::optional<float>*>(live_author_price_3d_void_ptr);
  if (KS_UNLIKELY(live_author_price_3d_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* live_author_price_14d_ptr = static_cast<absl::optional<float>*>(live_author_price_14d_void_ptr);
  if (KS_UNLIKELY(live_author_price_14d_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* live_realtime_total_gmv_ptr = static_cast<absl::optional<int64_t>*>(live_realtime_total_gmv_void_ptr);    // NOLINT
  if (KS_UNLIKELY(live_realtime_total_gmv_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* live_realtime_total_order_num_ptr =
      static_cast<absl::optional<int64_t>*>(live_realtime_total_order_num_void_ptr);
  if (KS_UNLIKELY(live_realtime_total_order_num_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return get_author_realtime_atv(*live_author_price_24h_ptr, *live_author_price_3d_ptr,
                                 *live_author_price_14d_ptr, *live_realtime_total_gmv_ptr,
                                 *live_realtime_total_order_num_ptr);
}

inline void c_get_p2l_sparse_feature_use_product_id(int64_t campaign_type, int64_t live_creative_type,
                                                    void* base_url_void_ptr, void* live_info_data_void_ptr,
                                                    void* res_void_ptr) {
  auto* base_url_ptr = static_cast<absl::string_view*>(base_url_void_ptr);
  if (KS_UNLIKELY(base_url_ptr == nullptr)) {
    return;
  }

  auto* live_info_data_ptr = static_cast<absl::optional<int64_t>*>(live_info_data_void_ptr);
  if (KS_UNLIKELY(live_info_data_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_p2l_sparse_feature_use_product_id(campaign_type, live_creative_type, *base_url_ptr,
                                                   *live_info_data_ptr);
}

inline int32_t c_get_action_type_deep(uint64_t action_type) { return get_action_type_deep(action_type); }

inline int64_t c_get_minimum_last_gap_timestamp(void* room_pattern_list_void_ptr,
                                                void* timestamp_list_void_ptr, int64_t adlog_timestamp,
                                                void* room_pattern_type_void_ptr) {
  auto* room_pattern_list_ptr = static_cast<absl::Span<const int64_t>*>(room_pattern_list_void_ptr);
  if (KS_UNLIKELY(room_pattern_list_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* room_pattern_type_ptr = static_cast<RoomPatternType*>(room_pattern_type_void_ptr);
  if (KS_UNLIKELY(room_pattern_type_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_minimum_last_gap_timestamp(*room_pattern_list_ptr, *timestamp_list_ptr, adlog_timestamp,
                                        *room_pattern_type_ptr);
}

inline void c_ug_product_flag(void* product_name_void_ptr, void* res_void_ptr) {
  auto* product_name_ptr = static_cast<absl::string_view*>(product_name_void_ptr);
  if (KS_UNLIKELY(product_name_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = ug_product_flag(*product_name_ptr);
}

inline size_t c_get_replaced_account_id(int64_t account_id, bool is_train) {
  return get_replaced_account_id(account_id, is_train);
}

inline float c_get_log_price(void* price_void_ptr) {
  auto* price_ptr = static_cast<absl::optional<int64_t>*>(price_void_ptr);
  if (KS_UNLIKELY(price_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return get_log_price(*price_ptr);
}

inline void c_get_edu_second_industry_id_hash_set(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::set<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_edu_second_industry_id_hash_set();
}

inline void c_get_user_active_30d_bins(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_active_30d_bins();
}

inline void c_get_photo_combine_features(void* product_name_void_ptr, int64_t photo_id, int64_t industry_id,
                                         void* ad_style_void_ptr, void* cooperation_mode_void_ptr,
                                         void* context_id_void_ptr, int64_t feature_prefix_idx,
                                         void* res_void_ptr) {
  auto* product_name_ptr = static_cast<absl::string_view*>(product_name_void_ptr);
  if (KS_UNLIKELY(product_name_ptr == nullptr)) {
    return;
  }

  auto* ad_style_ptr = static_cast<absl::optional<int64_t>*>(ad_style_void_ptr);
  if (KS_UNLIKELY(ad_style_ptr == nullptr)) {
    return;
  }

  auto* cooperation_mode_ptr = static_cast<absl::optional<int64_t>*>(cooperation_mode_void_ptr);
  if (KS_UNLIKELY(cooperation_mode_ptr == nullptr)) {
    return;
  }

  auto* context_id_ptr = static_cast<absl::optional<int64_t>*>(context_id_void_ptr);
  if (KS_UNLIKELY(context_id_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_photo_combine_features(*product_name_ptr, photo_id, industry_id, *ad_style_ptr,
                                        *cooperation_mode_ptr, *context_id_ptr, feature_prefix_idx);
}

inline uint64_t c_generate_model_name_combine(void* model_names_void_ptr, void* is_soft_queue_void_ptr,
                                              void* interactive_form_void_ptr, int64_t ocpc_action_type,
                                              int32_t model_name_prefix_index, int32_t f_type,
                                              bool is_train) {
  auto* model_names_ptr = static_cast<absl::optional<absl::string_view>*>(model_names_void_ptr);
  if (KS_UNLIKELY(model_names_ptr == nullptr)) {
    return static_cast<uint64_t>(0);
  }

  auto* is_soft_queue_ptr = static_cast<absl::optional<bool>*>(is_soft_queue_void_ptr);
  if (KS_UNLIKELY(is_soft_queue_ptr == nullptr)) {
    return static_cast<uint64_t>(0);
  }

  auto* interactive_form_ptr = static_cast<absl::optional<int64_t>*>(interactive_form_void_ptr);
  if (KS_UNLIKELY(interactive_form_ptr == nullptr)) {
    return static_cast<uint64_t>(0);
  }

  return generate_model_name_combine(*model_names_ptr, *is_soft_queue_ptr, *interactive_form_ptr,
                                     ocpc_action_type, model_name_prefix_index, f_type, is_train);
}

inline void c_find_match_target_key_value(void* user_attr_ptr_key_void_ptr,
                                          void* user_attr_ptr_value_void_ptr, int64_t target_key,
                                          void* res_void_ptr) {
  auto* user_attr_ptr_key_ptr = static_cast<absl::Span<const int64_t>*>(user_attr_ptr_key_void_ptr);
  if (KS_UNLIKELY(user_attr_ptr_key_ptr == nullptr)) {
    return;
  }

  auto* user_attr_ptr_value_ptr = static_cast<absl::Span<const int64_t>*>(user_attr_ptr_value_void_ptr);
  if (KS_UNLIKELY(user_attr_ptr_value_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = find_match_target_key_value(*user_attr_ptr_key_ptr, *user_attr_ptr_value_ptr, target_key);
}

inline void c_get_scene_by_page(int64_t page_id, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_scene_by_page(page_id);
}

inline void c_get_w_level_bins(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_w_level_bins();
}

inline float c_get_ad_queue_type(void* ad_queue_type_v2_void_ptr) {
  auto* ad_queue_type_v2_ptr = static_cast<absl::optional<bool>*>(ad_queue_type_v2_void_ptr);
  if (KS_UNLIKELY(ad_queue_type_v2_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return get_ad_queue_type(*ad_queue_type_v2_ptr);
}

inline void c_get_user_pay_30d_bins(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_pay_30d_bins();
}

inline void c_get_cur_time_gap(void* time_v_void_ptr, int64_t cur_time, int32_t gap, void* res_void_ptr) {
  auto* time_v_ptr = static_cast<absl::Span<const int64_t>*>(time_v_void_ptr);
  if (KS_UNLIKELY(time_v_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_cur_time_gap(*time_v_ptr, cur_time, gap);
}

inline float c_cast_to_float_i64(int64_t x4) { return cast_to_float(x4); }

inline int64_t c_get_kpin_live_recruit_clk_plc_user_cnt_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_live_recruit_clk_plc_user_cnt_bucket(*raw_input_ptr);
}

inline void c_get_single_product_flag_and_price(void* live_total_gmv_void_ptr,
                                                void* live_total_order_num_void_ptr,
                                                void* live_author_hist_gmv_list_void_ptr,
                                                void* live_ordernum_threshold_void_ptr,
                                                void* live_min_max_diff_ratio_void_ptr, void* res_void_ptr) {
  auto* live_total_gmv_ptr = static_cast<absl::optional<int64_t>*>(live_total_gmv_void_ptr);
  if (KS_UNLIKELY(live_total_gmv_ptr == nullptr)) {
    return;
  }

  auto* live_total_order_num_ptr = static_cast<absl::optional<int64_t>*>(live_total_order_num_void_ptr);
  if (KS_UNLIKELY(live_total_order_num_ptr == nullptr)) {
    return;
  }

  auto* live_author_hist_gmv_list_ptr =
      static_cast<absl::Span<const int64_t>*>(live_author_hist_gmv_list_void_ptr);
  if (KS_UNLIKELY(live_author_hist_gmv_list_ptr == nullptr)) {
    return;
  }

  auto* live_ordernum_threshold_ptr = static_cast<absl::optional<float>*>(live_ordernum_threshold_void_ptr);
  if (KS_UNLIKELY(live_ordernum_threshold_ptr == nullptr)) {
    return;
  }

  auto* live_min_max_diff_ratio_ptr = static_cast<absl::optional<float>*>(live_min_max_diff_ratio_void_ptr);
  if (KS_UNLIKELY(live_min_max_diff_ratio_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_single_product_flag_and_price(*live_total_gmv_ptr, *live_total_order_num_ptr,
                                               *live_author_hist_gmv_list_ptr, *live_ordernum_threshold_ptr,
                                               *live_min_max_diff_ratio_ptr);
}

inline void c_get_succ_time_gap(void* time_v_void_ptr, int64_t cur_time, int32_t gap, void* res_void_ptr) {
  auto* time_v_ptr = static_cast<absl::Span<const int64_t>*>(time_v_void_ptr);
  if (KS_UNLIKELY(time_v_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_succ_time_gap(*time_v_ptr, cur_time, gap);
}

inline bool c_get_is_esp_roas_ocpc_type(int64_t ocpc_action_type) {
  return get_is_esp_roas_ocpc_type(ocpc_action_type);
}

inline void c_get_wangfu_second_industry_id_hash_set(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::set<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_wangfu_second_industry_id_hash_set();
}

inline void c_get_user_last_order_date_diff_bins(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_last_order_date_diff_bins();
}

inline void c_get_lt_op_ts_bins_detail(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_lt_op_ts_bins_detail();
}

inline void c_get_industry_seq_feature(void* source_list_void_ptr, void* res_void_ptr) {
  auto* source_list_ptr = static_cast<absl::Span<const int64_t>*>(source_list_void_ptr);
  if (KS_UNLIKELY(source_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_industry_seq_feature(*source_list_ptr);
}

inline int64_t c_get_kpin_recruit_card_show_cnt_7d_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_recruit_card_show_cnt_7d_bucket(*raw_input_ptr);
}

inline int64_t c_get_kpin_live_recruit_deliver_cnt_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_live_recruit_deliver_cnt_bucket(*raw_input_ptr);
}

inline void c_get_real_pay_combine_category(void* user_category_list_void_ptr, void* user_ts_list_void_ptr,
                                            int64_t adlog_ts, void* author_category_list_void_ptr,
                                            void* res_void_ptr) {
  auto* user_category_list_ptr = static_cast<absl::Span<const int64_t>*>(user_category_list_void_ptr);
  if (KS_UNLIKELY(user_category_list_ptr == nullptr)) {
    return;
  }

  auto* user_ts_list_ptr = static_cast<absl::Span<const int64_t>*>(user_ts_list_void_ptr);
  if (KS_UNLIKELY(user_ts_list_ptr == nullptr)) {
    return;
  }

  auto* author_category_list_ptr = static_cast<absl::Span<const int64_t>*>(author_category_list_void_ptr);
  if (KS_UNLIKELY(author_category_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_real_pay_combine_category(*user_category_list_ptr, *user_ts_list_ptr, adlog_ts,
                                           *author_category_list_ptr);
}

inline int64_t c_get_kpin_recruit_live_deliver_cnt_td_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_recruit_live_deliver_cnt_td_bucket(*raw_input_ptr);
}

inline void c_cast_to_float_list_vector_bool(void* x7_void_ptr, void* res_void_ptr) {
  auto* x7_ptr = static_cast<std::vector<bool>*>(x7_void_ptr);
  if (KS_UNLIKELY(x7_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = cast_to_float_list(*x7_ptr);
}

inline int64_t c_get_kpin_latest_recruit_photo_deliver_time_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_latest_recruit_photo_deliver_time_bucket(*raw_input_ptr);
}

inline void c_parse_gmv_bucket_mean(void* raw_bucket_mean_list_void_ptr, void* res_void_ptr) {
  auto* raw_bucket_mean_list_ptr = static_cast<absl::Span<const int64_t>*>(raw_bucket_mean_list_void_ptr);
  if (KS_UNLIKELY(raw_bucket_mean_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = parse_gmv_bucket_mean(*raw_bucket_mean_list_ptr);
}

inline void c_is_storewide_roas(int64_t ocpc_action_type, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = is_storewide_roas(ocpc_action_type);
}

inline int64_t c_combine_ui64__ui64(uint64_t x1, uint64_t x2) { return combine(x1, x2); }

inline void c_hard_search_seq_with_single_key_span_i64__optional_i64__i32(void* x1_void_ptr,
                                                                          void* x2_void_ptr, int32_t int_arg2,    // NOLINT
                                                                          void* res_void_ptr) {
  auto* x1_ptr = static_cast<absl::Span<const int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* x2_ptr = static_cast<absl::optional<int64_t>*>(x2_void_ptr);
  if (KS_UNLIKELY(x2_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = hard_search_seq_with_single_key(*x1_ptr, *x2_ptr, int_arg2);
}

inline void c_get_user_seq_with_target_hit_v2(void* action_list_void_ptr, void* target_id_void_ptr,
                                              void* target_id_v2_void_ptr, void* res_void_ptr) {
  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* target_id_ptr = static_cast<absl::optional<int64_t>*>(target_id_void_ptr);
  if (KS_UNLIKELY(target_id_ptr == nullptr)) {
    return;
  }

  auto* target_id_v2_ptr = static_cast<absl::optional<int64_t>*>(target_id_v2_void_ptr);
  if (KS_UNLIKELY(target_id_v2_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_seq_with_target_hit_v2(*action_list_ptr, *target_id_ptr, *target_id_v2_ptr);
}

inline void c_universe_ks_type(void* kwai_did_tag_void_ptr, void* nebula_did_tag_void_ptr,
                               void* res_void_ptr) {
  auto* kwai_did_tag_ptr = static_cast<absl::optional<int64_t>*>(kwai_did_tag_void_ptr);
  if (KS_UNLIKELY(kwai_did_tag_ptr == nullptr)) {
    return;
  }

  auto* nebula_did_tag_ptr = static_cast<absl::optional<int64_t>*>(nebula_did_tag_void_ptr);
  if (KS_UNLIKELY(nebula_did_tag_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = universe_ks_type(*kwai_did_tag_ptr, *nebula_did_tag_ptr);
}

inline int64_t c_map_price_to_level(void* price_void_ptr) {
  auto* price_ptr = static_cast<absl::optional<int64_t>*>(price_void_ptr);
  if (KS_UNLIKELY(price_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return map_price_to_level(*price_ptr);
}

inline void c_get_author_max_on_car_bins(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_author_max_on_car_bins();
}

inline void c_get_cnt_with_target_hit_combine_v2(void* action_list_void_ptr, void* timestamp_list_void_ptr,
                                                 void* target_id_void_ptr, void* target_id_v2_void_ptr,
                                                 int64_t timestamp, void* res_void_ptr) {
  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* target_id_ptr = static_cast<absl::optional<int64_t>*>(target_id_void_ptr);
  if (KS_UNLIKELY(target_id_ptr == nullptr)) {
    return;
  }

  auto* target_id_v2_ptr = static_cast<absl::optional<int64_t>*>(target_id_v2_void_ptr);
  if (KS_UNLIKELY(target_id_v2_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_cnt_with_target_hit_combine_v2(*action_list_ptr, *timestamp_list_ptr, *target_id_ptr,
                                                *target_id_v2_ptr, timestamp);
}

inline void c_get_lt_op_ts_bins(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_lt_op_ts_bins();
}

inline void c_get_photo_sparse_feature_ui64__i32__optional_i64(uint64_t x1, int32_t x2, void* x12_void_ptr,
                                                               void* res_void_ptr) {
  auto* x12_ptr = static_cast<absl::optional<int64_t>*>(x12_void_ptr);
  if (KS_UNLIKELY(x12_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_photo_sparse_feature(x1, x2, *x12_ptr);
}

inline void c_add_empty_flag(void* fea_value_list_void_ptr, void* res_void_ptr) {
  auto* fea_value_list_ptr = static_cast<absl::Span<const float>*>(fea_value_list_void_ptr);
  if (KS_UNLIKELY(fea_value_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = add_empty_flag(*fea_value_list_ptr);
}

inline void c_get_lt_gs_ts_bins(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_lt_gs_ts_bins();
}

inline void c_get_author_avail_coupon_match_cnt(int64_t author_id, void* item_list_void_ptr,
                                                void* target_list_void_ptr, void* source_id_list_void_ptr,
                                                void* res_void_ptr) {
  auto* item_list_ptr = static_cast<absl::Span<const int64_t>*>(item_list_void_ptr);
  if (KS_UNLIKELY(item_list_ptr == nullptr)) {
    return;
  }

  auto* target_list_ptr = static_cast<absl::Span<const int64_t>*>(target_list_void_ptr);
  if (KS_UNLIKELY(target_list_ptr == nullptr)) {
    return;
  }

  auto* source_id_list_ptr = static_cast<absl::Span<const int64_t>*>(source_id_list_void_ptr);
  if (KS_UNLIKELY(source_id_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr =
      get_author_avail_coupon_match_cnt(author_id, *item_list_ptr, *target_list_ptr, *source_id_list_ptr);
}

inline void c_get_author_max_car_len_bins(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_author_max_car_len_bins();
}

inline void c_get_ecom_action_time_segment_v2_longterm(void* timestamp_list_void_ptr,
                                                       void* action_list_void_ptr, int64_t adlog_time,
                                                       int64_t target_id, size_t bucket_size, size_t max_cnt,
                                                       void* res_void_ptr) {
  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_ecom_action_time_segment_v2_longterm(*timestamp_list_ptr, *action_list_ptr, adlog_time,
                                                      target_id, bucket_size, max_cnt);
}

inline void c_filter_action_by_time_diff_span_i64__span_i64__ui64(void* x10_void_ptr, void* x11_void_ptr,
                                                                  uint64_t x7, void* res_void_ptr) {
  auto* x10_ptr = static_cast<absl::Span<const int64_t>*>(x10_void_ptr);
  if (KS_UNLIKELY(x10_ptr == nullptr)) {
    return;
  }

  auto* x11_ptr = static_cast<absl::Span<const int64_t>*>(x11_void_ptr);
  if (KS_UNLIKELY(x11_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = filter_action_by_time_diff(*x10_ptr, *x11_ptr, x7);
}

inline int64_t c_get_kpin_recruit_card_click_cnt_90d_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_recruit_card_click_cnt_90d_bucket(*raw_input_ptr);
}

inline void c_get_action_list_before_timestamp(void* action_list_void_ptr, void* timestamp_list_void_ptr,
                                               int64_t adlog_timestamp, int64_t gap_in_seconds,
                                               int32_t max_len, void* res_void_ptr) {
  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_action_list_before_timestamp(*action_list_ptr, *timestamp_list_ptr, adlog_timestamp,
                                              gap_in_seconds, max_len);
}

inline int64_t c_get_everyday_state(void* adlog_time_void_ptr) {
  auto* adlog_time_ptr = static_cast<absl::optional<int64_t>*>(adlog_time_void_ptr);
  if (KS_UNLIKELY(adlog_time_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_everyday_state(*adlog_time_ptr);
}

inline void c_get_photo_for_pdct_ltr_protomapview_i64_i64__f32(void* x1_void_ptr, float float_arg1,
                                                               void* res_void_ptr) {
  auto* x1_ptr = static_cast<ProtoMapView<int64_t, int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_photo_for_pdct_ltr(*x1_ptr, float_arg1);
}

inline size_t c_inherit_id_new(int64_t photo_id, bool is_train, void* new_photo_id_void_ptr) {
  auto* new_photo_id_ptr = static_cast<absl::optional<int64_t>*>(new_photo_id_void_ptr);
  if (KS_UNLIKELY(new_photo_id_ptr == nullptr)) {
    return static_cast<size_t>(0);
  }

  return inherit_id_new(photo_id, is_train, *new_photo_id_ptr);
}

inline uint64_t c_value_or_ui64__i32(uint64_t x1, int32_t int_arg1) { return value_or(x1, int_arg1); }

inline int64_t c_get_kpin_live_recruit_show_cnt_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_live_recruit_show_cnt_bucket(*raw_input_ptr);
}

inline void c_hard_search_seq_with_single_key_vector_i64__optional_stringview__i32(void* x11_void_ptr,
                                                                                   void* x12_void_ptr,
                                                                                   int32_t int_arg2,
                                                                                   void* res_void_ptr) {
  auto* x11_ptr = static_cast<std::vector<int64_t>*>(x11_void_ptr);
  if (KS_UNLIKELY(x11_ptr == nullptr)) {
    return;
  }

  auto* x12_ptr = static_cast<absl::optional<absl::string_view>*>(x12_void_ptr);
  if (KS_UNLIKELY(x12_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = hard_search_seq_with_single_key(*x11_ptr, *x12_ptr, int_arg2);
}

inline void c_get_min_gap_with_target_hit_combine(void* action_list_void_ptr, void* timestamp_list_void_ptr,
                                                  int64_t target_id, int64_t timestamp, void* res_void_ptr) {
  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_min_gap_with_target_hit_combine(*action_list_ptr, *timestamp_list_ptr, target_id, timestamp);    // NOLINT
}

inline size_t c_match_intersection_cnt(void* v1_void_ptr, void* v2_void_ptr) {
  auto* v1_ptr = static_cast<absl::Span<const int64_t>*>(v1_void_ptr);
  if (KS_UNLIKELY(v1_ptr == nullptr)) {
    return static_cast<size_t>(0);
  }

  auto* v2_ptr = static_cast<absl::Span<const int64_t>*>(v2_void_ptr);
  if (KS_UNLIKELY(v2_ptr == nullptr)) {
    return static_cast<size_t>(0);
  }

  return match_intersection_cnt(*v1_ptr, *v2_ptr);
}

inline float c_check_account_selection_v2(int64_t account_id) {
  return check_account_selection_v2(account_id);
}

inline void c_get_u_type_bins(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_u_type_bins();
}

inline void c_get_user_photo_combine_features(int64_t photo_id, int64_t age, void* gender_void_ptr,
                                              int64_t author_id, void* res_void_ptr) {
  auto* gender_ptr = static_cast<absl::string_view*>(gender_void_ptr);
  if (KS_UNLIKELY(gender_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_photo_combine_features(photo_id, age, *gender_ptr, author_id);
}

inline int64_t c_get_query_type_product_hash(void* x_void_ptr, void* y_void_ptr) {
  auto* x_ptr = static_cast<absl::string_view*>(x_void_ptr);
  if (KS_UNLIKELY(x_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* y_ptr = static_cast<absl::optional<int64_t>*>(y_void_ptr);
  if (KS_UNLIKELY(y_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_query_type_product_hash(*x_ptr, *y_ptr);
}

inline void c_get_ecom_action_time_segment(void* timestamp_list_void_ptr, void* action_list_void_ptr,
                                           int64_t adlog_time, int64_t target_id, size_t bucket_size,
                                           size_t max_cnt, void* res_void_ptr) {
  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_ecom_action_time_segment(*timestamp_list_ptr, *action_list_ptr, adlog_time, target_id,
                                          bucket_size, max_cnt);
}

inline void c_get_author_exp_ratio_bins(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_author_exp_ratio_bins();
}

inline void c_get_item_card_scene_flag(int64_t page_id, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_item_card_scene_flag(page_id);
}

inline void c_get_min_gap_with_target_hit_combine_v2(void* action_list_void_ptr,
                                                     void* timestamp_list_void_ptr, void* target_id_void_ptr,
                                                     void* target_id_v2_void_ptr, int64_t timestamp,
                                                     void* res_void_ptr) {
  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* target_id_ptr = static_cast<absl::optional<int64_t>*>(target_id_void_ptr);
  if (KS_UNLIKELY(target_id_ptr == nullptr)) {
    return;
  }

  auto* target_id_v2_ptr = static_cast<absl::optional<int64_t>*>(target_id_v2_void_ptr);
  if (KS_UNLIKELY(target_id_v2_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_min_gap_with_target_hit_combine_v2(*action_list_ptr, *timestamp_list_ptr, *target_id_ptr,
                                                    *target_id_v2_ptr, timestamp);
}

inline void c_get_ocpc_filter_action_list_before_timestamp(void* action_list_void_ptr,
                                                           void* ocpc_action_list_void_ptr,
                                                           void* timestamp_list_void_ptr, int64_t target_id_1,    // NOLINT
                                                           int64_t target_id_2, int64_t adlog_timestamp,
                                                           int64_t gap_in_seconds, int32_t max_len,
                                                           void* res_void_ptr) {
  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* ocpc_action_list_ptr = static_cast<absl::Span<const int64_t>*>(ocpc_action_list_void_ptr);
  if (KS_UNLIKELY(ocpc_action_list_ptr == nullptr)) {
    return;
  }

  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_ocpc_filter_action_list_before_timestamp(*action_list_ptr, *ocpc_action_list_ptr,
                                                          *timestamp_list_ptr, target_id_1, target_id_2,
                                                          adlog_timestamp, gap_in_seconds, max_len);
}

inline void c_timestamp_second_2_millisecond(void* vec_void_ptr, void* res_void_ptr) {
  auto* vec_ptr = static_cast<absl::Span<const int64_t>*>(vec_void_ptr);
  if (KS_UNLIKELY(vec_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = timestamp_second_2_millisecond(*vec_ptr);
}

inline void c_get_user_pay_365d_bins(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_pay_365d_bins();
}

inline int64_t c_get_kpin_live_recruit_out_show_user_cnt_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_live_recruit_out_show_user_cnt_bucket(*raw_input_ptr);
}

inline int32_t c_simple_item_type_label_extractor(int32_t item_type) {
  return simple_item_type_label_extractor(item_type);
}

inline void c_get_author_item_exp_time_bins(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_author_item_exp_time_bins();
}

inline int64_t c_get_kpin_recruit_card_click_cnt_30d_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_recruit_card_click_cnt_30d_bucket(*raw_input_ptr);
}

inline void c_get_follow_ocpx_type_i64(int64_t x1, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_follow_ocpx_type(x1);
}

inline float c_cast_to_float_ui32(uint32_t x1) { return cast_to_float(x1); }

inline int64_t c_get_kpin_latest_recruit_live_deliver_time_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_latest_recruit_live_deliver_time_bucket(*raw_input_ptr);
}

inline int64_t c_get_kpin_live_recruit_deliver_user_cnt_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_live_recruit_deliver_user_cnt_bucket(*raw_input_ptr);
}

inline int64_t c_get_kpin_recruit_card_click_cnt_7d_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_recruit_card_click_cnt_7d_bucket(*raw_input_ptr);
}

inline float c_get_im_ocpx_action(int64_t ocpc_action_type) { return get_im_ocpx_action(ocpc_action_type); }

inline void c_add_feature_result_f32__i32__vector_extractresult(float x2, int32_t size,
                                                                void* result_void_ptr) {
  auto* result_ptr = static_cast<std::vector<ExtractResult>*>(result_void_ptr);
  if (KS_UNLIKELY(result_ptr == nullptr)) {
    return;
  }

  return add_feature_result(x2, size, result_ptr);
}

inline void c_get_ad_meta_multi_action_list(void* features_void_ptr, void* res_void_ptr) {
  auto* features_ptr = static_cast<absl::Span<const int64_t>*>(features_void_ptr);
  if (KS_UNLIKELY(features_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_ad_meta_multi_action_list(*features_ptr);
}

inline void c_get_realtime_target_list_match_cnt(void* attr_list_void_ptr, void* target_list_void_ptr,
                                                 void* res_void_ptr) {
  auto* attr_list_ptr = static_cast<absl::Span<const int64_t>*>(attr_list_void_ptr);
  if (KS_UNLIKELY(attr_list_ptr == nullptr)) {
    return;
  }

  auto* target_list_ptr = static_cast<absl::Span<const int64_t>*>(target_list_void_ptr);
  if (KS_UNLIKELY(target_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_realtime_target_list_match_cnt(*attr_list_ptr, *target_list_ptr);
}

inline int64_t c_get_feature_from_industry_lists_map(void* key_list_void_ptr, void* value_list_void_ptr,
                                                     int64_t key) {
  auto* key_list_ptr = static_cast<absl::Span<const int64_t>*>(key_list_void_ptr);
  if (KS_UNLIKELY(key_list_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* value_list_ptr = static_cast<absl::Span<const int64_t>*>(value_list_void_ptr);
  if (KS_UNLIKELY(value_list_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_feature_from_industry_lists_map(*key_list_ptr, *value_list_ptr, key);
}

inline void c_get_l_type_bins(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_l_type_bins();
}

inline int64_t c_get_kpin_recruit_card_click_cnt_60d_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_recruit_card_click_cnt_60d_bucket(*raw_input_ptr);
}

inline int64_t c_get_kpin_deliver_cnt_7d_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_deliver_cnt_7d_bucket(*raw_input_ptr);
}

inline void c_get_seq_with_max_len(void* seq_void_ptr, int32_t max_len, void* res_void_ptr) {
  auto* seq_ptr = static_cast<absl::Span<const int64_t>*>(seq_void_ptr);
  if (KS_UNLIKELY(seq_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_seq_with_max_len(*seq_ptr, max_len);
}

inline void c_combine_vector_i64__i64(void* x2_void_ptr, int64_t x4, void* res_void_ptr) {
  auto* x2_ptr = static_cast<std::vector<int64_t>*>(x2_void_ptr);
  if (KS_UNLIKELY(x2_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = combine(*x2_ptr, x4);
}

inline void c_hard_search_seq_with_single_key_vector_i64__optional_i64__i32(void* x11_void_ptr,
                                                                            void* x12_void_ptr,
                                                                            int32_t int_arg2,
                                                                            void* res_void_ptr) {
  auto* x11_ptr = static_cast<std::vector<int64_t>*>(x11_void_ptr);
  if (KS_UNLIKELY(x11_ptr == nullptr)) {
    return;
  }

  auto* x12_ptr = static_cast<absl::optional<int64_t>*>(x12_void_ptr);
  if (KS_UNLIKELY(x12_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = hard_search_seq_with_single_key(*x11_ptr, *x12_ptr, int_arg2);
}

inline int64_t c_get_kpin_deliver_cnt_30d_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_deliver_cnt_30d_bucket(*raw_input_ptr);
}

inline bool c_get_is_photo_type(int64_t campaign_type, int64_t live_creative_type) {
  return get_is_photo_type(campaign_type, live_creative_type);
}

inline void c_get_item_comment_score(void* scores0_void_ptr, void* scores1_void_ptr, void* res_void_ptr) {
  auto* scores0_ptr = static_cast<absl::Span<const int64_t>*>(scores0_void_ptr);
  if (KS_UNLIKELY(scores0_ptr == nullptr)) {
    return;
  }

  auto* scores1_ptr = static_cast<absl::Span<const int64_t>*>(scores1_void_ptr);
  if (KS_UNLIKELY(scores1_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_item_comment_score(*scores0_ptr, *scores1_ptr);
}

inline void c_combine_v2(void* action_list_void_ptr, void* target_id_void_ptr, void* target_id_v2_void_ptr,
                         void* res_void_ptr) {
  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* target_id_ptr = static_cast<absl::optional<int64_t>*>(target_id_void_ptr);
  if (KS_UNLIKELY(target_id_ptr == nullptr)) {
    return;
  }

  auto* target_id_v2_ptr = static_cast<absl::optional<int64_t>*>(target_id_v2_void_ptr);
  if (KS_UNLIKELY(target_id_v2_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = combine_v2(*action_list_ptr, *target_id_ptr, *target_id_v2_ptr);
}

inline void c_get_item_id_from_yellow_car_list(void* item_attr_list_void_ptr, int32_t index,
                                               void* res_void_ptr) {
  auto* item_attr_list_ptr = static_cast<absl::Span<const int64_t>*>(item_attr_list_void_ptr);
  if (KS_UNLIKELY(item_attr_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_item_id_from_yellow_car_list(*item_attr_list_ptr, index);
}

inline void c_get_realtime_target_id_order_cross_list(void* attr_list_void_ptr, int64_t target_id,
                                                      void* res_void_ptr) {
  auto* attr_list_ptr = static_cast<absl::Span<const int64_t>*>(attr_list_void_ptr);
  if (KS_UNLIKELY(attr_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_realtime_target_id_order_cross_list(*attr_list_ptr, target_id);
}

inline void c_get_realtime_target_id_cross_list(void* attr_list_void_ptr, int64_t target_id,
                                                void* res_void_ptr) {
  auto* attr_list_ptr = static_cast<absl::Span<const int64_t>*>(attr_list_void_ptr);
  if (KS_UNLIKELY(attr_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_realtime_target_id_cross_list(*attr_list_ptr, target_id);
}

inline float c_log_e_or_default(float x, float default_v) { return log_e_or_default(x, default_v); }

inline void c_get_industry_v5_category_action_match_dense(
    void* log_interface_void_ptr, void* pos_type_accountid_void_ptr, void* pos_type_accountid_list_void_ptr,
    int32_t pos, int64_t adlog_time, int64_t adlog_account_id, void* actionlist_timestamp_list_void_ptr,
    size_t bucket_size, size_t max_cnt, void* res_void_ptr) {
  auto* log_interface_ptr = static_cast<AdLogInterface*>(log_interface_void_ptr);
  if (KS_UNLIKELY(log_interface_ptr == nullptr)) {
    return;
  }

  auto* pos_type_accountid_ptr = static_cast<KeyPosType*>(pos_type_accountid_void_ptr);
  if (KS_UNLIKELY(pos_type_accountid_ptr == nullptr)) {
    return;
  }

  auto* pos_type_accountid_list_ptr = static_cast<KeyPosType*>(pos_type_accountid_list_void_ptr);
  if (KS_UNLIKELY(pos_type_accountid_list_ptr == nullptr)) {
    return;
  }

  auto* actionlist_timestamp_list_ptr =
      static_cast<absl::Span<const int64_t>*>(actionlist_timestamp_list_void_ptr);
  if (KS_UNLIKELY(actionlist_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_industry_v5_category_action_match_dense(
      *log_interface_ptr, *pos_type_accountid_ptr, *pos_type_accountid_list_ptr, pos, adlog_time,
      adlog_account_id, *actionlist_timestamp_list_ptr, bucket_size, max_cnt);
}

inline void c_get_real_view_combine_category(void* user_category_list_void_ptr, void* user_ts_list_void_ptr,
                                             int64_t adlog_ts, void* author_category_list_void_ptr,
                                             void* res_void_ptr) {
  auto* user_category_list_ptr = static_cast<absl::Span<const int64_t>*>(user_category_list_void_ptr);
  if (KS_UNLIKELY(user_category_list_ptr == nullptr)) {
    return;
  }

  auto* user_ts_list_ptr = static_cast<absl::Span<const int64_t>*>(user_ts_list_void_ptr);
  if (KS_UNLIKELY(user_ts_list_ptr == nullptr)) {
    return;
  }

  auto* author_category_list_ptr = static_cast<absl::Span<const int64_t>*>(author_category_list_void_ptr);
  if (KS_UNLIKELY(author_category_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_real_view_combine_category(*user_category_list_ptr, *user_ts_list_ptr, adlog_ts,
                                            *author_category_list_ptr);
}

inline void c_add_time(void* adlog_time_void_ptr, int64_t time_diff, void* res_void_ptr) {
  auto* adlog_time_ptr = static_cast<absl::optional<int64_t>*>(adlog_time_void_ptr);
  if (KS_UNLIKELY(adlog_time_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = add_time(*adlog_time_ptr, time_diff);
}

inline void c_get_p2l_sparse_feature_ui64__i32__stringview__optional_i64__optional_i64(
    uint64_t x1, int32_t x2, void* x7_void_ptr, void* x8_void_ptr, void* x3_void_ptr, void* res_void_ptr) {
  auto* x7_ptr = static_cast<absl::string_view*>(x7_void_ptr);
  if (KS_UNLIKELY(x7_ptr == nullptr)) {
    return;
  }

  auto* x8_ptr = static_cast<absl::optional<int64_t>*>(x8_void_ptr);
  if (KS_UNLIKELY(x8_ptr == nullptr)) {
    return;
  }

  auto* x3_ptr = static_cast<absl::optional<int64_t>*>(x3_void_ptr);
  if (KS_UNLIKELY(x3_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_p2l_sparse_feature(x1, x2, *x7_ptr, *x8_ptr, *x3_ptr);
}

inline float c_get_period_avg_gmv_dense(void* gmv_list_void_ptr, void* paycnt_list_void_ptr, int32_t start,
                                        int32_t end) {
  auto* gmv_list_ptr = static_cast<absl::Span<const int64_t>*>(gmv_list_void_ptr);
  if (KS_UNLIKELY(gmv_list_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* paycnt_list_ptr = static_cast<absl::Span<const int64_t>*>(paycnt_list_void_ptr);
  if (KS_UNLIKELY(paycnt_list_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return get_period_avg_gmv_dense(*gmv_list_ptr, *paycnt_list_ptr, start, end);
}

inline void c_get_random_normal_float(int64_t n, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_random_normal_float(n);
}

inline void c_get_user_hist_req_sub_page_ids_cross_pos_id_platform(void* sub_page_ids_void_ptr,
                                                                   void* pos_id_void_ptr,
                                                                   void* platform_void_ptr,
                                                                   void* res_void_ptr) {
  auto* sub_page_ids_ptr = static_cast<absl::Span<const int64_t>*>(sub_page_ids_void_ptr);
  if (KS_UNLIKELY(sub_page_ids_ptr == nullptr)) {
    return;
  }

  auto* pos_id_ptr = static_cast<absl::optional<int64_t>*>(pos_id_void_ptr);
  if (KS_UNLIKELY(pos_id_ptr == nullptr)) {
    return;
  }

  auto* platform_ptr = static_cast<absl::string_view*>(platform_void_ptr);
  if (KS_UNLIKELY(platform_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr =
      get_user_hist_req_sub_page_ids_cross_pos_id_platform(*sub_page_ids_ptr, *pos_id_ptr, *platform_ptr);
}

inline void c_merge_int64_list_all_span_i64__span_i64(void* x1_void_ptr, void* x2_void_ptr,
                                                      void* res_void_ptr) {
  auto* x1_ptr = static_cast<absl::Span<const int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* x2_ptr = static_cast<absl::Span<const int64_t>*>(x2_void_ptr);
  if (KS_UNLIKELY(x2_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_int64_list_all(*x1_ptr, *x2_ptr);
}

inline int64_t c_get_kpin_live_recruit_show_user_cnt_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_live_recruit_show_user_cnt_bucket(*raw_input_ptr);
}

inline void c_merge_int64_list_all_optional_f32__optional_f32__optional_f32(void* x4_void_ptr,
                                                                            void* x10_void_ptr,
                                                                            void* x14_void_ptr,
                                                                            void* res_void_ptr) {
  auto* x4_ptr = static_cast<absl::optional<float>*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return;
  }

  auto* x10_ptr = static_cast<absl::optional<float>*>(x10_void_ptr);
  if (KS_UNLIKELY(x10_ptr == nullptr)) {
    return;
  }

  auto* x14_ptr = static_cast<absl::optional<float>*>(x14_void_ptr);
  if (KS_UNLIKELY(x14_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_int64_list_all(*x4_ptr, *x10_ptr, *x14_ptr);
}

inline void c_merge_int64_list_all_optional_i64__optional_i64__optional_i64(void* x4_void_ptr,
                                                                            void* x10_void_ptr,
                                                                            void* x14_void_ptr,
                                                                            void* res_void_ptr) {
  auto* x4_ptr = static_cast<absl::optional<int64_t>*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return;
  }

  auto* x10_ptr = static_cast<absl::optional<int64_t>*>(x10_void_ptr);
  if (KS_UNLIKELY(x10_ptr == nullptr)) {
    return;
  }

  auto* x14_ptr = static_cast<absl::optional<int64_t>*>(x14_void_ptr);
  if (KS_UNLIKELY(x14_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_int64_list_all(*x4_ptr, *x10_ptr, *x14_ptr);
}

inline void c_merge_float_list_all_optional_i64__optional_i64__optional_i64(void* x4_void_ptr,
                                                                            void* x10_void_ptr,
                                                                            void* x14_void_ptr,
                                                                            void* res_void_ptr) {
  auto* x4_ptr = static_cast<absl::optional<int64_t>*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return;
  }

  auto* x10_ptr = static_cast<absl::optional<int64_t>*>(x10_void_ptr);
  if (KS_UNLIKELY(x10_ptr == nullptr)) {
    return;
  }

  auto* x14_ptr = static_cast<absl::optional<int64_t>*>(x14_void_ptr);
  if (KS_UNLIKELY(x14_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_all(*x4_ptr, *x10_ptr, *x14_ptr);
}

inline void c_get_live_audience(void* live_audience_void_ptr, void* res_void_ptr) {
  auto* live_audience_ptr = static_cast<absl::optional<int64_t>*>(live_audience_void_ptr);
  if (KS_UNLIKELY(live_audience_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_audience(*live_audience_ptr);
}

inline void c_cast_to_int64_list_vector_f32(void* x6_void_ptr, void* res_void_ptr) {
  auto* x6_ptr = static_cast<std::vector<float>*>(x6_void_ptr);
  if (KS_UNLIKELY(x6_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = cast_to_int64_list(*x6_ptr);
}

inline int32_t c_java_hash_code_stringview(void* x2_void_ptr) {
  auto* x2_ptr = static_cast<absl::string_view*>(x2_void_ptr);
  if (KS_UNLIKELY(x2_ptr == nullptr)) {
    return static_cast<int32_t>(0);
  }

  return java_hash_code(*x2_ptr);
}

inline void c_get_user_attribute_combine_live(void* gender_void_ptr, int32_t age, int64_t live_id,
                                              int64_t author_id, bool is_train, void* avg_paycnt_void_ptr,
                                              void* play_duration_void_ptr, void* res_void_ptr) {
  auto* gender_ptr = static_cast<absl::string_view*>(gender_void_ptr);
  if (KS_UNLIKELY(gender_ptr == nullptr)) {
    return;
  }

  auto* avg_paycnt_ptr = static_cast<absl::optional<int64_t>*>(avg_paycnt_void_ptr);
  if (KS_UNLIKELY(avg_paycnt_ptr == nullptr)) {
    return;
  }

  auto* play_duration_ptr = static_cast<absl::optional<int64_t>*>(play_duration_void_ptr);
  if (KS_UNLIKELY(play_duration_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_attribute_combine_live(*gender_ptr, age, live_id, author_id, is_train, *avg_paycnt_ptr,
                                             *play_duration_ptr);
}

inline float c_get_single_product_flag(void* price_list_void_ptr) {
  auto* price_list_ptr = static_cast<absl::Span<const int64_t>*>(price_list_void_ptr);
  if (KS_UNLIKELY(price_list_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return get_single_product_flag(*price_list_ptr);
}

inline void c_get_time_segment_cnt_log_value_v1(void* timestamp_list_void_ptr, void* action_list_void_ptr,
                                                int64_t adlog_time, void* target_ids0_void_ptr,
                                                void* target_id_void_ptr, void* target_ids1_void_ptr,
                                                size_t bucket_size, size_t max_cnt, void* res_void_ptr) {
  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* target_ids0_ptr = static_cast<absl::Span<const int64_t>*>(target_ids0_void_ptr);
  if (KS_UNLIKELY(target_ids0_ptr == nullptr)) {
    return;
  }

  auto* target_id_ptr = static_cast<absl::optional<int64_t>*>(target_id_void_ptr);
  if (KS_UNLIKELY(target_id_ptr == nullptr)) {
    return;
  }

  auto* target_ids1_ptr = static_cast<absl::Span<const int64_t>*>(target_ids1_void_ptr);
  if (KS_UNLIKELY(target_ids1_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr =
      get_time_segment_cnt_log_value_v1(*timestamp_list_ptr, *action_list_ptr, adlog_time, *target_ids0_ptr,
                                        *target_id_ptr, *target_ids1_ptr, bucket_size, max_cnt);
}

inline void c_log_plus_1_e(float x, void* res_void_ptr) {
  auto* res_ptr = static_cast<absl::optional<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = log_plus_1_e(x);
}

inline void c_get_user_hist_req_sub_page_ids_cross_pos_id(void* sub_page_ids_void_ptr, void* pos_id_void_ptr,
                                                          void* res_void_ptr) {
  auto* sub_page_ids_ptr = static_cast<absl::Span<const int64_t>*>(sub_page_ids_void_ptr);
  if (KS_UNLIKELY(sub_page_ids_ptr == nullptr)) {
    return;
  }

  auto* pos_id_ptr = static_cast<absl::optional<int64_t>*>(pos_id_void_ptr);
  if (KS_UNLIKELY(pos_id_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_hist_req_sub_page_ids_cross_pos_id(*sub_page_ids_ptr, *pos_id_ptr);
}

inline int64_t c_target_author_in_author_list_or_not(void* author_list_void_ptr, int64_t target_id) {
  auto* author_list_ptr = static_cast<absl::Span<const int64_t>*>(author_list_void_ptr);
  if (KS_UNLIKELY(author_list_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return target_author_in_author_list_or_not(*author_list_ptr, target_id);
}

inline void c_get_user_hist_page_num_succ_cross_pos_id(void* page_num_list_void_ptr,
                                                       void* cur_page_num_void_ptr, void* pos_id_void_ptr,
                                                       void* res_void_ptr) {
  auto* page_num_list_ptr = static_cast<absl::Span<const int64_t>*>(page_num_list_void_ptr);
  if (KS_UNLIKELY(page_num_list_ptr == nullptr)) {
    return;
  }

  auto* cur_page_num_ptr = static_cast<absl::optional<int64_t>*>(cur_page_num_void_ptr);
  if (KS_UNLIKELY(cur_page_num_ptr == nullptr)) {
    return;
  }

  auto* pos_id_ptr = static_cast<absl::optional<int64_t>*>(pos_id_void_ptr);
  if (KS_UNLIKELY(pos_id_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_hist_page_num_succ_cross_pos_id(*page_num_list_ptr, *cur_page_num_ptr, *pos_id_ptr);
}

inline void c_get_gmv_buckets_wanglianhai03(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_gmv_buckets_wanglianhai03();
}

inline int64_t c_get_merchant_gap_dis_rs(int64_t latest_timestamp, int64_t adlog_timestamp,
                                         int64_t matched_cnt, int64_t seconds) {
  return get_merchant_gap_dis_rs(latest_timestamp, adlog_timestamp, matched_cnt, seconds);
}

inline int64_t c_combine_i64__optional_i64(int64_t x9, void* x10_void_ptr) {
  auto* x10_ptr = static_cast<absl::optional<int64_t>*>(x10_void_ptr);
  if (KS_UNLIKELY(x10_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return combine(x9, *x10_ptr);
}

inline int64_t c_log_10_or_default_cast_int(float x, int64_t default_v) {
  return log_10_or_default_cast_int(x, default_v);
}

inline void c_combine_vector_i64__ui64(void* x4_void_ptr, uint64_t x5, void* res_void_ptr) {
  auto* x4_ptr = static_cast<std::vector<int64_t>*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = combine(*x4_ptr, x5);
}

inline void c_add_feature_result_span_i64__vector_extractresult(void* x1_void_ptr, void* result_void_ptr) {
  auto* x1_ptr = static_cast<absl::Span<const int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* result_ptr = static_cast<std::vector<ExtractResult>*>(result_void_ptr);
  if (KS_UNLIKELY(result_ptr == nullptr)) {
    return;
  }

  return add_feature_result(*x1_ptr, result_ptr);
}

inline void c_combine_span_i64__i64(void* x1_void_ptr, int64_t x3, void* res_void_ptr) {
  auto* x1_ptr = static_cast<absl::Span<const int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = combine(*x1_ptr, x3);
}

inline void c_get_period_gmv_trend(void* gmv_list_void_ptr, void* paycnt_list_void_ptr, int32_t start,
                                   int32_t duration, void* res_void_ptr) {
  auto* gmv_list_ptr = static_cast<absl::Span<const int64_t>*>(gmv_list_void_ptr);
  if (KS_UNLIKELY(gmv_list_ptr == nullptr)) {
    return;
  }

  auto* paycnt_list_ptr = static_cast<absl::Span<const int64_t>*>(paycnt_list_void_ptr);
  if (KS_UNLIKELY(paycnt_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_period_gmv_trend(*gmv_list_ptr, *paycnt_list_ptr, start, duration);
}

inline int64_t c_combine_f32__ui64(float x3, uint64_t x4) { return combine(x3, x4); }

inline void c_attr_check_zero_int64(void* v_void_ptr, void* res_void_ptr) {
  auto* v_ptr = static_cast<absl::optional<int64_t>*>(v_void_ptr);
  if (KS_UNLIKELY(v_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = attr_check_zero_int64(*v_ptr);
}

inline int64_t c_combine_i64__i64(int64_t x1, int64_t x3) { return combine(x1, x3); }

inline int64_t c_combine_ui64__i64(uint64_t x1, int64_t x3) { return combine(x1, x3); }

inline void c_int64_split_to_float(int64_t v, int32_t mod_num, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = int64_split_to_float(v, mod_num);
}

inline int64_t c_combine_ui32__ui64(uint32_t x1, uint64_t x2) { return combine(x1, x2); }

inline int64_t c_get_kpin_first_recruit_live_deliver_time_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_first_recruit_live_deliver_time_bucket(*raw_input_ptr);
}

inline void c_get_live_realtime_acc_cnt(void* play_time_void_ptr, void* acc_std_lps_cnt_void_ptr,
                                        void* acc_pay_cnt_void_ptr, void* res_void_ptr) {
  auto* play_time_ptr = static_cast<absl::optional<int64_t>*>(play_time_void_ptr);
  if (KS_UNLIKELY(play_time_ptr == nullptr)) {
    return;
  }

  auto* acc_std_lps_cnt_ptr = static_cast<absl::optional<int64_t>*>(acc_std_lps_cnt_void_ptr);
  if (KS_UNLIKELY(acc_std_lps_cnt_ptr == nullptr)) {
    return;
  }

  auto* acc_pay_cnt_ptr = static_cast<absl::optional<int64_t>*>(acc_pay_cnt_void_ptr);
  if (KS_UNLIKELY(acc_pay_cnt_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_realtime_acc_cnt(*play_time_ptr, *acc_std_lps_cnt_ptr, *acc_pay_cnt_ptr);
}

inline void c_get_user_hist_page_num_ordered_cross_pos_id_platform(void* page_num_list_void_ptr,
                                                                   void* cur_page_num_void_ptr,
                                                                   void* pos_id_void_ptr,
                                                                   void* platform_void_ptr,
                                                                   void* res_void_ptr) {
  auto* page_num_list_ptr = static_cast<absl::Span<const int64_t>*>(page_num_list_void_ptr);
  if (KS_UNLIKELY(page_num_list_ptr == nullptr)) {
    return;
  }

  auto* cur_page_num_ptr = static_cast<absl::optional<int64_t>*>(cur_page_num_void_ptr);
  if (KS_UNLIKELY(cur_page_num_ptr == nullptr)) {
    return;
  }

  auto* pos_id_ptr = static_cast<absl::optional<int64_t>*>(pos_id_void_ptr);
  if (KS_UNLIKELY(pos_id_ptr == nullptr)) {
    return;
  }

  auto* platform_ptr = static_cast<absl::string_view*>(platform_void_ptr);
  if (KS_UNLIKELY(platform_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_hist_page_num_ordered_cross_pos_id_platform(*page_num_list_ptr, *cur_page_num_ptr,
                                                                  *pos_id_ptr, *platform_ptr);
}

inline void c_get_context_candidate_info_top_n_span_f32(void* x1_void_ptr, void* res_void_ptr) {
  auto* x1_ptr = static_cast<absl::Span<const float>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_context_candidate_info_top_n(*x1_ptr);
}

inline void c_get_one_hot_live_creative_type(void* live_creative_type_void_ptr, void* res_void_ptr) {
  auto* live_creative_type_ptr = static_cast<absl::optional<int64_t>*>(live_creative_type_void_ptr);
  if (KS_UNLIKELY(live_creative_type_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_one_hot_live_creative_type(*live_creative_type_ptr);
}

inline void c_ods_cart_item_match_target_item(
    void* item_ids0_void_ptr, void* item_ids1_void_ptr, void* item_ids2_void_ptr, void* car_item_ids_void_ptr,    // NOLINT
    void* explain_item_id_void_ptr, void* hist_item_ids_void_ptr, void* author_ids0_void_ptr,
    void* author_ids1_void_ptr, void* author_ids2_void_ptr, void* target_author_id_void_ptr,
    void* label_cat_ids_void_ptr, void* label_spu_ids_void_ptr, void* label_brand_ids_void_ptr,
    void* label_x7_level1_void_ptr, void* label_x7_level2_void_ptr, void* label_x7_level3_void_ptr,
    void* label_x7_level4_void_ptr, void* car_cat_ids_void_ptr, void* car_spu_ids_void_ptr,
    void* car_brand_ids_void_ptr, void* car_x7_level1_void_ptr, void* car_x7_level2_void_ptr,
    void* car_x7_level3_void_ptr, void* car_x7_level4_void_ptr, void* explain_cat_id_void_ptr,
    void* explain_spu_id_void_ptr, void* explain_brand_id_void_ptr, void* hist_cat_ids_void_ptr,
    void* hist_spu_ids_void_ptr, void* hist_brand_ids_void_ptr, void* res_void_ptr) {
  auto* item_ids0_ptr = static_cast<absl::Span<const int64_t>*>(item_ids0_void_ptr);
  if (KS_UNLIKELY(item_ids0_ptr == nullptr)) {
    return;
  }

  auto* item_ids1_ptr = static_cast<absl::Span<const int64_t>*>(item_ids1_void_ptr);
  if (KS_UNLIKELY(item_ids1_ptr == nullptr)) {
    return;
  }

  auto* item_ids2_ptr = static_cast<absl::Span<const int64_t>*>(item_ids2_void_ptr);
  if (KS_UNLIKELY(item_ids2_ptr == nullptr)) {
    return;
  }

  auto* car_item_ids_ptr = static_cast<absl::Span<const int64_t>*>(car_item_ids_void_ptr);
  if (KS_UNLIKELY(car_item_ids_ptr == nullptr)) {
    return;
  }

  auto* explain_item_id_ptr = static_cast<absl::optional<int64_t>*>(explain_item_id_void_ptr);
  if (KS_UNLIKELY(explain_item_id_ptr == nullptr)) {
    return;
  }

  auto* hist_item_ids_ptr = static_cast<absl::Span<const int64_t>*>(hist_item_ids_void_ptr);
  if (KS_UNLIKELY(hist_item_ids_ptr == nullptr)) {
    return;
  }

  auto* author_ids0_ptr = static_cast<absl::Span<const int64_t>*>(author_ids0_void_ptr);
  if (KS_UNLIKELY(author_ids0_ptr == nullptr)) {
    return;
  }

  auto* author_ids1_ptr = static_cast<absl::Span<const int64_t>*>(author_ids1_void_ptr);
  if (KS_UNLIKELY(author_ids1_ptr == nullptr)) {
    return;
  }

  auto* author_ids2_ptr = static_cast<absl::Span<const int64_t>*>(author_ids2_void_ptr);
  if (KS_UNLIKELY(author_ids2_ptr == nullptr)) {
    return;
  }

  auto* target_author_id_ptr = static_cast<absl::optional<int64_t>*>(target_author_id_void_ptr);
  if (KS_UNLIKELY(target_author_id_ptr == nullptr)) {
    return;
  }

  auto* label_cat_ids_ptr = static_cast<absl::Span<const int64_t>*>(label_cat_ids_void_ptr);
  if (KS_UNLIKELY(label_cat_ids_ptr == nullptr)) {
    return;
  }

  auto* label_spu_ids_ptr = static_cast<absl::Span<const int64_t>*>(label_spu_ids_void_ptr);
  if (KS_UNLIKELY(label_spu_ids_ptr == nullptr)) {
    return;
  }

  auto* label_brand_ids_ptr = static_cast<absl::Span<const int64_t>*>(label_brand_ids_void_ptr);
  if (KS_UNLIKELY(label_brand_ids_ptr == nullptr)) {
    return;
  }

  auto* label_x7_level1_ptr = static_cast<absl::Span<const int64_t>*>(label_x7_level1_void_ptr);
  if (KS_UNLIKELY(label_x7_level1_ptr == nullptr)) {
    return;
  }

  auto* label_x7_level2_ptr = static_cast<absl::Span<const int64_t>*>(label_x7_level2_void_ptr);
  if (KS_UNLIKELY(label_x7_level2_ptr == nullptr)) {
    return;
  }

  auto* label_x7_level3_ptr = static_cast<absl::Span<const int64_t>*>(label_x7_level3_void_ptr);
  if (KS_UNLIKELY(label_x7_level3_ptr == nullptr)) {
    return;
  }

  auto* label_x7_level4_ptr = static_cast<absl::Span<const int64_t>*>(label_x7_level4_void_ptr);
  if (KS_UNLIKELY(label_x7_level4_ptr == nullptr)) {
    return;
  }

  auto* car_cat_ids_ptr = static_cast<absl::Span<const int64_t>*>(car_cat_ids_void_ptr);
  if (KS_UNLIKELY(car_cat_ids_ptr == nullptr)) {
    return;
  }

  auto* car_spu_ids_ptr = static_cast<absl::Span<const int64_t>*>(car_spu_ids_void_ptr);
  if (KS_UNLIKELY(car_spu_ids_ptr == nullptr)) {
    return;
  }

  auto* car_brand_ids_ptr = static_cast<absl::Span<const int64_t>*>(car_brand_ids_void_ptr);
  if (KS_UNLIKELY(car_brand_ids_ptr == nullptr)) {
    return;
  }

  auto* car_x7_level1_ptr = static_cast<absl::Span<const int64_t>*>(car_x7_level1_void_ptr);
  if (KS_UNLIKELY(car_x7_level1_ptr == nullptr)) {
    return;
  }

  auto* car_x7_level2_ptr = static_cast<absl::Span<const int64_t>*>(car_x7_level2_void_ptr);
  if (KS_UNLIKELY(car_x7_level2_ptr == nullptr)) {
    return;
  }

  auto* car_x7_level3_ptr = static_cast<absl::Span<const int64_t>*>(car_x7_level3_void_ptr);
  if (KS_UNLIKELY(car_x7_level3_ptr == nullptr)) {
    return;
  }

  auto* car_x7_level4_ptr = static_cast<absl::Span<const int64_t>*>(car_x7_level4_void_ptr);
  if (KS_UNLIKELY(car_x7_level4_ptr == nullptr)) {
    return;
  }

  auto* explain_cat_id_ptr = static_cast<absl::optional<int64_t>*>(explain_cat_id_void_ptr);
  if (KS_UNLIKELY(explain_cat_id_ptr == nullptr)) {
    return;
  }

  auto* explain_spu_id_ptr = static_cast<absl::optional<int64_t>*>(explain_spu_id_void_ptr);
  if (KS_UNLIKELY(explain_spu_id_ptr == nullptr)) {
    return;
  }

  auto* explain_brand_id_ptr = static_cast<absl::optional<int64_t>*>(explain_brand_id_void_ptr);
  if (KS_UNLIKELY(explain_brand_id_ptr == nullptr)) {
    return;
  }

  auto* hist_cat_ids_ptr = static_cast<absl::Span<const int64_t>*>(hist_cat_ids_void_ptr);
  if (KS_UNLIKELY(hist_cat_ids_ptr == nullptr)) {
    return;
  }

  auto* hist_spu_ids_ptr = static_cast<absl::Span<const int64_t>*>(hist_spu_ids_void_ptr);
  if (KS_UNLIKELY(hist_spu_ids_ptr == nullptr)) {
    return;
  }

  auto* hist_brand_ids_ptr = static_cast<absl::Span<const int64_t>*>(hist_brand_ids_void_ptr);
  if (KS_UNLIKELY(hist_brand_ids_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = ods_cart_item_match_target_item(
      *item_ids0_ptr, *item_ids1_ptr, *item_ids2_ptr, *car_item_ids_ptr, *explain_item_id_ptr,
      *hist_item_ids_ptr, *author_ids0_ptr, *author_ids1_ptr, *author_ids2_ptr, *target_author_id_ptr,
      *label_cat_ids_ptr, *label_spu_ids_ptr, *label_brand_ids_ptr, *label_x7_level1_ptr,
      *label_x7_level2_ptr, *label_x7_level3_ptr, *label_x7_level4_ptr, *car_cat_ids_ptr, *car_spu_ids_ptr,
      *car_brand_ids_ptr, *car_x7_level1_ptr, *car_x7_level2_ptr, *car_x7_level3_ptr, *car_x7_level4_ptr,
      *explain_cat_id_ptr, *explain_spu_id_ptr, *explain_brand_id_ptr, *hist_cat_ids_ptr, *hist_spu_ids_ptr,
      *hist_brand_ids_ptr);
}

inline int64_t c_combine_ui32__i64(uint32_t x1, int64_t x3) { return combine(x1, x3); }

inline void c_combine_i64__span_i64(int64_t x3, void* x4_void_ptr, void* res_void_ptr) {
  auto* x4_ptr = static_cast<absl::Span<const int64_t>*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = combine(x3, *x4_ptr);
}

inline void c_get_fangchan_second_industry_id_hash_set(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::set<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_fangchan_second_industry_id_hash_set();
}

inline void c_split_int64_to_4_float(void* id_void_ptr, void* res_void_ptr) {
  auto* id_ptr = static_cast<absl::optional<uint64_t>*>(id_void_ptr);
  if (KS_UNLIKELY(id_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = split_int64_to_4_float(*id_ptr);
}

inline void c_get_live_realtime_acc_cnt3(void* play_time_void_ptr, void* live_acc_delivery_cnt_void_ptr,
                                         void* live_acc_std_lps_cnt_void_ptr, void* live_acc_pay_cnt_void_ptr,    // NOLINT
                                         void* photo_acc_delivery_cnt_void_ptr,
                                         void* photo_acc_std_lps_cnt_void_ptr,
                                         void* photo_acc_pay_cnt_void_ptr, void* res_void_ptr) {
  auto* play_time_ptr = static_cast<absl::optional<int64_t>*>(play_time_void_ptr);
  if (KS_UNLIKELY(play_time_ptr == nullptr)) {
    return;
  }

  auto* live_acc_delivery_cnt_ptr = static_cast<absl::optional<int64_t>*>(live_acc_delivery_cnt_void_ptr);
  if (KS_UNLIKELY(live_acc_delivery_cnt_ptr == nullptr)) {
    return;
  }

  auto* live_acc_std_lps_cnt_ptr = static_cast<absl::optional<int64_t>*>(live_acc_std_lps_cnt_void_ptr);
  if (KS_UNLIKELY(live_acc_std_lps_cnt_ptr == nullptr)) {
    return;
  }

  auto* live_acc_pay_cnt_ptr = static_cast<absl::optional<int64_t>*>(live_acc_pay_cnt_void_ptr);
  if (KS_UNLIKELY(live_acc_pay_cnt_ptr == nullptr)) {
    return;
  }

  auto* photo_acc_delivery_cnt_ptr = static_cast<absl::optional<int64_t>*>(photo_acc_delivery_cnt_void_ptr);
  if (KS_UNLIKELY(photo_acc_delivery_cnt_ptr == nullptr)) {
    return;
  }

  auto* photo_acc_std_lps_cnt_ptr = static_cast<absl::optional<int64_t>*>(photo_acc_std_lps_cnt_void_ptr);
  if (KS_UNLIKELY(photo_acc_std_lps_cnt_ptr == nullptr)) {
    return;
  }

  auto* photo_acc_pay_cnt_ptr = static_cast<absl::optional<int64_t>*>(photo_acc_pay_cnt_void_ptr);
  if (KS_UNLIKELY(photo_acc_pay_cnt_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_realtime_acc_cnt3(*play_time_ptr, *live_acc_delivery_cnt_ptr, *live_acc_std_lps_cnt_ptr,    // NOLINT
                                        *live_acc_pay_cnt_ptr, *photo_acc_delivery_cnt_ptr,
                                        *photo_acc_std_lps_cnt_ptr, *photo_acc_pay_cnt_ptr);
}

inline void c_seq_list_by_hard_search(void* key_void_ptr, void* seq_list_void_ptr, void* res_void_ptr) {
  auto* key_ptr = static_cast<absl::optional<absl::string_view>*>(key_void_ptr);
  if (KS_UNLIKELY(key_ptr == nullptr)) {
    return;
  }

  auto* seq_list_ptr = static_cast<absl::Span<const int64_t>*>(seq_list_void_ptr);
  if (KS_UNLIKELY(seq_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = seq_list_by_hard_search(*key_ptr, *seq_list_ptr);
}

inline float c_value_or_optional_f32__i32(void* x1_void_ptr, int32_t int_arg1) {
  auto* x1_ptr = static_cast<absl::optional<float>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return value_or(*x1_ptr, int_arg1);
}

inline void c_get_value_from_map_protomapview_i64_i64__i64(void* x3_void_ptr, int64_t x5,
                                                           void* res_void_ptr) {
  auto* x3_ptr = static_cast<ProtoMapView<int64_t, int64_t>*>(x3_void_ptr);
  if (KS_UNLIKELY(x3_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_value_from_map(*x3_ptr, x5);
}

inline void c_combine_span_i64__ui64(void* x1_void_ptr, uint64_t x2, void* res_void_ptr) {
  auto* x1_ptr = static_cast<absl::Span<const int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = combine(*x1_ptr, x2);
}

inline void c_get_value_from_map_protomapview_i32_i64__i32(void* x1_void_ptr, int32_t int_arg1,
                                                           void* res_void_ptr) {
  auto* x1_ptr = static_cast<ProtoMapView<int32_t, int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_value_from_map(*x1_ptr, int_arg1);
}

inline void c_get_live_cover(void* orig_url_void_ptr, void* raw_url_void_ptr, bool is_train,
                             void* avg_paycnt_void_ptr, void* play_duration_void_ptr, void* res_void_ptr) {
  auto* orig_url_ptr = static_cast<absl::string_view*>(orig_url_void_ptr);
  if (KS_UNLIKELY(orig_url_ptr == nullptr)) {
    return;
  }

  auto* raw_url_ptr = static_cast<absl::string_view*>(raw_url_void_ptr);
  if (KS_UNLIKELY(raw_url_ptr == nullptr)) {
    return;
  }

  auto* avg_paycnt_ptr = static_cast<absl::optional<int64_t>*>(avg_paycnt_void_ptr);
  if (KS_UNLIKELY(avg_paycnt_ptr == nullptr)) {
    return;
  }

  auto* play_duration_ptr = static_cast<absl::optional<int64_t>*>(play_duration_void_ptr);
  if (KS_UNLIKELY(play_duration_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_cover(*orig_url_ptr, *raw_url_ptr, is_train, *avg_paycnt_ptr, *play_duration_ptr);
}

inline void c_merge_int64_list_2_vector_i64__vector_i64(void* x13_void_ptr, void* x17_void_ptr,
                                                        void* res_void_ptr) {
  auto* x13_ptr = static_cast<std::vector<int64_t>*>(x13_void_ptr);
  if (KS_UNLIKELY(x13_ptr == nullptr)) {
    return;
  }

  auto* x17_ptr = static_cast<std::vector<int64_t>*>(x17_void_ptr);
  if (KS_UNLIKELY(x17_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_int64_list_2(*x13_ptr, *x17_ptr);
}

inline void c_trim_whitespace(void* s_void_ptr, void* res_void_ptr) {
  auto* s_ptr = static_cast<std::string*>(s_void_ptr);
  if (KS_UNLIKELY(s_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::string_view*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = trim_whitespace(*s_ptr);
}

inline int64_t c_calc_certain_avg_val(void* val_list_void_ptr, int32_t start, int32_t step, int32_t max_len) {    // NOLINT
  auto* val_list_ptr = static_cast<absl::Span<const int64_t>*>(val_list_void_ptr);
  if (KS_UNLIKELY(val_list_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return calc_certain_avg_val(*val_list_ptr, start, step, max_len);
}

inline void c_follow_coeff(int64_t pos_id, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = follow_coeff(pos_id);
}

inline void c_get_segment_cnt_log_value(void* segment_list_void_ptr, void* res_void_ptr) {
  auto* segment_list_ptr = static_cast<std::vector<int64_t>*>(segment_list_void_ptr);
  if (KS_UNLIKELY(segment_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_segment_cnt_log_value(*segment_list_ptr);
}

inline int64_t c_city_hash64_optional_stringview(void* x3_void_ptr) {
  auto* x3_ptr = static_cast<absl::optional<absl::string_view>*>(x3_void_ptr);
  if (KS_UNLIKELY(x3_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return city_hash64(*x3_ptr);
}

inline bool c_is_photo_type(int64_t campaign_type, int64_t live_creative_type) {
  return is_photo_type(campaign_type, live_creative_type);
}

inline void c_get_long_term_user_author_cvr(float user_cvr, void* cvr_list_void_ptr,
                                            void* author_list_void_ptr, int64_t author_id,
                                            void* res_void_ptr) {
  auto* cvr_list_ptr = static_cast<absl::Span<const float>*>(cvr_list_void_ptr);
  if (KS_UNLIKELY(cvr_list_ptr == nullptr)) {
    return;
  }

  auto* author_list_ptr = static_cast<absl::Span<const int64_t>*>(author_list_void_ptr);
  if (KS_UNLIKELY(author_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_long_term_user_author_cvr(user_cvr, *cvr_list_ptr, *author_list_ptr, author_id);
}

inline bool c_is_directive_live_type(int64_t campaign_type, int64_t live_creative_type) {
  return is_directive_live_type(campaign_type, live_creative_type);
}

inline int64_t c_log_e_or_default_cast_int(float x, int64_t default_v) {
  return log_e_or_default_cast_int(x, default_v);
}

inline void c_merge_float_list_all_f32__f32__f32__f32__f32__f32__f32(float x2, float x4, float x6, float x8,
                                                                     float x13, float x16, float x18,
                                                                     void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_all(x2, x4, x6, x8, x13, x16, x18);
}

inline void c_get_action_list_fea(void* action_list_void_ptr, void* timestamp_list_void_ptr, int64_t adlog_ts,    // NOLINT
                                  void* res_void_ptr) {
  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_action_list_fea(*action_list_ptr, *timestamp_list_ptr, adlog_ts);
}

inline void c_hard_search_seq_with_single_key_span_i64__optional_stringview__i32(void* x1_void_ptr,
                                                                                 void* x2_void_ptr,
                                                                                 int32_t int_arg2,
                                                                                 void* res_void_ptr) {
  auto* x1_ptr = static_cast<absl::Span<const int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* x2_ptr = static_cast<absl::optional<absl::string_view>*>(x2_void_ptr);
  if (KS_UNLIKELY(x2_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = hard_search_seq_with_single_key(*x1_ptr, *x2_ptr, int_arg2);
}

inline void c_log_e_cast_int(float x, void* res_void_ptr) {
  auto* res_ptr = static_cast<absl::optional<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = log_e_cast_int(x);
}

inline void c_get_industry_filter_action_list_before_timestamp(
    void* action_list_void_ptr, void* industry_id_list_void_ptr, void* timestamp_list_void_ptr,
    int64_t target_id, int64_t adlog_timestamp, int64_t gap_in_seconds, int32_t max_len, void* res_void_ptr) {    // NOLINT
  auto* action_list_ptr = static_cast<absl::Span<const int64_t>*>(action_list_void_ptr);
  if (KS_UNLIKELY(action_list_ptr == nullptr)) {
    return;
  }

  auto* industry_id_list_ptr = static_cast<absl::Span<const int64_t>*>(industry_id_list_void_ptr);
  if (KS_UNLIKELY(industry_id_list_ptr == nullptr)) {
    return;
  }

  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_industry_filter_action_list_before_timestamp(*action_list_ptr, *industry_id_list_ptr,
                                                              *timestamp_list_ptr, target_id, adlog_timestamp,    // NOLINT
                                                              gap_in_seconds, max_len);
}

inline void c_get_live_id(int64_t live_id, bool is_train, void* avg_paycnt_void_ptr,
                          void* play_duration_void_ptr, void* res_void_ptr) {
  auto* avg_paycnt_ptr = static_cast<absl::optional<int64_t>*>(avg_paycnt_void_ptr);
  if (KS_UNLIKELY(avg_paycnt_ptr == nullptr)) {
    return;
  }

  auto* play_duration_ptr = static_cast<absl::optional<int64_t>*>(play_duration_void_ptr);
  if (KS_UNLIKELY(play_duration_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_id(live_id, is_train, *avg_paycnt_ptr, *play_duration_ptr);
}

inline void c_get_user_hist_req_sub_page_ids_cross_platform(void* sub_page_ids_void_ptr,
                                                            void* platform_void_ptr, void* res_void_ptr) {
  auto* sub_page_ids_ptr = static_cast<absl::Span<const int64_t>*>(sub_page_ids_void_ptr);
  if (KS_UNLIKELY(sub_page_ids_ptr == nullptr)) {
    return;
  }

  auto* platform_ptr = static_cast<absl::string_view*>(platform_void_ptr);
  if (KS_UNLIKELY(platform_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_hist_req_sub_page_ids_cross_platform(*sub_page_ids_ptr, *platform_ptr);
}

inline void c_get_match_detail(void* user_action_content_list_void_ptr,
                               void* user_action_timestamp_list_void_ptr, int64_t target_id, size_t max_len,
                               int64_t adlog_timestamp, void* res_void_ptr) {
  auto* user_action_content_list_ptr =
      static_cast<absl::Span<const int64_t>*>(user_action_content_list_void_ptr);
  if (KS_UNLIKELY(user_action_content_list_ptr == nullptr)) {
    return;
  }

  auto* user_action_timestamp_list_ptr =
      static_cast<absl::Span<const int64_t>*>(user_action_timestamp_list_void_ptr);
  if (KS_UNLIKELY(user_action_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_match_detail(*user_action_content_list_ptr, *user_action_timestamp_list_ptr, target_id,
                              max_len, adlog_timestamp);
}

inline void c_take_span_i64__i32(void* x1_void_ptr, int32_t int_arg1, void* res_void_ptr) {
  auto* x1_ptr = static_cast<absl::Span<const int64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::Span<const int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = take(*x1_ptr, int_arg1);
}

inline void c_get_period_diff_action(void* first_timestamp_list_void_ptr, void* first_photo_list_void_ptr,
                                     void* first_industry_list_void_ptr, void* second_timestamp_list_void_ptr,    // NOLINT
                                     void* second_photo_list_void_ptr, void* second_industry_list_void_ptr,
                                     int32_t process_time, int32_t period, void* res_void_ptr) {
  auto* first_timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(first_timestamp_list_void_ptr);
  if (KS_UNLIKELY(first_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* first_photo_list_ptr = static_cast<absl::Span<const int64_t>*>(first_photo_list_void_ptr);
  if (KS_UNLIKELY(first_photo_list_ptr == nullptr)) {
    return;
  }

  auto* first_industry_list_ptr = static_cast<absl::Span<const int64_t>*>(first_industry_list_void_ptr);
  if (KS_UNLIKELY(first_industry_list_ptr == nullptr)) {
    return;
  }

  auto* second_timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(second_timestamp_list_void_ptr);
  if (KS_UNLIKELY(second_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* second_photo_list_ptr = static_cast<absl::Span<const int64_t>*>(second_photo_list_void_ptr);
  if (KS_UNLIKELY(second_photo_list_ptr == nullptr)) {
    return;
  }

  auto* second_industry_list_ptr = static_cast<absl::Span<const int64_t>*>(second_industry_list_void_ptr);
  if (KS_UNLIKELY(second_industry_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_period_diff_action(*first_timestamp_list_ptr, *first_photo_list_ptr,
                                    *first_industry_list_ptr, *second_timestamp_list_ptr,
                                    *second_photo_list_ptr, *second_industry_list_ptr, process_time, period);
}

inline void c_get_live_history_avg_price(void* gmv_list_void_ptr, void* order_cnt_list_void_ptr,
                                         void* sku_cnt_list_void_ptr, void* res_void_ptr) {
  auto* gmv_list_ptr = static_cast<absl::Span<const float>*>(gmv_list_void_ptr);
  if (KS_UNLIKELY(gmv_list_ptr == nullptr)) {
    return;
  }

  auto* order_cnt_list_ptr = static_cast<absl::Span<const int64_t>*>(order_cnt_list_void_ptr);
  if (KS_UNLIKELY(order_cnt_list_ptr == nullptr)) {
    return;
  }

  auto* sku_cnt_list_ptr = static_cast<absl::Span<const int64_t>*>(sku_cnt_list_void_ptr);
  if (KS_UNLIKELY(sku_cnt_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_history_avg_price(*gmv_list_ptr, *order_cnt_list_ptr, *sku_cnt_list_ptr);
}

inline void c_merge_int64_list_all_ui64__i64(uint64_t x1, int64_t x2, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_int64_list_all(x1, x2);
}

inline int64_t c_get_cur_page_num_cross_platform(void* cur_page_num_void_ptr, void* platform_void_ptr) {
  auto* cur_page_num_ptr = static_cast<absl::optional<int64_t>*>(cur_page_num_void_ptr);
  if (KS_UNLIKELY(cur_page_num_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  auto* platform_ptr = static_cast<absl::string_view*>(platform_void_ptr);
  if (KS_UNLIKELY(platform_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_cur_page_num_cross_platform(*cur_page_num_ptr, *platform_ptr);
}

inline int64_t c_get_kpin_live_recruit_clk_plc_cnt_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_live_recruit_clk_plc_cnt_bucket(*raw_input_ptr);
}

inline void c_sum_pooling_certain_item(void* item_list_void_ptr, int32_t start, int32_t step, int32_t max_len,    // NOLINT
                                       void* res_void_ptr) {
  auto* item_list_ptr = static_cast<absl::Span<const int64_t>*>(item_list_void_ptr);
  if (KS_UNLIKELY(item_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = sum_pooling_certain_item(*item_list_ptr, start, step, max_len);
}

inline void c_log_e(float x, void* res_void_ptr) {
  auto* res_ptr = static_cast<absl::optional<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = log_e(x);
}

inline void c_seq_list_input_with_sparse_hard_search_filter(
    void* industry_tag_void_ptr, void* is_playable_void_ptr, void* ad_style_void_ptr,
    void* cooperation_mode_void_ptr, void* material_feature_type_void_ptr, void* industry_tag_target_void_ptr,    // NOLINT
    void* is_playable_target_void_ptr, void* ad_style_target_void_ptr, void* cooperation_mode_target_void_ptr,    // NOLINT
    void* material_feature_type_target_void_ptr, void* res_void_ptr) {
  auto* industry_tag_ptr = static_cast<absl::Span<const int64_t>*>(industry_tag_void_ptr);
  if (KS_UNLIKELY(industry_tag_ptr == nullptr)) {
    return;
  }

  auto* is_playable_ptr = static_cast<absl::Span<const int64_t>*>(is_playable_void_ptr);
  if (KS_UNLIKELY(is_playable_ptr == nullptr)) {
    return;
  }

  auto* ad_style_ptr = static_cast<absl::Span<const int64_t>*>(ad_style_void_ptr);
  if (KS_UNLIKELY(ad_style_ptr == nullptr)) {
    return;
  }

  auto* cooperation_mode_ptr = static_cast<absl::Span<const int64_t>*>(cooperation_mode_void_ptr);
  if (KS_UNLIKELY(cooperation_mode_ptr == nullptr)) {
    return;
  }

  auto* material_feature_type_ptr = static_cast<absl::Span<const int64_t>*>(material_feature_type_void_ptr);
  if (KS_UNLIKELY(material_feature_type_ptr == nullptr)) {
    return;
  }

  auto* industry_tag_target_ptr =
      static_cast<absl::optional<absl::string_view>*>(industry_tag_target_void_ptr);
  if (KS_UNLIKELY(industry_tag_target_ptr == nullptr)) {
    return;
  }

  auto* is_playable_target_ptr = static_cast<absl::optional<int64_t>*>(is_playable_target_void_ptr);
  if (KS_UNLIKELY(is_playable_target_ptr == nullptr)) {
    return;
  }

  auto* ad_style_target_ptr = static_cast<absl::optional<int64_t>*>(ad_style_target_void_ptr);
  if (KS_UNLIKELY(ad_style_target_ptr == nullptr)) {
    return;
  }

  auto* cooperation_mode_target_ptr = static_cast<absl::optional<int64_t>*>(cooperation_mode_target_void_ptr);    // NOLINT
  if (KS_UNLIKELY(cooperation_mode_target_ptr == nullptr)) {
    return;
  }

  auto* material_feature_type_target_ptr =
      static_cast<absl::optional<int64_t>*>(material_feature_type_target_void_ptr);
  if (KS_UNLIKELY(material_feature_type_target_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = seq_list_input_with_sparse_hard_search_filter(
      *industry_tag_ptr, *is_playable_ptr, *ad_style_ptr, *cooperation_mode_ptr, *material_feature_type_ptr,
      *industry_tag_target_ptr, *is_playable_target_ptr, *ad_style_target_ptr, *cooperation_mode_target_ptr,
      *material_feature_type_target_ptr);
}

inline void c_get_user_attribute_combine_live_vanilla_stringview__i32__ui64__ui64__bool__optional_i64(
    void* x1_void_ptr, int32_t x2, uint64_t x3, uint64_t x4, bool x5, void* x6_void_ptr, void* res_void_ptr) {    // NOLINT
  auto* x1_ptr = static_cast<absl::string_view*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return;
  }

  auto* x6_ptr = static_cast<absl::optional<int64_t>*>(x6_void_ptr);
  if (KS_UNLIKELY(x6_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_attribute_combine_live_vanilla(*x1_ptr, x2, x3, x4, x5, *x6_ptr);
}

inline void c_merge_float_list_all_optional_i64__vector_i64__optional_i64(void* x4_void_ptr,
                                                                          void* x9_void_ptr,
                                                                          void* x13_void_ptr,
                                                                          void* res_void_ptr) {
  auto* x4_ptr = static_cast<absl::optional<int64_t>*>(x4_void_ptr);
  if (KS_UNLIKELY(x4_ptr == nullptr)) {
    return;
  }

  auto* x9_ptr = static_cast<std::vector<int64_t>*>(x9_void_ptr);
  if (KS_UNLIKELY(x9_ptr == nullptr)) {
    return;
  }

  auto* x13_ptr = static_cast<absl::optional<int64_t>*>(x13_void_ptr);
  if (KS_UNLIKELY(x13_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_all(*x4_ptr, *x9_ptr, *x13_ptr);
}

inline void c_get_live_top5_price(void* gmv_list_void_ptr, void* price_list_void_ptr,
                                  void* sku_cnt_list_void_ptr, void* timestamp_list_void_ptr,
                                  void* res_void_ptr) {
  auto* gmv_list_ptr = static_cast<absl::Span<const int64_t>*>(gmv_list_void_ptr);
  if (KS_UNLIKELY(gmv_list_ptr == nullptr)) {
    return;
  }

  auto* price_list_ptr = static_cast<absl::Span<const int64_t>*>(price_list_void_ptr);
  if (KS_UNLIKELY(price_list_ptr == nullptr)) {
    return;
  }

  auto* sku_cnt_list_ptr = static_cast<absl::Span<const int64_t>*>(sku_cnt_list_void_ptr);
  if (KS_UNLIKELY(sku_cnt_list_ptr == nullptr)) {
    return;
  }

  auto* timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_list_void_ptr);
  if (KS_UNLIKELY(timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_top5_price(*gmv_list_ptr, *price_list_ptr, *sku_cnt_list_ptr, *timestamp_list_ptr);
}

inline void c_get_live_realtime_avg_price(void* gmv_list_void_ptr, void* price_list_void_ptr,
                                          void* sku_cnt_list_void_ptr, void* res_void_ptr) {
  auto* gmv_list_ptr = static_cast<absl::Span<const int64_t>*>(gmv_list_void_ptr);
  if (KS_UNLIKELY(gmv_list_ptr == nullptr)) {
    return;
  }

  auto* price_list_ptr = static_cast<absl::Span<const int64_t>*>(price_list_void_ptr);
  if (KS_UNLIKELY(price_list_ptr == nullptr)) {
    return;
  }

  auto* sku_cnt_list_ptr = static_cast<absl::Span<const int64_t>*>(sku_cnt_list_void_ptr);
  if (KS_UNLIKELY(sku_cnt_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_realtime_avg_price(*gmv_list_ptr, *price_list_ptr, *sku_cnt_list_ptr);
}

inline void c_get_universe_hour_seq_with_max_len_and_timestamp(void* medium_id_seq_void_ptr,
                                                               void* timestamp_seq_void_ptr, int32_t max_len,
                                                               int64_t current_timestamp,
                                                               int64_t timestamp_delta, void* res_void_ptr) {
  auto* medium_id_seq_ptr = static_cast<absl::Span<const int64_t>*>(medium_id_seq_void_ptr);
  if (KS_UNLIKELY(medium_id_seq_ptr == nullptr)) {
    return;
  }

  auto* timestamp_seq_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_seq_void_ptr);
  if (KS_UNLIKELY(timestamp_seq_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_universe_hour_seq_with_max_len_and_timestamp(*medium_id_seq_ptr, *timestamp_seq_ptr, max_len,    // NOLINT
                                                              current_timestamp, timestamp_delta);
}

inline void c_get_user_live_discount_rate(void* price_list_void_ptr, void* discount_list_void_ptr,
                                          void* order_cnt_list_void_ptr, void* res_void_ptr) {
  auto* price_list_ptr = static_cast<absl::Span<const float>*>(price_list_void_ptr);
  if (KS_UNLIKELY(price_list_ptr == nullptr)) {
    return;
  }

  auto* discount_list_ptr = static_cast<absl::Span<const float>*>(discount_list_void_ptr);
  if (KS_UNLIKELY(discount_list_ptr == nullptr)) {
    return;
  }

  auto* order_cnt_list_ptr = static_cast<absl::Span<const int64_t>*>(order_cnt_list_void_ptr);
  if (KS_UNLIKELY(order_cnt_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_live_discount_rate(*price_list_ptr, *discount_list_ptr, *order_cnt_list_ptr);
}

inline void c_cast_to_bool_list_vector_i64(void* x11_void_ptr, void* res_void_ptr) {
  auto* x11_ptr = static_cast<std::vector<int64_t>*>(x11_void_ptr);
  if (KS_UNLIKELY(x11_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<bool>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = cast_to_bool_list(*x11_ptr);
}

inline void c_get_period_avg_gmv(void* gmv_list_void_ptr, void* paycnt_list_void_ptr, int32_t start,
                                 int32_t end, void* res_void_ptr) {
  auto* gmv_list_ptr = static_cast<absl::Span<const int64_t>*>(gmv_list_void_ptr);
  if (KS_UNLIKELY(gmv_list_ptr == nullptr)) {
    return;
  }

  auto* paycnt_list_ptr = static_cast<absl::Span<const int64_t>*>(paycnt_list_void_ptr);
  if (KS_UNLIKELY(paycnt_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_period_avg_gmv(*gmv_list_ptr, *paycnt_list_ptr, start, end);
}

inline void c_cast_to_bool_list_vector_f32(void* x6_void_ptr, void* res_void_ptr) {
  auto* x6_ptr = static_cast<std::vector<float>*>(x6_void_ptr);
  if (KS_UNLIKELY(x6_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<bool>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = cast_to_bool_list(*x6_ptr);
}

inline float c_get_gmv_round(void* x_void_ptr) {
  auto* x_ptr = static_cast<absl::optional<int64_t>*>(x_void_ptr);
  if (KS_UNLIKELY(x_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return get_gmv_round(*x_ptr);
}

inline void c_merge_int64_list_5_i64__i64__i64__i64__i64(int64_t x3, int64_t x6, int64_t x9, int64_t x12,
                                                         int64_t x15, void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_int64_list_5(x3, x6, x9, x12, x15);
}

inline int64_t c_get_kpin_recruit_card_show_cnt_60d_bucket(void* raw_input_void_ptr) {
  auto* raw_input_ptr = static_cast<absl::optional<int64_t>*>(raw_input_void_ptr);
  if (KS_UNLIKELY(raw_input_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_kpin_recruit_card_show_cnt_60d_bucket(*raw_input_ptr);
}

inline int64_t c_get_last_time_gap_bin_helper(void* ts_list_void_ptr, int64_t adlog_ts) {
  auto* ts_list_ptr = static_cast<absl::Span<const int64_t>*>(ts_list_void_ptr);
  if (KS_UNLIKELY(ts_list_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_last_time_gap_bin_helper(*ts_list_ptr, adlog_ts);
}

inline void c_log_10(float x, void* res_void_ptr) {
  auto* res_ptr = static_cast<absl::optional<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = log_10(x);
}

inline float c_get_author_atv(void* live_author_price_24h_void_ptr, void* live_author_price_3d_void_ptr,
                              void* live_author_price_14d_void_ptr,
                              void* live_author_ocpc_roi_price_7d_void_ptr,
                              void* live_author_ocpc_roas_price_7d_void_ptr,
                              void* ocpc_action_type_void_ptr) {
  auto* live_author_price_24h_ptr = static_cast<absl::optional<float>*>(live_author_price_24h_void_ptr);
  if (KS_UNLIKELY(live_author_price_24h_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* live_author_price_3d_ptr = static_cast<absl::optional<float>*>(live_author_price_3d_void_ptr);
  if (KS_UNLIKELY(live_author_price_3d_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* live_author_price_14d_ptr = static_cast<absl::optional<float>*>(live_author_price_14d_void_ptr);
  if (KS_UNLIKELY(live_author_price_14d_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* live_author_ocpc_roi_price_7d_ptr =
      static_cast<absl::optional<float>*>(live_author_ocpc_roi_price_7d_void_ptr);
  if (KS_UNLIKELY(live_author_ocpc_roi_price_7d_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* live_author_ocpc_roas_price_7d_ptr =
      static_cast<absl::optional<float>*>(live_author_ocpc_roas_price_7d_void_ptr);
  if (KS_UNLIKELY(live_author_ocpc_roas_price_7d_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  auto* ocpc_action_type_ptr = static_cast<absl::optional<int64_t>*>(ocpc_action_type_void_ptr);
  if (KS_UNLIKELY(ocpc_action_type_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return get_author_atv(*live_author_price_24h_ptr, *live_author_price_3d_ptr, *live_author_price_14d_ptr,
                        *live_author_ocpc_roi_price_7d_ptr, *live_author_ocpc_roas_price_7d_ptr,
                        *ocpc_action_type_ptr);
}

inline void c_get_last_coupon_time_gap(void* receive_timestamp_list1_void_ptr,
                                       void* receive_timestamp_list2_void_ptr,
                                       void* use_timestamp_list_void_ptr, int64_t adlog_ts,
                                       void* res_void_ptr) {
  auto* receive_timestamp_list1_ptr =
      static_cast<absl::Span<const int64_t>*>(receive_timestamp_list1_void_ptr);
  if (KS_UNLIKELY(receive_timestamp_list1_ptr == nullptr)) {
    return;
  }

  auto* receive_timestamp_list2_ptr =
      static_cast<absl::Span<const int64_t>*>(receive_timestamp_list2_void_ptr);
  if (KS_UNLIKELY(receive_timestamp_list2_ptr == nullptr)) {
    return;
  }

  auto* use_timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(use_timestamp_list_void_ptr);
  if (KS_UNLIKELY(use_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_last_coupon_time_gap(*receive_timestamp_list1_ptr, *receive_timestamp_list2_ptr,
                                      *use_timestamp_list_ptr, adlog_ts);
}

inline void c_get_real_attr_list(void* action_timestamp_list_void_ptr, void* timestamp_flag_list_void_ptr,
                                 void* action_content_list_void_ptr, void* cps_type_list_void_ptr,
                                 void* seller_id_list_void_ptr, void* distribute_id_list_void_ptr,
                                 void* carrier_id_list_void_ptr, int64_t target_type, int32_t max_len,
                                 void* res_void_ptr) {
  auto* action_timestamp_list_ptr = static_cast<absl::Span<const int64_t>*>(action_timestamp_list_void_ptr);
  if (KS_UNLIKELY(action_timestamp_list_ptr == nullptr)) {
    return;
  }

  auto* timestamp_flag_list_ptr = static_cast<absl::Span<const int64_t>*>(timestamp_flag_list_void_ptr);
  if (KS_UNLIKELY(timestamp_flag_list_ptr == nullptr)) {
    return;
  }

  auto* action_content_list_ptr = static_cast<absl::Span<const int64_t>*>(action_content_list_void_ptr);
  if (KS_UNLIKELY(action_content_list_ptr == nullptr)) {
    return;
  }

  auto* cps_type_list_ptr = static_cast<absl::Span<const int64_t>*>(cps_type_list_void_ptr);
  if (KS_UNLIKELY(cps_type_list_ptr == nullptr)) {
    return;
  }

  auto* seller_id_list_ptr = static_cast<absl::Span<const int64_t>*>(seller_id_list_void_ptr);
  if (KS_UNLIKELY(seller_id_list_ptr == nullptr)) {
    return;
  }

  auto* distribute_id_list_ptr = static_cast<absl::Span<const int64_t>*>(distribute_id_list_void_ptr);
  if (KS_UNLIKELY(distribute_id_list_ptr == nullptr)) {
    return;
  }

  auto* carrier_id_list_ptr = static_cast<absl::Span<const int64_t>*>(carrier_id_list_void_ptr);
  if (KS_UNLIKELY(carrier_id_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_real_attr_list(*action_timestamp_list_ptr, *timestamp_flag_list_ptr,
                                *action_content_list_ptr, *cps_type_list_ptr, *seller_id_list_ptr,
                                *distribute_id_list_ptr, *carrier_id_list_ptr, target_type, max_len);
}

inline void c_merge_float_list_all_f32__f32__f32__f32(float x2, float x4, float x6, float x8,
                                                      void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = merge_float_list_all(x2, x4, x6, x8);
}

inline void c_get_author_history_sell_bins(void* res_void_ptr) {
  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_author_history_sell_bins();
}

inline int64_t c_get_rank_bin(int64_t rank) { return get_rank_bin(rank); }

inline void c_get_ad_meta_timestamp_list(void* features_void_ptr, uint64_t adlog_time, void* res_void_ptr) {
  auto* features_ptr = static_cast<absl::Span<const int64_t>*>(features_void_ptr);
  if (KS_UNLIKELY(features_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_ad_meta_timestamp_list(*features_ptr, adlog_time);
}

inline void c_optional_value_or_int64(void* x_void_ptr, void* y_void_ptr, void* res_void_ptr) {
  auto* x_ptr = static_cast<absl::optional<int64_t>*>(x_void_ptr);
  if (KS_UNLIKELY(x_ptr == nullptr)) {
    return;
  }

  auto* y_ptr = static_cast<absl::optional<int64_t>*>(y_void_ptr);
  if (KS_UNLIKELY(y_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<absl::optional<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = optional_value_or_int64(*x_ptr, *y_ptr);
}

inline int64_t c_get_dense_fea_bin_helper(void* bins_void_ptr, float val) {
  auto* bins_ptr = static_cast<std::vector<int64_t>*>(bins_void_ptr);
  if (KS_UNLIKELY(bins_ptr == nullptr)) {
    return static_cast<int64_t>(0);
  }

  return get_dense_fea_bin_helper(*bins_ptr, val);
}

inline void c_get_author_live_avg_price_paycnt(void* avg_price_void_ptr, void* avg_paycnt_void_ptr,
                                               void* res_void_ptr) {
  auto* avg_price_ptr = static_cast<absl::optional<int64_t>*>(avg_price_void_ptr);
  if (KS_UNLIKELY(avg_price_ptr == nullptr)) {
    return;
  }

  auto* avg_paycnt_ptr = static_cast<absl::optional<int64_t>*>(avg_paycnt_void_ptr);
  if (KS_UNLIKELY(avg_paycnt_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<uint64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_author_live_avg_price_paycnt(*avg_price_ptr, *avg_paycnt_ptr);
}

inline void c_get_query_ad_combine(void* values_list_void_ptr, void* lengths_list_void_ptr,
                                   void* creative_ids_list_void_ptr, int64_t creative_id,
                                   void* res_void_ptr) {
  auto* values_list_ptr = static_cast<absl::Span<const int64_t>*>(values_list_void_ptr);
  if (KS_UNLIKELY(values_list_ptr == nullptr)) {
    return;
  }

  auto* lengths_list_ptr = static_cast<absl::Span<const int64_t>*>(lengths_list_void_ptr);
  if (KS_UNLIKELY(lengths_list_ptr == nullptr)) {
    return;
  }

  auto* creative_ids_list_ptr = static_cast<absl::Span<const int64_t>*>(creative_ids_list_void_ptr);
  if (KS_UNLIKELY(creative_ids_list_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<float>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_query_ad_combine(*values_list_ptr, *lengths_list_ptr, *creative_ids_list_ptr, creative_id);
}

inline float c_get_max_gmv(int64_t max, void* pay_cnt_opt_void_ptr) {
  auto* pay_cnt_opt_ptr = static_cast<absl::optional<int64_t>*>(pay_cnt_opt_void_ptr);
  if (KS_UNLIKELY(pay_cnt_opt_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return get_max_gmv(max, *pay_cnt_opt_ptr);
}

inline void c_add_feature_result_optional_ui64__vector_extractresult(void* x3_void_ptr,
                                                                     void* result_void_ptr) {
  auto* x3_ptr = static_cast<absl::optional<uint64_t>*>(x3_void_ptr);
  if (KS_UNLIKELY(x3_ptr == nullptr)) {
    return;
  }

  auto* result_ptr = static_cast<std::vector<ExtractResult>*>(result_void_ptr);
  if (KS_UNLIKELY(result_ptr == nullptr)) {
    return;
  }

  return add_feature_result(*x3_ptr, result_ptr);
}

inline void c_get_live_author_7day_price(void* author_7day_mid_price_void_ptr,
                                         void* author_7day_std_price_void_ptr, void* res_void_ptr) {
  auto* author_7day_mid_price_ptr = static_cast<absl::optional<int64_t>*>(author_7day_mid_price_void_ptr);
  if (KS_UNLIKELY(author_7day_mid_price_ptr == nullptr)) {
    return;
  }

  auto* author_7day_std_price_ptr = static_cast<absl::optional<int64_t>*>(author_7day_std_price_void_ptr);
  if (KS_UNLIKELY(author_7day_std_price_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_live_author_7day_price(*author_7day_mid_price_ptr, *author_7day_std_price_ptr);
}

inline float c_cast_to_float_optional_ui64(void* x1_void_ptr) {
  auto* x1_ptr = static_cast<absl::optional<uint64_t>*>(x1_void_ptr);
  if (KS_UNLIKELY(x1_ptr == nullptr)) {
    return static_cast<float>(0);
  }

  return cast_to_float(*x1_ptr);
}

inline void c_get_user_hist_page_num_succ_cross_platform(void* page_num_list_void_ptr,
                                                         void* cur_page_num_void_ptr, void* platform_void_ptr,    // NOLINT
                                                         void* res_void_ptr) {
  auto* page_num_list_ptr = static_cast<absl::Span<const int64_t>*>(page_num_list_void_ptr);
  if (KS_UNLIKELY(page_num_list_ptr == nullptr)) {
    return;
  }

  auto* cur_page_num_ptr = static_cast<absl::optional<int64_t>*>(cur_page_num_void_ptr);
  if (KS_UNLIKELY(cur_page_num_ptr == nullptr)) {
    return;
  }

  auto* platform_ptr = static_cast<absl::string_view*>(platform_void_ptr);
  if (KS_UNLIKELY(platform_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_user_hist_page_num_succ_cross_platform(*page_num_list_ptr, *cur_page_num_ptr, *platform_ptr);    // NOLINT
}

inline void c_get_realtime_target_id_match_cnt_v2(void* attr_list_void_ptr, void* target_id_void_ptr,
                                                  void* res_void_ptr) {
  auto* attr_list_ptr = static_cast<absl::Span<const int64_t>*>(attr_list_void_ptr);
  if (KS_UNLIKELY(attr_list_ptr == nullptr)) {
    return;
  }

  auto* target_id_ptr = static_cast<absl::optional<int64_t>*>(target_id_void_ptr);
  if (KS_UNLIKELY(target_id_ptr == nullptr)) {
    return;
  }

  auto* res_ptr = static_cast<std::vector<int64_t>*>(res_void_ptr);
  if (KS_UNLIKELY(res_ptr == nullptr)) {
    return;
  }

  *res_ptr = get_realtime_target_id_match_cnt_v2(*attr_list_ptr, *target_id_ptr);
}
}

}  // namespace ad_algorithm
}  // namespace ks
