#include "teams/ad/ad_algorithm/dragon/command/processor/feature/retriever/batchrow_feature_generation_retriever.h"

#include <cstddef>
#include <vector>
#include <string>
#include <map>
#include <unordered_set>
#include <utility>
#include <chrono>

#include "algo-engine/kaiworks/kaiworks-stream-proto/src/main/proto/kuaishou/kaiworks/stream/enums/enums.pb.h"
#include "algo-engine/kaiworks/kaiworks-stream-proto/src/main/proto/kuaishou/kaiworks/stream/proto/row.pb.h"
#include "ks/reco_proto/proto/reco.pb.h"
#include "dragon/src/core/common_reco_util.h"
#include "teams/ad/ad_algorithm/feature/fast/ad_log_wrapper.h"
#include "teams/ad/ad_nn/feature_extract/processors/constant.h"
#include "teams/ad/ad_nn/feature_extract/processors/ad_bs/util/util.h"
#include "teams/ad/ad_nn/feature_extract/extract_framework/include/eval_context_interface.h"
#include "teams/ad/ad_algorithm/dragon/command/processor/feature/enricher/adlog_kafka_feature_enricher.h"

#include "arrow/pretty_print.h"

#include "google/protobuf/util/json_util.h"

namespace ks {
namespace platform {

using kuaishou::ad::algorithm::AdJointLabeledLog;
using kuaishou::ad::algorithm::Item;
using ks::ad_algorithm::AdLogWrapper;
using kuaishou::ad::algorithm::LabelInfo;
using kuaishou::kaiworks::stream::BatchRow;
using kuaishou::kaiworks::stream::FieldValue;
using kuaishou::kaiworks::stream::FieldType;
using ks::platform::ItemAttr;

#define GET_ARROW_ARRAY_OR_RETURN(ARRAY, ATTR_NAME)                         \
  if (ATTR_NAME.empty()) return;                                            \
  ARRAY = record_batch->GetColumnByName(ATTR_NAME);                         \
  if (!ARRAY) {                                                             \
    CL_LOG_ERROR_EVERY("record_batch_retriever", (#ATTR_NAME ":null"), 100) \
        << #ATTR_NAME << ":null, attr_name:" << ATTR_NAME;                  \
    return;                                                                 \
  }

void BatchRowFeatureGenrationRetriever::Retrieve(AddibleRecoContextInterface *context) {
  AdLogWrapper *wrapper = context->GetMutablePtrCommonAttr<AdLogWrapper>(input_attr_);
  const BatchRow *batch_row = retriev_skeleton_from_batchrow_
    ? context->GetPtrCommonAttr<BatchRow>("br_dragon")
    : nullptr;
  if (log_) {
    bool is_null = batch_row == nullptr;
    LOG(INFO) << "Get BatchRow From Dragon Context : BatchRow is nullptr " << is_null
              << " Common Schema size " << batch_row->common_fields_schema().fields_size()
              << " Common Field size " << batch_row->common_fields_size()
              << " batch_row size " << batch_row->ByteSizeLong();
  }

  CHECK(wrapper) << "BatchRowFeatureGenrationRetriever adlog nullptr";
  wrapper->SetUserExtend((wrapper->Get()).user_extend());
  wrapper->set_is_train(true);

  if (stream_flag_) {
    PreProcessForStream(wrapper);
  }

  feature_handler_.Handle(wrapper);
  const FeatureResult& feature_result = feature_handler_.feature_result();

  if (feature_result.total_sample_len() > 0) {
    if (log_) {
      LOG(INFO) << "total_sample_len = " << feature_result.total_sample_len();
    }
    FillCommonAttrs(context, &feature_result, *wrapper);
    FillItemAttrs(context, &feature_result, *wrapper, *batch_row);
  } else {
    CL_LOG_ERROR_EVERY("adlog_fg_retriever", "feature_result:zero_item", 100) << "FeatureResult has 0 item";
  }
}

void BatchRowFeatureGenrationRetriever::HandlePrefixFeature(
    AddibleRecoContextInterface *context,
    const std::string &key,
    const std::vector<int64> &values,
    bool is_common,
    uint64_t pos) {
  if (features_prefix_.count(key) != 0) {
    std::map<uint64_t, std::vector<int64>> prefix_values;
    for (const auto &prefix : features_prefix_[key]) {
      prefix_values.insert(std::make_pair(prefix, std::vector<int64>()));
    }
    for (const auto &value : values) {
      auto prefix = static_cast<uint64>(value) >> 52;
      if (prefix_values.count(prefix) != 0) {
        prefix_values[prefix].push_back(value);
      }
    }
    for (const auto &prefix : features_prefix_[key]) {
      auto prefix_key = key + "_" + std::to_string(prefix);
      if (is_common) {
        context->SetIntListCommonAttr(prefix_key, std::move(prefix_values[prefix]));
      } else {
        auto *attr_accessor = context->GetItemAttrAccessor(prefix_key);
        auto result = item_results_[pos];
        result.SetIntListAttr(attr_accessor, std::move(prefix_values[prefix]));
      }
    }
  }
}

void BatchRowFeatureGenrationRetriever::FillCommonAttrs(
    AddibleRecoContextInterface *context,
    const FeatureResult *feature_result,
    const AdLogWrapper &ad_log_wrapper) {
  auto user_sparse_iter = feature_result->user_sparse().begin();
  while (user_sparse_iter != feature_result->user_sparse().end()) {
    auto key = user_sparse_iter->first;
    if (log_) {
      LOG(INFO)  << "FillCommonAttrs user_sparse:" << key;
    }
    auto value = user_sparse_iter->second;
    HandlePrefixFeature(context, key, value, true, -1);
    if (features_alias_.count(key) != 0) {
      context->SetIntListCommonAttr(features_alias_[key], std::move(value));
    } else {
      context->SetIntListCommonAttr(key, std::move(value));
    }
    user_sparse_iter++;
  }

  auto user_dense_iter = feature_result->user_dense().begin();
  while (user_dense_iter != feature_result->user_dense().end()) {
    auto key = user_dense_iter->first;
    if (log_) {
      LOG(INFO)  << "FillCommonAttrs user_dense:" << key;
    }
    auto value = user_dense_iter->second;
    if (features_alias_.count(key) != 0) {
      context->SetDoubleListCommonAttr(features_alias_[key], std::move(ConvertList<double>(value)));
    } else {
      context->SetDoubleListCommonAttr(key, std::move(ConvertList<double>(value)));
    }
    user_dense_iter++;
  }

  auto user_int_data_iter = feature_result->user_int_data().begin();
  while (user_int_data_iter != feature_result->user_int_data().end()) {
    auto key = user_int_data_iter->first;
    if (log_) {
      LOG(INFO)  << "FillCommonAttrs user_int_data:" << key;
    }
    auto value = user_int_data_iter->second;
    if (sample_skeleton_.count(key) != 0) {
      context->SetIntCommonAttr(key, value[0]);
    }
    user_int_data_iter++;
  }

  auto sub_context_sparse_iter = feature_result->sub_context_sparse().begin();
  while (sub_context_sparse_iter != feature_result->sub_context_sparse().end()) {
    auto key = sub_context_sparse_iter->first;
    if (log_) {
      LOG(INFO)  << "FillCommonAttrs sub_context_sparse:" << key;
    }
    auto value = sub_context_sparse_iter->second;
    if (!value.empty()) {
      HandlePrefixFeature(context, key, value[0], true, -1);
      if (features_alias_.count(key) != 0) {
        context->SetIntListCommonAttr(features_alias_[key], std::move(value[0]));
      } else {
        context->SetIntListCommonAttr(key, std::move(value[0]));
      }
    }
    sub_context_sparse_iter++;
  }

  auto sub_context_dense_iter = feature_result->sub_context_dense().begin();
  while (sub_context_dense_iter != feature_result->sub_context_dense().end()) {
    auto key = sub_context_dense_iter->first;
    if (log_) {
      LOG(INFO)  << "FillCommonAttrs sub_context_dense:" << key;
    }
    auto value = sub_context_dense_iter->second;
    if (!value.empty()) {
      if (features_alias_.count(key) != 0) {
        context->SetDoubleListCommonAttr(features_alias_[key], std::move(ConvertList<double>(value[0])));
      } else {
        context->SetDoubleListCommonAttr(key, std::move(ConvertList<double>(value[0])));
      }
    }
    sub_context_dense_iter++;
  }

  for (auto sub_context_int_data_iter = feature_result->sub_context_int_data().begin();
      sub_context_int_data_iter != feature_result->sub_context_int_data().end();
      ++sub_context_int_data_iter) {
    auto key = sub_context_int_data_iter->first;
    if (log_) {
      LOG(INFO)  << "FillCommonAttrs sub_context_int_data:" << key;
    }
    auto &value = sub_context_int_data_iter->second;
    if (!value.empty() && !(value[0]).empty()) {
      context->SetIntCommonAttr(key, value[0][0]);
    }
  }

  for (auto sub_context_str_data_iter = feature_result->sub_context_str_data().begin();
      sub_context_str_data_iter != feature_result->sub_context_str_data().end();
      ++sub_context_str_data_iter) {
    auto key = sub_context_str_data_iter->first;
    if (log_) {
      LOG(INFO)  << "FillCommonAttrs sub_context_str_data:" << key;
    }
    auto &value = sub_context_str_data_iter->second;
    if (!value.empty() && !(value[0]).empty()) {
      auto str_value = value[0][0];
      context->SetStringCommonAttr(key, std::move(str_value));
    }
  }

  // 流模式下特有逻辑
  if (stream_flag_) {
    // 记录除 item 外其它信息，用于离线调研
    if (!common_column_for_offline_.empty()) {
      std::string adLogStr;
      AdJointLabeledLog ad_log_for_conv_offline;
      if (ad_conversion_stream_) {
        // 激活模型做字段裁剪
        LOG_EVERY_N(WARNING, 1000) << "ad_conversion_stream set ad_log_for_conv_offline";
        ad_log_for_conv_offline.set_time(ad_log_wrapper.timestamp());
        ad_log_for_conv_offline.SerializeToString(&adLogStr);
      } else {
        ad_log_wrapper.Get().SerializeToString(&adLogStr);
      }
      context->SetStringCommonAttr(common_column_for_offline_, std::move(adLogStr));
    }
    // 记录 kafka tag
    std::vector<std::string> tags;
    GenerateKafkaTags(feature_result, &tags);
    context->SetStringListCommonAttr(kafka_tag_column_name_, std::move(tags));
  }
}

void BatchRowFeatureGenrationRetriever::FillItemAttrs(
      AddibleRecoContextInterface *context,
      const FeatureResult *feature_result,
      const AdLogWrapper &ad_log_wrapper,
      const BatchRow& batch_row) {
  std::vector<CommonRecoRetrieveResult> candidates;
  if (log_) {
    LOG(INFO) << "feature_result item_ids = " << feature_result->item_ids().size();
  }
  candidates.reserve(feature_result->item_ids().size());
  for (int i = 0; i < feature_result->item_ids().size(); i++) {
    auto id_feature = feature_result->item_ids()[i];
    // 使用 item_id 作为 context item_key ，适配下游 SIM processor
    candidates.emplace_back(id_feature, reason_);
  }

  item_results_.clear();
  if (union_training_flag_) {
    item_results_.reserve(item_results_.size() + candidates.size());
    for (const auto &candidate : candidates) {
        item_results_.emplace_back(context->NewCommonRecoResult(candidate.item_key,
                                        candidate.reason, candidate.score, 0, GetTableName()));
      }
  } else {
    AddToRecoResults(context, candidates, &item_results_);
  }
  auto item_sparse_iter = feature_result->item_sparse().begin();
  while (item_sparse_iter != feature_result->item_sparse().end()) {
    auto key = item_sparse_iter->first;
    if (log_) {
      LOG(INFO)  << "FillItemAttrs item_sparse:" << key;
    }
    auto value = item_sparse_iter->second;
    ItemAttr *attr_accessor = nullptr;
    if (features_alias_.count(key) != 0) {
      attr_accessor = context->GetItemAttrAccessor(features_alias_[key]);
    } else {
      attr_accessor = context->GetItemAttrAccessor(key);
    }

    for (int i = 0; i < value.size(); i++) {
      auto result = item_results_[i];
      HandlePrefixFeature(context, key, value[i], false, i);
      result.SetIntListAttr(attr_accessor, std::move(value[i]));
    }
    item_sparse_iter++;
  }

  auto item_dense_iter = feature_result->item_dense().begin();
  while (item_dense_iter != feature_result->item_dense().end()) {
    auto key = item_dense_iter->first;
    if (log_) {
      LOG(INFO)  << "FillItemAttrs item_dense:" << key;
    }
    auto value = item_dense_iter->second;
    ItemAttr *attr_accessor = nullptr;
    if (features_alias_.count(key) != 0) {
      attr_accessor = context->GetItemAttrAccessor(features_alias_[key]);
    } else {
      attr_accessor = context->GetItemAttrAccessor(key);
    }

    for (int i = 0; i < value.size(); i++) {
      auto result = item_results_[i];
      result.SetDoubleListAttr(attr_accessor, std::move(ConvertList<double>(value[i])));
    }
    item_dense_iter++;
  }

  auto item_int_data_iter = feature_result->item_int_data().begin();
  while (item_int_data_iter != feature_result->item_int_data().end()) {
    auto key = item_int_data_iter->first;
    if (log_) {
      LOG(INFO)  << "FillItemAttrs item_int_data:" << key;
    }
    auto value = item_int_data_iter->second;

    std::map<std::string, std::string>::iterator map_iter;
    bool is_list_feature = false;
    auto real_name = key;
    if ((map_iter = item_filters_.find(key)) != item_filters_.end()) {
      real_name = map_iter->second;
    } else if ((map_iter = label_extractors_.find(key)) != label_extractors_.end()) {
      real_name = map_iter->second;
      is_list_feature = true;
    }
    auto *attr_accessor = context->GetItemAttrAccessor(real_name);
    for (int i = 0; i < value.size(); i++) {
      auto &result = item_results_[i];
      if (is_list_feature) {
        result.SetIntListAttr(attr_accessor, std::move(value[i]));
      } else {
        if (value[i].empty()) {
          LOG_EVERY_N(WARNING, 10000) << "key [" << key << "] has no value, and skip.";
          continue;
        }
        result.SetIntAttr(attr_accessor, std::move(value[i][0]));
      }
    }
    item_int_data_iter++;
  }

  auto item_str_data_iter = feature_result->item_str_data().begin();
  while (item_str_data_iter != feature_result->item_str_data().end()) {
    auto key = item_str_data_iter->first;
    if (log_) {
      LOG(INFO)  << "FillItemAttrs item_str_data:" << key;
    }
    auto value = item_str_data_iter->second;
    auto *attr_accessor = context->GetItemAttrAccessor(key);
    if (sample_skeleton_.count(key) != 0) {
      for (int i = 0; i < value.size(); i++) {
        if (value[i].empty()) {
          LOG(ERROR) << "FillItemAttrs item_str_data key:" << key << ", value is empty, and skip.";
          continue;
        }
        auto result = item_results_[i];
        result.SetStringAttr(attr_accessor, std::move(value[i][0]));
      }
    }
    item_str_data_iter++;
  }

  const arrow::RecordBatch* record_batch =
      context->GetPtrCommonAttr<arrow::RecordBatch>(input_arrow_attr_);

  // 讲一些透传的列从原始数据结构中放入 Dragon Context ，整个处理过程不能过滤 item，否则会导致有问题
  if (record_batch != nullptr) {
    if (log_) {
      ArrowUtils::LogRecordBatch(record_batch, "BatchRowFeatureGenrationRetriever");
    }
    if (retriev_skeleton_from_batchrow_) {
      // Kaiworks-Stream v2.0 , use BatchRow in Arrow
      auto common_schema = batch_row.common_fields_schema();
      thread_local std::map<std::string, int64_t> common_attr2index;
      for (int i = 0; i < common_schema.fields_size(); i++) {
        auto attr_name = common_schema.fields(i).field_name();
        common_attr2index.emplace(attr_name, i);
      }

      auto item_schema = batch_row.item_fields_schema();
      thread_local std::map<std::string, int64_t> item_attr2index;
      for (int i = 0; i < item_schema.fields_size(); i++) {
        auto attr_name = item_schema.fields(i).field_name();
          item_attr2index.emplace(attr_name, i);
      }
      if (item_schema.fields_size() == 0) {
        item_attr2index.emplace("not_exist_item_attr", 0);
      }


      if (!batch_row.has_common_fields_schema() || batch_row.common_fields_size() == 0) {
        LOG(ERROR) << "BatchRow In Arrow is nullptr , colname : " << serialized_brp_attr_in_arrow_;
      } else {
        for (const auto &attr_name : pass_through_columns_) {
          SetAttrFromBatchRowIntoDragonContext(common_attr2index, item_attr2index,
           context, batch_row, item_results_, attr_name);
        }
      }
    } else {
      // Kaiworks-Stream v1.0 , use apache Arrow
      for (const auto &attr_name : pass_through_columns_) {
        ItemAttrArrowArrayHandler item_attr_handler(context, item_results_, attr_name);
        SetAttr(context, record_batch, attr_name, &item_attr_handler);
      }
    }
  } else {
    LOG(ERROR) << "BatchRowFeatureGenrationRetriever record_batch is nullptr";
  }

  // 流模式下记录 item ，用于离线调研
  if (stream_flag_ && !item_column_for_offline_.empty()) {
    auto *attr_accessor = context->GetItemAttrAccessor(item_column_for_offline_);
    std::map<uint64_t, int> item_id_index_map;
    for (int i = 0; i < ad_log_wrapper.item_size(); i++) {
      item_id_index_map[ad_log_wrapper.item_id(i)] = i;
    }
    std::map<uint64_t, int>::iterator iter;
    for (int i = 0; i < feature_result->item_ids().size(); i++) {
      uint64_t item_id = feature_result->item_ids()[i];
      iter = item_id_index_map.find(item_id);
      if (iter != item_id_index_map.end()) {
        int item_index_in_log = iter->second;
        std::string itemStr;
        if (ad_conversion_stream_) {
          LOG_EVERY_N(WARNING, 1000) << "ad_conversion_stream set item_for_conv_offline";
          Item item_for_conv_offline;
          LabelInfo *label_info_ptr_for_conv_offline = item_for_conv_offline.mutable_label_info();
          label_info_ptr_for_conv_offline->set_played_time(
              ad_log_wrapper.item(item_index_in_log).label_info().played_time());
          label_info_ptr_for_conv_offline->mutable_label_id()->CopyFrom(
              ad_log_wrapper.item(item_index_in_log).label_info().label_id());
          google::protobuf::Map<uint64, kuaishou::ad::LabelAttr> label_infos(
            ad_log_wrapper.item(item_index_in_log).label_info().label_infos());
          label_info_ptr_for_conv_offline->mutable_label_infos()->swap(label_infos);
          item_for_conv_offline.SerializeToString(&itemStr);
        } else {
          ad_log_wrapper.item(item_index_in_log).SerializeToString(&itemStr);
        }
        if (!itemStr.empty()) {
          auto &result = item_results_[i];
          result.SetStringAttr(attr_accessor, std::move(itemStr));
        } else {
          LOG(ERROR) << "item is empty for dump.";
        }
      } else {
        LOG(ERROR) << "item id not found in ad log.";
      }
    }
  }
}

bool BatchRowFeatureGenrationRetriever::initSampleSkeleton() {
  // 解析样本骨架文件，获取样本骨架字段
  std::string sample_skeleton_config_name = config()->GetString("sample_skeleton_name",
       "ad.algorithm_feature.kaiworks_sample_skeleton");
  LOG(INFO) << "sample_skeleton_config_name:" << sample_skeleton_config_name;

  KconfNode<KconfJson> sample_skeleton_kconf_instance(sample_skeleton_config_name);
  auto sample_skeleton_json = sample_skeleton_kconf_instance.Get()->data;
  if (sample_skeleton_json == nullptr) {
    LOG(ERROR) << "no kconf key:" << sample_skeleton_config_name;
    return false;
  }

  auto &sample_skeleton_map = sample_skeleton_json->objects();
  for (auto iter : sample_skeleton_map) {
    auto name = iter.first;
    LOG(INFO) << "sample_skeleton: " << name;
    sample_skeleton_.insert(name);
  }
  return true;
}

void BatchRowFeatureGenrationRetriever::InitConfigFromSchemaOutLine(
    const std::string &schema_out_line) {
  std::regex p_space("[ \\t]");
  std::vector<std::string> arr =
      absl::StrSplit(std::regex_replace(schema_out_line, p_space, ""), ",");
  bool is_item_filter = false;
  bool is_label_extractor = false;
  std::string label_extractor_orig_name;
  std::string item_filter_orig_name;
  std::string column_name;
  uint64_t prefix = -1;
  std::string extractor;
  for (auto& token : arr) {
    std::vector<std::string> seg = absl::StrSplit(token, "=");
    if (seg.size() >= 2) {
      auto &key = seg[0];
      auto &value = seg[1];
      for (int i = 2; i < seg.size(); i++) {
        value.append("=");
        value.append(seg[i]);
      }
      if (key == "column_name") {
        // LOG(INFO) << "column_name: " << value;
        column_name = value;
      } else if (key == "compression_type") {
        // LOG(INFO) << "compression_type: " << value;
      } else if (key == "item_filter") {
        is_item_filter = true;
        // LOG(INFO) << "item_filter:"  << value;
        item_filter_orig_name = value;
      } else if (key == "label_extractor") {
        is_label_extractor = true;
        // LOG(INFO) << "label_extractor:"  << value;
        label_extractor_orig_name = value;
      } else if (key == "prefix") {
        prefix = std::stoul(value);
      } else if (key == "extractor") {
        extractor = value;
      }
    }
  }
  if (prefix != -1) {
    if (extractor.empty()) {
      LOG(ERROR) << "wrong prefix feature with no extractor, prefix = " << prefix;
    } else {
      if (features_prefix_.count(extractor) == 0) {
        features_prefix_.insert(std::make_pair(extractor, std::set<uint64_t>()));
      }
      features_prefix_[extractor].insert(prefix);
      LOG(INFO) << "extractor " << extractor << " add prefix " << prefix;
    }
  } else {
    if (!extractor.empty()) {
      features_alias_.insert(std::make_pair(extractor, column_name));
      if (log_) {
        LOG(INFO) << "add extractor alias extractor = " << extractor << ", alias = " << column_name;
      }
    }
  }
  if (is_item_filter) {
    item_filters_.insert(std::make_pair(item_filter_orig_name, column_name));
  } else if (is_label_extractor) {
    label_extractors_.insert(std::make_pair(label_extractor_orig_name, column_name));
  }
}

void BatchRowFeatureGenrationRetriever::initExtractConfig() {
  std::string extract_config_file_ = config()->GetString("fg_schema_out", "default.txt");
  LOG(INFO) << "extract_config_file_:" << extract_config_file_;
  std::ifstream infile(extract_config_file_);
  if (infile.is_open()) {
    std::string line;
    while (std::getline(infile, line)) {
      InitConfigFromSchemaOutLine(line);
    }
  } else {
    LOG(ERROR) << "open file failed: " << extract_config_file_;
  }
}

void BatchRowFeatureGenrationRetriever::initExtractConfigFromKconf() {
  std::string extract_config_kconf = config()->GetString("fg_schema_out_kconf", "");
  if (extract_config_kconf.empty()) {
    LOG(ERROR) << "config of [fg_schema_out_kconf] is missing.";
    return;
  }
  LOG(INFO) << "extract_config_kconf: " << extract_config_kconf;
  KconfNode<std::string> extract_config_kconf_instance(extract_config_kconf, std::string());
  auto config_data = extract_config_kconf_instance.Get();
  if (nullptr == config_data) {
    LOG(ERROR) << "no kconf key: " << extract_config_kconf;
    return;
  }
  std::vector<std::string> lines = absl::StrSplit(*config_data, "\n");
  for (auto &line : lines) {
    InitConfigFromSchemaOutLine(line);
  }
}

bool BatchRowFeatureGenrationRetriever::InitForStream() {
  // 解析 SIM 相关配置
  stream_config_ = config()->Get("stream_sim_config");
  if (nullptr != stream_config_) {
    if (stream_config_->GetBoolean("enable_ad_live_item_colossus", false)) {
      ad_live_colossus_client_.reset(
          new colossus::CommonItemClient("grpc_colossusAdLiveItem"));
    }
    if (stream_config_->GetBoolean("enable_reco_live_item_v1_colossus", false)) {
      reco_live_v1_colossus_client_.reset(
          new colossus::CommonItemClient("grpc_colossusLiveItem"));
    }
    if (stream_config_->GetBoolean("enable_reco_live_item_colossus", false)) {
      reco_live_colossus_client_.reset(
          new colossus::CommonItemClient("grpc_colossusLiveItemV3"));
    }
    if (stream_config_->GetBoolean("enable_ad_photo_item_colossus", false)) {
      ad_photo_colossus_client_.reset(
          new colossus::CommonItemClient("grpc_colossusSimAdStateItemNew"));
    }
    if (stream_config_->GetBoolean("enable_ad_photo_v3_item_colossus", false)) {
      ad_photo_v3_colossus_client_.reset(
          new colossus::CommonItemClient("grpc_colossusSimAdStateItemV3"));
    }
    if (stream_config_->GetBoolean("enable_ad_goods_item_colossus", false)) {
      ad_goods_colossus_client_.reset(
          new colossus::CommonItemClient("grpc_colossusSimAdGoodsItem"));
    }
    if (stream_config_->GetBoolean("enable_ad_goods_item_new_colossus", false)) {
      ad_goods_colossus_new_client_.reset(
          new colossus::CommonItemClient("grpc_colossusAdGoodsItem"));
    }
#ifdef ENABLE_KUIBA
    if (stream_config_->GetBoolean("enable_remote_author_cluster", false)) {
      author_enricher_.reset(new ks::ad_algorithm::LiveAuthorClusterEnricher());
    }
#endif
    if (stream_config_->GetBoolean("enable_remote_live_cluster", false)) {
      live_cluster_enricher_.reset(new ad_algorithm::LiveRecoClusterEnricher());
    }
    auto *feature_process_config = stream_config_->Get("feature_process_config");
    if (feature_process_config != nullptr) {
      feature_processor_ = std::make_unique<FeatureProcessor>();
      feature_processor_->Init(feature_process_config);
      LOG(INFO) << "feature_process_config init success";
    } else {
      LOG(INFO) << "feature_process_config not found";
    }
  } else {
    LOG(ERROR) << "config [stream_sim_config] is missing!";
  }

  // 解析 dump 相关配置
  auto *stream_dump_config = config()->Get("stream_dump_config");
  if (nullptr != stream_dump_config) {
    item_column_for_offline_ = stream_dump_config->GetString("item_field", "");
    common_column_for_offline_ = stream_dump_config->GetString("common_field", "");
    LOG(INFO) << "stream_dump_config init done, item_field:" << item_column_for_offline_
        << ", common_field:" << common_column_for_offline_;
  } else {
    LOG(ERROR) << "config [stream_dump_config] is missing!";
  }

  // 解析 kafka tag 相关配置
  kafka_tag_column_name_ = config()->GetString("kafka_tag_column_name", "kafka_tag");
  LOG(INFO) << "kafka_tag_column_name_:" << kafka_tag_column_name_;

  return true;
}

template<typename T>
void BatchRowFeatureGenrationRetriever::QueryFromColossus(
    const std::unique_ptr<colossus::CommonItemClient> &client,
    std::shared_ptr<std::vector<T>> data_in_ad_log_wrapper,
    const std::string &type_desc,
    int64_t user_id) {
  do {
    auto pms =
        std::make_shared<std::promise<colossus::CommonItemResponse*>>();
    colossus::CommonItemResponse common_item_response;
    // 1. async request
    auto ok = client->AsyncBrpcQueryItem(user_id, &common_item_response, pms);
    if (!ok) {
      LOG_EVERY_N(WARNING, 1000) << type_desc << " get_user_sim_failed: " << user_id;
      break;
    }
    // 2. judge response
    auto fut = pms->get_future();
    colossus::CommonItemResponse* common_item_response_ptr = fut.get();
    if (common_item_response_ptr == nullptr) {
      LOG_EVERY_N(WARNING, 1000)
          << type_desc
          << " colossus future get is nullptr, user_id: " << user_id;
      break;
    }
    // 3. deal with response
    int item_num = 0;
    T const* start_addr = nullptr;
    if (!client->DecodeCommonItemResponse(&common_item_response, &start_addr, &item_num)) {
      LOG_EVERY_N(WARNING, 1000)
          << type_desc
          << "decode colossus response failed, user_id: " << user_id;
      break;
    }
    data_in_ad_log_wrapper->resize(item_num);
    std::copy_n(start_addr, item_num, data_in_ad_log_wrapper->begin());
    if (log_) {
      LOG_EVERY_N(INFO, 10000) << type_desc << " query from colossus success, user_id: " << user_id
          << ", item size: " << data_in_ad_log_wrapper->size();
    }
  } while (false);
}

void BatchRowFeatureGenrationRetriever::QueryFromAuthorCluster(
    ks::ad_algorithm::AdLogWrapper *ad_log_wrapper) {
  do {
    std::unordered_set<int64> request_aids;
    uint64_t time = ad_log_wrapper->Get().time() / 1000;
    if (ad_log_wrapper->colossus_ad_live_item()) {
      for (auto& item : *(ad_log_wrapper->colossus_ad_live_item())) {
        if (item.timestamp > time) {
          continue;
        }
        request_aids.emplace(item.author_id);
      }
    }
    if (ad_log_wrapper->colossus_reco_live_item()) {
      for (auto& item : *(ad_log_wrapper->colossus_reco_live_item())) {
        if (item.timestamp > time) {
          continue;
        }
        request_aids.emplace(item.author_id);
      }
    }
    if (request_aids.empty()) {
      break;
    }
    for (int i = 0; i < ad_log_wrapper->Get().item_size(); i++) {
      int64 author_id = get_author_id_from_item(ad_log_wrapper->Get().item(i));
      if (author_id > 0) {
        request_aids.emplace(author_id);
      }
    }
#ifdef ENABLE_KUIBA
    author_enricher_->FetchClusterIdsFromEmbeddingServer(
        request_aids, ad_log_wrapper->mutable_author_cluster_map());
#endif
  } while (false);
}

void BatchRowFeatureGenrationRetriever::QueryFromLiveCluster(
    ks::ad_algorithm::AdLogWrapper *ad_log_wrapper) {
  std::unordered_set<int64> request_ids;
  for (int i = 0; i < ad_log_wrapper->Get().item_size(); i++) {
    int64 live_id = get_live_id_from_item(ad_log_wrapper->Get().item(i));
    if (live_id > 0) {
      request_ids.emplace(live_id);
    }
  }
  if (!request_ids.empty()) {
    live_cluster_enricher_->FetchClusterIdsFromEmbeddingServer(
        request_ids, ad_log_wrapper->mutable_live_cluster_map());
    ad_log_wrapper->live_cluster_map_ready_ = true;
  }
}

void BatchRowFeatureGenrationRetriever::ParseMerchantUserAttrs(
    ks::ad_algorithm::AdLogWrapper *ad_log_wrapper) {
  auto user_sample_attrs = ad_log_wrapper->mutable_merchant_user_attrs();
  if (!ad_log_wrapper->Get().has_user_info() ||
      ad_log_wrapper->user_info().serialized_merchant_user_attrs().empty() ||
      !user_sample_attrs->ParseFromString(
          ad_log_wrapper->Get().user_info().serialized_merchant_user_attrs())) {
    LOG_EVERY_N(WARNING, 10000) << "merchant_user_info empty or parse error";
  }
}

void BatchRowFeatureGenrationRetriever::ParseMerchantItemAttrs(
    ks::ad_algorithm::AdLogWrapper *ad_log_wrapper) {
  for (size_t i = 0; i < ad_log_wrapper->Get().item_size(); ++i) {
    auto& item = ad_log_wrapper->Get().item(i);
    if (item.has_merchant_info() &&
        !item.merchant_info().serialized_merchant_item_attrs().empty()) {
      auto item_sample_attrs = ad_log_wrapper->mutable_merchant_item_attrs(i);
      if (!item_sample_attrs->ParseFromString(
          item.merchant_info().serialized_merchant_item_attrs())) {
        LOG_EVERY_N(WARNING, 10000) << "merchant_item_attrs parse error";
      }
    }
  }
}

void BatchRowFeatureGenrationRetriever::ParseRecoUserInfo(
      ks::ad_algorithm::AdLogWrapper *ad_log_wrapper) {
  auto reco_user_info = ad_log_wrapper->mutable_reco_user_info();
  if (ad_log_wrapper->Get().serialized_reco_user_info().empty() ||
      !reco_user_info->ParseFromString(
          ad_log_wrapper->Get().serialized_reco_user_info())) {
    LOG_EVERY_N(WARNING, 1000000)
        << "reco_user_info empty or parse reco_user_info error";
  }
}

void BatchRowFeatureGenrationRetriever::PreProcessForStream(AdLogWrapper *ad_log_wrapper) {
  // *** 请求各种 colossus ***
  if (ad_log_wrapper->has_user_info()) {
    auto user_id = ad_log_wrapper->user_info().id();

    if (ad_live_colossus_client_) {
      QueryFromColossus(ad_live_colossus_client_,
          ad_log_wrapper->mutable_colossus_ad_live_item(), "ad_live_item", user_id);
    }
    if (reco_live_colossus_client_) {
      QueryFromColossus(reco_live_colossus_client_,
          ad_log_wrapper->mutable_colossus_reco_live_item(), "reco_live_item", user_id);
    }
    if (reco_live_v1_colossus_client_) {
      QueryFromColossus(reco_live_v1_colossus_client_,
          ad_log_wrapper->mutable_colossus_reco_live_v1_item(), "reco_live_v1_item", user_id);
    }
    if (ad_photo_colossus_client_) {
      QueryFromColossus(ad_photo_colossus_client_,
          ad_log_wrapper->mutable_colossus_reco_photo(), "ad_photo_item", user_id);
    }
    if (ad_photo_v3_colossus_client_) {
      auto ad_photo_v3_item = ad_log_wrapper->mutable_colossus_reco_photo_v3();
      QueryFromColossus(ad_photo_v3_colossus_client_,
          ad_photo_v3_item, "ad_photo_v3_item", user_id);
      if (!ad_photo_v3_item->empty()) {
        ks::ad_nn::PostProcessRecoSimV3(ad_log_wrapper);
      }
    }
    if (ad_goods_colossus_client_) {
      QueryFromColossus(ad_goods_colossus_client_,
          ad_log_wrapper->mutable_colossus_ad_goods_item(), "ad_goods_item", user_id);
    }
    if (ad_goods_colossus_new_client_) {
      auto ad_goods_item_new = ad_log_wrapper->mutable_colossus_ad_goods_item_new();
      QueryFromColossus(ad_goods_colossus_new_client_,
          ad_goods_item_new, "ad_goods_new_item", user_id);
      if (!ad_goods_item_new->empty()) {
        ks::ad_nn::PostProcessAdGoodsSimV3(ad_log_wrapper);
      }
    }
  }
  // *** author_enricher 相关逻辑 ***
#ifdef ENABLE_KUIBA
  if (author_enricher_) {
    QueryFromAuthorCluster(ad_log_wrapper);
  }
#endif
  // *** live_cluster_enricher 相关逻辑 ***
  if (live_cluster_enricher_) {
    QueryFromLiveCluster(ad_log_wrapper);
  }
  // *** 使用 FeatureProcessor 处理以上从 colossus 获取的数据 ***
  if (feature_processor_) {
    feature_processor_->Process(ad_log_wrapper);
  }
  // *** 反序列化 reco_user_info & merchant_user_attrs & merchant_item_attrs ***
  ParseRecoUserInfo(ad_log_wrapper);
  ParseMerchantUserAttrs(ad_log_wrapper);
  ParseMerchantItemAttrs(ad_log_wrapper);
}

void BatchRowFeatureGenrationRetriever::GenerateKafkaTags(
    const FeatureResult *feature_result,
    std::vector<std::string> *tags) {
  if (nullptr == feature_result || nullptr == tags) {
    return;
  }
  std::map<std::string, std::string>::iterator map_iter;
  for (auto item_int_data_iter = feature_result->item_int_data().begin();
      item_int_data_iter != feature_result->item_int_data().end(); ++item_int_data_iter) {
    auto& key = item_int_data_iter->first;
    map_iter = item_filters_.find(key);
    if (map_iter != item_filters_.end()) {
      auto& value = item_int_data_iter->second;
      for (int i = 0; i < value.size(); i++) {
        if (!value[i].empty() && value[i][0]) {
          tags->emplace_back(map_iter->second);
          break;
        }
      }
    }
  }
}

// ****************************** DrgonContext Writer Functions For Arrow ******************************
void BatchRowFeatureGenrationRetriever::SetAttr(
    AddibleRecoContextInterface *context,
    const arrow::RecordBatch *record_batch,
    const std::string &attr_name,
    ArrowArrayHandler *handler) {
  std::shared_ptr<arrow::Array> array;
  GET_ARROW_ARRAY_OR_RETURN(array, attr_name);
  switch (array->type_id()) {
    case arrow::Int64Type::type_id: {
      auto int64_array = std::dynamic_pointer_cast<arrow::Int64Array>(array);
      CHECK(int64_array) << "nullptr: downcast Array -> Int64Array";
      handler->Process(int64_array.get());
    } break;
    case arrow::DoubleType::type_id: {
      auto double_array = std::dynamic_pointer_cast<arrow::DoubleArray>(array);
      CHECK(double_array) << "nullptr: downcast Array -> DoubleArray";
      handler->Process(double_array.get());
    } break;
    case arrow::BinaryType::type_id: {
      auto binary_array = std::dynamic_pointer_cast<arrow::BinaryArray>(array);
      CHECK(binary_array) << "nullptr: downcast Array -> BinaryArray";
      handler->Process(binary_array.get());
    } break;
    case arrow::StringType::type_id: {
      auto string_array = std::dynamic_pointer_cast<arrow::StringArray>(array);
      CHECK(string_array) << "nullptr: downcast Array -> StringArray";
      handler->Process(string_array.get());
    } break;
    case arrow::ListType::type_id: {
      auto list_array = std::dynamic_pointer_cast<arrow::ListArray>(array);
      CHECK(list_array) << "nullptr: downcast Array -> ListArray";
      SetListAttr(context, list_array.get(), attr_name, handler);
    } break;
    default:
      CL_LOG_ERROR_EVERY("ArrowConvertToAdlogEnricher", "array:type_error", 100)
          << "type_error, attr_name:" << attr_name << ", type_id:" << array->type_id();
  }
}

void BatchRowFeatureGenrationRetriever::SetListAttr(
    AddibleRecoContextInterface *context,
    const arrow::ListArray *list_array,
    const std::string &attr_name,
    ArrowArrayHandler *handler) {
  std::shared_ptr<arrow::Array> array = list_array->values();
  if (list_array->length() < handler->LeastListArrayLengthRequired()) {
    CL_LOG_WARNING_EVERY("ArrowConvertToAdlogEnricher", "list_array:under_size", 100)
        << "list_array is under_size: attr_name = " << attr_name << ", " << list_array->length() << " vs "
        << handler->LeastListArrayLengthRequired();
    return;
  }
  if (array == nullptr) {
    return;
  }
  switch (array->type_id()) {
    case arrow::Int64Type::type_id: {
      auto int64_array = std::dynamic_pointer_cast<arrow::Int64Array>(array);
      CHECK(int64_array) << "nullptr: downcast Array -> Int64Array";
      handler->Process(list_array, int64_array.get());
    } break;
    case arrow::DoubleType::type_id: {
      auto double_array = std::dynamic_pointer_cast<arrow::DoubleArray>(array);
      CHECK(double_array) << "nullptr: downcast Array -> DoubleArray";
      handler->Process(list_array, double_array.get());
    } break;
    case arrow::BinaryType::type_id: {
      auto binary_array = std::dynamic_pointer_cast<arrow::BinaryArray>(array);
      CHECK(binary_array) << "nullptr: downcast Array -> BinaryArray";
      handler->Process(list_array, binary_array.get());
    } break;
    case arrow::StringType::type_id: {
      auto string_array = std::dynamic_pointer_cast<arrow::StringArray>(array);
      CHECK(string_array) << "nullptr: downcast Array -> string_array";
      handler->Process(list_array, string_array.get());
    } break;
    default:
      CL_LOG_ERROR_EVERY("ArrowConvertToAdlogEnricher", "array:type_error", 100)
          << "type_error, attr_name:" << attr_name << ", type_id:" << array->type_id();
  }
}

// ****************************** DrgonContext Writer Functions For BatchRow ******************************
void BatchRowFeatureGenrationRetriever::SetAttrFromBatchRowIntoDragonContext(
  std::map<std::string, int64_t> common_attr2index,
  std::map<std::string, int64_t> item_attr2index,
  AddibleRecoContextInterface *context,
  const BatchRow& batch_row,
  const std::vector<CommonRecoResult> &item_results,
  const std::string &attr_name) {
  auto type = getFieldTypeFromBatchRow(common_attr2index, item_attr2index, batch_row, attr_name);
  if (type == FieldType::NULL_) {
    LOG_EVERY_N(INFO, 10000) << "Column " << attr_name << " type is null in BatchRow !";
    return;
  }

  ItemAttr *attr_accessor = context->GetItemAttrAccessor(attr_name);
  switch (type) {
    case FieldType::LONG:
    case FieldType::INT: {
      for (int i = 0; i < item_results_.size(); ++i) {
        bool a = false;
        bool *has_value = &a;
        auto sample_attr = getFieldValueFromBatchRow(common_attr2index, item_attr2index,
         batch_row, attr_name, i, has_value);
        if (!(*has_value)) continue;
        item_results_[i].SetIntAttr(attr_accessor, sample_attr.long_value());
      }
    } break;
    case FieldType::FLOAT:
    case FieldType::DOUBLE: {
      for (int i = 0; i < item_results_.size(); ++i) {
        bool a = false;
        bool *has_value = &a;
        auto sample_attr = getFieldValueFromBatchRow(common_attr2index, item_attr2index,
         batch_row, attr_name, i, has_value);
        if (!(*has_value)) continue;
        item_results_[i].SetDoubleAttr(attr_accessor, sample_attr.float_value());
      }
    } break;
    case FieldType::STRING:
    case FieldType::BYTES: {
      for (int i = 0; i < item_results_.size(); ++i) {
        bool a = false;
        bool *has_value = &a;
        auto sample_attr = getFieldValueFromBatchRow(common_attr2index, item_attr2index,
         batch_row, attr_name, i, has_value);
        if (!(*has_value)) continue;
        auto value = sample_attr.bytes_value().size() > 0
        ? sample_attr.bytes_value()
        : sample_attr.string_value();
        item_results_[i].SetStringAttr(attr_accessor, value);
      }
    } break;
    case FieldType::LONG_LIST:
    case FieldType::INT_LIST: {
      thread_local std::vector<int64_t> int_values;
      for (int i = 0; i < item_results_.size(); ++i) {
        bool a = false;
        bool *has_value = &a;
        auto sample_attr = getFieldValueFromBatchRow(common_attr2index, item_attr2index,
         batch_row, attr_name, i, has_value);
        if (!(*has_value)) continue;
        int_values.clear();
        for (int j = 0; j < sample_attr.long_list_value_size(); j++) {
          int_values.push_back(sample_attr.long_list_value(j));
        }
        item_results_[i].SetIntListAttr(attr_accessor, std::move(int_values));
      }
    } break;
    case FieldType::FLOAT_LIST:
    case FieldType::DOUBLE_LIST: {
      thread_local std::vector<double> double_values;
      for (int i = 0; i < item_results_.size(); ++i) {
        bool a = false;
        bool *has_value = &a;
        auto sample_attr = getFieldValueFromBatchRow(common_attr2index, item_attr2index,
         batch_row, attr_name, i, has_value);
        if (!(*has_value)) continue;
        double_values.clear();
        for (int j = 0; j < sample_attr.float_list_value_size(); j++) {
          double_values.push_back(sample_attr.float_list_value(j));
        }
        item_results_[i].SetDoubleListAttr(attr_accessor, std::move(double_values));
      }
    } break;
    case FieldType::STRING_LIST:
    case FieldType::BYTES_LIST: {
      thread_local std::vector<std::string> string_values;
      for (int i = 0; i < item_results_.size(); ++i) {
        bool a = false;
        bool *has_value = &a;
        auto sample_attr = getFieldValueFromBatchRow(common_attr2index, item_attr2index,
         batch_row, attr_name, i, has_value);
        if (!(*has_value)) continue;
        string_values.clear();
        auto string_list = sample_attr.string_list_value_size() > 0
          ? sample_attr.string_list_value()
          : sample_attr.bytes_list_value();
        for (int j = 0; j < string_list.size(); j++) {
          string_values.push_back(string_list.at(j));
        }
        item_results_[i].SetStringListAttr(attr_accessor, std::move(string_values));
      }
    } break;
    default:
      break;
  }
  }

// ****************************** Utils Functions For BatchRow ******************************
FieldType BatchRowFeatureGenrationRetriever::getFieldTypeFromBatchRow(
    std::map<std::string, int64_t> common_attr2index,
    std::map<std::string, int64_t> item_attr2index,
    const BatchRow& batch_row,
    const std::string &col) {
  if (common_attr2index.find(col) != common_attr2index.end()) {
    return batch_row.common_fields_schema().fields(common_attr2index[col]).type();
  }

  if (item_attr2index.find(col) != item_attr2index.end()) {
    return batch_row.item_fields_schema().fields(common_attr2index[col]).type();
  }
  return FieldType::NULL_;
}


FieldValue BatchRowFeatureGenrationRetriever::getFieldValueFromBatchRow(
    std::map<std::string, int64_t> common_attr2index,
    std::map<std::string, int64_t> item_attr2index,
    const BatchRow& batch_row,
    const std::string &colname,
    int pos,
    bool *has_value) {
  if (common_attr2index.find(colname) != common_attr2index.end()) {
    *has_value = true;
    return batch_row.common_fields(common_attr2index[colname]);
  }

  if (item_attr2index.find(colname) != item_attr2index.end()) {
    if (pos >= batch_row.item_row_data_size()) {
      *has_value = false;
      return FieldValue::default_instance();
    }
    *has_value = true;
    return batch_row.item_row_data(pos).item_fields(item_attr2index[colname]);
  }

  *has_value = false;
  return FieldValue::default_instance();
}

// void BatchRowFeatureGenrationRetriever::initLookUpIndexs(BatchRow batch_row) {
//     std::ostringstream oss1;
//     std::ostringstream oss2;
//     if (batch_row.has_common_fields_schema()) {
//       for (int index = 0; index < batch_row.common_fields_schema().fields_size(); ++index) {
//         const std::string& fieldName = batch_row.common_fields_schema().fields(index).field_name();
//         colname_to_index_map_common_[fieldName] = index;
//         oss1 << fieldName << " : " << std::to_string(index) << " / ";
//         common_attr_set_.insert(fieldName);
//       }
//     }
//     if (batch_row.has_item_fields_schema()) {
//       for (int index = 0; index < batch_row.item_fields_schema().fields_size(); ++index) {
//         const std::string& fieldName = batch_row.item_fields_schema().fields(index).field_name();
//         colname_to_index_map_item_[fieldName] = index;
//         oss2 << fieldName << " : " << std::to_string(index) << " / ";
//       }
//     }
//     init_lookup_index_ = true;
//     LOG_EVERY_N(INFO, 1) << " Processor 2 DiffLog InitLookUpIndexs common map "
//     << oss1.str() << " item map " << oss2.str();
//   }


typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, BatchRowFeatureGenrationRetriever, BatchRowFeatureGenrationRetriever)

}  // namespace platform
}  // namespace ks
