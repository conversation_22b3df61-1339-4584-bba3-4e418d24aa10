#pragma once

#include <string>
#include <unordered_set>
#include <vector>
#include <set>
#include <memory>
#include <map>

#include "dragon/src/processor/base/common_reco_base_retriever.h"
#include "dragon/src/processor/ext/arrow/utils/array_handler.h"

#include "teams/ad/ad_algorithm/dragon/training/adlog_kafka_feature/adlog_kafka_feature.h"
#include "algo-engine/kaiworks/kaiworks-stream-proto/src/main/proto/kuaishou/kaiworks/stream/enums/enums.pb.h"
#include "algo-engine/kaiworks/kaiworks-stream-proto/src/main/proto/kuaishou/kaiworks/stream/proto/field.pb.h"
#include "algo-engine/kaiworks/kaiworks-stream-proto/src/main/proto/kuaishou/kaiworks/stream/proto/row.pb.h"

#include "dragon/src/processor/ext/arrow/utils/array_util.h"
#include "arrow/record_batch.h"
#include "arrow/type.h"
#include "arrow/array.h"
#include "arrow/array/builder_base.h"
#include "arrow/array/builder_binary.h"
#include "arrow/array/builder_nested.h"
#include "arrow/array/builder_primitive.h"
#include "arrow/memory_pool.h"

// 流模式
#include "teams/ad/ad_nn/feature/preprocess/async_pdn_processor.h"
#include "teams/ad/ad_algorithm/dragon/training/kafka_feature_wrapper/data_processor.h"
#include "teams/ad/ad_algorithm/dragon/training/kafka_feature_wrapper/tool.h"
#ifdef ENABLE_KUIBA
#include "ks/krp/c++/kuiba/reco_ad_model_server/reco_feature/enricher/live_author_cluster_enricher.h"
#endif
#include "ks/krp/c++/kuiba/reco_ad_model_server/reco_feature/enricher/live_reco_cluster_enricher.h"

namespace ks {
namespace platform {

using kuaishou::kaiworks::stream::BatchRow;
using kuaishou::kaiworks::stream::FieldValue;
using kuaishou::kaiworks::stream::FieldType;

class BatchRowFeatureGenrationRetriever : public CommonRecoBaseRetriever {
 public:
  BatchRowFeatureGenrationRetriever() = default;

  void Retrieve(AddibleRecoContextInterface *context) override;

  void FillCommonAttrs(AddibleRecoContextInterface *context,
      const FeatureResult *feature_result,
      const ks::ad_algorithm::AdLogWrapper &ad_log_wrapper);

  void FillItemAttrs(AddibleRecoContextInterface *context,
      const FeatureResult *feature_result,
      const ks::ad_algorithm::AdLogWrapper &ad_log_wrapper,
      const BatchRow& batch_row);

  void HandlePrefixFeature(AddibleRecoContextInterface *context,
      const std::string &key,
      const std::vector<int64> &values,
      bool is_common,
      uint64_t pos);

  void SetAttr(
    AddibleRecoContextInterface *context,
    const arrow::RecordBatch *record_batch,
    const std::string &attr_name,
    ArrowArrayHandler *handler);

  void SetListAttr(
    AddibleRecoContextInterface *context,
    const arrow::ListArray *list_array,
    const std::string &attr_name,
    ArrowArrayHandler *handler);

  void SetAttrFromBatchRowIntoDragonContext(
    std::map<std::string, int64_t> common_attr2index,
    std::map<std::string, int64_t> item_attr2index,
    AddibleRecoContextInterface *context,
    const BatchRow& batch_row,
    const std::vector<CommonRecoResult> &item_results,
    const std::string &attr_name);

  bool initSampleSkeleton();

  void initExtractConfig();
  void initExtractConfigFromKconf();
  void InitConfigFromSchemaOutLine(const std::string &schema_out_line);

  // Utils Functions For batchrow ***************
  BatchRow getBatchRowFromArrow(const arrow::RecordBatch* record_batch, const std::string &col);
  // void initLookUpIndexs(BatchRow batch_row);

  FieldType getFieldTypeFromBatchRow(
    std::map<std::string, int64_t> common_attr2index,
    std::map<std::string, int64_t> item_attr2index,
    const BatchRow& batch_row, const std::string &col);

  FieldValue getFieldValueFromBatchRow(
    std::map<std::string, int64_t> common_attr2index,
    std::map<std::string, int64_t> item_attr2index,
    const BatchRow& batch_row, const std::string &colname,
    int pos, bool *has_value);

  // *** 以下方法仅在流模式下使用 ***
  bool InitForStream();
  void PreProcessForStream(ks::ad_algorithm::AdLogWrapper *ad_log_wrapper);  // 流模式下的前置处理逻辑
  template<typename T> void QueryFromColossus(
      const std::unique_ptr<colossus::CommonItemClient> &client,
      std::shared_ptr<std::vector<T>> data_in_ad_log_wrapper,
      const std::string &type_desc,
      int64_t user_id);
  void QueryFromAuthorCluster(ks::ad_algorithm::AdLogWrapper *ad_log_wrapper);
  void QueryFromLiveCluster(ks::ad_algorithm::AdLogWrapper *ad_log_wrapper);
  void ParseMerchantUserAttrs(ks::ad_algorithm::AdLogWrapper *ad_log_wrapper);
  void ParseMerchantItemAttrs(ks::ad_algorithm::AdLogWrapper *ad_log_wrapper);
  void ParseRecoUserInfo(ks::ad_algorithm::AdLogWrapper *ad_log_wrapper);
  void GenerateKafkaTags(const FeatureResult *feature_result, std::vector<std::string> *tags);

 private:
  bool InitProcessor() override {
    input_attr_ = config()->GetString("input_attr");
    if (input_attr_.empty()) {
      LOG(ERROR) << "BatchRowFeatureGenrationRetriever init failed! 'input_attr_' cannot be empty!";
      return false;
    }
    LOG(INFO) << "input_attr_:" << input_attr_;

    input_arrow_attr_ = config()->GetString("input_arrow_attr");
    if (input_arrow_attr_.empty()) {
      LOG(ERROR) << "BatchRowFeatureGenrationRetriever init failed! 'input_arrow_attr_' cannot be empty!";
      return false;
    }
    LOG(INFO) << "input_arrow_attr_:" << input_arrow_attr_;

    if (!feature_handler_.Init(config())) {
      LOG(INFO) << "feature_handler_ init failed!";
      return false;
    }

    retriev_skeleton_from_batchrow_ = config()->GetBoolean("use_batchrow", true);
    // init_lookup_index_ = false;
    log_ = config()->GetBoolean("log", false);
    LOG(INFO) << "log_:" << log_;

    union_training_flag_ = config()->GetBoolean("union_training_flag", false);
    LOG(INFO) << "union_training_flag_:" << union_training_flag_;

    reason_ = config()->GetInt("reason", 0);

    RecoUtil::ExtractStringSetFromJsonConfig(config()->Get("pass_through_columns"), &pass_through_columns_);
    pass_through_columns_.insert("sample_id");
    pass_through_columns_.insert("sort_key");
    pass_through_columns_.insert("level1_compression");
    pass_through_columns_.insert("level2_compression");
    pass_through_columns_.insert("user_hash");

    initSampleSkeleton();
    stream_flag_ = config()->GetBoolean("stream_flag", false);
    LOG(INFO) << "stream_flag_:" << stream_flag_;
    if (stream_flag_) {
      InitForStream();
      initExtractConfig();
      initExtractConfigFromKconf();
    } else {
      initExtractConfig();
    }

    ad_conversion_stream_ = config()->GetBoolean("ad_conversion_stream", false);

    return true;
  }


 private:
  std::string input_attr_;
  std::string input_arrow_attr_;
  std::string output_attr_;
  AdlogKafkaFeature feature_handler_;
  bool log_;

  int reason_ = 0;
  std::vector<CommonRecoResult> item_results_;

  std::set<std::string> sample_skeleton_;
  std::map<std::string, std::string> label_extractors_;
  std::map<std::string, std::string> item_filters_;
  std::map<std::string, std::set<uint64_t>> features_prefix_;
  std::map<std::string, std::string> features_alias_;  // 给 FG 输出的 Extractor 起别名

  std::unordered_set<std::string> pass_through_columns_;

  // bool init_lookup_index_;
  // std::map<std::string, size_t> colname_to_index_map_common_;
  // std::map<std::string, size_t> colname_to_index_map_item_;
  // std::set<std::string> common_attr_set_;

  // *** 以下成员仅在流模式下使用 ***
  bool stream_flag_;
  base::Json* stream_config_;
  bool retriev_skeleton_from_batchrow_;  // defalut from arrow record_batch
  std::string serialized_brp_attr_in_arrow_;
  std::unique_ptr<colossus::CommonItemClient> ad_live_colossus_client_;
  std::unique_ptr<colossus::CommonItemClient> reco_live_colossus_client_;
  std::unique_ptr<colossus::CommonItemClient> reco_live_v1_colossus_client_;
  std::unique_ptr<colossus::CommonItemClient> ad_photo_colossus_client_;
  std::unique_ptr<colossus::CommonItemClient> ad_photo_v3_colossus_client_;
  std::unique_ptr<colossus::CommonItemClient> ad_goods_colossus_client_;
  std::unique_ptr<colossus::CommonItemClient> ad_goods_colossus_new_client_;
#ifdef ENABLE_KUIBA
  std::unique_ptr<ks::ad_algorithm::LiveAuthorClusterEnricher> author_enricher_;
#endif
  std::unique_ptr<ad_algorithm::LiveRecoClusterEnricher> live_cluster_enricher_;
  std::unique_ptr<FeatureProcessor> feature_processor_;
  std::string kafka_tag_column_name_;
  //以下配置用于为离线调研场景导出数据
  std::string item_column_for_offline_;
  std::string common_column_for_offline_;

  bool union_training_flag_ = false;

  uint64_t EVENT_CONVERSION_ID = 10000001;
  uint64_t EVENT_APPOINT_FORM_ID = 10000078;
  uint64_t EVENT_APPOINT_JUMP_CLICK_ID = 10000079;
  uint64_t ITEM_CLICK = 20000005;

  bool ad_conversion_stream_ = false;

  DISALLOW_COPY_AND_ASSIGN(BatchRowFeatureGenrationRetriever);
};

}  // namespace platform
}  // namespace ks
