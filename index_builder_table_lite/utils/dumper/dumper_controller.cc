#include "teams/ad/index_builder_table_lite/utils/dumper/dumper_controller.h"

#include <algorithm>
#include <set>
#include "absl/strings/substitute.h"
#include "absl/time/clock.h"
#include "base/common/logging.h"
#include "base/common/stl_logging.h"
#include "base/encoding/base64.h"
#include "base/file/file_util.h"
#include "base/thread/thread_pool.h"
#include "ks/serving_util/dynamic_config.h"
#include "perfutil/perfutil.h"
#include "pub/src/base/time/timestamp.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/index_adapter/utils/consumer/message_consumer.h"
#include "teams/ad/index_adapter/utils/kconf/kconf.h"
#include "teams/ad/index_adapter/utils/stream_map/stream_map_maker.h"
#include "teams/ad/index_builder/utils/table_config.h"
#include "teams/ad/index_adapter/utils/perf_manager/perf_manager.h"
#include "teams/ad/index_builder_table_lite/utils/kconf/kconf.h"

using ks::index_adapter::PerfManager;
using ks::infra::PerfUtil;
using ks::index_builder::TableConfigManager;

namespace ks {
namespace index_builder_table_lite {

DataDumper::DataDumper() {}

bool DataDumper::Start() {
  int64_t start_ts = base::GetTimestamp();
  LOG(INFO) << "DataDumper start...";
  ks::index_builder::DumpConfig config;
  ks::index_builder::TableConfigManager::GetInstance()->Init(config);
  bool once_start = running_.exchange(true);
  if (once_start) {
    LOG_ASSERT(false) << "DataDumper start twice please check!!!";
  }
  PerfManager::GetInstance()->Start();
  dump_thread_ = std::thread([this]() { DumpThread(); });
  if (!Kconf::enableDumpOnKfs()) {
    mkdir("../ad_index", 0777);
  }
  return true;
}

void DataDumper::DumpThread() {
  LOG(INFO) << "DumpThread start...";
  while (running_) {
    std::this_thread::sleep_for(std::chrono::seconds{60});
    LOG(INFO) << "Try to dump data!";
    auto configs = Kconf::builderConfig()->data();
    int shard_no = 0;
    thread::ThreadPool dump_pool{10};
    for (auto config : configs.configs()) {
      LOG(INFO) << config.ShortDebugString() << " begin to build";
      std::shared_ptr<BuilderTableLite> ptr;
      ptr.reset(new BuilderTableLite(config, shard_no));
      dump_pool.AddTask(::NewCallback(this, &DataDumper::BuilderRun, ptr));
      shard_no++;
    }
    dump_pool.JoinAll();
  }
  LOG(INFO) << "DumpThread end...";
}

void DataDumper::BuilderRun(std::shared_ptr<BuilderTableLite> builder_ptr) {
  builder_ptr->Run();
}

void DataDumper::Stop() {
  running_.store(false);
  dump_thread_.join();
  PerfManager::GetInstance()->Stop();
}

}  // namespace index_builder_table_lite
}  // namespace ks
