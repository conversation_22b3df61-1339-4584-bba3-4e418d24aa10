#pragma once

#include <third_party/abseil/absl/time/time.h>
#include <atomic>
#include <memory>
#include <string>
#include <thread>
#include <utility>
#include <vector>
#include "absl/container/flat_hash_map.h"
#include "absl/container/flat_hash_set.h"
#include "absl/strings/str_cat.h"
#include "falcon/counter.h"
#include "perfutil/perfutil.h"
#include "teams/ad/index_builder_table_lite/utils/builder/builder.h"

namespace ks {
namespace index_builder_table_lite {

class DataDumper {
 public:
  DataDumper();
  bool Start();
  void Stop();

 private:
  void DumpThread();
  void BuilderRun(std::shared_ptr<BuilderTableLite> builder_ptr);
  std::atomic<bool> running_{false};
  std::thread dump_thread_;
  absl::Time t{absl::FromTimeT(0)};
};
}  // namespace index_builder_table_lite
}  // namespace ks
