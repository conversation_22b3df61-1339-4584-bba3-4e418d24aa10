#include <memory>
#include <string>
#include "teams/ad/index_builder_table_lite/utils/builder/builder.h"
#include "base/file/file_path.h"
#include "base/file/file_util.h"
#include "teams/ad/index_adapter/utils/hdfs_uploader.h"
#include "teams/ad/index_adapter/utils/perf_manager/perf_manager.h"
#include "teams/ad/engine_base/utils/deploy_variable.h"
#include "teams/ad/index_builder_table_lite/utils/kconf/kconf.h"
namespace ks {
namespace index_builder_table_lite {

using ks::index_adapter::StreamMapBase;
using ks::index_adapter::StreamMapMakerFactory;
using base::file_util::FileEnumerator;
using ks::index_adapter::HdfsUploader;
using ks::index_adapter::PerfManager;
using ks::engine_base::DeployVariable;

BuilderTableLite::~BuilderTableLite() {
  PerfManager::GetInstance()->UpdateBuildEndTime(GetPrefix());
}

bool BuilderTableLite::Run() {
  PerfManager::GetInstance()->RegisterIndexMonitorTask(GetPrefix(), hdfs_path_);
  PerfManager::GetInstance()->UpdateBuildBeginTime(GetPrefix());

  std::unique_ptr<StreamMapBase> stream_map_maker_ptr;
  std::string source_version, dest_version;
  if (source_path_.find("kfs-ad-index") != std::string::npos) {
    stream_map_maker_ptr.reset(
        StreamMapMakerFactory::GetInstance()->GetStreamMapTableLiteOnKfs());
    stream_map_maker_ptr->CheckLastetSuccessVersionOfAdInstanceOnKfs(
        source_path_, &source_version);
    stream_map_maker_ptr->CheckLastetSuccessVersionOfAdInstance(hdfs_path_,
                                                                &dest_version);

  } else {
    stream_map_maker_ptr.reset(
        StreamMapMakerFactory::GetInstance()->GetStreamMapTableLite());
    stream_map_maker_ptr->CheckLastetSuccessVersionOfAdInstance(
        source_path_, &source_version);
    stream_map_maker_ptr->CheckLastetSuccessVersionOfAdInstance(hdfs_path_,
                                                                &dest_version);
  }
  if (source_version.empty() || source_version == dest_version) {
    LOG(INFO) << "source_version: " << source_version
              << " dest_version: " << dest_version << " not match! Skip";
    return false;
  }

  std::string hdfs_src =
      base::FilePath(source_path_).Append(source_version).ToString();
  std::string hdfs_dst =
      base::FilePath(hdfs_path_).Append(source_version).ToString();
  local_path_ = base::FilePath(local_path_).Append(source_version).ToString();
    // prepare local dir
  if (base::file_util::PathExists(base::FilePath(local_path_))) {
    if (!base::file_util::Delete(base::FilePath(local_path_), true)) {
      LOG(ERROR) << "Delete(" << local_path_ << ") failed";
    }
  }
  mkdir(local_path_.c_str(), 0777);
  stream_map_maker_ptr->InitAdInstanceMessageStreamMap(hdfs_src, &stream_map_);

  ks::index_adapter::TableLiteConsumerMgr lite_mgr(seq_);
  std::vector<std::string> tables;
  thread::ThreadPool dump_pool{40};
  for (auto item : stream_map_) {
    LOG(INFO) << "table_name: " << item.first;
    auto last_stream_ptr = item.second.back();
    if (IsTableMatch(item.first)) {
      tables.push_back(item.first);
    }
  }
  // ks::index_adapter::AdapterConfig config;
  index_adapter::ExtraConsumerDataInfo extra_info;
  lite_mgr.Init(tables, local_path_, hdfs_dst, extra_info);
  for (auto item : stream_map_) {
    if (!IsTableMatch(item.first)) {
      continue;
    }
    for (auto& shard : item.second) {
      lite_mgr.RegisterTable(item.first);
      LOG(INFO) << "RegisterTable table_name: " << item.first;
    }
  }
  for (auto item : stream_map_) {
    for (auto stream_ptr : item.second) {
      LOG(INFO) << stream_ptr->DebugInfo();
      if (IsTableMatch(item.first)) {
        dump_pool.AddTask(::NewCallback(this, &BuilderTableLite::StreamDumper,
                                        stream_ptr, &lite_mgr));
      }
    }
  }
  dump_pool.JoinAll();
  lite_mgr.Finish();
  return true;
}

void BuilderTableLite::StreamDumper(
    InstanceStreamPtr stream_ptr,
    ks::index_adapter::TableLiteConsumerMgr* lite_mgr) {
  while (stream_ptr->Valid()) {
    const auto* ad_inst = stream_ptr->NextAdInstance();
    auto ad_inst_tmp = *ad_inst;
    if (admit_strategy_ && !admit_strategy_->Process(&ad_inst_tmp)) {
      LOG_EVERY_N(INFO, 10000) << ad_inst_tmp.ShortDebugString();
      continue;
    }
    field_strategy_->Process(&ad_inst_tmp);
    LOG_EVERY_N(INFO, 10000) << ad_inst_tmp.ShortDebugString();
    lite_mgr->Consume(ad_inst_tmp);
  }
  lite_mgr->UnRegisterTable(stream_ptr->GetTableName());
  LOG(INFO) << "UnRegisterTable table_name: " << stream_ptr->GetTableName();
  LOG(INFO) << "stream: " << stream_ptr->DebugInfo() << " finish dump";
}

}  // namespace index_builder_table_lite
}  // namespace ks
