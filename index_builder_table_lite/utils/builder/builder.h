#pragma once

#include <algorithm>
#include <map>
#include <mutex>
#include <string>
#include <vector>
#include <memory>
#include "base/file/file_path.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/index_manager.h"
#include "teams/ad/engine_base/utils/deploy_variable.h"
#include "teams/ad/index_adapter/strategy/strategy_common.h"
#include "teams/ad/index_adapter/utils/stream_map/stream_map_maker.h"
#include "teams/ad/index_builder_table_lite/utils/kconf/kconf.h"
#include "teams/ad/index_builder_table_lite/utils/kconf/kconf.pb.h"
#include "teams/ad/index_adapter/utils/consumer/table_lite_consumer_mgr.h"
namespace ks {
namespace index_builder_table_lite {

/*
  每个 builder 负责一个分片的制作
*/

using ks::index_builder::InstanceStreamMap;
using ks::index_builder::InstanceStreamPtr;
using ks::engine_base::DeployVariable;

class BuilderTableLite {
 public:
  BuilderTableLite(const ShardConfig& config, int64_t seq)
      : config_(config), seq_(seq) {
    source_path_ = config_.src();
    hdfs_path_ = config.dst();
    if (Kconf::enableDumpOnKfs()) {
      mkdir(
          "/home/<USER>/kuaishou-worker/project/kfs-ad-index/"
          "ad-index-builder-table-lite-kfs",
          0777);
      local_path_ = base::FilePath(hdfs_path_).BaseName().value();
      local_path_ = base::FilePath(
                        "/home/<USER>/kuaishou-worker/project/kfs-ad-index/"
                        "ad-index-builder-table-lite-kfs")
                        .Append(local_path_)
                        .value();
      mkdir(local_path_.c_str(), 0777);
      local_path_ =
          base::FilePath(local_path_).Append(std::to_string(seq)).value();
      mkdir(local_path_.c_str(), 0777);
    } else {
      local_path_ =
          base::FilePath("../ad_index/").Append(std::to_string(seq)).ToString();
      mkdir(local_path_.c_str(), 0777);
    }
    ks::index_adapter::CleanLocalIndex(local_path_, 4);
    ks::index_adapter::ClearHistoryFile(hdfs_path_);
    if (config_.pb_wb_list_key().empty()) {
      field_strategy_ =
          std::make_unique<ks::index_adapter::FieldFilterStrategy>(
              "field_strategy");
    } else {
      field_strategy_ =
          std::make_unique<ks::index_adapter::FieldFilterStrategy>(
              "field_strategy", config_.pb_wb_list_key());
    }
    admit_strategy_.reset(ks::index_adapter::StrategyFactory::GetInstance()->GetStrategy(
      config.filter_param()));
  }

  ~BuilderTableLite();

  bool Run();

  std::string GetPrefix() {
    if (seq_ == -1) {
      return absl::Substitute("$0_$1", DeployVariable::GetKwsName(),
                              DeployVariable::GetStage());
    } else {
      return absl::Substitute("$0_$1_$2", DeployVariable::GetKwsName(),
                              DeployVariable::GetStage(), seq_);
    }
  }

  bool IsTableMatch(const std::string& table_name) {
    const auto& table_names = config_.table_names();
    if (table_names.size() != 0) {
      return std::find(table_names.cbegin(), table_names.cend(), table_name) !=
             table_names.cend();
    }
    return true;
  }

 private:
  void StreamDumper(InstanceStreamPtr stream_ptr,
                    ks::index_adapter::TableLiteConsumerMgr* lite_mgr);
  std::string source_path_;
  std::string local_path_;
  std::string hdfs_path_;
  InstanceStreamMap stream_map_;
  ShardConfig config_;
  int64_t seq_{-1};
  kuaishou::ad::tables::DumpInfo dump_info_;
  std::shared_ptr<ks::index_adapter::FieldFilterStrategy> field_strategy_;
  std::shared_ptr<ks::index_adapter::StrategyBase> admit_strategy_;
};

}  // namespace index_builder_table_lite
}  // namespace ks
