syntax = "proto3";

package ks.index_builder_table_lite;
import "teams/ad/ad_proto/kuaishou/ad/ad_inc.proto";
import "teams/ad/index_builder/multiform_filter/multiform_kconf.proto";
import "teams/ad/index_builder/admit_filters/admit_filter.proto";

message ShardConfig {
  string src = 1;
  string dst = 2;
  repeated string table_names = 3;  // 支持导出的表
  ks.index_builder.AdmitFilterEnum.Type filter_param = 4;  // 业务过滤规则
  string pb_wb_list_key = 5;
}

message BuilderConfig {
  repeated ShardConfig configs = 1;
}
