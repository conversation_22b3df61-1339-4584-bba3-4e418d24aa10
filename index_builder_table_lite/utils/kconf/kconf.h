#pragma once

#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/index_builder_table_lite/utils/kconf/kconf.pb.h"

namespace ks {
namespace index_builder_table_lite {
using namespace ks::ad_base::kconf; //NOLINT
class Kconf {
 public:
  DEFINE_PROTOBUF_NODE_KCONF(BuilderConfig, ad.index_builder, builderConfig);
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableLoadBaseFromKfs,
                         false);  //  将 base 输入改为 Kfs 默认为 HDFS
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableDumpOnKfs,
                         false);  //  将选择 Kfs 为导出位置
};
}  // namespace index_builder_table_lite
}  // namespace ks
