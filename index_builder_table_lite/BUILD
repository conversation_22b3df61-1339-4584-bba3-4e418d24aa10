

proto_library(
  name = 'kconf_proto',
  srcs = [
    'utils/kconf/kconf.proto',
  ],
  deps = [
    "//teams/ad/ad_proto/kuaishou/BUILD:all_table_proto"
  ],
)

cc_library(
  name = 'builder',
  srcs= [
    'utils/builder/builder.cc'
  ],
  deps = [
    ':kconf_proto',
    '//teams/ad/ad_table_lite/BUILD:ad_table_lite',
    '//teams/ad/index_adapter/BUILD:stream_map',
    "//teams/ad/index_builder/BUILD:message_stream",
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_forward_index_proto',
    '//infra/kconf/BUILD:kconf',
    '//teams/ad/ad_base/src/pb_helper/BUILD:pb_helper',
    '//teams/ad/engine_base/BUILD:kconf',
    "//teams/ad/index_adapter/BUILD:perf_manager",
    "//teams/ad/index_adapter/BUILD:strategy",
    "//teams/ad/index_adapter/BUILD:index_adapter_dumper",
  ],
)



cc_library(
  name = 'dumper',
  srcs = [
    'utils/dumper/dumper_controller.cc',
  ],
  deps = [
    ':builder',
    '//third_party/abseil/BUILD:abseil',
    '//third_party/gflags/BUILD:gflags',
    '//third_party/glog/BUILD:glog',
  ],
)

cc_binary(name = 'index_builder_table_lite',
  srcs = [
    'main.cc'
  ],
  deps = [
    ':dumper',
    "//teams/ad/index_builder/BUILD:kafka2hive",
    '//third_party/abseil/BUILD:abseil',
    '//third_party/gflags/BUILD:gflags',
    '//third_party/glog/BUILD:glog',
    "//teams/ad/index_builder/BUILD:multiform_filter",
    "//teams/ad/index_message_proxy/BUILD:proxy_proto",
    "//teams/ad/ad_base/src/jemalloc_hook/BUILD:link_jemalloc",
  ],
  cppflags = [
    '-Ithird_party/boost',
    '-Ithird_party/double-conversion',
    "-std=gnu++17",
  ]
)

