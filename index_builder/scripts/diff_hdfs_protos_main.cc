// A script that diffs parsed HDFS proto files and writes out the diff report.
//
// It does the following steps:
// 1. Find the common latest version between test and prod HDFS directories.
// 2. Compare the parsed test ad instance ToString() against the prod server's
// ad instance. If --compare_raw_protos is enabled, compare the raw protos
// instead using MessageDifferencer.
// 3. Write sampled diffs (by default 20) of each table to HDFS file
// "proto_diff.txt" to latest test HDFS directory.
//
// Note that this script uses CHECK() extensively. Do not copy code into prod
// servers without careful inspection!
//
// Example run (only on cloud machines with hadoop, not on dev machines):
// ./diff_hdfs_protos_main --hadoop_namenode_ip=default --hadoop_namenode_port=0 \
//   --hdfs_test_directory=/home/<USER>/ad_mysql_dumper/hdfs_base_index_cascade_preonline/hot_index/ \
//   --hdfs_prod_directory=/home/<USER>/ad_mysql_dumper/hdfs_base_index_tianchi/hot_index/
//
// After it completes, you can find the diff report in
// `/tmp/local_proto_diff.txt` locally.

#include <gflags/gflags.h>
#include <fstream>
#include <iostream>
#include <memory>

#include "absl/container/flat_hash_map.h"
#include "absl/container/flat_hash_set.h"
#include "base/file/file_path.h"
#include "base/file/file_util.h"
#include "serving_base/hdfs_read/hdfs_file_util.h"
#include "ks/serving_util/server_base.h"
#include "serving_base/util/hdfs.h"
#include "teams/ad/ad_index/framework/pb_reader/pb_file_loader.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/tables/data_gate/all_tables.h"
#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"

DEFINE_string(hdfs_test_directory,
              "/home/<USER>/ad_mysql_dumper/hdfs_base_index_test/ad_server/",
              "hdfs test url, dumped by the test server");
DEFINE_string(
    hdfs_prod_directory,
    "/home/<USER>/ad_mysql_dumper/hdfs_base_index_adserver_prod/ad_server/",
    "hdfs prod url, dumped by the prod server");
DEFINE_string(tmp_local_directory, "/tmp",
              "directory storing tmp HDFS files and report");
DEFINE_int64(sample_count, 1000,
             "Only load at most this number of records from the HDFS test "
             "table to diff, in order to save memory.");
DEFINE_bool(
    compare_raw_protos, false,
    "Whether to diff raw AdInstance protos rather than parsed structs.");
DEFINE_string(
    diff_table_names,
    "ad_dsp_account,ad_dsp_campaign,ad_dsp_unit,ad_dsp_creative,ad_dsp_target",
    "table names to run the diff against, separated by comma");
DEFINE_string(fields_to_clear,
              "range_budget,seq_no,range_begin_time,range_end_time,"
              "resource_ids,city_app_package_name_id,unit_support_info",
              "Do not diff against fields specified here, separated by comma");

constexpr char kLocalDiffFileName[] = "local_proto_diff.txt";

using kuaishou::ad::AdInstance;

// Loads at most `item_limit` number of protos from HDFS file, if id appears in
// reference_map, and inserts into ad_map.
bool LoadHDFSFileToMap(
    const std::string &table_name, const std::string &hdfs_file,
    const std::string &tmp_local_directory,
    int64 item_limit,
    const std::vector<std::string>& fields_to_clear,
    absl::flat_hash_map<int64_t, std::string> *reference_map,
    absl::flat_hash_map<int64_t, std::string> *ad_map) {
  absl::optional<std::string> pb_type = ks::index_builder::TableToProtoType(table_name);
  if (!pb_type.has_value()) return false;
  std::string local_path =
      base::FilePath(tmp_local_directory).Append(table_name).ToString();

  // Download the HDFS to local_path first.
  if (hadoop::HDFSForceGet(hdfs_file.c_str(), local_path.c_str()) != 0) {
    LOG(ERROR) << "HDFS get failed. HDFS file: " << hdfs_file
               << " tmp_local_directory: " << local_path;
    return false;
  }
  auto pb_reader =
      std::make_unique<ks::ad_base::PbReader<kuaishou::ad::AdInstance>>();
  int ret = pb_reader->Open(local_path.c_str());
  if (ret != 0) {
    LOG(ERROR) << "pb reader Open() failed. tmp_local_directory: "
               << local_path;
    return false;
  }

  VLOG(2) << "Starting to read local file " << local_path;
  const kuaishou::ad::AdInstance *p_instance = nullptr;
  while ((p_instance = pb_reader->Read()) != nullptr) {
    VLOG(8) << "Reading instance from HDFS " << p_instance->ShortDebugString();
    AdInstance tmp_ad_instance;
    tmp_ad_instance.CopyFrom(*p_instance);
    int64_t key = ks::index_builder::GetPrimaryKey(&tmp_ad_instance);
    if (reference_map != nullptr && !reference_map->contains(key)) {
      LOG_EVERY_N(INFO, 10000) << "HDFS file: " << hdfs_file
                              << " test key not found in prod: " << key;
      continue;
    }
    if (--item_limit < 0) {
      LOG(INFO) << "Reaching item_limit reading table  " << table_name;
      break;
    }
    if (key == 0) {
      LOG_EVERY_N(INFO, 10000) << "primary key is 0 for table " << table_name;
      continue;
    }
    CHECK(ad_map != nullptr) << "ad_map shouldn't be null";
    if (ad_map->find(key) != ad_map->end()) {
      const std::string &found_ad_instance_bytes = ad_map->at(key);
      AdInstance found_ad_instance;
      CHECK(found_ad_instance.ParseFromString(found_ad_instance_bytes))
          << "key " << key << " already exists, but cannot be parsed "
          << found_ad_instance_bytes;
      LOG_EVERY_N(ERROR, 10000)
          << "key " << key << " already exists in ad_map. Instance: "
          << found_ad_instance.ShortDebugString();
    } else {
      auto *extension_msg =
          ks::index_builder::GetExtensionField(&tmp_ad_instance);
      ks::index_builder::ClearFields(extension_msg, fields_to_clear);
      VLOG(8) << "Ready to insert sanitized msg to ad_map "
              << tmp_ad_instance.ShortDebugString();
      std::string& serialized_msg = (*ad_map)[key];
      CHECK(tmp_ad_instance.SerializeToString(&serialized_msg))
          << "tmp_ad_instance serialization failed "
          << tmp_ad_instance.ShortDebugString();
    }
  }
  LOG(INFO) << "table: " << table_name << ", record num: " << ad_map->size()
            << ", file: " << hdfs_file;
  return true;
}

// Get the file name without the extension from an HDFS url.
bool GetFileNameFromHDFSUrl(const std::string &hdfs_url,
                            std::string *table_name) {
  std::vector<absl::string_view> v = absl::StrSplit(hdfs_url, "/");
  if (v.empty()) return false;
  std::vector<std::string> file_name_and_extension =
      absl::StrSplit(v.back(), ".");
  if (file_name_and_extension.empty()) return false;
  *table_name = file_name_and_extension[0];
  return true;
}

// Get the latest test and prod HDFS directory under hdfs_prod_url, i.e. the one
// with the largest timestamp in folder name.
std::pair<std::string, std::string> LatestVersionPaths(
    const std::string &hdfs_test_url, const std::string &hdfs_prod_url) {
  std::vector<hadoop::HDFSPathInfo> paths;
  CHECK(hadoop::HDFSListDirectory(hdfs_prod_url.c_str(), &paths))
      << " listing prod directories failed " << hdfs_prod_url;
  CHECK(!paths.empty());
  std::sort(paths.begin(), paths.end(),
            [](const hadoop::HDFSPathInfo &a, const hadoop::HDFSPathInfo &b) {
              return a.name > b.name;
            });
  for (int i = 0; i < paths.size(); i++) {
    std::string version_timestamp =
        std::vector<std::string>(absl::StrSplit(paths[i].name, "/")).back();
    std::string candidate_test_url =
        base::FilePath(hdfs_test_url).Append(version_timestamp).ToString();
    if (hadoop::HDFSExists(candidate_test_url.c_str()))
      return {candidate_test_url, paths[i].name};
  }
  LOG(FATAL) << "No version-matching HDFS path exists in test url "
             << hdfs_test_url << " that matches a sub directory under prod url "
             << hdfs_prod_url;
}

// Diff all the files between test and prod directories and write the report to
// local_ofstream.
void DiffAndAppendToLocal(const std::string &hdfs_test_url,
                          const std::string &hdfs_prod_url,
                          std::ofstream &local_ofstream) {
  std::string test_table_name, prod_table_name;
  CHECK(GetFileNameFromHDFSUrl(hdfs_test_url, &test_table_name));
  CHECK(GetFileNameFromHDFSUrl(hdfs_prod_url, &prod_table_name));
  CHECK_EQ(test_table_name, prod_table_name)
      << "test and prod table names are not equal. "
      << " test HDFS url: " << hdfs_test_url
      << " prod HDFS url: " << hdfs_prod_url << std::endl;
  // Skip the file if it's not in the FLAGS_diff_table_names.
  static absl::flat_hash_set<std::string> diff_table_name_set =
      absl::StrSplit(FLAGS_diff_table_names, ",");
  if (!diff_table_name_set.contains(test_table_name)) {
    LOG(INFO) << "Skip table " << test_table_name
              << ". It's not in kDiffTableNameSet.";
    return;
  }

  std::cout << "==============\nstart diffing HDFS test url " << hdfs_test_url
            << " prod url: " << hdfs_prod_url << std::endl;
  // Load all the protos into the prod map (key is primary key, value is
  // serialized pb bytes). Here the limit is set to
  // std::numeric_limits<int>::max().
  absl::flat_hash_map<int64_t, std::string> prod_map, test_map;
  CHECK(LoadHDFSFileToMap(prod_table_name, hdfs_prod_url,
                          FLAGS_tmp_local_directory,
                          std::numeric_limits<int>::max(),
                          absl::StrSplit(FLAGS_fields_to_clear, ","),
                          /*reference_map=*/nullptr, &prod_map));
  // Now load the sampled test HDFS url.
  CHECK(LoadHDFSFileToMap(test_table_name, hdfs_test_url,
                          FLAGS_tmp_local_directory, FLAGS_sample_count,
                          absl::StrSplit(FLAGS_fields_to_clear, ","), &prod_map,
                          &test_map));

  // Run the diff reports for each sampled instance and write report to HDFS.
  absl::flat_hash_map<int64_t, std::string> id_to_diff_report;
  for (const auto &item : test_map) {
    int64_t key = item.first;
    const std::string& test_instance_bytes = item.second;
    AdInstance test_instance;
    CHECK(test_instance.ParseFromString(test_instance_bytes));

    AdInstance prod_instance;
    CHECK(prod_instance.ParseFromString(prod_map[key]));
    VLOG(8) << "Start diffing test " << test_instance.ShortDebugString()
            << "\n prod " << prod_instance.ShortDebugString();
    if (FLAGS_compare_raw_protos) {
      ks::index_builder::DiffRawAdInstances(&test_instance, &prod_instance,
                                            &id_to_diff_report[key]);
    }
  }
  local_ofstream << "\n ======== Start diffing table " << test_table_name
                 << " ("
                 << *ks::index_builder::TableToProtoType(test_table_name)
                 << ") ========\n";
  int report_cnt = 0;
  for (const auto &diff_t : id_to_diff_report) {
    int64_t id = diff_t.first;
    const auto& diff_report = diff_t.second;
    if (!diff_report.empty()) {
      local_ofstream.write(diff_report.data(), diff_report.size());
      local_ofstream << "\n";
      report_cnt++;
    }
  }
  local_ofstream << "\n ======== End diffing table " << test_table_name
                 << " ========\n";
  local_ofstream.flush();
  std::cout << "Finish writing " << report_cnt << " lines of report for table "
            << test_table_name << std::endl;
}

int main(int argc, char *argv[]) {
  base::InitApp(&argc, &argv, "diff_two_hdfs_protos");
  // Init HDFS.
  base::HDFSWrapper hdfs(hadoop::FLAGS_hadoop_namenode_ip,
                         hadoop::FLAGS_hadoop_namenode_port, "ad");

  // Check all .base files in the test and prod directories.
  const auto path_pairs =
      LatestVersionPaths(FLAGS_hdfs_test_directory, FLAGS_hdfs_prod_directory);
  const std::string& base_hdfs_test_path = path_pairs.first;
  const std::string& base_hdfs_prod_path = path_pairs.second;
  std::vector<hadoop::HDFSPathInfo> test_files, prod_files;
  CHECK(hadoop::HDFSListDirectory(base_hdfs_test_path.c_str(), &test_files))
      << " listing test files failed " << FLAGS_hdfs_test_directory;
  CHECK(hadoop::HDFSListDirectory(base_hdfs_prod_path.c_str(), &prod_files))
      << " listing prod files failed " << FLAGS_hdfs_prod_directory;

  // Store all .base prod file names in a set so that we can find matching test
  // files against it.
  absl::flat_hash_set<std::string> prod_file_set;
  for (const auto &path_info : prod_files) {
    std::string file_name;
    CHECK(GetFileNameFromHDFSUrl(path_info.name, &file_name));
    prod_file_set.insert(file_name);
  }
  LOG(INFO) << "prod file set: " << absl::StrJoin(prod_file_set, ",");

  // Loop through each eligible file in test_files and append diff to a local
  // file.
  const std::string local_report_path =
      base::FilePath(FLAGS_tmp_local_directory)
          .Append(kLocalDiffFileName)
          .ToString();
  std::vector<std::thread> diff_threads;
  std::ofstream local_ofstream(local_report_path.c_str(), std::ofstream::out);
  for (const hadoop::HDFSPathInfo &test_path_info : test_files) {
    // Filter out test files that don't end with .base and those not in prod.
    const std::string &test_path_str = test_path_info.name;
    if (!absl::EndsWith(test_path_str, ".base")) {
      LOG(INFO) << "test_path " << test_path_str
                << " does not end in .base. Skip!";
      continue;
    }
    std::string test_file_name;
    CHECK(GetFileNameFromHDFSUrl(test_path_str, &test_file_name));
    if (!prod_file_set.contains(test_file_name)) {
      LOG(INFO) << "prod HDFS directory " << base_hdfs_prod_path
                << " does not have a corresponding test file " << test_file_name
                << ". Skip!" << std::endl;
      continue;
    }

    std::string prod_path_str =
        base::FilePath(base_hdfs_prod_path)
            .Append(absl::StrCat(test_file_name, ".base"))
            .ToString();
    // Not running DiffAndAppendToLocal in parallel since memory won't fit.
    DiffAndAppendToLocal(test_path_str, prod_path_str, local_ofstream);
  }
  local_ofstream.close();
  std::cout << "==============\nAll done! Local report can be found at "
            << local_report_path << std::endl;
  return 0;
}
