## coding:utf-8
#! /usr/bin/python
'''
Diff old and new dumped index and write diff to --ouput.

How to run (python2 only):

$ python index_valid_benmark_test.py --f single_level --new 0310/new/ --old 0310/old/ --output oldandnew --detail unit
'''

import sys
import os
import re
import argparse

table_com = re.compile(
    'table_name:(?P<table_name>[^ ]*), name:(?P<name>[^ ]*), positive:(?P<pos>[^ ]*), key:(?P<key>[^ ]*), cardinality:(?P<car>[^ ]*)')


def GetFileList(path):
    file_list = os.listdir(path)
    return [file_name for file_name in file_list if not file_name.startswith('table') and not file_name.startswith('engine')]


def GetTableEngineList(path):
    file_list = os.listdir(path)
    return [file_name for file_name in file_list if file_name.startswith('table') or file_name.startswith('engine')]


def DiffFile(new_file, old_file):
    set1 = set()
    set2 = set()
    for file_name in new_file:
      set1.add(file_name)
    for file_name in old_file:
      set2.add(file_name)
    print "diff1"
    print set1 - set2

    print "diff2"
    print set2 - set1


def GetKeyValue(line):
    words = line.strip().split(",")
    key_w = words[:-1]
    key = "_".join([w.split(":")[-1] for w in key_w])
    value = int(words[-1].split(":")[-1])
    return key, value


def CompareTableEngine(new_index, old_index, common_file, is_show_diff=True, output_dir="diff"):
    if common_file.find("table") == -1 and common_file.find("engine") == -1:
      return
    print common_file
    report_record = list()
    new_file = "%s/%s" % (new_index, common_file)
    old_file = "%s/%s" % (old_index, common_file)
    print new_file, old_file
    new_line_set = set()
    new_line_dict = dict()
    for line in open(new_file):
      key, value = GetKeyValue(line)
      #print key,value
      if value < 10:
        continue
      new_line_dict[key] = value
    old_line_set = set()
    old_line_dict = dict()
    for line in open(old_file):
      key, value = GetKeyValue(line)
      #print key,value
      if value < 10:
        continue
      old_line_dict[key] = value
    # compare two dict
    new_keys = new_line_dict.keys()
    old_keys = set(old_line_dict.keys())
    print len(new_keys), len(old_keys)
    for key in new_keys:
      if key not in old_keys:
        report_record.append("%s %d not found" % (key, new_line_dict[key]))
      if key in old_keys:
        new_value = new_line_dict[key]
        old_value = old_line_dict[key]
        # too small
        if new_value < 10:
          continue
        if new_value > (old_value * 1.1) or new_value < (old_value * 0.9):
          report_record.append(
              "key[%s] new: %d old: %d not match" % (key, new_value, old_value))
    report_record.insert(0, "total %d diff %d" %
                         (len(new_keys), len(report_record)))
    WriteToFile("\n".join(report_record), output_dir + "/" + common_file)


def GetCommon(new_files, old_file):
    return list(set(new_files) - set(set(new_files) - set(old_files)))


def FilePath(path, file_list):
    file_list = ["%s/%s" % (path, file_name) for file_name in file_list]


"""
  seq_no, range_budget 字段屏蔽
"""
def ClearUselessAttr(line):


def ClearUselessAttr(line):
    line = re.sub(
        "[, ]*(range_budget|seq_no|range_begin_time|range_end_time):[ ]*[0-9]+", "", line)
    #line = re.sub("[, ]*(range_budget|seq_no|range_begin_time|range_end_time):[ ]*[0-9]+", "", line)
    #line = re.sub("[, ]*(range_budget|seq_no|range_begin_time|range_end_time):[ ]*[0-9]+", "", line)
    #line = re.sub("(range_begin_time|range_end_time): [0-9]+", "", line)
    #line = re.sub("[,][ ](range_budget|seq_no|range_begin_time|range_end_time): [0-9]+", "", line)
    line = re.sub("[, ]*resource_ids:[ ]*[\[\],0-9 ]+", "", line)

    line = re.sub("[, ]*unit_support_info[\[\],0-9 a-zA-Z_:]+", "", line)
    line = re.sub("[, ]*target_optional\(nullptr\)", "", line)
    line = re.sub("[, ]*city_app_package_name_id:[ ]*[\[\]\-0-9 ]+", "", line)
    #print line
    return line


def WriteToFile(report_record, file_dir):
    writer = open(file_dir, "w")
    writer.write(report_record)
    writer.close()


def CompareFile(new_index, old_index, common_file, is_show_diff=True, output_dir="diff"):
    report_record = list()
    new_file = "%s/%s" % (new_index, common_file)
    old_file = "%s/%s" % (old_index, common_file)
    new_line_set = set()
    for line in open(new_file):
      line = ClearUselessAttr(line)
      #print line
      new_line_set.add(line.strip())
    old_line_set = set()
    for line in open(old_file):
      line = ClearUselessAttr(line)
      old_line_set.add(line.strip())
    tmp_str = "%s - %s diff: %d" % (new_file,
                                    old_file, len(new_line_set - old_line_set))
    print tmp_str
    report_record.append(tmp_str)

    tmp_str = "%s - %s diff: %d" % (old_file,
                                    new_file, len(old_line_set - new_line_set))
    print tmp_str
    report_record.append(tmp_str)
    report_record.append("new - old")
    diff_set = new_line_set - old_line_set
    if is_show_diff:
      for line in (new_line_set - old_line_set):
        report_record.append(line)

    report_record.append("old - new")
    diff_set = old_line_set - new_line_set
    if is_show_diff:
      for line in diff_set:
        report_record.append(line)

    WriteToFile("\n".join(report_record), output_dir + "/" + common_file)


def Prepare(output_dir):
    try:
      os.mkdir(output_dir)
    except:
      print "%s exist" % (output_dir)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--f", help="func choose all/single_level/single_table")
    parser.add_argument("--new", help="new index path")
    parser.add_argument("--old", help="old index path")
    parser.add_argument(
        "--detail", help="in single mode, which file to compare")
    parser.add_argument("--output", help="output dir")

    args = parser.parse_args()

    output_dir = args.output
    Prepare(output_dir)

    if args.f == "all":
      new_index = args.new
      old_index = args.old
      output_dir = args.output
      new_files = GetFileList(new_index)
      old_files = GetFileList(old_index)
      DiffFile(new_files, old_files)
      print new_files
      print old_files
      common_files = GetCommon(new_files, old_files)
      print "common_files"
      print common_files
      for common_file in common_files:
        print common_file
        CompareFile(new_index, old_index, common_file, False, output_dir)
    elif args.f == "single_level":
      new_file = args.new + "/" + args.detail
      old_file = args.old + "/" + args.detail
      output_dir = args.output
      CompareFile(args.new, args.old, args.detail, True, output_dir)
    elif args.f == "table":
      new_index = args.new
      old_index = args.old
      output_dir = args.output
      new_files = GetTableEngineList(new_index)
      old_files = GetTableEngineList(old_index)
      DiffFile(new_files, old_files)
      print new_files
      print old_files
      common_files = GetCommon(new_files, old_files)
      print "common_files"
      print common_files
      for common_file in common_files:
        print common_file
        CompareTableEngine(new_index, old_index,
                           common_file, False, output_dir)
