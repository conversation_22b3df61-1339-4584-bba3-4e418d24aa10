#include "gtest/gtest.h"
#include "serving_base/jansson/json.h"

TEST(builderparser, zrktest) {
  std::string adjust_price_caliber = "{\"87\":\"kk\",\"8337\":2}";
  LOG(INFO) << "SHURU IS " << adjust_price_caliber;
  // adjust_price_caliber = "{\"111\":3}";
  base::J<PERSON> json(base::StringToJson(adjust_price_caliber));
  if (!json.IsObject()) return;
  for (const auto& [k, v] : json.objects()) {
    if (v == nullptr) {
      LOG_EVERY_N(ERROR, 100000)
          << "ParseAdjustPriceCaliberForExtendFields json error adjust_price_caliber "
          << adjust_price_caliber;
      continue;
    } else {
      int64_t int_value;
      if (v->IntValue(&int_value)) {
      } else {
        LOG_EVERY_N(ERROR, 100000)
            << "ParseAdjustPriceCaliberForExtendFields value error, adjust_price_caliber "
            << adjust_price_caliber;
        continue;
      }
      LOG(INFO) << "key is "<< k << "value is " << int_value;
    }
  }
}
