#if 0
#include <string>
#include "gtest/gtest.h"
#include "glog/logging.h"
#include "teams/ad/ad_base/src/container/shared_cuckoo/cuckoohash_map.hh"
#include "boost/interprocess/managed_shared_memory.hpp"
#include "teams/ad/index_builder/tables/data_gate/account.h"
#include "teams/ad/index_builder/tables/data_gate/creative.h"
#include "teams/ad/index_builder/tables/shared_mem_allocator.h"
using namespace tables::util;  // NOLINT
using namespace boost::interprocess;  // NOLINT

DEFINE_string(account_path, "test/ad_dsp_account", "");
TEST(SharedMemory, InnerAllocator) {
  struct MyType {
    MyType(int i, int j) : i_(i), j_(j) {}
    int i_;
    int j_;
    int arr[1<<29];
  };
  MyType *tmp = InnerAllocator::Instance().create_or_reuse<MyType>("helloworld", 1, 2);
  if (tmp) {
    LOG(INFO) << tmp->i_  << ", " << tmp->j_;
  }
  for (int i = 0; i < 26; ++i) {
    std::string name = "helloworld.";
    name.append(1,  static_cast<char>('A' +i));
    InnerAllocator::Instance().create_or_reuse<MyType>(name.c_str(), i, 2);
  }
}

// TEST(SharedMemory, shared_cuckoohash_map) {
//   typedef shared_cuckoohash_map<uint64_t, uint64_t, std::hash<uint64_t>,
//     std::equal_to<uint64_t>, AllocatorWrapper<std::pair<uint64_t, uint64_t>>> DataHash;
//   DataHash * data = InnerAllocator::Instance().create_or_reuse<DataHash>("cuckoohash_test", 1024);
//   LOG(INFO) << "hash size " << data->size();
//   CHECK_EQ(data->hash_function()(1), 1);
//   EXPECT_TRUE(data->key_eq()(1, 1));
//   if (data->empty()) {
//     for (int i = 0; i < 30000; ++i) {
//       data->insert(i, i);
//       LOG_EVERY_N(INFO, 10000) << "hash insert k:" << i << " v:" << i;
//     }
//   }
//   for (auto &it : data->lock_table()) {
//     LOG_EVERY_N(INFO, 10000) << "hash read k:" << it.first << " v:" << it.second;
//   }
//   LOG(INFO) << "hash size " << data->size();
// }

TEST(SharedMemory, AccountContainer) {
  tables::data_gate::AccountAttachContainer c;
  c.Init("account_container_");
  c.LoadFromFile(FLAGS_account_path);
  std::string pb_str_of_3 = c.GetAttachData(2);
  std::function<bool(const tables::data_gate::Account&, const char*, size_t)> record_func =
  [](const tables::data_gate::Account& acc, const char* attach, size_t len) {
    LOG(INFO) << "account read: " << acc.id  << "\t" << std::hash<std::string>()(std::string(attach, len));
    return true;
  };
  c.LogStatus();
  if (!pb_str_of_3.empty()) {
    auto pb = tables::data_gate::Account::gen_proto_from_bytes(pb_str_of_3.c_str(), pb_str_of_3.length());
    pb.set_id(3);
    for (int i = 0; i < 100; ++i) {
      c.Insert(pb);
    }
  }
  c.LogStatus();
  c.CheckAttach();
  int abort_idx = 0;
  std::function<bool(uint64_t k, const tables::data_gate::Account&, const char*, size_t)> all_func =
  [&abort_idx](uint64_t k, const tables::data_gate::Account& acc, const char* attach, size_t len) {
    // LOG_IF(FATAL, ++abort_idx > 1000) << "test abort";
    return true;
  };
  for (uint64_t i = 1; i < 100; ++i) {
    c.Execute(i, record_func);
  }
  c.ExecuteAll(all_func);
  c.DumpToFile("/tmp/test_dump");
}

// TEST(SharedMemory, Creative) {
//   tables::data_gate::CreativeAttachContainer c;
//   c.Init("creative_container_");
//   std::function<bool(uint64_t k, const tables::data_gate::Creative&, const char*, size_t, bool)>
//   record_func =
//   [](uint64_t k, const tables::data_gate::Creative& c, const char* attach, size_t len, bool is_static) {
//     kuaishou::ad::tables::Creative pb;
//     if (!pb.ParseFromArray(attach, len)) {
//       LOG(INFO) << "creative read: " << c.id  << " is static: " <<  (is_static ? "true" : "false");
//     } else {
//       LOG_IF(INFO, c.id != pb.id()) << "creative read: " << c.id << " pb id " << pb.id();
//     }
//     return true;
//   };
//   c.LogStatus();
//   //c.ExecuteAll2(record_func);
// }

#endif