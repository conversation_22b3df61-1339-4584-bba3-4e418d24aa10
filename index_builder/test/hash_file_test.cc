#include "gtest/gtest.h"
#include "teams/ad/index_builder/tables/data_gate/creative.h"
#include "teams/ad/index_builder/tables/data_gate/account.h"

DEFINE_string(creative_path1, "", "");
DEFINE_string(creative_path2, "", "");

using namespace tables::data_gate;  // NOLINT
TEST(Creative, LoadAndMerge) {
  CreativeAttachContainer container;
  if (!FLAGS_creative_path1.empty())
    container.LoadFromFile(FLAGS_creative_path1);

  if (!FLAGS_creative_path2.empty())
    container.LoadFromFile(FLAGS_creative_path2);
}

DEFINE_string(account_path1, "", "");
DEFINE_string(account_path2, "", "");
TEST(Account, LoadAndMerge) {
  AccountAttachContainer container;
  if (!FLAGS_account_path1.empty())
    container.LoadFromFile(FLAGS_account_path1);

  if (!FLAGS_account_path2.empty())
    container.LoadFromFile(FLAGS_account_path2);
}
