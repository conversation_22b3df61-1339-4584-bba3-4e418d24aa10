#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"
#include "gtest/gtest.h"

TEST(AdInstanceProtoHelpers, TableToProtoType) {
  using ks::index_builder::TableToProtoType;
  // DSP regulars.
  EXPECT_EQ(*TableToProtoType("ad_dsp_account"), "kuaishou.ad.tables.Account");
  EXPECT_EQ(*TableToProtoType("ad_dsp_campaign"),
            "kuaishou.ad.tables.Campaign");
  EXPECT_EQ(*TableToProtoType("ad_dsp_unit"), "kuaishou.ad.tables.Unit");
  EXPECT_EQ(*TableToProtoType("ad_dsp_unit_target"),
            "kuaishou.ad.tables.UnitTarget");
  EXPECT_EQ(*TableToProtoType("ad_dsp_account_daily_charge"),
            "kuaishou.ad.tables.AccountDailyCharge");

  // Risk regulars.
  EXPECT_EQ(*TableToProtoType("ad_risk_industry_initiative"),
            "kuaishou.ad.tables.RiskIndustryInitiative");
  EXPECT_EQ(*TableToProtoType("ad_risk_creative_target"),
            "kuaishou.ad.tables.RiskCreativeTarget");
  EXPECT_EQ(*TableToProtoType("ad_risk_industry_white_account"),
            "kuaishou.ad.tables.IndustryWhiteAccount");

  // Special table names.
  EXPECT_EQ(*TableToProtoType("ad_lp_page_das"),
            "kuaishou.ad.tables.AdLpPageDas");
  EXPECT_EQ(*TableToProtoType("ad_dsp_app"), "kuaishou.ad.tables.AdApp");
  EXPECT_EQ(*TableToProtoType("ad_dsp_photo"),
            "kuaishou.ad.tables.PhotoStatus");
  EXPECT_EQ(*TableToProtoType("factory_creative_info"),
            "kuaishou.ad.tables.FactoryCreativeInfo");

  // Unknown.
  EXPECT_FALSE(TableToProtoType("unknown").has_value());
}
