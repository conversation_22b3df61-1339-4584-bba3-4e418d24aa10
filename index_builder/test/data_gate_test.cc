#include "gtest/gtest.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/tables/data_gate/account.h"
#include "teams/ad/index_builder/tables/data_gate/account_balance.h"
#include "teams/ad/index_builder/tables/data_gate/account_day_charge.h"
#include "teams/ad/index_builder/tables/data_gate/ad_app.h"
#include "teams/ad/index_builder/tables/data_gate/ad_creative_preview.h"
#include "teams/ad/index_builder/tables/data_gate/ad_position.h"
#include "teams/ad/index_builder/tables/data_gate/ad_position_resource.h"
#include "teams/ad/index_builder/tables/data_gate/agent.h"
#include "teams/ad/index_builder/tables/data_gate/campaign_day_charge.h"
#include "teams/ad/index_builder/tables/data_gate/cert.h"
#include "teams/ad/index_builder/tables/data_gate/risk_industry_white_account.h"
#include "teams/ad/index_builder/tables/data_gate/risk_photo_initiative.h"
#include "teams/ad/index_builder/tables/data_gate/risk_target.h"
#include "teams/ad/index_builder/tables/data_gate/risk_unit_initiative.h"
#include "teams/ad/index_builder/tables/data_gate/target_paid_audience.h"
#include "teams/ad/index_builder/tables/data_gate/unit.h"
#include "teams/ad/index_builder/tables/data_gate/upload_population_orientation.h"

#define TEST_OF_TABLE_C(Table, PbSetter, DumpContainer, LoadContainer) \
TEST(Table, DumpContainer## _ ## LoadContainer) { \
  std::string test_case_name = std::string() \
      .append(test_info_->test_case_name()) \
      .append(".").append(test_info_->name()); \
  kuaishou::ad::tables::Table pb; \
  tables::data_gate::Table##DumpContainer dump; \
  tables::data_gate::Table##LoadContainer load; \
  for (int j = 1; j < 1000; ++j) { \
    for (int i = 1; i < 100; ++i) { \
      PbSetter(pb, i, std::string("hello ") + test_case_name + " -> " + std::to_string(i+j)); \
      dump.Insert(pb); \
    } \
  } \
  dump.DumpToFile("/tmp/cuckoohash_map." + test_case_name); \
  load.LoadFromFile("/tmp/cuckoohash_map." + test_case_name); \
  for (int i = 1; i < 10; ++i) { \
    pb.ParseFromString(load.GetAttachData(i)); \
    LOG(INFO) << test_case_name \
        << " load key: " << load.GetData(i).key_id() \
        << " pb --- " << pb.ShortDebugString(); \
  } \
}
#define TEST_OF_TABLE(Table, PbIniter) \
TEST_OF_TABLE_C(Table, PbIniter, Container, Container) \
TEST_OF_TABLE_C(Table, PbIniter, AttachContainer, AttachContainer) \
TEST_OF_TABLE_C(Table, PbIniter, Container, AttachContainer) \
TEST_OF_TABLE_C(Table, PbIniter, AttachContainer, Container) \

void AccountPbInit(kuaishou::ad::tables::Account &pb, int id, std::string &&str) {
  pb.set_id(id);
  pb.set_product_name(str);
}

void AccountBalancePbInit(kuaishou::ad::tables::AccountBalance &pb, int id, std::string &&str) {
  pb.set_account_id(id);
}

void AccountDayChargePbInit(kuaishou::ad::tables::AccountDayCharge &pb, int id, std::string &&str) {
  pb.set_key(id);
}

void AdAppPbInit(kuaishou::ad::tables::AdApp &pb, int id, std::string &&str) {
  pb.set_app_id(id);
  pb.set_app_name(str);
}

void AdCreativePreviewPbInit(kuaishou::ad::tables::AdCreativePreview &pb, int id, std::string &&str) {
  pb.set_id(id);
}

void AdPositionPbInit(kuaishou::ad::tables::AdPosition &pb, int id, std::string &&str) {
  pb.set_id(id);
  pb.set_name(str);
}

void AdPositionResourcePbInit(kuaishou::ad::tables::AdPositionResource &pb, int id, std::string &&str) {
  pb.set_id(id);
  pb.set_name(str);
}

void AgentPbInit(kuaishou::ad::tables::Agent &pb, int id, std::string &&str) {
  pb.set_agent_id(id);
  pb.set_user_name(str);
}

void CampaignDayChargePbInit(kuaishou::ad::tables::CampaignDayCharge &pb, int id, std::string &&str) {
  pb.set_key(id);
}

void CertPbInit(kuaishou::ad::tables::Cert &pb, int id, std::string &&str) {
  pb.set_account_id(id);
  pb.set_licence_id(str);
}

void RiskIndustryWhiteAccountPbInit(kuaishou::ad::tables::RiskIndustryWhiteAccount &pb, int id, std::string &&str) {
  pb.set_id(id);
}

void RiskPhotoInitiativePbInit(kuaishou::ad::tables::RiskPhotoInitiative &pb, int id, std::string &&str) {
  pb.set_id(id);
  pb.set_age(str);
}

void RiskTargetPbInit(kuaishou::ad::tables::RiskTarget &pb, int id, std::string &&str) {
  pb.set_id(id);
  pb.set_age(str);
}

void RiskUnitInitiativePbInit(kuaishou::ad::tables::RiskUnitInitiative &pb, int id, std::string &&str) {
  pb.set_id(id);
  pb.set_age(str);
}

void TargetPaidAudiencePbInit(kuaishou::ad::tables::TargetPaidAudience &pb, int id, std::string &&str) {
  pb.set_id(id);
}

void UnitPbInit(kuaishou::ad::tables::Unit &pb, int id, std::string &&str) {
  pb.set_id(id);
  pb.set_app_icon_url(str);
}

void UploadPopulationOrientationPbInit(kuaishou::ad::tables::UploadPopulationOrientation &pb, int id, std::string &&str) {
  pb.set_orientation_id(id);
}

//TEST_OF_TABLE(Account, AccountPbInit)
//TEST_OF_TABLE(AccountBalance, AccountBalancePbInit)
//TEST_OF_TABLE(AccountDayCharge, AccountDayChargePbInit)
//TEST_OF_TABLE(AdApp, AdAppPbInit)
//TEST_OF_TABLE(AdCreativePreview, AdCreativePreviewPbInit)
//TEST_OF_TABLE(AdPosition, AdPositionPbInit)
//TEST_OF_TABLE(AdPositionResource, AdPositionResourcePbInit)
//TEST_OF_TABLE(Agent, AgentPbInit)
//TEST_OF_TABLE(CampaignDayCharge, CampaignDayChargePbInit)
//TEST_OF_TABLE(Cert, CertPbInit)
//TEST_OF_TABLE(RiskIndustryWhiteAccount, RiskIndustryWhiteAccountPbInit)
//TEST_OF_TABLE(RiskPhotoInitiative, RiskPhotoInitiativePbInit)
//TEST_OF_TABLE(RiskTarget, RiskTargetPbInit)
//TEST_OF_TABLE(RiskUnitInitiative, RiskUnitInitiativePbInit)
//TEST_OF_TABLE(TargetPaidAudience, TargetPaidAudiencePbInit)
//TEST_OF_TABLE(Unit, UnitPbInit)

