#pragma once
#include <memory>

#include "teams/ad/index_builder/admit_filters/admit_filter_interface.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/data_build/schemafree/table_row.h"

namespace ks {
namespace index_builder {

class PsMaterialAlphaAdmitFilter : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
};

class AdThanosAdmitFilter : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
};

class BidwordSearchAdmitFilter : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
};

class GalaxyAdmitFilter : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
};

class ExternalAdmitFilter : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
  bool process(ks::ad::build_service::TableRow* table_row) override;
};

class DpaAdmitFilter : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
};

class OrientationPreferAdmitFilter : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
};

class BudgetAdAdmitFilter : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
};

class BudgetFanstopAdmitFilter : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
};

class UniverseDpaAdmitFilter : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
};

class UniverseDpaOptAdmitFilter : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
};

class BidServiceAdmitFilter : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
  bool process(ks::ad::build_service::TableRow* table_row) override;
};

class BudgetAllAdmitFilter : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
  bool process(ks::ad::build_service::TableRow* table_row) override;
};
}  // namespace index_builder
}  // namespace ks
