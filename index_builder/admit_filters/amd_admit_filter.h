#pragma once
#include <memory>

#include "teams/ad/index_builder/admit_filters/admit_filter_interface.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/data_build/schemafree/table_row.h"

namespace ks {
namespace index_builder {

class AmdAdmitFilter : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
  bool process(ks::ad::build_service::TableRow* table_row) override;
};

}  // namespace index_builder
}  // namespace ks
