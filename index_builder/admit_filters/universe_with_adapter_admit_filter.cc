#include "teams/ad/index_builder/admit_filters/universe_with_adapter_admit_filter.h"

#include "teams/ad/index_builder/multiform_filter/ad_filter.h"
#include "teams/ad/index_builder/multiform_filter/adapter_creative_filter.h"

namespace ks {
namespace index_builder {

bool UniverseWithAdapterAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsUnivserWithAdapterData(ad);
}

}  // namespace index_builder
}  // namespace ks
