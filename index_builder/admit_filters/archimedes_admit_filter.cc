#include "teams/ad/index_builder/admit_filters/archimedes_admit_filter.h"

#include "teams/ad/index_builder/multiform_filter/fanstop_filter.h"

namespace ks {
namespace index_builder {

bool ArchimedesAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  // 内粉保留定向表
  if (ad->type() == kuaishou::ad::AdEnum::TARGET) {
    return true;
  }
  return IsInnerFanstop(ad);
}

}  // namespace index_builder
}  // namespace ks
