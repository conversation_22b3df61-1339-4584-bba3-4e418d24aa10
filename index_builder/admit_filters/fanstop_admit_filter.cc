#include "teams/ad/index_builder/admit_filters/fanstop_admit_filter.h"

#include "teams/ad/index_builder/multiform_filter/fanstop_filter.h"

namespace ks {
namespace index_builder {

bool FanstopAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  // IsFansTopData() 已经包含了金牛
  return IsFansTopData(ad);
}

bool FanstopAdmitFilter::process(ks::ad::build_service::TableRow* table_row) {
  return IsFansTopData(table_row);
}

}  // namespace index_builder
}  // namespace ks
