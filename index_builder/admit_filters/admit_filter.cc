#include "teams/ad/index_builder/admit_filters/admit_filter.h"

#include "teams/ad/index_builder/multiform_filter/ad_filter.h"
#include "teams/ad/index_builder/multiform_filter/fanstop_filter.h"

namespace ks {
namespace index_builder {

bool PsMaterialAlphaAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsPsMaterialAlphaData(ad);
}

bool AdThanosAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsThanosData(ad);
}

bool BidwordSearchAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsBidwordSearchData(ad);
}

bool GalaxyAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsGalaxyData(ad);
}

bool ExternalAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsExternalData(ad);
}
bool ExternalAdmitFilter::process(ks::ad::build_service::TableRow* table_row) {
  return IsExternalData(table_row);
}

bool DpaAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsDpaData(ad);
}

bool OrientationPreferAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsOrientationPreferData(ad);
}

bool BudgetAdAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsAdBudget(ad);
}

bool BudgetFanstopAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsFanstopBudget(ad);
}

bool UniverseDpaAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsUniverseDpa(ad);
}

bool UniverseDpaOptAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsUniverseDpaOpt(ad);
}

bool BidServiceAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsBidServiceData(ad);
}
bool BidServiceAdmitFilter::process(ks::ad::build_service::TableRow* table_row) {
  return IsBidServiceData(table_row);
}

bool BudgetAllAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsBudgetAllData(ad);
}
bool BudgetAllAdmitFilter::process(ks::ad::build_service::TableRow* table_row) {
  return IsBudgetAllData(table_row);
}

}  // namespace index_builder
}  // namespace ks
