#include "teams/ad/index_builder/admit_filters/dsp_only_admit_filter.h"

#include "teams/ad/index_builder/multiform_filter/ad_filter.h"
#include "teams/ad/index_builder/multiform_filter/amd_filter.h"
#include "teams/ad/index_builder/multiform_filter/fanstop_filter.h"

namespace ks {
namespace index_builder {

bool DspOnlyAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsAdData(ad);
}

bool DspOnlyAdmitFilterV2::admit(kuaishou::ad::AdInstance *ad) {
  return IsAdDataV2(ad);
}

bool SearchAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsSearchData(ad);
}

bool TargetSearchAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsTargetSearchData(ad);
}

}  // namespace index_builder
}  // namespace ks
