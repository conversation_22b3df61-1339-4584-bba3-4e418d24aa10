#include "teams/ad/index_builder/admit_filters/amd_admit_filter.h"

#include "teams/ad/index_builder/multiform_filter/adapter_creative_filter.h"
#include "teams/ad/index_builder/multiform_filter/amd_filter.h"

namespace ks {
namespace index_builder {

bool AmdAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsAmdData(ad);
}

bool AmdAdmitFilter::process(ks::ad::build_service::TableRow* table_row) {
  return IsAmdData(table_row);
}

}  // namespace index_builder
}  // namespace ks
