#pragma once
#include <memory>

#include "teams/ad/index_builder/admit_filters/admit_filter_interface.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"

namespace ks {
namespace index_builder {

class UniverseNoAdapterAdmitFilter : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
};

class UniverseNoAdapterAdmitFilterV2 : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
};

class UniverseTinyFilter : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
};

class UniverseSwiftFilter : public AdmitFilterInterface {
 public:
  bool admit(kuaishou::ad::AdInstance *ad) override;
};

}  // namespace index_builder
}  // namespace ks
