#pragma once

#include <memory>
#include <unordered_map>

#include "absl/container/flat_hash_map.h"
#include "teams/ad/index_builder/admit_filters/admit_filter.pb.h"
#include "teams/ad/index_builder/admit_filters/admit_filter.h"
#include "teams/ad/index_builder/admit_filters/adserver_admit_filter.h"
#include "teams/ad/index_builder/admit_filters/amd_admit_filter.h"
#include "teams/ad/index_builder/admit_filters/archimedes_admit_filter.h"
#include "teams/ad/index_builder/admit_filters/dsp_only_admit_filter.h"
#include "teams/ad/index_builder/admit_filters/fanstop_admit_filter.h"
#include "teams/ad/index_builder/admit_filters/non_dsp_admit_filter.h"
#include "teams/ad/index_builder/admit_filters/universe_no_adapter_admit_filter.h"
#include "teams/ad/index_builder/admit_filters/universe_with_adapter_admit_filter.h"
#include "teams/ad/index_builder/admit_filters/ems_admit_filter.h"
#include "teams/ad/index_builder/admit_filters/creative_server_filter.h"

namespace ks {
namespace index_builder {

std::shared_ptr<ks::index_builder::AdmitFilterInterface> GetAdmitFilterByEnum(
    AdmitFilterEnum::Type type);

void RegisterAdmitFilter(AdmitFilterEnum::Type filter_type,
                         AdmitFilterInterface* admit_filter_ptr);
}  // namespace index_builder
}  // namespace ks
