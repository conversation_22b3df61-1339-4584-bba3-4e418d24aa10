#include "teams/ad/index_builder/admit_filters/creative_server_filter.h"

#include "teams/ad/engine_base/material_feature_type/material_feature_type.h"

namespace ks {
namespace index_builder {

bool CreativeServerExternalFilter::admit(kuaishou::ad::AdInstance *ad) {
  if (ad == nullptr) return false;
  if (ad->type() != kuaishou::ad::AdEnum::CREATIVE) return true;
  const auto& creative = ad->GetExtension(kuaishou::ad::tables::Creative::creative_old);

  // STICKER 便利贴过滤
  if (kuaishou::ad::AdEnum::STICKER == creative.creative_material_type()) {
    return false;
  }

  // 外循环不过滤
  if (ks::engine_base::IsCreativeExternalCirculation(creative)) return true;

  // rtb 物料不过滤
  if (creative.creative_material_type() == kuaishou::ad::AdEnum::SPLASH_PHOTO ||
      creative.creative_material_type() == kuaishou::ad::AdEnum::SPLASH_IMAGES ||
      creative.creative_material_type() == kuaishou::ad::AdEnum::ESP_EYE_MAX) {
    return true;
  }

  return false;
}

bool CreativeServerInternalFilter::admit(kuaishou::ad::AdInstance *ad) {
  if (ad == nullptr) return false;
  if (ad->type() != kuaishou::ad::AdEnum::CREATIVE) return true;
  const auto& creative = ad->GetExtension(kuaishou::ad::tables::Creative::creative_old);

  // STICKER 便利贴过滤
  if (kuaishou::ad::AdEnum::STICKER == creative.creative_material_type()) {
    return false;
  }

  // 内循环不过滤
  if (ks::engine_base::IsCreativeInternalCirculation(creative)) return true;

  // 直播类不过滤
  if (creative.live_creative_type() != kuaishou::ad::AdEnum::UNKNOWN_LIVE_CREATIVE_TYPE) {
    return true;
  }

  return false;
}


}  // namespace index_builder
}  // namespace ks
