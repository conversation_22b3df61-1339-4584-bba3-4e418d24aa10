#pragma once
#include "teams/ad/index_builder/admit_filters/admit_common.h"
#include "teams/ad/index_builder/multiform_filter/ad_filter.h"
#include "teams/ad/index_builder/multiform_filter/fanstop_filter.h"

namespace ks {
namespace index_builder {
#define ADMIT_DECLARE(CLASS_NAME, ADMIT_FUNC, ADMIT_TYPE)                                               \
  class CLASS_NAME : public AdmitFilterInterface {                                                      \
   public:                                                                                              \
    bool admit(kuaishou::ad::AdInstance* ad) override { return ADMIT_FUNC(ad); }                        \
    bool process(ks::ad::build_service::TableRow* table_row) override { return ADMIT_FUNC(table_row); } \
  };                                                                                                    \
  __attribute__((constructor, weak)) void register_##CLASS_NAME() {                                     \
    CLASS_NAME* temp = new CLASS_NAME;                                                                  \
    RegisterAdmitFilter(AdmitFilterEnum::ADMIT_TYPE, temp);                                             \
  }

ADMIT_DECLARE(SplashAdmitFilter, IsSplashData, SPLASH)
ADMIT_DECLARE(SplashAdmitFilterKtable, IsSplashDataKtable, SPLASH_KTABLE)
ADMIT_DECLARE(HostingAdmitFilter, IsHostingData, HOSTING)
ADMIT_DECLARE(BoyleAdmitFilter, IsBoyleData, BOYLE)
ADMIT_DECLARE(InternalAdmitFilter, IsInternalData, INTERNAL)
ADMIT_DECLARE(StyleSplashFilter, IsStyleSplash, STYLE_SPLASH)
ADMIT_DECLARE(StyleDefaultFilter, IsStyleDefault, STYLE_DEFAULT)
ADMIT_DECLARE(StyleUniverseFilter, IsStyleUniverse, STYLE_UNIVERSE)
ADMIT_DECLARE(StyleAllFilter, IsStyleAll, STYLE_ALL)
ADMIT_DECLARE(MaterialUniverseFilter, IsMaterialUniverse, MATERIAL_UNIVERSE)
ADMIT_DECLARE(I18nAllFilter, IsI18nAll, I18N_ALL)
#undef ADMIT_DECLARE
}  // namespace index_builder
}  // namespace ks
