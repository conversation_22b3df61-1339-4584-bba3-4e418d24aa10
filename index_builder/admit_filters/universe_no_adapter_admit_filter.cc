#include "teams/ad/index_builder/admit_filters/universe_no_adapter_admit_filter.h"

#include "teams/ad/index_builder/multiform_filter/ad_filter.h"
#include "teams/ad/index_builder/multiform_filter/adapter_creative_filter.h"

namespace ks {
namespace index_builder {

bool UniverseNoAdapterAdmitFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsUnivserNoAdapterData(ad);
}

bool UniverseNoAdapterAdmitFilterV2::admit(kuaishou::ad::AdInstance *ad) {
  return IsUnivserNoAdapterDataV2(ad);
}

bool UniverseTinyFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsUnivserTinyData(ad);
}

bool UniverseSwiftFilter::admit(kuaishou::ad::AdInstance *ad) {
  return IsUniverseSwiftData(ad);
}

}  // namespace index_builder
}  // namespace ks
