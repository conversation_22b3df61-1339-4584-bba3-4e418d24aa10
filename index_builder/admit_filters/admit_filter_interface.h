#pragma once

#include <memory>

#include "glog/logging.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/index_builder/multiform_filter/ad_filter.h"
#include "teams/ad/data_build/schemafree/table_row.h"

namespace ks {
namespace index_builder {

// See ks.index_builder.AdmitFilterEnum.Type.
// Each filter class should map to one enum.
class AdmitFilterInterface {
 public:
  AdmitFilterInterface() = default;
  // Returns true if ad is allowed to flow. Otherwise ad is filtered out.
  virtual bool admit(kuaishou::ad::AdInstance *ad) = 0;
  virtual bool process(ks::ad::build_service::TableRow *table_row) {
    return false;
  }
  virtual ~AdmitFilterInterface() = default;
};

class AllAdmitFilter : public AdmitFilterInterface {
 public:
  // ALL filter Dpa Creative
  bool admit(kuaishou::ad::AdInstance *ad) override {
    if (ad == nullptr) return false;
    auto msg_type = ad->type();
    if (msg_type == kuaishou::ad::AdEnum::CREATIVE && IsDpaNotContainSdpa(ad)) {
      LOG_EVERY_N(INFO, 100000) << "ALL filter skip, dpa item, item=" << ad->ShortDebugString();
      return false;
    }
    return true;
  }
  bool process(ks::ad::build_service::TableRow *table_row) override {
    if (table_row == nullptr) return false;
    // 表配置解析
    auto table_name = table_row->GetTableName();
    if (table_name.empty()) {
      LOG_EVERY_N(WARNING, 1000) << "get table name  fail, table_number=" << table_row->GetTableNum();
      return false;
    }
    if (table_name == "ad_dsp_creative" && IsDpaNotContainSdpa(table_row)) {
      LOG_EVERY_N(INFO, 100000) << "ALL filter skip, dpa item";
      return false;
    }
    return true;
  }
  virtual ~AllAdmitFilter() = default;
};

}  // namespace index_builder
}  // namespace ks
