# Index Builder
广告索引文档 (持续更新中): https://docs.corp.kuaishou.com/d/home/<USER>

## 配置文件

- dynamic_json_config: https://git.corp.kuaishou.com/ad/ad/-/blob/master/index_builder/pub/ad_index_builder/config/dynamic_json_config.py
- 控制导出文件和dynamic_json_config: ad.engine.indexBuilderConf
- p2p kconf: https://kconf.corp.kuaishou.com/#/ad/dataPush/globalDataPushConfig

## 容器云环境

https://halo.corp.kuaishou.com/devcloud/cloud/cloud/detail/?node_global_id=SERVICE_CATALOG-16648&tabActiveName=service-node-template

## Pipeline

https://halo.corp.kuaishou.com/devcloud/pipeline/history/6392?differ=all&back=mine-pipeline

## DAS 接口查询文档

1. 使用说明
https://docs.corp.kuaishou.com/d/home/<USER>

2. 查询网址
https://addas.corp.kuaishou.com/#/basic

## DAS hdfs文件查询

正式环境请用上面DAS接口. Debug 也可以用 check_das_message 和 check_message，具体用法见 .cc 文件

