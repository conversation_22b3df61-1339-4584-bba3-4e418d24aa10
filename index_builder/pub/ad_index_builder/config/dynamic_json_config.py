# -*- coding: UTF8 -*-
import json
import os

config = {
  "clean_fields" : {
    "creative" : {
      "id" : 1,
      "unit_id" : 1,
      "campaign_id" : 1,
      "account_id" : 1,
      "photo_id" : 1,
      "cover_id" : 1,
      "create_time" : 1,
      "live_creative_type" : 1,
      "put_status" : 1,
      "review_status" : 1,
      "create_source_type" : 1,
      "derivative_creative_flag" : 1,
      "creative_feature" : 1,
      "dup_photo_id" : 1,
      "biz_id": 1,
      "creative_score_ext": 1,
      "creative_score": 1,
      "extend_fields": 1,
    }
  },
  "dump_conf": {
    #"limit" : 1000,
    "index_build_interval": 3600,
    "output": "../ad_index/",
    "table_dump_configs": [
      # --------- charge database config ------------
      {
        "type": "ACCOUNT_BALANCE",
        "table_name": "ad_dsp_account_balance",
        "server_name": [
          "dpa_forward_index",
          "budget_status",
          "budget_status_forward_index",
        ]
      },
      {
        "type": "ACCOUNT_DAY_CHARGE",
        "table_name": "ad_dsp_account_daily_charge",
        "server_name": [
          "budget_status",
          "budget_status_forward_index",
        ]
      },
      {
        "type": "CAMPAIGN_DAY_CHARGE",
        "table_name": "ad_dsp_campaign_daily_charge",
        "server_name": [
          "budget_status",
          "budget_status_forward_index",
        ]
      },
      {
        "type": "UNIT_DAY_CHARGE",
        "table_name": "ad_dsp_unit_daily_charge",
        "server_name": [
          "budget_status",
          "budget_status_forward_index",
        ]
      },
      {
        "type": "AD_UNIT_DAILY_CHARGE_TAG",
        "table_name": "ad_dsp_unit_daily_charge_tag",
        "server_name": [
          "budget_status",
          "budget_status_forward_index",
        ]
      },
      {
        "type": "AD_CAMPAIGN_DAILY_CHARGE_TAG",
        "table_name": "ad_dsp_campaign_daily_charge_tag",
        "server_name": [
          "budget_status",
          "budget_status_forward_index",
        ]
      },
      {
        "type": "AD_ACCOUNT_DAILY_CHARGE_TAG",
        "table_name": "ad_dsp_account_daily_charge_tag",
        "server_name": [
          "budget_status",
          "budget_status_forward_index",
        ]
      },
      # ---------- default database config ------------
      {
        "type": "ACCOUNT",
        "table_name": "ad_dsp_account",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "ad_collective_index",
          "hot_collective_index",
          "search_collective_index",
          "ad_server_forward_index",
          "dpa_server",
          "dpa_forward_index",
          "amd_aihosting",
          "budget_status",
          "budget_status_forward_index",
          "fanstop_budget",
          "fanstop_forward_index",
          "base_forward_index",
          "amd_photo_forward_index",
          "ai_hosting_forward_index",
          "orientation_prefer_forward",
        ]
      },
      {
        "type": "AD_DSP_ACCOUNT_SUPPORT_INFO",
        "table_name": "ad_dsp_account_support_info",
        "server_name": [
        ]
      },
      {
        "type": "AD_APP",
        "table_name": "ad_dsp_app",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "ad_collective_index",
          "ad_server_forward_index",
          "dpa_server",
          "dpa_forward_index",
          "fanstop_forward_index",
        ]
      },
      {
        "type": "CAMPAIGN",
        "table_name": "ad_dsp_campaign",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "ad_collective_index",
          "hot_collective_index",
          "search_collective_index",
          "ad_server_forward_index",
          "dpa_server",
          "dpa_forward_index",
          "amd_aihosting",
          "budget_status",
          "budget_status_forward_index",
          "fanstop_budget",
          "fanstop_forward_index",
          "base_forward_index",
          "amd_photo_forward_index",
          "ai_hosting_forward_index",
          "orientation_prefer_forward",
        ]
      },
      {
        "type": "UNIT",
        "table_name": "ad_dsp_unit",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "ad_collective_index",
          "hot_collective_index",
          "search_collective_index",
          "ad_server_forward_index",
          "dpa_server",
          "dpa_forward_index",
          "amd_aihosting",
          "budget_status",
          "budget_status_forward_index",
          "fanstop_budget",
          "fanstop_forward_index",
          "base_forward_index",
          "amd_photo_forward_index",
          "ai_hosting_forward_index",
          "orientation_prefer_forward",
        ]
      },
      {
        "type": "CREATIVE",
        "table_name": "ad_dsp_creative",
        "server_name": [
          "ad_collective_index",
          "hot_collective_index",
          "search_collective_index",
          "dpa_server",
          "dpa_forward_index",
          "amd_aihosting",
          "fanstop_forward_index",
          "hot_forward_index",
          "amd_photo_forward_index",
          "orientation_prefer_forward",
        ]
      },
      {
        "type": "AGENT",
        "table_name": "ad_dsp_agent",
        "server_name": [

        "ad_server_forward_index",
          "amd_aihosting",
          "ad_forward_index",
          "hot_forward_index",
          "fanstop_forward_index",
          "budget_status",
        ]
      },
      {
        "type": "AGENT_ACCOUNT",
        "table_name": "ad_dsp_agent_account",
        "server_name": [
          "ad_server_forward_index",
          
          "amd_aihosting",
          "ad_forward_index",
          "hot_forward_index",
          "fanstop_forward_index",
          "budget_status",
        ]
      },
      {
        "type": "TARGET",
        "table_name": "ad_dsp_target",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "ad_collective_index",
          "dpa_server",
          "dpa_forward_index",
          "amd_aihosting",
          "fanstop_forward_index",
          "orientation_prefer_forward",
          "hot_collective_index",
        ]
      },
      {
        "type": "TARGET_PAID_AUDIENCE",
        "table_name": "ad_dsp_target_paid_audience",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "ad_collective_index",
          "ad_server_forward_index",
          "dpa_forward_index",
          "amd_aihosting",
          "fanstop_forward_index",
        ]
      },
      {
        "type": "PHOTO_STATUS",
        "table_name": "ad_dsp_photo",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "ad_collective_index",
          "hot_collective_index",
          "search_collective_index",
          "dpa_server",
          "dpa_forward_index",
          "amd_aihosting",
          "fanstop_forward_index",
        ]
      },
      {
        "type": "AD_POSITION",
        "table_name": "ad_dsp_position",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "ad_collective_index",
          "ad_server_forward_index",
          "dpa_forward_index",
          "fanstop_forward_index",
          "dpa_server",
        ]
      },
      {
        "type": "AD_POSITION_RESOURCE",
        "table_name": "ad_dsp_position_resource",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "ad_collective_index",
          "dpa_forward_index",
          "fanstop_forward_index",
          "dpa_server",
        ]
      },
      {
        "type": "AD_STYLE_MATERIAL",
        "table_name": "ad_style_material",
        "server_name": [
          "ad_style",
          "ad_style_forward",
        ]
      },
      {
        "type": "AD_STYLE_MATERIAL_BIND",
        "table_name": "ad_style_material_bind",
        "server_name": [
          "ad_style",
          "ad_style_forward",
        ]
      },
      {
        "type": "AD_STYLE_TEMPLATE",
        "table_name": "ad_style_template",
        "server_name": [
          "ad_style",
          "ad_style_forward",
        ]
      },
      {
        "type": "AD_STYLE_TEMPLATE_SCENE_RULE",
        "table_name": "ad_style_template_scene_rule",
        "server_name": [
          "ad_style",
          "ad_style_forward",
        ]
      },
      {
        "type": "AD_MATRIX_STYLE_MATERIAL",
        "table_name": "ad_matrix_style_material",
        "server_name": [
          "ad_style",
          "ad_style_forward",
        ]
      },
      {
        "type": "AD_MATRIX_STYLE_MATERIAL_SCENE_RULE",
        "table_name": "ad_matrix_style_material_scene_rule",
        "server_name": [
          "ad_style",
          "ad_style_forward",
        ]
      },
      {
        "type": "AD_POSITION_STRATEGY",
        "table_name": "ad_dsp_position_strategy",
        "server_name": [
          "ad_collective_index",
          "dpa_forward_index",
          "ad_forward_index",
          "hot_forward_index",
          "fanstop_forward_index",
        ]
      },
      {
        "type": "AD_CREATIVE_PREVIEW",
        "table_name": "ad_dsp_creative_preview",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "ad_collective_index",
          "dpa_forward_index",
          "fanstop_forward_index",
        ]
      },
      {
        "type": "MATERIAL",
        "table_name": "ad_dsp_material",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "ad_collective_index",
          "ad_server_forward_index",
          "dpa_forward_index",
          "amd_aihosting",
          "fanstop_forward_index",
        ]
      },
      {
        "type": "INDUSTRY_V3",
        "table_name": "ad_dsp_industry_v3",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "ad_collective_index",
          "ad_server_forward_index",
          "dpa_forward_index",
          "amd_aihosting",
          "fanstop_forward_index",
          "base_forward_index",
          "ai_hosting_forward_index",
          "budget_status",
        ]
      },
      {
        "type": "RISK_CREATIVE_TARGET",
        "table_name": "ad_risk_creative_target",
        "server_name": [
          ]
      },
      {
        "type": "RISK_TARGET",
        "table_name": "ad_risk_target",
        "server_name": [
          ]
      },
      {
        "type": "RISK_ACCOUNT_INITIATIVE",
        "table_name": "ad_risk_account_initiative",
        "server_name": [
          ]
      },
      {
        "type": "RISK_INDUSTRY_INITIATIVE",
        "table_name": "ad_risk_industry_initiative",
        "server_name": [
          ]
      },
      {
        "type": "RISK_PHOTO_INITIATIVE",
        "table_name": "ad_risk_photo_initiative",
        "server_name": [
          ]
      },
      {
        "type": "FACTORY_CREATIVE_INFO",
        "table_name": "factory_creative_info",
        "server_name": [
          ]
      },
      {
        "type": "RISK_UNIT_INITIATIVE",
        "table_name": "ad_risk_unit_initiative",
        "server_name": [
          ]
      },
      {
        "type": "RISK_INDUSTRY_WHITE_ACCOUNT",
        "table_name": "ad_risk_industry_white_account",
        "server_name": [
          ]
      },
      {
        "type": "TRACE_UTIL",
        "table_name": "ad_dsp_trace_util",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "ad_collective_index",
          "ad_server_forward_index",
          "dpa_forward_index",
          "fanstop_forward_index",
        ]
      },
      {
        "type": "TRACE_API_DETECTION",
        "table_name": "ad_dsp_trace_api_detection",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "ad_collective_index",
          "ad_server_forward_index",
          "dpa_forward_index",
          "fanstop_forward_index",
        ]
      },
      {
        "type": "UPLOAD_POPULATION_ORIENTATION",
        "table_name": "ad_dsp_upload_population_orientation",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "ad_collective_index",
          "ad_server_forward_index",
          "dpa_forward_index",
          "amd_aihosting",
          "fanstop_forward_index",
        ]
      },
      {
        "type": "SITE_EXT_INFO",
        "table_name": "ad_dsp_site_ext_info",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "ad_collective_index",
          "dpa_forward_index",
          "fanstop_forward_index",
        ]
      },
      {
        "table_name": "ad_dsp_unit_small_shop_merchant_support_info",
        "type": "AD_DSP_UNIT_SMALL_SHOP_MERCHANT_SUPPORT_INFO",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "dpa_forward_index",
          "ad_collective_index",
          "fanstop_forward_index",
          "dpa_server",
          "amd_photo_forward_index",
          "orientation_prefer_forward",
        ]
      },
      {
        "table_name": "ad_dsp_small_shop_product_spu",
        "type": "AD_DSP_SMALL_SHOP_SPU",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "ad_collective_index",
          "dpa_forward_index",
          "fanstop_forward_index",
        ]
      },
      {
        "table_name":"ad_dpa_category_target",
        "type": "DPA_CATEGORY_TARGET",
        "server_name": [
          "dpa_server",
        ]
      },
      {
        "table_name": "ad_dsp_hosting_project",
        "type": "AD_DSP_HOSTING_PROJECT",
        "server_name": [
          "ai_hosting_forward_index",
          "budget_status",
        ]
      },
      {
        "table_name": "ad_dsp_hosting_project_target",
        "type": "AD_DSP_HOSTING_PROJECT_TARGET",
        "server_name": [
          "ai_hosting_forward_index",
        ]
      },
      {
        "table_name": "ad_dsp_ecom_hosting_project",
        "type": "AD_DSP_ECOM_HOSTING_PROJECT",
        "server_name": [
          "amd_aihosting",
          "hot_forward_index",
          "ad_forward_index"
        ]
      },
      {
        "table_name": "ad_dsp_ecom_hosting_project_creative_create_param",
        "type": "AD_DSP_ECOM_HOSTING_PROJECT_CREATIVE_CREATE_PARAM",
        "server_name": [
          "amd_aihosting",
        ]
      },
      {
        "table_name": "ad_dsp_adv_card",
        "type": "AD_DSP_ADV_CARD",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "dpa_forward_index",
          "fanstop_forward_index",
        ]
      },
      {
        "table_name": "ad_dsp_live_stream_user_info",
        "type": "LIVE_STREAM_USER_INFO",
        "server_name": [
          "ad_forward_index",
          "ad_collective_index",
          "fanstop_forward_index",
          "hot_forward_index",
          "amd_aihosting",
          "ad_server_forward_index",
          "hot_collective_index",
          "amd_photo_forward_index",
          "base_forward_index",
          "orientation_prefer_forward",
        ]
      },
      {
        "table_name": "ad_dsp_target_media",
        "type": "AD_DSP_TARGET_MEDIA",
        "server_name": [
        ]
      },
      {
        "table_name": "ad_risk_material_target",
        "type": "AD_RISK_MATERIAL_TARGET",
        "server_name": [
        ]
      },
      {
        "table_name": "ad_lp_page_das",
        "type": "AD_LP_PAGE_DAS",
        "server_name": [
          "ad_forward_index",
          "hot_forward_index",
          "fanstop_forward_index",
        ]
      },
      {
        "table_name": "ad_dsp_fanstop_live_hosting_project",
        "type": "AD_DSP_FANSTOP_LIVE_HOSTING_PROJECT",
        "server_name": [
          "fanstop_forward_index",
          "hot_forward_index",
        ]
      },
      {
        "table_name": "ad_charge_balance",
        "type": "AD_CHARGE_BALANCE",
        "server_name": [
          "fanstop_budget",
        ]
      },
      {
        "table_name": "ad_unit_range_charge",
        "type": "AD_UNIT_RANGE_CHARGE",
        "server_name": [
          "fanstop_budget",
        ]
      },
      {
        "table_name": "ad_fans_top_campaign_range_charge_tag",
        "type": "AD_CAMPAIGN_RANGE_CHARGE_TAG",
        "server_name": [
          "fanstop_budget",
        ]
      },
      {
        "table_name": "ad_fans_top_unit_range_charge_tag",
        "type": "AD_UNIT_RANGE_CHARGE_TAG",
        "server_name": [
          "fanstop_budget",
        ]
      },
      {
        "table_name": "ad_dsp_mini_app",
        "type": "AD_DSP_MINI_APP",
        "server_name": [
          "hot_forward_index",
        ]
      },
      {
        "table_name": "ad_dsp_hosting_project_fiction",
        "type": "AD_DSP_HOSTING_PROJECT_FICTION",
        "server_name": [
          "ai_hosting_forward_index",
        ],
      },
      {
        "table_name": "ad_dsp_play_info",
        "type": "AD_DSP_PLAY_INFO",
        "server_name": [
          "hot_forward_index",
        ],
      },
      {
        "table_name": "ad_dsp_winfo",
        "type": "AD_DSP_WINFO",
        "server_name": [
          "hot_forward_index",
          "ad_collective_index"
        ],
      },
      {
        "table_name": "ad_dsp_negative_word",
        "type": "AD_DSP_NEGATIVE_WORD",
        "server_name": [
        ],
      },
      {
        "table_name": "ad_dpa_library",
        "type": "AD_DPA_LIBRARY",
        "server_name": [
          "ad_style",
          "ad_style_forward"
        ],
      },
    ]
  }
}

if __name__ == "__main__":
  json.encoder.FLOAT_REPR = str
  service_name = os.getenv('KWS_SERVICE_NAME')
  stage_name = os.getenv('KWS_SERVICE_STAGE')
  pod_name = os.getenv('MY_POD_NAME') 
  if service_name == 'ad-index-builder-ad-style' and stage_name == 'PREONLINE':
    config['shard_id'] = int(pod_name[pod_name.rfind('-')+1:])
  print json.dumps(config, indent=2, sort_keys=True)