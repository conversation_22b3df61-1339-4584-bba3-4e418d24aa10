syntax = "proto3";

package ks.index_builder.index_base;

enum IndexType {
  DAS = 0;
  TARGET = 1;
  LITE = 2;
}

enum TransType {
  KFS = 0;
  HDFS = 1;
}

enum ReaderType {
  CACHE_READER = 0;
  HUGE_CACHE_READER = 1;
}

enum StreamType {
  SINGLE_FILE_STREAM = 0;
  MUTI_FILE_STREAM = 1;
}

message IndexPathConfig {
  repeated string local_prefix = 1;
  repeated string hdfs_prefix = 2;
  repeated string das_prefix = 3;
  repeated string target_prefix = 4;
  repeated string lite_prefix = 5;
}