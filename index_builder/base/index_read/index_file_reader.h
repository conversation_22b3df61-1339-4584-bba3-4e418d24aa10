#pragma once
#include <glog/logging.h>
#include <sstream>
#include <string>
#include <vector>
#include <memory>
#include "teams/ad/index_builder/base/index_read/file_wrapper.h"
#include "teams/ad/index_builder/base/index_read/index_wrapper.h"

namespace ks {
namespace index_builder {
namespace index_base {

// 文件读取器
class IndexFileInstanceReaderBase {
 public:
  explicit IndexFileInstanceReaderBase(ReaderType reader_type)
      : reader_type_(reader_type) {}
  virtual ~IndexFileInstanceReaderBase() {}
  void Init(TransType trans_type, std::string path, IndexType index_type,
            kuaishou::ad::AdEnum::AdInstanceType ad_type);
  IndexBase* GetIndexHandle() { return index_wrapper_.get(); }
  FileWrapperBase* GetFileHandle() { return file_wrapper_.get(); }
  virtual bool Valid() = 0;
  const kuaishou::ad::AdInstance* GetAdInstance() {
    return index_wrapper_->GetIndexInstance();
  }
  std::string DebugInfo() {
    std::stringstream ss;
    ss << "Read " << index_wrapper_->DebugInfo() << " on "
       << file_wrapper_->DebugInfo() << " by " << ReaderType_Name(reader_type_);
    return ss.str();
  }
  static std::shared_ptr<IndexFileInstanceReaderBase> Create(
      ReaderType reader_type);

 protected:
  std::shared_ptr<FileWrapperBase> file_wrapper_;
  std::shared_ptr<IndexBase> index_wrapper_;
  ReaderType reader_type_;
};

// 单文件带 buffer
class FileCacheReader : public IndexFileInstanceReaderBase {
 public:
  FileCacheReader() : IndexFileInstanceReaderBase(CACHE_READER) {}
  bool Valid() override;
 protected:
  std::vector<char> buffer_;
  int32_t cache_size_{0};
  int32_t read_offset_{0};
  static const int32_t CACHE_SIZE;
};



}  // namespace index_base
}  // namespace index_builder
}  // namespace ks
