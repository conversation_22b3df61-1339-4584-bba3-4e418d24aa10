#include "teams/ad/index_builder/base/index_read/message_stream.h"
namespace ks {
namespace index_builder {
namespace index_base {
MessageStream::~MessageStream() {
  if (record_num_ != 0) {
    LOG(INFO) << "file: " << reader_base_->GetFileHandle()->GetPath()
              << " read " << record_num_ << "records";
    LOG_ASSERT(ValidChecker::GetInstance()->OutputValidCheck(
        reader_base_->GetFileHandle()->GetPath(), record_num_))
        << reader_base_->GetFileHandle()->GetPath()
        << " read record not meet expect :" << record_num_;
    PerfUtil::SetLogStash(end_time_ - start_time_, "ad.index_builder",
                          "dump_time",
                          reader_base_->GetFileHandle()->GetPath());
    PerfUtil::SetLogStash(record_num_, "ad.index_builder", "record_num",
                          reader_base_->GetFileHandle()->GetPath());
  }
}
}  // namespace index_base
}  // namespace index_builder
}  // namespace ks
