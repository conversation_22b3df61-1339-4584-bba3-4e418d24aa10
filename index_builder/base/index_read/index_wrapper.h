#pragma once
#include <glog/logging.h>
#include <memory>
#include <string>
#include "teams/ad/ad_index/framework/pb_reader/pb_header.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/base/index_read/index_read_proto.pb.h"

namespace ks {
namespace index_builder {
namespace index_base {

// 封装不同索引格式的处理
class IndexBase {
 public:
  explicit IndexBase(IndexType index_type) : index_type_(index_type) {}
  virtual ~IndexBase() {}
  virtual std::string SuccessFlag() = 0;
  virtual int32_t GetHeaderSize() = 0;
  virtual bool Parse(char* ptr, int32_t size) = 0;
  virtual std::string TimeFormat() = 0;
  IndexType GetType() const { return index_type_; }
  kuaishou::ad::AdEnum::AdInstanceType GetMsgType() const { return msg_type_; }
  void SetMsgType(kuaishou::ad::AdEnum::AdInstanceType ad_type);
  std::string DebugInfo() { return IndexType_Name(index_type_); }
  kuaishou::ad::AdInstance* GetIndexInstance() { return &default_instance_; }
  google::protobuf::Message* GetExtendMessage() { return base_msg_; }
  static std::shared_ptr<IndexBase> Create(IndexType index_type);

 protected:
  IndexType index_type_;
  kuaishou::ad::AdInstance default_instance_;
  google::protobuf::Message* base_msg_{nullptr};
  kuaishou::ad::AdEnum::AdInstanceType msg_type_;
};

// DAS 索引
class DasIndex : public IndexBase {
 public:
  DasIndex() : IndexBase(DAS) {}
  std::string TimeFormat() override { return "%Y-%m-%d_%H%M%S"; }
  std::string SuccessFlag() override { return "dump_done"; }
  int32_t GetHeaderSize() override { return 0; }
  bool Parse(char* ptr, int32_t size) override {
    base_msg_->Clear();
    return base_msg_->ParseFromArray(ptr, size);
  }
};

// Target 索引
class TargetIndex : public IndexBase {
 public:
  TargetIndex() : IndexBase(TARGET) {}

  std::string TimeFormat() override { return "%Y-%m-%d_%H%M"; }
  std::string SuccessFlag() override { return "_SUCCESS"; }
  int32_t GetHeaderSize() override { return sizeof(ks::ad_base::PbHeader); }
  bool Parse(char* ptr, int32_t size) override {
    default_instance_.Clear();
    return default_instance_.ParseFromArray(ptr, size);
  }
};

// 简版索引
class LiteIndex : public IndexBase {
 public:
  LiteIndex() : IndexBase(LITE) {}
  std::string TimeFormat() override { return "%Y-%m-%d_%H%M"; }
  std::string SuccessFlag() override { return "_SUCCESS"; }
  int32_t GetHeaderSize() override { return 0; }
  bool Parse(char* ptr, int32_t size) override {
    base_msg_->Clear();
    return base_msg_->ParseFromArray(ptr, size);
  }
};

}  // namespace index_base
}  // namespace index_builder
}  // namespace ks
