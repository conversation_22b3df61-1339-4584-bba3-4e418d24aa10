#include "teams/ad/index_builder/base/index_read/index_file_reader.h"
#include <memory>
#include "teams/ad/index_builder/utils/kconf.h"
namespace ks {
namespace index_builder {
namespace index_base {
const int32_t FileCacheReader::CACHE_SIZE = 1 << 22;

std::shared_ptr<IndexFileInstanceReaderBase>
IndexFileInstanceReaderBase::Create(ReaderType reader_type) {
  if (reader_type == CACHE_READER) {
    return std::make_shared<FileCacheReader>();
  }
  return nullptr;
}
void IndexFileInstanceReaderBase::Init(
    TransType trans_type, std::string path, IndexType index_type,
    kuaishou::ad::AdEnum::AdInstanceType ad_type) {
  index_wrapper_ = IndexBase::Create(index_type);
  index_wrapper_->SetMsgType(ad_type);
  file_wrapper_ = FileWrapperBase::Create(trans_type);
  file_wrapper_->SetPath(path);
}
bool FileCacheReader::Valid() {
  // Cache 初始化，读头
  if (buffer_.empty()) {
    buffer_.resize(CACHE_SIZE);
    if (index_wrapper_->GetHeaderSize() != 0) {
      cache_size_ = file_wrapper_->Read(buffer_.data(), index_wrapper_->GetHeaderSize());
      if (cache_size_ != index_wrapper_->GetHeaderSize()) {
        LOG(FATAL) << "Read index Header failed. Info: " << DebugInfo();
      }
    }
    cache_size_ = file_wrapper_->Read(buffer_.data(), CACHE_SIZE);
    if (AdKconfUtil::enableReadOpt()) {
      if (cache_size_ == 0) {
        // 数据已读完
        return false;
      }
      if (cache_size_ < 0) {
        // 读异常, HDFS 接口错误码只返回 -1，KFS 接口错误码返回 -1,-2,-3,-4
        LOG(FATAL) << "Read index source failed, errno=" << cache_size_;
      }
    } else {
      if (cache_size_ <= 0) {
        return false;
      }
    }
  }

  if (cache_size_ - read_offset_ > 4) {
    uint32_t pb_size =
        *reinterpret_cast<uint32_t*>(buffer_.data() + read_offset_);
    if (pb_size > (120 << 10)) {
      LOG(ERROR) << ", pb_size: " << pb_size << " is too large. Info "
                 << DebugInfo();
    }

    if (read_offset_ + 4 + pb_size <= cache_size_) {
      char* data = buffer_.data() + read_offset_ + 4;
      read_offset_ += 4 + pb_size;
      bool flag = index_wrapper_->Parse(data, pb_size);
      return flag;
    }
  }

  cache_size_ = cache_size_ - read_offset_;
  memcpy(buffer_.data(), buffer_.data() + read_offset_, cache_size_);
  int read_size = file_wrapper_->Read(buffer_.data() + cache_size_,
                                      CACHE_SIZE - cache_size_);
  if (AdKconfUtil::enableReadOpt()) {
    if (read_size == 0) {
      // 数据已读完
      return false;
    }
    if (read_size < 0) {
      // 读数据异常
      LOG(FATAL) << "Read index source failed, errno=" << read_size;
    }
  } else {
    if (read_size <= 0) {
      return false;
    }
  }
  cache_size_ += read_size;
  read_offset_ = 0;
  return Valid();
}
}  // namespace index_base
}  // namespace index_builder
}  // namespace ks
