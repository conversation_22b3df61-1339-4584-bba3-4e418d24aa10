#pragma once
#include <string>
#include <unordered_map>
#include "base/file/file_path.h"
#include "base/file/file_util.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/meta.pb.h"

namespace ks {
namespace index_builder {
namespace index_base {
class ValidChecker {
 public:
  static ValidChecker* GetInstance() {
    static ValidChecker inst;
    return &inst;
  }
  // in case diff dir has file which has same name and diff content
  void InitDumpInfo(const kuaishou::ad::tables::DumpInfo dump_info) {
    for (auto info : dump_info.info()) {
      auto path = ::base::FilePath(info.file_name()).ToString();
      file2recordnum_[path] = info.record_num();
      LOG(INFO) << "path: " << path << " record_num: " << info.record_num();
    }
  }

  bool OutputValidCheck(const std::string hdfs_path, int64_t record_num) {
    if (file2recordnum_.find(hdfs_path) == file2recordnum_.end()) {
      return false;
    }
    // 那些没有 dump 的文件
    if (record_num == 0) {
      return true;
    }
    return file2recordnum_[hdfs_path] == record_num;
  }

  int64_t GetRecordNum(const std::string& hdfs_path) {
    return file2recordnum_[hdfs_path];
  }

 private:
  std::unordered_map<std::string, int64_t> file2recordnum_;
};

}  // namespace index_base
}  // namespace index_builder
}  // namespace ks
