#pragma once
#include <glog/logging.h>
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include "base/time/timestamp.h"
#include "perfutil/perfutil.h"
#include "teams/ad/index_builder/base/index_read/file_wrapper.h"
#include "teams/ad/index_builder/base/index_read/index_file_reader.h"
#include "teams/ad/index_builder/base/index_read/index_read_proto.pb.h"
#include "teams/ad/index_builder/base/index_read/index_wrapper.h"
#include "teams/ad/index_builder/base/index_read/valid_checker.h"
#include "teams/ad/index_builder/multiform_filter/proto_tools.h"
namespace ks {
namespace index_builder {
namespace index_base {
using ks::infra::PerfUtil;

class MessageStreamBase {
 public:
  explicit MessageStreamBase(std::string table_name)
      : table_name_(table_name) {}
  virtual ~MessageStreamBase() {}
  virtual kuaishou::ad::AdEnum::AdInstanceType MessageType() = 0;
  virtual std::string MessageName() = 0;
  virtual bool Valid() = 0;
  virtual const google::protobuf::Message* NextMessage() = 0;
  virtual const kuaishou::ad::AdInstance* NextAdInstance() = 0;
  virtual std::string DebugInfo() = 0;
  int64_t GetOutputRecordNum() { return record_num_; }
  virtual std::string GetFileName() = 0;
  std::string GetTableName() { return table_name_; }

 protected:
  std::string table_name_;
  int64_t record_num_{0};
  int64_t start_time_{::base::GetTimestamp() / 1000};
  int64_t end_time_{0};
};

class MessageStream : public MessageStreamBase {
 public:
  explicit MessageStream(std::string table_name)
      : MessageStreamBase(table_name) {}
  virtual ~MessageStream();
  void Init(ReaderType reader_type, TransType trans_type, std::string path,
            IndexType index_type,
            kuaishou::ad::AdEnum::AdInstanceType ad_type) {
    reader_base_ = IndexFileInstanceReaderBase::Create(reader_type);
    reader_base_->Init(trans_type, path, index_type, ad_type);
  }
  bool Init(ReaderType reader_type, TransType trans_type, std::string path,
            IndexType index_type, const std::string& proto_name) {
    std::string ad_instance_type =
        ks::index_builder::GetEnumNameByPbName(proto_name);
    kuaishou::ad::AdEnum::AdInstanceType ad_type;
    if (kuaishou::ad::AdEnum_AdInstanceType_Parse(ad_instance_type, &ad_type)) {
      Init(reader_type, trans_type, path, index_type, ad_type);
      return true;
    } else {
      LOG(ERROR) << "not supportted proto_name " << proto_name
                 << ", maybe is deleted or code needed update";
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "parse_instance_type_failed",
                                  ad_instance_type, "init_message_stream");
      return false;
    }
  }
  kuaishou::ad::AdEnum::AdInstanceType MessageType() override {
    return reader_base_->GetIndexHandle()->GetMsgType();
  }

  std::string MessageName() override {
    return reader_base_->GetIndexHandle()->GetExtendMessage()->GetTypeName();
  }
  bool Valid() override {
    auto ret = reader_base_->Valid();
    if (!ret) {
      end_time_ = ::base::GetTimestamp() / 1000;
    }
    return ret;
  }

  const google::protobuf::Message* NextMessage() override {
    return reader_base_->GetIndexHandle()->GetExtendMessage();
  }

  const kuaishou::ad::AdInstance* NextAdInstance() override {
    return reader_base_->GetIndexHandle()->GetIndexInstance();
  }

  std::string DebugInfo() override {
    return reader_base_->GetFileHandle()->GetPath();
  }
  std::string GetFileName() override {
    return reader_base_->GetFileHandle()->GetPath();
  }

 protected:
  std::shared_ptr<IndexFileInstanceReaderBase> reader_base_;
};

class LiteMessageStream : public MessageStream {
 public:
  explicit LiteMessageStream(std::string table_name)
      : MessageStream(table_name) {}
  void InitMuitFile(ReaderType reader_type, TransType trans_type,
                    std::vector<std::string> paths, IndexType index_type,
                    kuaishou::ad::AdEnum::AdInstanceType ad_type) {
    for (auto path : paths) {
      auto reader = IndexFileInstanceReaderBase::Create(reader_type);
      reader->Init(trans_type, path, index_type, ad_type);
      readers_.push_back(reader);
    }
    curr_index_ = 0;
  }
  bool InitMuitFile(ReaderType reader_type, TransType trans_type,
                    std::vector<std::string> paths, IndexType index_type,
                    const std::string& proto_name) {
    std::string ad_instance_type =
        ks::index_builder::GetEnumNameByPbName(proto_name);
    kuaishou::ad::AdEnum::AdInstanceType ad_type;
    if (kuaishou::ad::AdEnum_AdInstanceType_Parse(ad_instance_type, &ad_type)) {
      InitMuitFile(reader_type, trans_type, paths, index_type, ad_type);
      return true;
    } else {
      LOG(ERROR) << "not supportted proto_name " << proto_name
                 << ", maybe is deleted or code needed update";
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "parse_instance_type_failed",
                                  ad_instance_type, "init_message_stream_multi_file");
      return false;
    }
  }
  bool Valid() override {
    if (curr_index_ >= readers_.size()) {
      end_time_ = ::base::GetTimestamp() / 1000;
      return false;
    }
    auto ret = readers_[curr_index_]->Valid();
    if (ret) {
      return ret;
    }
    curr_index_++;
    return Valid();
  }

  const google::protobuf::Message* NextMessage() override {
    return readers_[curr_index_]->GetIndexHandle()->GetExtendMessage();
  }

  const kuaishou::ad::AdInstance* NextAdInstance() override {
    return readers_[curr_index_]->GetIndexHandle()->GetIndexInstance();
  }

 private:
  std::vector<std::shared_ptr<IndexFileInstanceReaderBase>> readers_;
  int32_t curr_index_{0};
};

using MessageStreamPtr = std::shared_ptr<MessageStreamBase>;
using MessageStreamMap =
    std::unordered_map<std::string, std::vector<MessageStreamPtr>>;

}  // namespace index_base
}  // namespace index_builder
}  // namespace ks
