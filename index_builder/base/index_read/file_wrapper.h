#pragma once
#include <glog/logging.h>
#include <memory>
#include <string>
#include <vector>
#include "base/file/file_stream.h"
#include "serving_base/hdfs_read/hdfs_file_stream.h"
#include "teams/ad/index_builder/base/index_read/index_read_proto.pb.h"

namespace ks {
namespace index_builder {
namespace index_base {

// 封装不同传输介质的处理方式
class FileWrapperBase {
 public:
  explicit FileWrapperBase(TransType trans_type) : trans_type_(trans_type) {}
  virtual ~FileWrapperBase() {}
  TransType GetType() const { return trans_type_; }
  void SetPath(const std::string& path) { path_ = path; }
  const std::string& GetPath() const { return path_; }
  virtual int32_t Read(char* ptr, int32_t size) = 0;
  std::string DebugInfo() { return TransType_Name(trans_type_); }
  static std::shared_ptr<FileWrapperBase> Create(TransType trans_type);
  virtual bool ReadToString(const std::string path, std::string* contents) = 0;
  virtual bool FindLastestSuccessVersion(const std::string& path,
                                         std::string* lastest_version,
                                         const std::string& suc_file) = 0;
  virtual bool FindLastestSuccessVersionList(const std::string& path,
                                             std::vector<std::string>* lastest_version,
                                             const std::string& suc_file) = 0;
  virtual void CleanIndex(const std::string& output, int keep_num) = 0;
 protected:
  TransType trans_type_;
  std::string path_;
};

// 对应本地文件
class LocalFileWrapper : public FileWrapperBase {
 public:
  LocalFileWrapper() : FileWrapperBase(KFS) {}
  int32_t Read(char* ptr, int32_t size) override;
  bool FindLastestSuccessVersion(const std::string& local_path,
                                 std::string* lastest_version,
                                 const std::string& suc_file) override;
  bool FindLastestSuccessVersionList(const std::string& path,
                                     std::vector<std::string>* lastest_version,
                                     const std::string& suc_file) override;
  bool ReadToString(const std::string path, std::string* contents) override;
  void CleanIndex(const std::string& output, int keep_num) override;
 private:
  std::unique_ptr<::base::FileStream> file_stream_;
};

// 对应 HDFS 文件
class HdfsFileWrapper : public FileWrapperBase {
 public:
  HdfsFileWrapper() : FileWrapperBase(HDFS) {}
  int32_t Read(char* ptr, int32_t size) override;
  bool FindLastestSuccessVersion(const std::string& hdfs_path,
                                 std::string* lastest_version,
                                 const std::string& suc_file) override;
  bool FindLastestSuccessVersionList(const std::string& path,
                                     std::vector<std::string>* lastest_version,
                                     const std::string& suc_file) override;
  bool ReadToString(const std::string path, std::string* contents) override;
  void CleanIndex(const std::string& output, int keep_num) override;
 private:
  std::unique_ptr<::hadoop::HDFSFileStream> hdfs_stream_;
};

}  // namespace index_base
}  // namespace index_builder
}  // namespace ks
