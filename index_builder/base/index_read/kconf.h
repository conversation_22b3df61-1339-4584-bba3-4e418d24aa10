#pragma once
#include "teams/ad/index_builder/base/index_read/index_read_proto.pb.h"
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
namespace ks {
namespace index_builder {
namespace index_base {
class Kconf {
 public:
  DEFINE_PROTOBUF_NODE_KCONF(IndexPathConfig, ad.index_builder,
                             indexPathConfig);
};
}  // namespace index_base
}  // namespace index_builder
}  // namespace ks
