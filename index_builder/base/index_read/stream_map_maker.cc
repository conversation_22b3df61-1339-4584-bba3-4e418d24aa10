#include "teams/ad/index_builder/base/index_read/stream_map_maker.h"
#include <algorithm>
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>
#include "absl/strings/match.h"
#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/meta.pb.h"
#include "teams/ad/index_builder/base/index_read/kconf.h"
#include "teams/ad/index_builder/base/index_read/valid_checker.h"
#include "teams/ad/index_builder/utils/table_config.h"
namespace ks {
namespace index_builder {
namespace index_base {

void CheckTableConfigCoverStatus(MessageStreamMap* input_stream_map) {
  std::set<std::string> table_name_set;
  std::set<std::string> miss_table_set;
  auto config = AdKconfUtil::tableConfig()->data();
  for (auto& item : config.table_dump_configs()) {
    table_name_set.insert(item.table_name());
  }
  for (const auto& item : *input_stream_map) {
    auto table_name = item.first;
    if (table_name_set.count(table_name) > 0) {
      PerfUtil::CountLogStash(1, "ad.index_adapter", "table_name_hit",
                              table_name);
      LOG(INFO) << table_name << " match";
    } else {
      PerfUtil::CountLogStash(1, "ad.index_adapter", "table_name_miss",
                              table_name);
      LOG(INFO) << table_name << " miss";
      miss_table_set.insert(table_name);
    }
  }
  // 不在 kconf 内的表不会读取
  for (auto table_name : miss_table_set) {
    input_stream_map->erase(table_name);
  }
}

bool StreamMapBase::IsVersionInTimeScope(const std::string& version,
                                         int64_t seconds) {
  std::string export_version = version;
  absl::Time t;
  std::string err;
  if (!absl::ParseTime(index_wrapper_->TimeFormat(), export_version,
                       absl::LocalTimeZone(), &t, &err)) {
    LOG(ERROR) << "absl::ParseTime(" << export_version
               << ") failed. err: " << err;
    return false;
  }
  // 如果最新版本距离当前时间超过 2 小时，说明版本过老
  if (absl::ToUnixSeconds(absl::Now()) - absl::ToUnixSeconds(t) > 7200) {
    LOG(ERROR) << "The latest version: " << export_version
               << " is also too old.";
    return false;
  }
  return true;
}

bool StreamMapBase::FindUpdateTableConfig() {
  const auto& input_dump_info = dump_info_;
  auto* output_dump_info = &table_dump_info_;
  std::set<std::string> dup_table;
  // temp_dump_info
  for (const auto& info : input_dump_info.info()) {
    const auto& table_name = info.table_name();
    const auto& proto_name = info.proto_name();
    if (dup_table.count(table_name) > 0) {
      continue;
    }
    dup_table.insert(table_name);
    auto* add_info = output_dump_info->add_info();
    add_info->CopyFrom(info);
    add_info->clear_file_name();
    add_info->clear_record_num();
  }
  return true;
}

MessageStreamPtr StreamMapBase::GetMessageStream(
    const kuaishou::ad::tables::DumpData& dump_data) {
  LOG(INFO) << "dump_data: " << dump_data.ShortDebugString();
  bool enable_mulfile = AdKconfUtil::enableOneTbMulFile();
  if (index_type_ == LITE || enable_mulfile) {
    return GetLiteMessageStream(dump_data);
  } else {
    return GetNormalMessageStream(dump_data);
  }
}
MessageStreamPtr StreamMapBase::GetLiteMessageStream(
    const kuaishou::ad::tables::DumpData& dump_data) {
  std::shared_ptr<LiteMessageStream> stream =
      std::make_shared<LiteMessageStream>(dump_data.table_name());
  // 简版索引 DumpInfo file_name 原始为空，hook table_name 作为标识
  stream->Init(reader_type_, trans_type_,
               base::FilePath(dump_data.file_name())
                   .Append(dump_data.table_name())
                   .ToString(),
               index_type_, dump_data.proto_name());
  std::vector<std::string> real_files;
  for (auto file_info : dump_data.files()) {
    real_files.push_back(file_info.file());
  }
  bool init = stream->InitMuitFile(reader_type_, trans_type_, real_files, index_type_,
                       dump_data.proto_name());
  return init ? stream : nullptr;
}
MessageStreamPtr StreamMapBase::GetNormalMessageStream(
    const kuaishou::ad::tables::DumpData& dump_data) {
  std::shared_ptr<MessageStream> stream =
      std::make_shared<MessageStream>(dump_data.table_name());
  bool init = stream->Init(reader_type_, trans_type_, dump_data.file_name(), index_type_,
                      dump_data.proto_name());
  return init ? stream : nullptr;
}

bool MutiPathStreamMap::CheckLastetValidVersion(std::string* latest_version) {
  const auto& input_dirs = stream_map_.input_dirs();
  bool enable_version_detect_opt = AdKconfUtil::enableVersionDetectOpt();
  if (enable_version_detect_opt) {
    std::unordered_map<std::string, int> version_num;
    bool bad_flag = false;
    int size = input_dirs.size();
    for (auto& dir_path : input_dirs) {
      std::vector<std::string> version_list;
      if (file_wrapper_->FindLastestSuccessVersionList(
              dir_path, &version_list, index_wrapper_->SuccessFlag())) {
        for (auto it : version_list) {
          version_num[it]++;
        }
      } else {
        bad_flag = true;
      }
    }
    std::string max_common_version;
    for (const auto& it : version_num) {
      if (it.second == size && it.first > max_common_version) {
        max_common_version = it.first;
      }
    }
    if (bad_flag || max_common_version.empty()) {
      LOG(INFO) << "not all dir reach same version, fail!";
      return false;
    }
    *latest_version = max_common_version;
  } else {
    std::set<std::string> all_versions;
    for (auto& dir_path : input_dirs) {
      std::string lastest_version;
      if (file_wrapper_->FindLastestSuccessVersion(
              dir_path, &lastest_version, index_wrapper_->SuccessFlag())) {
        all_versions.insert(lastest_version);
      } else {
        LOG(INFO) << "hdfs: " << dir_path << " not prepared! fail...";
        return false;
      }
    }
    if (all_versions.size() != 1) {
      LOG(INFO) << "not all dir reach same version, fail!";
      return false;
    }
    std::string export_version = *(all_versions.rbegin());
    *latest_version = export_version;
  }
  return true;
}

bool SinglePathStreamMap::CheckLastetValidVersion(std::string* latest_version) {
  return file_wrapper_->FindLastestSuccessVersion(
      path_, latest_version, index_wrapper_->SuccessFlag());
}
bool MutiPathStreamMap::InitMessageStreamMap(const std::string& version,
                                             MessageStreamMap* stream_map) {
  const auto& input_dirs = stream_map_.input_dirs();
  for (const auto& input_dir : input_dirs) {
    GetDumpInfoByPath(input_dir, version, &dump_info_);
  }
  LOG(INFO) << "total dump_info: " << dump_info_.ShortDebugString();

  FindUpdateTableConfig();

  LOG(INFO) << "table dump_info: " << table_dump_info_.ShortDebugString();

  if (table_dump_info_.info_size() == 0) {
    LOG(ERROR) << "CheckBenchmarkFiles failed";
    return false;
  }
  ks::index_builder::TableConfigManager::GetInstance()->InitByDumpInfo(
      table_dump_info_);
  ValidChecker::GetInstance()->InitDumpInfo(dump_info_);
  for (auto& info : dump_info_.info()) {
    auto table_name = info.table_name();
    MessageStreamPtr stream = GetMessageStream(info);
    if (stream) {
      (*stream_map)[table_name].emplace_back(stream);
    }
    LOG(INFO) << "single_table dump_info: " << info.ShortDebugString();
  }
  CheckTableConfigCoverStatus(stream_map);
  return true;
}

bool SinglePathStreamMap::InitMessageStreamMap(const std::string& version,
                                               MessageStreamMap* stream_map) {
  GetDumpInfoByPath(path_, version, &dump_info_);
  LOG(INFO) << "total dump_info: " << dump_info_.ShortDebugString();

  FindUpdateTableConfig();

  LOG(INFO) << "table dump_info: " << table_dump_info_.ShortDebugString();

  if (table_dump_info_.info_size() == 0) {
    LOG(ERROR) << "CheckBenchmarkFiles failed";
    return false;
  }
  ks::index_builder::TableConfigManager::GetInstance()->InitByDumpInfo(
      table_dump_info_);
  ValidChecker::GetInstance()->InitDumpInfo(dump_info_);
  for (auto& info : dump_info_.info()) {
    auto table_name = info.table_name();
    MessageStreamPtr stream = GetMessageStream(info);
    if (stream) {
      (*stream_map)[table_name].emplace_back(stream);
    }
    LOG(INFO) << "single_table dump_info: " << info.ShortDebugString();
  }
  CheckTableConfigCoverStatus(stream_map);
  return true;
}

bool MutiPathStreamMap::GetDumpInfoByPath(
    const std::string& path, const std::string& version,
    kuaishou::ad::tables::DumpInfo* dump_info) {
  const auto& forbid_input = stream_map_.forbid_input_hdfs();
  const auto& map_it = forbid_input.find(path);
  std::set<std::string> filter_tables_names;
  if (map_it != forbid_input.end()) {
    filter_tables_names.insert(forbid_input.at(path).tables().cbegin(),
                               forbid_input.at(path).tables().cend());
  }
  std::string content;
  if (!file_wrapper_->ReadToString(
          ::base::FilePath(path).Append(version).Append("dump_info").ToString(),
          &content)) {
    LOG(INFO) << " Read String failed:"
              << ::base::FilePath(path)
                     .Append(version)
                     .Append("dump_info")
                     .ToString();
  }
  const auto& redirections = stream_map_.redirections();

  kuaishou::ad::tables::DumpInfo temp_dump_info;
  auto status =
      google::protobuf::util::JsonStringToMessage(content, &temp_dump_info);
  if (!status.ok()) {
    LOG(ERROR) << "JsonStringToMessage failed. error: "
               << status.error_message() << ", dump_info: " << content;
    return false;
  }
  for (auto& temp_info : temp_dump_info.info()) {
    if (filter_tables_names.count(temp_info.table_name()) > 0) {
      LOG(INFO) << "dir: " << path << " table_name: " << temp_info.table_name()
                << " be filtered";
      continue;
    }
    auto* info = dump_info->add_info();
    info->CopyFrom(temp_info);
    info->set_file_name(::base::FilePath(path)
                            .Append(version)
                            .Append(temp_info.file_name())
                            .ToString());
    // 重定向修改表名
    if (redirections.count(temp_info.table_name()) > 0) {
      info->set_table_name(redirections.at(temp_info.table_name()));
    }
    if (index_type_ == LITE) {
      for (auto& file : *info->mutable_files()) {
        file.set_file(::base::FilePath(path)
                          .Append(version)
                          .Append(file.file())
                          .ToString());
      }
    }
    LOG(INFO) << "table_name: " << info->table_name()
              << " proto_name:" << info->proto_name()
              << " file_name: " << info->file_name();
  }

  // dump_info_ 中合并同名的且在同一个文件夹中的 table 文件路径到一个数组中
  bool enable_mulfile = AdKconfUtil::enableOneTbMulFile();
  if (enable_mulfile) {
    LOG(INFO) << "enable_mulfile = true";
    std::unordered_map<std::string, kuaishou::ad::tables::DumpData> name2dumpdata;
    for (auto& dump_data : dump_info->info()) {
      auto name = dump_data.table_name();
      auto itr = name2dumpdata.find(name);
      if (itr != name2dumpdata.end()) {
        // 已经存在该表
        itr->second.add_files()->set_file(dump_data.file_name());
      } else {
        // 不存在该表，插入
        kuaishou::ad::tables::DumpData data;
        data.set_file_name(::base::FilePath(path)
                            .Append(version)
                            .ToString());
        data.set_proto_name(dump_data.proto_name());
        data.set_table_name(dump_data.table_name());
        data.add_files()->set_file(dump_data.file_name());
        name2dumpdata.emplace(dump_data.table_name(), data);
      }
    }

    dump_info->clear_info();
    for (auto& p : name2dumpdata) {
      dump_info->add_info()->CopyFrom(p.second);
      LOG(INFO) << "after enable multifile : "
              << "table_name: " << p.second.table_name()
              << " proto_name:" << p.second.proto_name()
              << " file_name: " << p.second.file_name();
      for (auto& file : p.second.files()) {
        LOG(INFO) << "files : " << file.file();
      }
    }
  }

  return true;
}

bool SinglePathStreamMap::GetDumpInfoByPath(
    const std::string& path, const std::string& version,
    kuaishou::ad::tables::DumpInfo* dump_info) {
  std::string content;
  if (!file_wrapper_->ReadToString(
          ::base::FilePath(path).Append(version).Append("dump_info").ToString(),
          &content)) {
    LOG(INFO) << " Read String failed:"
              << ::base::FilePath(path)
                     .Append(version)
                     .Append("dump_info")
                     .ToString();
  }
  kuaishou::ad::tables::DumpInfo temp_dump_info;
  auto status =
      google::protobuf::util::JsonStringToMessage(content, &temp_dump_info);
  if (!status.ok()) {
    LOG(ERROR) << "JsonStringToMessage failed. error: "
               << status.error_message() << ", dump_info: " << content;
    return false;
  }
  for (auto& temp_info : temp_dump_info.info()) {
    auto* info = dump_info->add_info();
    info->CopyFrom(temp_info);
    info->set_file_name(::base::FilePath(path)
                            .Append(version)
                            .Append(temp_info.file_name())
                            .ToString());

    if (index_type_ == LITE) {
      for (auto& file : *info->mutable_files()) {
        file.set_file(::base::FilePath(path)
                          .Append(version)
                          .Append(file.file())
                          .ToString());
      }
    }
    LOG(INFO) << "table_name: " << info->table_name()
              << " proto_name:" << info->proto_name()
              << " file_name: " << info->file_name();
  }
  return true;
}

std::shared_ptr<StreamMapBase> StreamMapMaker::GetMutiPathStreamMap(
    TransType trans_type, ReaderType reader_type, IndexType index_type,
    ks::index_adapter::StreamMap stream_map) {
  std::shared_ptr<MutiPathStreamMap> streamMap =
      std::make_shared<MutiPathStreamMap>();
  streamMap->Init(trans_type, reader_type, index_type);
  streamMap->SetStreamMap(stream_map);
  LOG(INFO) << "StreamMapInfo: " << stream_map.ShortDebugString() << "  "
            << TransType_Name(trans_type) << "  "
            << ReaderType_Name(reader_type) << "  "
            << IndexType_Name(index_type);
  return streamMap;
}
std::shared_ptr<StreamMapBase> StreamMapMaker::GetSinglePathStreamMap(
    TransType trans_type, ReaderType reader_type, IndexType index_type,
    const std::string& path) {
  std::shared_ptr<SinglePathStreamMap> streamMap =
      std::make_shared<SinglePathStreamMap>();
  streamMap->Init(trans_type, reader_type, index_type);
  streamMap->SetPath(path);
  LOG(INFO) << "StreamMapInfo: " << path << "  " << TransType_Name(trans_type)
            << "  " << ReaderType_Name(reader_type) << "  "
            << IndexType_Name(index_type);
  return streamMap;
}

std::shared_ptr<StreamMapBase> StreamMapMaker::DetectMutiPathStreamMap(
    ReaderType reader_type, ks::index_adapter::StreamMap stream_map) {
  return GetMutiPathStreamMap(
      GetTransType(stream_map.input_dirs()[0]), reader_type,
      GetIndexType(stream_map.input_dirs()[0]), stream_map);
}
std::shared_ptr<StreamMapBase> StreamMapMaker::DetectSinglePathStreamMap(
    ReaderType reader_type, const std::string& path) {
  return GetSinglePathStreamMap(GetTransType(path), reader_type,
                                GetIndexType(path), path);
}

IndexType StreamMapMaker::GetIndexType(const std::string& path) {
  auto config = Kconf::indexPathConfig()->data();
  std::vector<std::pair<std::string, IndexType>> prefixs;

  for (auto& f : config.das_prefix()) {
    if (absl::StartsWith(path, f)) {
      prefixs.push_back({f, DAS});
    }
  }
  for (auto& f : config.target_prefix()) {
    if (absl::StartsWith(path, f)) {
      prefixs.push_back({f, TARGET});
    }
  }
  for (auto& f : config.lite_prefix()) {
    if (absl::StartsWith(path, f)) {
      prefixs.push_back({f, LITE});
    }
  }
  std::sort(prefixs.begin(), prefixs.end(),
            [](const std::pair<std::string, IndexType>& lhs,
               const std::pair<std::string, IndexType>& rhs) {
              return lhs.first < rhs.first;
            });  // 升序
  if (prefixs.empty()) {
    LOG(FATAL) << "Cannot find configed index prefix" << path;
  }
  return prefixs.back().second;
}

TransType StreamMapMaker::GetTransType(const std::string& path) {
  auto config = Kconf::indexPathConfig()->data();
  std::vector<std::pair<std::string, TransType>> prefixs;

  for (auto& f : config.local_prefix()) {
    if (absl::StartsWith(path, f)) {
      prefixs.push_back({f, KFS});
    }
  }
  for (auto& f : config.hdfs_prefix()) {
    if (absl::StartsWith(path, f)) {
      prefixs.push_back({f, HDFS});
    }
  }
  std::sort(prefixs.begin(), prefixs.end(),
            [](const std::pair<std::string, TransType>& lhs,
               const std::pair<std::string, TransType>& rhs) {
              return lhs.first < rhs.first;
            });  // 升序
  if (prefixs.empty()) {
    LOG(FATAL) << "Cannot find configed trans prefix" << path;
  }
  return prefixs.back().second;
}

}  // namespace index_base
}  // namespace index_builder
}  // namespace ks
