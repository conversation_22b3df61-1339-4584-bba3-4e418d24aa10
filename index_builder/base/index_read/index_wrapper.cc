#include "teams/ad/index_builder/base/index_read/index_wrapper.h"
#include <memory>
namespace ks {
namespace index_builder {
namespace index_base {

std::shared_ptr<IndexBase> IndexBase::Create(IndexType index_type) {
  if (index_type == DAS) {
    return std::make_shared<DasIndex>();
  } else if (index_type == TARGET) {
    return std::make_shared<TargetIndex>();
  } else {
    return std::make_shared<LiteIndex>();
  }
}

void IndexBase::SetMsgType(kuaishou::ad::AdEnum::AdInstanceType ad_type) {
  msg_type_ = ad_type;

  if (msg_type_ ==
      kuaishou::ad::AdEnum_AdInstanceType_UNKNOWN_AD_INSTANCE_TYPE) {
    return;
  }
  default_instance_.set_type(msg_type_);
  auto* ad_desc = default_instance_.GetDescriptor();

  int field_number = ad_desc->extension_range(0)->start + msg_type_;
  auto* desc_pool = google::protobuf::DescriptorPool::generated_pool();
  auto* field_desc = desc_pool->FindExtensionByNumber(ad_desc, field_number);
  if (!field_desc) {
    LOG(ERROR) << "AdInstanceType [" << msg_type_ << "] maybe invalid";
    return;
  }

  auto* ad_reflect = default_instance_.GetReflection();
  base_msg_ = ad_reflect->MutableMessage(&default_instance_, field_desc);
  LOG(INFO) << "SetBaseMessage| type: " << msg_type_ << " -> "
            << base_msg_->GetTypeName();
}
}  // namespace index_base
}  // namespace index_builder
}  // namespace ks
