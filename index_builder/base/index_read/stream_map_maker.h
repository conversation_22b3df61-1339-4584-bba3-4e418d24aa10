#pragma once
#include <string>
#include <memory>
#include "teams/ad/ad_proto/kuaishou/ad/tables/meta.pb.h"
#include "teams/ad/index_adapter/utils/kconf/kconf.pb.h"
#include "teams/ad/index_builder/base/index_read/message_stream.h"
namespace ks {
namespace index_builder {
namespace index_base {

void CheckTableConfigCoverStatus(MessageStreamMap* input_stream_map);

class StreamMapBase {
 public:
  StreamMapBase() = default;
  virtual ~StreamMapBase() {}
  virtual bool CheckLastetValidVersion(std::string* latest_version) = 0;
  virtual bool InitMessageStreamMap(const std::string& version,
                                    MessageStreamMap* stream_map) = 0;
  // 获取的版本是否在时间范围内 False: 已过时，考虑丢弃
  bool IsVersionInTimeScope(const std::string& version, int64_t seconds);
  virtual bool GetDumpInfoByPath(const std::string& path,
                                 const std::string& version,
                                 kuaishou::ad::tables::DumpInfo* dump_info) = 0;
  void Init(TransType trans_type, ReaderType reader_type,
            IndexType index_type) {
    trans_type_ = trans_type;
    reader_type_ = reader_type;
    index_type_ = index_type;
    index_wrapper_ = IndexBase::Create(index_type);
    file_wrapper_ = FileWrapperBase::Create(trans_type);
  }
  // 返回 dump_info
  kuaishou::ad::tables::DumpInfo GetDumpInfo() { return dump_info_; }
  IndexBase* GetIndexHandle() { return index_wrapper_.get(); }
  FileWrapperBase* GetFileHandle() { return file_wrapper_.get(); }

 protected:
  MessageStreamPtr GetMessageStream(
      const kuaishou::ad::tables::DumpData& dump_data);
  MessageStreamPtr GetLiteMessageStream(
      const kuaishou::ad::tables::DumpData& dump_data);
  MessageStreamPtr GetNormalMessageStream(
      const kuaishou::ad::tables::DumpData& dump_data);
  bool FindUpdateTableConfig();

 protected:
  kuaishou::ad::tables::DumpInfo dump_info_;
  kuaishou::ad::tables::DumpInfo table_dump_info_;
  IndexType index_type_;
  TransType trans_type_;
  ReaderType reader_type_;
  std::shared_ptr<IndexBase> index_wrapper_;
  std::shared_ptr<FileWrapperBase> file_wrapper_;
};

class MutiPathStreamMap : public StreamMapBase {
 public:
  void SetStreamMap(ks::index_adapter::StreamMap stream_map) {
    stream_map_ = stream_map;
  }
  bool CheckLastetValidVersion(std::string* latest_version) override;
  bool InitMessageStreamMap(const std::string& version,
                            MessageStreamMap* stream_map) override;
  bool GetDumpInfoByPath(const std::string& path, const std::string& version,
                         kuaishou::ad::tables::DumpInfo* dump_info) override;

 private:
  ks::index_adapter::StreamMap stream_map_;
};
class SinglePathStreamMap : public StreamMapBase {
 public:
  void SetPath(const std::string path) { path_ = path; }
  bool CheckLastetValidVersion(std::string* latest_version) override;
  bool InitMessageStreamMap(const std::string& version,
                            MessageStreamMap* stream_map) override;
  bool GetDumpInfoByPath(const std::string& path, const std::string& version,
                         kuaishou::ad::tables::DumpInfo* dump_info) override;

 private:
  std::string path_;
};

class StreamMapMaker {
 public:
  std::shared_ptr<StreamMapBase> GetMutiPathStreamMap(
      TransType trans_type, ReaderType reader_type, IndexType index_type,
      ks::index_adapter::StreamMap stream_map);
  std::shared_ptr<StreamMapBase> GetSinglePathStreamMap(
      TransType trans_type, ReaderType reader_type, IndexType index_type,
      const std::string& path);
  // 通过 Kconf 推断 KFS HDFS  以及索引格式
  std::shared_ptr<StreamMapBase> DetectMutiPathStreamMap(
      ReaderType reader_type, ks::index_adapter::StreamMap stream_map);
  std::shared_ptr<StreamMapBase> DetectSinglePathStreamMap(
      ReaderType reader_type, const std::string& path);

  IndexType GetIndexType(const std::string& path);
  TransType GetTransType(const std::string& path);
};

}  // namespace index_base
}  // namespace index_builder
}  // namespace ks
