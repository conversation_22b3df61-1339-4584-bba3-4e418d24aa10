#include "teams/ad/index_builder/base/index_read/file_wrapper.h"
#include <algorithm>
#include <set>
#include <utility>
#include <vector>
#include "base/file/dir_reader_posix.h"
#include "base/file/file_path.h"
#include "base/file/file_util.h"
#include "serving_base/hdfs_read/hdfs_file_util.h"

namespace ks {
namespace index_builder {
namespace index_base {

int32_t LocalFileWrapper::Read(char* ptr, int32_t size) {
  if (file_stream_.get() == nullptr) {
    file_stream_.reset(new ::base::FileStream());
    int flags = ::base::PLATFORM_FILE_OPEN | ::base::PLATFORM_FILE_READ;
    if (file_stream_->Open(path_, flags) != 0) {
      LOG(ERROR) << "Open local file failed. Path :" << path_;
      return -1;
    }
  }
  return file_stream_->Read(ptr, size);
}
int32_t HdfsFileWrapper::Read(char* ptr, int32_t size) {
  if (hdfs_stream_.get() == nullptr) {
    hdfs_stream_.reset(
        new hadoop::HDFSFileStream(hadoop::FLAGS_hadoop_namenode_ip.c_str(),
                                   hadoop::FLAGS_hadoop_namenode_port));
    if (!hdfs_stream_->Open(path_.c_str(), O_RDONLY)) {
      LOG(ERROR) << "Open hdfs failed. Path:" << path_;
      return -1;
    }
  }
  return hdfs_stream_->Read(ptr, size);
}
bool LocalFileWrapper::ReadToString(const std::string path,
                                    std::string* contents) {
  return ::base::file_util::ReadFileToString(::base::FilePath(path), contents);
}

bool HdfsFileWrapper::ReadToString(const std::string path,
                                   std::string* contents) {
  hadoop::HDFSFileStream hdfs_stream(hadoop::FLAGS_hadoop_namenode_ip.c_str(),
                                     hadoop::FLAGS_hadoop_namenode_port);
  if (!hdfs_stream.Open(path.c_str(), O_RDONLY)) {
    LOG(ERROR) << "hdfs_stream.Open(" << path << ") failed";
    return false;
  }
  int BUF_SIZE = 1 << 21, offset = 0;
  std::vector<char> buf;
  buf.resize(BUF_SIZE);
  while (true) {
    int read_size = hdfs_stream.Read(buf.data() + offset, BUF_SIZE - offset);
    if (read_size == 0) {
      break;
    } else {
      offset += read_size;
      if (offset >= BUF_SIZE / 2) {
        BUF_SIZE = (BUF_SIZE << 1);
        buf.resize(BUF_SIZE);
        LOG(INFO) << "buff to Read " << path << "  Need Resize";
      }
    }
  }
  std::string temp(buf.data(), offset);
  *contents = temp;
  return true;
}

bool HdfsFileWrapper::FindLastestSuccessVersion(const std::string& hdfs_path,
                                                std::string* lastest_version,
                                                const std::string& suc_file) {
  if (lastest_version == nullptr) {
    return false;
  }
  std::vector<hadoop::HDFSPathInfo> hdfs_files;
  if (!hadoop::HDFSListDirectory(hdfs_path.c_str(), &hdfs_files)) {
    LOG(ERROR) << "hadoop::HDFSListDirectory(" << hdfs_path << ") failed.";
    return false;
  }
  std::sort(hdfs_files.begin(), hdfs_files.end(),
            [](const hadoop::HDFSPathInfo& a, const hadoop::HDFSPathInfo& b) {
              return a.type > b.type || (a.type == b.type && a.name > b.name);
            });
  for (auto hdfs_file : hdfs_files) {
    if (hdfs_file.type != hadoop::kDirectory) {
      LOG(ERROR) << "Invalid latest path: " << hdfs_file.name
                 << ", it's not a directory.";
      continue;
    }

    std::string path = hdfs_file.name;
    if (path.back() == '/') {
      path.pop_back();
    }
    std::string flag_file = path + "/" + suc_file;
    if (!hadoop::HDFSExists(flag_file.c_str())) {
      LOG(ERROR) << "flag_file: " << flag_file
                 << " does not exist, maybe not prepared.";
      continue;
    }
    size_t pos = path.rfind('/');
    *lastest_version = path.substr(pos + 1);
    return true;
  }
  return false;
}

bool HdfsFileWrapper::FindLastestSuccessVersionList(const std::string& hdfs_path,
                                                    std::vector<std::string>* lastest_version,
                                                    const std::string& suc_file) {
  if (lastest_version == nullptr) {
    return false;
  }
  std::vector<hadoop::HDFSPathInfo> hdfs_files;
  if (!hadoop::HDFSListDirectory(hdfs_path.c_str(), &hdfs_files)) {
    LOG(ERROR) << "hadoop::HDFSListDirectory(" << hdfs_path << ") failed.";
    return false;
  }
  std::sort(hdfs_files.begin(), hdfs_files.end(),
            [](const hadoop::HDFSPathInfo& a, const hadoop::HDFSPathInfo& b) {
              return a.type > b.type || (a.type == b.type && a.name > b.name);
            });
  int limit = 100, count = 0;
  for (auto hdfs_file : hdfs_files) {
    if (hdfs_file.type != hadoop::kDirectory) {
      LOG(ERROR) << "Invalid latest path: " << hdfs_file.name
                 << ", it's not a directory.";
      continue;
    }
    if (++count > limit) {
      break;
    }

    std::string path = hdfs_file.name;
    if (path.back() == '/') {
      path.pop_back();
    }
    std::string flag_file = path + "/" + suc_file;
    if (!hadoop::HDFSExists(flag_file.c_str())) {
      LOG(WARNING) << "flag_file: " << flag_file
                   << " does not exist, maybe not prepared.";
      continue;
    }
    size_t pos = path.rfind('/');
    std::string version = path.substr(pos + 1);
    lastest_version->push_back(version);
  }
  return !lastest_version->empty();
}
bool LocalFileWrapper::FindLastestSuccessVersion(const std::string& local_path,
                                                 std::string* lastest_version,
                                                 const std::string& suc_file) {
  if (lastest_version == nullptr) {
    return false;
  }
  std::vector<std::string> elems;
  ::base::DirReaderPosix dir_reader(local_path.c_str());
  std::string dot_dir = ::base::FilePath(local_path).Append(".").ToString();
  std::string dot_dot_dir =
      ::base::FilePath(local_path).Append("..").ToString();
  std::set<std::string> black_paths{dot_dir, dot_dot_dir};
  while (dir_reader.Next()) {
    auto curr = ::base::FilePath(local_path.c_str())
                    .Append(dir_reader.name())
                    .ToString();
    if (black_paths.count(curr) != 0) continue;
    elems.push_back(::base::FilePath(local_path.c_str())
                        .Append(dir_reader.name())
                        .ToString());
  }
  if (elems.empty()) {
    LOG(INFO) << "Find No Version ";
    return false;
  }
  std::vector<std::pair<std::string, ::base::PlatformFileInfo>> infos;
  for (auto& elem : elems) {
    ::base::PlatformFileInfo temp_info;
    if (::base::file_util::GetFileInfo(elem, &temp_info)) {
      infos.emplace_back(elem, temp_info);
    } else {
      LOG(ERROR) << "GetFileInfo [" << elem << "] failed";
    }
  }
  std::sort(infos.begin(), infos.end(),
            [](const std::pair<std::string, ::base::PlatformFileInfo>& a,
               const std::pair<std::string, ::base::PlatformFileInfo>& b) {
              return a.second.is_directory > b.second.is_directory ||
                     (a.second.is_directory == b.second.is_directory &&
                      a.first > b.first);
            });

  for (auto info : infos) {
    if (!info.second.is_directory) {
      LOG(ERROR) << "Invalid latest path: " << info.first
                 << ", it's not a directory.";
      continue;
    }

    std::string path = info.first;
    if (path.back() == '/') {
      path.pop_back();
    }
    std::string flag_file = path + "/" + suc_file;
    if (!::base::file_util::PathExists(flag_file)) {
      LOG(ERROR) << "flag_file: " << flag_file
                 << " does not exist, maybe not prepared.";
      continue;
    }
    size_t pos = path.rfind('/');
    *lastest_version = path.substr(pos + 1);
    return true;
  }
  return false;
}

bool LocalFileWrapper::FindLastestSuccessVersionList(const std::string& local_path,
                                                     std::vector<std::string>* lastest_version,
                                                     const std::string& suc_file) {
  if (lastest_version == nullptr) {
    return false;
  }
  std::vector<std::string> elems;
  ::base::DirReaderPosix dir_reader(local_path.c_str());
  std::string dot_dir = ::base::FilePath(local_path).Append(".").ToString();
  std::string dot_dot_dir =
      ::base::FilePath(local_path).Append("..").ToString();
  std::set<std::string> black_paths{dot_dir, dot_dot_dir};
  while (dir_reader.Next()) {
    auto curr = ::base::FilePath(local_path.c_str())
                    .Append(dir_reader.name())
                    .ToString();
    if (black_paths.count(curr) != 0) continue;
    elems.push_back(::base::FilePath(local_path.c_str())
                        .Append(dir_reader.name())
                        .ToString());
  }
  if (elems.empty()) {
    LOG(INFO) << "Find No Version ";
    return false;
  }
  std::vector<std::pair<std::string, ::base::PlatformFileInfo>> infos;
  for (auto& elem : elems) {
    ::base::PlatformFileInfo temp_info;
    if (::base::file_util::GetFileInfo(elem, &temp_info)) {
      infos.emplace_back(elem, temp_info);
    } else {
      LOG(ERROR) << "GetFileInfo [" << elem << "] failed";
    }
  }
  std::sort(infos.begin(), infos.end(),
            [](const std::pair<std::string, ::base::PlatformFileInfo>& a,
               const std::pair<std::string, ::base::PlatformFileInfo>& b) {
              return a.second.is_directory > b.second.is_directory ||
                     (a.second.is_directory == b.second.is_directory &&
                      a.first > b.first);
            });

  for (auto info : infos) {
    if (!info.second.is_directory) {
      LOG(ERROR) << "Invalid latest path: " << info.first
                 << ", it's not a directory.";
      continue;
    }

    std::string path = info.first;
    if (path.back() == '/') {
      path.pop_back();
    }
    std::string flag_file = path + "/" + suc_file;
    if (!::base::file_util::PathExists(flag_file)) {
      LOG(ERROR) << "flag_file: " << flag_file
                 << " does not exist, maybe not prepared.";
      continue;
    }
    size_t pos = path.rfind('/');
    std::string version = path.substr(pos + 1);
    lastest_version->push_back(version);
  }
  return !lastest_version->empty();
}

std::shared_ptr<FileWrapperBase> FileWrapperBase::Create(TransType trans_type) {
  if (trans_type == HDFS) {
    return std::make_shared<HdfsFileWrapper>();
  } else {
    return std::make_shared<LocalFileWrapper>();
  }
}

void LocalFileWrapper::CleanIndex(const std::string& output,
                                       int keep_num) {
  using ::base::file_util::FileEnumerator;
  FileEnumerator file_enumerator(output, false, FileEnumerator::DIRECTORIES);
  std::vector<std::string> directories;
  for (auto path = file_enumerator.Next(); !path.value().empty();
       path = file_enumerator.Next()) {
    directories.push_back(path.value());
  }

  if (directories.size() < keep_num) {
    return;
  }

  std::sort(
      directories.begin(), directories.end(),
      [](const std::string& a, const std::string& b) -> bool { return a < b; });

  while (directories.size() > keep_num) {
    std::string&& delete_path = std::string(directories.front());
    LOG(INFO) << "delete_dir: " << delete_path;
    ::base::file_util::Delete(::base::FilePath(delete_path), true);
    directories.erase(directories.begin());
  }
}

void HdfsFileWrapper::CleanIndex(const std::string& hdfs_path, int keep_num) {
  std::vector<hadoop::HDFSPathInfo> hdfs_files;
  if (!hadoop::HDFSListDirectory(hdfs_path.c_str(), &hdfs_files)) {
    LOG(ERROR) << "hadoop::HDFSListDirectory(" << hdfs_path << ") failed.";
    return;
  }
  std::sort(hdfs_files.begin(), hdfs_files.end(),
            [](const hadoop::HDFSPathInfo& a, const hadoop::HDFSPathInfo& b) {
              return a.type > b.type || (a.type == b.type && a.name > b.name);
            });
  static const int64_t reserved_num = keep_num;
  int64_t success_num = 0;
  int64_t delete_num = 0;
  for (auto hdfs_file : hdfs_files) {
    std::string path = hdfs_file.name;
    if (hdfs_file.type != hadoop::kDirectory) {
      LOG(ERROR) << "Invalid latest path: " << hdfs_file.name
                 << ", it's not a directory. Delete It!";
      LOG(INFO) << "Path: " << path << " delete!";
      bool ret = hadoop::HDFSRmr(path.c_str());
      delete_num++;
      if (delete_num > 20) {
        return;
      }
      continue;
    }
    if (path.back() == '/') {
      path.pop_back();
    }
    std::string flag_file = path + "/_SUCCESS";
    if (!hadoop::HDFSExists(flag_file.c_str())) {
      LOG(ERROR) << "flag_file: " << flag_file
                 << " does not exist, maybe not prepared.";
      LOG(INFO) << "Path: " << path << " delete!";
      bool ret = hadoop::HDFSRmr(path.c_str());
      delete_num++;
      if (delete_num > 20) {
        return;
      }
      continue;
    }
    success_num++;
    if (success_num > reserved_num) {
      bool ret = hadoop::HDFSRmr(path.c_str());
      delete_num++;
      if (delete_num > 20) {
        return;
      }
      continue;
    }
  }
  return;
}

}  // namespace index_base
}  // namespace index_builder
}  // namespace ks
