#pragma once

#include <functional>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/multiform_filter/common_tools.h"

namespace ks {
namespace index_builder {
/*
  note: offer function for extension format
*/

int32_t RemoveUselessField(
    kuaishou::ad::AdInstance* ad_instance,
    std::function<bool(std::string)> expected_func);

bool IsModifyFieldConcernedByTarget(
    kuaishou::ad::AdInstance* ad_instance,
    std::function<bool(std::string)> expected_func);

/*
  横级联项目， 如果 support_info 有效则原主键有设置值
*/
void AccountMarkUpdate(kuaishou::ad::AdInstance* ad_instance);

void AccountMarkUpdate(google::protobuf::Message* message);

// 保留必要字段 用于 Creative 强制转成下线状态
int32_t OfflineCreativeClearField(kuaishou::ad::AdInstance* ad_instance);

// 用于横级联统一正确性验证，例如 unit_support_info 验证数据是否有效
void TableValidate(kuaishou::ad::AdInstance* ad_instance);

/*
  双层结构（横级联），清除指定字段
*/
int32_t RemovePointedField(google::protobuf::Message* message,
                          const std::unordered_set<std::string>&  delete_fields);

bool IsWinfoInBlacklist(kuaishou::ad::AdInstance* ad_instance);
}  // namespace index_builder
}  // namespace ks
