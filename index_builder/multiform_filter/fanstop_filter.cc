#include "teams/ad/index_builder/multiform_filter/fanstop_filter.h"

#include <set>
#include <memory>
#include <string>
#include "perfutil/perfutil.h"
#include "teams/ad/ad_index/index/utils/ad_index_helper.h"
#include "teams/ad/engine_base/material_feature_type/material_feature_type.h"
#include "teams/ad/index_builder/multiform_filter/common_tools.h"
#include "teams/ad/index_builder/multiform_filter/amd_filter.h"
#include "teams/ad/index_builder/multiform_filter/multiform_kconf.h"
#include "teams/ad/index_builder/multiform_filter/search_filter.h"
#include "teams/ad/index_builder/multiform_filter/sf_micro.h"

namespace ks {
namespace index_builder {

using ks::infra::PerfUtil;
using kuaishou::ad::AdEnum;

using namespace kuaishou::ad::tables;  // NOLINT
using ks::ad_server::AdIndexHelper;
static std::set<AdEnum::AdInstanceType> fanstop_check_types = {
    kuaishou::ad::AdEnum::CREATIVE, kuaishou::ad::AdEnum::UNIT,
    kuaishou::ad::AdEnum::CAMPAIGN, kuaishou::ad::AdEnum::ACCOUNT};
static std::set<std::string> fanstop_check_types_sf = {
    "ad_dsp_creative", "ad_dsp_unit", "ad_dsp_campaign", "ad_dsp_account"};

bool IsFanstopLiveCampaign(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  LOG_ASSERT(table_name == "ad_dsp_campaign")
      << "IsFanstopLiveCampaign unexpected_type: " << table_name;
  auto type = static_cast<AdEnum::CampaignType>(table_row->GetValue<int32_t>("type"));
  return type == AdEnum::AD_FANSTOP_LIVE_TO_FANS
      || type == AdEnum::AD_FANSTOP_LIVE_TO_SHOW
      || type == AdEnum::AD_FANSTOP_LIVE_TO_ALL;
}
bool IsFanstopLiveCampaign(const kuaishou::ad::tables::Campaign& campaign) {
  return campaign.type() == AdEnum::AD_FANSTOP_LIVE_TO_FANS
      || campaign.type() == AdEnum::AD_FANSTOP_LIVE_TO_SHOW
      || campaign.type() == AdEnum::AD_FANSTOP_LIVE_TO_ALL;
}

bool IsFansTopDataImp(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  LOG_ASSERT(fanstop_check_types_sf.count(table_name) != 0)
      << "unexpected_type: " << table_name;
  using kuaishou::ad::AdEnum;
  if (table_name == "ad_dsp_creative") {
    auto creative_feature = static_cast<AdEnum::AdDspCreativeFeature>(
                              table_row->GetValue<int32_t>("creative_feature"));
    if (creative_feature != AdEnum::CREATIVE_FANSTOP_MERCHANT
        && creative_feature != AdEnum::CREATIVE_FANSTOP_NO_MERCHANT
        && creative_feature != AdEnum::CREATIVE_ESP_MOBILE_FEATURE
        && creative_feature != AdEnum::CREATIVE_ESP_MOBILE_FEATURE_NO_MERCHANT) {
      return false;
    }
    // 高光聚合创意不进索引
    auto creative_type = static_cast<AdEnum::AdDspCreativeType>(
                              table_row->GetValue<int32_t>("creative_type"));
    if (creative_type == AdEnum::ESP_HIGHLIGHT_FLASH) {
      return false;
    }
  } else if (table_name == "ad_dsp_unit") {
    auto unit_feature = static_cast<AdEnum::AdDspUnitFeature>(
                              table_row->GetValue<int32_t>("unit_feature"));
    if (unit_feature != AdEnum::FANSTOP_MERCHANT
        && unit_feature != AdEnum::FANSTOP_NO_MERCHANT
        && unit_feature != AdEnum::UNIT_ESP_MOBILE_FEATURE
        && unit_feature != AdEnum::UNIT_ESP_MOBILE_FEATURE_NO_MERCHANT) {
      return false;
    }
  } else if (table_name == "ad_dsp_campaign") {
    auto type = static_cast<AdEnum::CampaignType>(
                              table_row->GetValue<int32_t>("type"));
    if (type != kuaishou::ad::AdEnum::CampaignType::
                    AdEnum_CampaignType_AD_FANSTOP_TO_FANS &&
        type != kuaishou::ad::AdEnum::CampaignType::
                    AdEnum_CampaignType_AD_FANSTOP_TO_ALL &&
        type != kuaishou::ad::AdEnum::CampaignType::
                    AdEnum_CampaignType_AD_FANSTOP_TO_SHOW &&
        !IsFanstopLiveCampaign(table_row)) {
      return false;
    }
  } else if (table_name == "ad_dsp_account") {
    auto account_type = static_cast<AdEnum::AdDspAccountType>(
                              table_row->GetValue<int32_t>("account_type"));
    if (account_type != kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 &&
        account_type != AdEnum::ACCOUNT_ESP_MOBILE) {
      return false;
    }
  }
  return true;
}
bool IsFansTopDataImp(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  LOG_ASSERT(fanstop_check_types.count(msg_type) != 0)
      << "unexpected_type: " << msg_type;
  auto* pb = ad;
  using kuaishou::ad::AdEnum;
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    const Creative& creative = pb->GetExtension(Creative::creative_old);
    auto creative_feature = creative.creative_feature();
    if (creative_feature != AdEnum::CREATIVE_FANSTOP_MERCHANT
        && creative_feature != AdEnum::CREATIVE_FANSTOP_NO_MERCHANT
        && creative_feature != AdEnum::CREATIVE_ESP_MOBILE_FEATURE
        && creative_feature != AdEnum::CREATIVE_ESP_MOBILE_FEATURE_NO_MERCHANT) {
      return false;
    }
    // 高光聚合创意不进索引
    if (creative.creative_type() == AdEnum::ESP_HIGHLIGHT_FLASH) {
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = pb->GetExtension(Unit::unit_old);
    auto unit_feature = unit.unit_feature();
    if (unit_feature != AdEnum::FANSTOP_MERCHANT
        && unit_feature != AdEnum::FANSTOP_NO_MERCHANT
        && unit_feature != AdEnum::UNIT_ESP_MOBILE_FEATURE
        && unit_feature != AdEnum::UNIT_ESP_MOBILE_FEATURE_NO_MERCHANT) {
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::CAMPAIGN) {
    const Campaign& campaign = pb->GetExtension(Campaign::campaign_old);
    auto type = campaign.type();
    if (type != kuaishou::ad::AdEnum::CampaignType::
                    AdEnum_CampaignType_AD_FANSTOP_TO_FANS &&
        type != kuaishou::ad::AdEnum::CampaignType::
                    AdEnum_CampaignType_AD_FANSTOP_TO_ALL &&
        type != kuaishou::ad::AdEnum::CampaignType::
                    AdEnum_CampaignType_AD_FANSTOP_TO_SHOW &&
        !IsFanstopLiveCampaign(campaign)) {
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::ACCOUNT) {
    const Account& account = pb->GetExtension(Account::account_old);
    auto account_type = account.account_type();

    if (account_type != kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 &&
        account_type != AdEnum::ACCOUNT_ESP_MOBILE) {
      return false;
    }
  }
  return true;
}

/*
  粉条加入金牛极速数据
*/
bool IsFansTopData(const ks::ad::build_service::TableRow* table_row) {
  // 本地推物料
  if (IsLocalPromote(table_row)) {
    return true;
  }
  // 搜索直投智能扩量物料
  if (MultiFormKconfUtil::enableSearchMingtouToInnerOuter() && IsExtendSearchData(table_row)) {
    return true;
  }
  // 搜索直投人群追投物料
  if (MultiFormKconfUtil::enableSearchRetargetingToInnerOuter() && IsSearchPopulationRetargeting(table_row)) {
    return true;
  }
  CHECKTABLEROW(table_row)
  bool ret = true;
  if (IsNotFanstopBudget(table_row)) {
    return false;
  }
  // auto bid
  if (IsNotFanstopWtAutoBidData(table_row)) {
    return false;
  }
  if (ret && fanstop_check_types_sf.count(table_name) != 0) {
    ret &= IsFansTopDataImp(table_row);
  }

  if (table_name == "ad_dsp_account") {
    auto account_type = static_cast<AdEnum::AdDspAccountType>(
                              table_row->GetValue<int32_t>("account_type"));
    auto id = table_row->GetValue<int64_t>("id");

    // 聚星流量助推 2.0
    if (account_type == kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_SOCIAL) {
      return true;
    }

    // B 端粉条白名单
    if (MultiFormKconfUtil::BusinessFanstopAccountWhitelist()->count(id)) {
      return true;
    }
    // 陆续放量，控制风险
    // todo[zrk] 这里的开关是不是可以下了
    if (account_type == kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_CPC) {
      if (id % 100 < MultiFormKconfUtil::businessFanstopAccountAdmitTailNum()) {
        return true;
      }
    }
  }
  return ret || IsEspFlashOnlyData(table_row) || IsEspSpecialOnlyData(table_row);
}
bool IsFansTopData(const kuaishou::ad::AdInstance* ad) {
  // 本地推物料
  if (IsLocalPromote(ad)) {
    return true;
  }
  // 搜索直投智能扩量物料
  if (MultiFormKconfUtil::enableSearchMingtouToInnerOuter() && IsExtendSearchData(ad)) {
    return true;
  }
  // 搜索直投人群追投物料
  if (MultiFormKconfUtil::enableSearchRetargetingToInnerOuter() && IsSearchPopulationRetargeting(ad)) {
    return true;
  }
  auto msg_type = ad->type();
  bool ret = true;
  if (ret && fanstop_check_types.count(msg_type) != 0) {
    ret &= IsFansTopDataImp(ad);
  }

  if (msg_type == kuaishou::ad::AdEnum::ACCOUNT) {
    const Account& account = ad->GetExtension(Account::account_old);

    // 聚星流量助推 2.0
    if (account.account_type() == kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_SOCIAL) {
      return true;
    }

    // B 端粉条白名单
    if (MultiFormKconfUtil::BusinessFanstopAccountWhitelist()->count(account.id())) {
      return true;
    }
    // 陆续放量，控制风险
    if (account.account_type() == kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_CPC) {
      if (account.id() % 100 < MultiFormKconfUtil::businessFanstopAccountAdmitTailNum()) {
        return true;
      }
    }
  }
  return ret || IsEspFlashOnlyData(ad) || IsEspSpecialOnlyData(ad);
}

bool IsNotFanstopData(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  bool ret = true;
  if (ret && fanstop_check_types_sf.count(table_name) != 0) {
    ret &= !IsFansTopDataImp(table_row);
  }
  return ret;
}
bool IsNotFanstopData(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  bool ret = true;
  if (ret && fanstop_check_types.count(msg_type) != 0) {
    ret &= !IsFansTopDataImp(ad);
  }
  return ret;
}

bool IsNotFanstopWtAutoBidData(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (table_name == "wt_auto_bid_unit" || table_name == "wt_auto_bid_campaign") {
    int32_t campaign_type = table_row->GetValue<int32_t>("campaign_type");
    int64_t group_tag = table_row->GetValue<int64_t>("group_tag");
    if (group_tag != 5 ||
        !(campaign_type == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE ||
        campaign_type == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE ||
        campaign_type == kuaishou::ad::AdEnum::AD_FANSTOP_TO_FANS ||
        campaign_type == kuaishou::ad::AdEnum::AD_FANSTOP_TO_SHOW ||
        campaign_type == kuaishou::ad::AdEnum::AD_FANSTOP_TO_ALL ||
        campaign_type == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_FANS ||
        campaign_type == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_SHOW ||
        campaign_type == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_ALL ||
        campaign_type == kuaishou::ad::AdEnum::FANS_LIVE_STREAM_PROMOTE)) {
      LOG_FIRST_N(INFO, 10) << "IsNotFanstopWtAutoBidData";
      return true;
    }
  }
  return false;
}

bool IsFansTopOnly(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (fanstop_check_types_sf.count(table_name) != 0) {
    return IsFansTopDataImp(table_row);
  }
  return false;
}
bool IsFansTopOnly(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (fanstop_check_types.count(msg_type) != 0) {
    return IsFansTopDataImp(ad);
  }
  return false;
}

bool IsNotFanstopBudget(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (table_name == "wt_ad_budget_status") {
    auto unit_feature = static_cast<AdEnum::AdDspUnitFeature>(
                                  table_row->GetValue<int32_t>("unit_feature"));
    return !ks::engine_base::IsUnitInternalCirculation(unit_feature);
  }
  return false;
}

bool IsFanstopBudget(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  bool ret = true;
  if (ret && fanstop_check_types.count(msg_type) != 0) {
    ret &= IsFansTopDataImp(ad);
  }
  if (!ret && ad->type() == kuaishou::ad::AdEnum::ACCOUNT) {
    const Account &account = ad->GetExtension(Account::account_old);
    auto account_type = account.account_type();

    // B 端粉条是 ACCOUNT_CPC
    // 聚星流量助推是 ACCOUNT_SOCIAL
    if (account_type == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
        account_type == AdEnum::ACCOUNT_ESP_MOBILE || account_type == AdEnum::ACCOUNT_CPC ||
        account_type == AdEnum::ACCOUNT_SOCIAL) {
      ret = true;
    }
  }
  return ret;
}

bool IsInnerFanstop(const kuaishou::ad::AdInstance* ad) {
  if (ad == nullptr) {
    return false;
  }
  bool ret = IsFansTopOnly(ad);
  if (ret) {
    if (ad->type() == kuaishou::ad::AdEnum::CAMPAIGN) {
      const Campaign& campaign = ad->GetExtension(Campaign::campaign_old);
      if (campaign.has_campaign_fanstop_support_info() &&
          campaign.campaign_fanstop_support_info().is_inner_delivery()) {
        ret = true;
      } else {
        ret = false;
      }
    }
  }
  return ret;
}
}  // namespace index_builder
}  // namespace ks
