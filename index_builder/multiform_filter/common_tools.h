#pragma once
#include <string>
#include <vector>
#include <set>
#include <memory>
#include <unordered_map>
#include "absl/strings/substitute.h"
#include "glog/logging.h"
#include "google/protobuf/reflection.h"
#include "teams/ad/ad_base/src/common/basis_enum_types.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/multiform_filter/proto_tools.h"

using ks::ad_base::DeployType;

namespace ks {
namespace index_builder {

class DeployVariable {
 public:
  static std::string GetKwsName() {
    static std::string kws_name =
        getenv("KWS_SERVICE_NAME") == NULL ? "" : getenv("KWS_SERVICE_NAME");
    return kws_name;
  }
  static std::string GetPodName() {
    static std::string pod_name =
        getenv("MY_POD_NAME") == NULL ? "" : getenv("MY_POD_NAME");
    return pod_name;
  }
};

// CUCA return account_id, others try to return primary key
int64_t GetAccountIdOfAdInstance(kuaishou::ad::AdInstance* ad_instance);

// only cuca, else return -1
int64_t GetAccountIdOfAdInstanceV2(kuaishou::ad::AdInstance* ad_instance);

int64_t GetAccountIdOfAdInstanceV3(kuaishou::ad::AdInstance* ad_instance);

int64_t GetValueByFieldName(kuaishou::ad::AdInstance* ad_instance,
                            std::string field_name);
int64_t GetValueByFieldNameV2(kuaishou::ad::AdInstance* ad_instance,
                              std::string field_name);

void GetAllFieldNames(kuaishou::ad::AdInstance* ad_instance, std::set<std::string>* name_set);

static std::string GetPbTypeStr(kuaishou::ad::AdEnum::AdInstanceType type) {
  std::string str_enum = AdEnum_AdInstanceType_Name(type);
  str_enum = str_enum.empty() ? absl::Substitute("unknown type $0",
                                                 static_cast<int32_t>(type))
                              : str_enum;
  return str_enum;
}

// 复杂域解析
void TableParserProcess(kuaishou::ad::AdInstance* ad);

bool IsCreativeHotOnly(kuaishou::ad::AdInstance* ad);

// 只判断本身状态，不考虑 creative_score
// put_status != 1  review_status not in (2,5,6) 即认为下线 creative
bool IsCreativeOffline(kuaishou::ad::AdInstance* ad_instance);

//  程序化创意判断下线
bool IsCreativeProgrammedOffline(kuaishou::ad::AdInstance* ad_instance);

//  得到 ad_instance 可以放置的部署 目前只支持 creative
std::set<std::string> GetAllowedDeploy(const kuaishou::ad::AdInstance* ad_instance);

std::string GetTypeString(kuaishou::ad::AdInstance* ad);


}  // namespace index_builder
}  // namespace ks

