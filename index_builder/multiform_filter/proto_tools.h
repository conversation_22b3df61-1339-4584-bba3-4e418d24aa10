#pragma once

#include <string>
#include <unordered_map>

#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"

namespace ks {
namespace index_builder {
/*
  ad_instance_type = enums.AdInstanceType
  PbName = message类型 如 ad.tables.Creative
*/
// 从 enum str 到 pb str
static int32_t GetAdInstanceType(const std::string& ad_instance_type) {
  kuaishou::ad::AdEnum::AdInstanceType type;
  if (!kuaishou::ad::AdEnum_AdInstanceType_Parse(ad_instance_type, &type)) {
    return 0;
  }
  return static_cast<int32_t>(type);
}

std::string GetPbNameByAdInstanceType(const std::string& ad_instance_type);

std::string GetEnumNameByPbName(const std::string& pb_name);

void InitEnum2Type();
/*
  note:
    将横级联 AdInstance 拆分
    如 creative 将拆分成 creative + creative_support_info,
    传入convert_config_map = {"kuaishou.ad.tables.Creative.creative_support_info",
      "kuaishou.ad.tables.CreativeSupportInfo"}
  *传入值不做更改*
*/
std::vector<std::shared_ptr<kuaishou::ad::AdInstance>> AdInstanceSplit(
    const kuaishou::ad::AdInstance* ad_instance,
    const std::unordered_map<std::string, std::string>& convert_config_map);

/*
  note:
    target 字段白名单
    计算 md5 并填充到 unit
*/
void TargetIdCalculate(std::vector<std::shared_ptr<kuaishou::ad::AdInstance>>* ptr_vec);

}  // namespace index_builder

}  // namespace ks
