#include "teams/ad/index_builder/multiform_filter/search_filter.h"

#include <string>

#include "teams/ad/ad_index/index/utils/ad_index_helper.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/multiform_filter/sf_micro.h"

namespace ks {
namespace index_builder {

using namespace kuaishou::ad::tables;  // NOLINT

bool IsSearchOnlyData(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (table_name == "ad_dsp_creative") {
    auto ad_type = table_row->GetValue<int32_t>("ad_type");
    if (ad_type == AdType::search) {
      return true;
    }
  } else if (table_name == "ad_dsp_unit") {
    // todo[zrk] 这里的 ad_type 在 hub 配置中错误，先写成 ad_type 之后再 check 下哈
    auto ad_type = table_row->GetValue<int32_t>("ad_type");
    if (ad_type == AdType::search) {
      return true;
    }
  } else if (table_name == "ad_dsp_campaign") {
    auto ad_type = table_row->GetValue<int32_t>("ad_type");
    if (ad_type == AdType::search) {
      return true;
    }
  } else if (table_name == "ad_dsp_winfo") {
    return true;
  } else if (table_name == "ad_dsp_negative_word") {
    return true;
  }
  return false;
}
bool IsSearchOnlyData(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    const Creative& creative = ad->GetExtension(Creative::creative_old);
    if (creative.creative_support_info().ad_type() == AdType::search) {
      return true;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = ad->GetExtension(Unit::unit_old);
    if (unit.unit_support_info().ad_type() == AdType::search) {
      return true;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::CAMPAIGN) {
    const Campaign& campaign = ad->GetExtension(Campaign::campaign_old);
    if (campaign.ad_type() == AdType::search) {
      return true;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::AD_DSP_WINFO) {
    return true;
  } else if (msg_type == kuaishou::ad::AdEnum::AD_DSP_NEGATIVE_WORD) {
    return true;
  }
  return false;
}

// 搜索明投专业版智能扩量
bool IsExtendSearchData(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (!IsSearchOnlyData(table_row)) {
    return false;
  }
  if (table_name == "ad_dsp_unit") {
    // NOTE(weiyilong): 原 pb message 里是 int32，但 hub & 召回 配置是 int64
    auto extend_search = table_row->GetValue<int64_t>("extend_search");
    if (extend_search != 1) {
      return false;
    }
  }
  // 其他层级上无法区分
  return true;
}
bool IsExtendSearchData(const kuaishou::ad::AdInstance* ad) {
  if (!IsSearchOnlyData(ad)) {
    return false;
  }
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = ad->GetExtension(Unit::unit_old);
    if (unit.unit_support_info().extend_search() != 1) {
      return false;
    }
  }
  // 其他层级上无法区分
  return true;
}

// 搜索明投专业版人群追投
bool IsSearchPopulationRetargeting(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (!IsSearchOnlyData(table_row)) {
    return false;
  }
  if (table_name == "ad_dsp_unit") {
    auto search_population_retargeting = table_row->GetValue<int32_t>("search_population_retargeting");
    if (search_population_retargeting != 1) {
      return false;
    }
  }
  // 其他层级上无法区分
  return true;
}
bool IsSearchPopulationRetargeting(const kuaishou::ad::AdInstance* ad) {
  if (!IsSearchOnlyData(ad)) {
    return false;
  }
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = ad->GetExtension(Unit::unit_old);
    if (unit.unit_support_info().search_population_retargeting() != 1) {
      return false;
    }
  }
  // 其他层级上无法区分
  return true;
}

}  // namespace index_builder
}  // namespace ks
