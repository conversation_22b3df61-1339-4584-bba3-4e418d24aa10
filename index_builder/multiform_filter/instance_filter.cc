#include "teams/ad/index_builder/multiform_filter/instance_filter.h"
#include <algorithm>
#include <set>
#include <string>
#include <unordered_map>
#include <limits>
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/multiform_filter/multiform_kconf.h"
#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"
#include "teams/ad/index_builder/utils/cache_loader/account_mark_no_smr.h"
#include "teams//ad/index_builder/utils/cache_loader/search_winfo_backlist.h"
#include "teams/ad/dpa_server/dpa_index/level/creative.h"
#include "teams/ad/dpa_server/dpa_index/level/unit.h"
using kuaishou::ad::AdEnum;

namespace ks {
namespace index_builder {

int32_t RemoveUselessField(kuaishou::ad::AdInstance* ad_instance,
                           std::function<bool(std::string)> expected_func) {
  int32_t ret = 0;
  auto* message = GetExtensionField(ad_instance);
  if (!message) {
    return ret;
  }
  auto* ref = message->GetReflection();
  auto* desc = message->GetDescriptor();
  for (int32_t i = 0; i < desc->field_count(); ++i) {
    auto* field_desc = desc->field(i);
    if (!expected_func(field_desc->name())) {
      ref->ClearField(message, field_desc);
      ret++;
    }
  }
  return ret;
}

bool IsModifyFieldConcernedByTarget(
    kuaishou::ad::AdInstance* ad_instance,
    std::function<bool(std::string)> expected_func) {
  std::set<std::string> modify_fields;
  const auto& modify_field_list = ad_instance->modify_field_list();
  modify_fields.insert(modify_field_list.cbegin(), modify_field_list.cend());
  const auto v_it =
      std::find_if(modify_field_list.cbegin(), modify_field_list.cend(),
                   [&](const std::string& modify_field) -> bool {
                     return expected_func(modify_field);
                   });
  return v_it == modify_field_list.cend();
}

void UnitSupportInfoValidate(kuaishou::ad::AdInstance* ad_instance) {
  if (ad_instance->type() == AdEnum::UNIT) {
    auto* message = GetExtensionField(ad_instance);
    if (!message) return;
    auto* unit = dynamic_cast<kuaishou::ad::tables::Unit*>(message);
    auto* unit_support_info = unit->mutable_unit_support_info();

    if (unit_support_info->explore_budget() != 0 ||
        !unit_support_info->ks_order_id().empty() ||
        unit_support_info->conversion_type() != 0 ||
        !unit_support_info->site_quick_app_url().empty() ||
        unit_support_info->adv_card_type() > 0 ||
        unit_support_info->merchandise_type() > 0 ||
        unit_support_info->fiction_id() > 0 ||
        unit_support_info->consult_id() > 0 ||
        !unit_support_info->strategy_info().empty() ||
        unit_support_info->explore_budget_status() != 0 ||
        unit_support_info->support_type() != 0 ||
        unit_support_info->unit_add_on_feature() != 0 ||
        unit_support_info->live_user_id() != 0 ||
        unit_support_info->put_type() != 0 ||
        unit_support_info->bid_strategy() != 0 ||
        unit_support_info->mcb_value_type() != 0 ||
        unit_support_info->playable_switch() != 0 ||
        unit_support_info->playable_orientation() != -1 ||
        unit_support_info->playable_id() != 0 ||
        unit_support_info->extend_search() > 0 ||
        unit_support_info->quick_search() > 0 ||
        unit_support_info->target_explore() > 0 ||
        unit_support_info->dpa_industry_id() > 0 ||
        unit_support_info->enhance_conversion_type() > 0 ||
        unit_support_info->target_type() > 0 ) {
      unit_support_info->set_unit_id(unit->id());
    } else {
      unit_support_info->set_unit_id(0);
    }
  }
}

void AccountSupportInfoValidate(kuaishou::ad::AdInstance* ad_instance) {
  if (ad_instance->type() == AdEnum::ACCOUNT) {
    auto* message = GetExtensionField(ad_instance);
    if (!message) {
      return;
    }
    auto account = dynamic_cast<kuaishou::ad::tables::Account*>(message);
    auto* account_support_info = account->mutable_account_support_info();
    if (account_support_info->dup_photo_ratio() > std::numeric_limits<float>::epsilon() ||
        account_support_info->small_shop_account_label() != 0 ||
        (!account_support_info->key_action_switch().empty() &&
         account_support_info->key_action_switch() != "[]")) {
      account_support_info->set_account_id(account->id());
    }
  }
}

void TableValidate(kuaishou::ad::AdInstance* ad_instance) {
  UnitSupportInfoValidate(ad_instance);
  AccountSupportInfoValidate(ad_instance);
}

// 保留必要字段 可复用所有下线情况
int32_t OfflineCreativeClearField(kuaishou::ad::AdInstance* ad_instance) {  // NOLINT
  auto lambda_func = [](const std::string& value) -> bool {
    static const std::set<std::string> reversed_field{
        "id", "unit_id", "account_id", "campaign_id"};
    return reversed_field.count(value) > 0;
  };
  return RemoveUselessField(ad_instance, lambda_func);
}

int32_t RemovePointedField(
    google::protobuf::Message* message,
    const std::unordered_set<std::string>& delete_fields) {
  int32_t ret = 0;
  auto* ref = message->GetReflection();
  auto* desc = message->GetDescriptor();

  for (int32_t i = 0; i < desc->field_count(); ++i) {
    auto* field_desc = desc->field(i);
    if (delete_fields.count(field_desc->name()) > 0) {
      ref->ClearField(message, field_desc);
      ret++;
    }
    // 清除第二层字段
    if (field_desc->is_repeated()) {
      continue;
    }
    if (field_desc->cpp_type() ==
        google::protobuf::FieldDescriptor::CPPTYPE_MESSAGE) {
      google::protobuf::Message* sub_message =
          ref->MutableMessage(message, field_desc);
      auto* sub_ref = sub_message->GetReflection();
      auto* sub_desc = sub_message->GetDescriptor();
      for (int32_t sub_i = 0; sub_i < sub_desc->field_count(); ++sub_i) {
        auto* sub_field_desc = sub_desc->field(sub_i);
        if (delete_fields.count(sub_field_desc->name()) > 0) {
          sub_ref->ClearField(sub_message, sub_field_desc);
          ret++;
        }
      }
    }
  }
  return ret;
}

void AccountMarkUpdate(kuaishou::ad::AdInstance* ad_instance) {
  if (ad_instance->type() == AdEnum::ACCOUNT) {
    auto* message = GetExtensionField(ad_instance);
    if (!message) return;
    auto* account = dynamic_cast<kuaishou::ad::tables::Account*>(message);

    int64_t account_mark =
        AccountMarkTransform::GetInstance()->GetAccountMarkByAccountId(
            account->id());
    account->set_account_mark(static_cast<AdEnum::AccountMark>(account_mark));
  }
}

void AccountMarkUpdate(google::protobuf::Message* message) {
  if (message->GetTypeName() == "kuaishou.ad.tables.Account") {
    auto* account = dynamic_cast<kuaishou::ad::tables::Account*>(message);
    int64_t account_mark =
        AccountMarkTransform::GetInstance()->GetAccountMarkByAccountId(
            account->id());
    account->set_account_mark(static_cast<AdEnum::AccountMark>(account_mark));
  }
}

bool IsWinfoInBlacklist(kuaishou::ad::AdInstance* ad_instance) {
  if (ad_instance->type() == AdEnum::AD_DSP_WINFO) {
    auto* message = GetExtensionField(ad_instance);
    if (!message) return false;
    auto* winfo = dynamic_cast<kuaishou::ad::tables::AdDspWinfo*>(message);
    if (!winfo) return false;
    return SearchWinfoBlacklist::GetInstance().IsInBlacklist(winfo->winfo_id());
  }
  return false;
}

}  // namespace index_builder
}  // namespace ks
