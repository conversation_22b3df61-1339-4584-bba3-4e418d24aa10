#include "teams/ad/index_builder/multiform_filter/adapter_creative_filter.h"

#include <set>
#include <memory>
#include <string>
#include <unordered_set>
#include "perfutil/perfutil.h"
#include "teams/ad/ad_index/index/utils/ad_index_helper.h"
#include "teams/ad/index_builder/multiform_filter/common_tools.h"
#include "teams/ad/index_builder/multiform_filter/sf_micro.h"
namespace ks {
namespace index_builder {

using ks::infra::PerfUtil;
using kuaishou::ad::AdEnum;

using namespace kuaishou::ad::tables;  // NOLINT
using ks::ad_server::AdIndexHelper;
static std::set<AdEnum::AdInstanceType> adapter_creative_check_types = {
    kuaishou::ad::AdEnum::CREATIVE, kuaishou::ad::AdEnum::MATERIAL,
    kuaishou::ad::AdEnum::PHOTO_STATUS};
static std::set<std::string> adapter_creative_check_types_sf = {
    "ad_dsp_creative", "ad_dsp_material", "ad_dsp_photo"};

static const char* kws_name = getenv("KWS_SERVICE_NAME");
static const char* pod_name = getenv("MY_POD_NAME");

bool IsAdapterCreativeImp(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  LOG_ASSERT(adapter_creative_check_types_sf.count(table_name) != 0)
      << "unexpected_type: " << table_name;
  if (table_name == "ad_dsp_creative" || table_name == "ad_dsp_material" || table_name == "ad_dsp_photo") {
    auto derivative_creative_flag = table_row->GetValue<bool>("derivative_creative_flag");
    if (!derivative_creative_flag) {
      return false;
    }
  }
  return true;
}
bool IsAdapterCreativeImp(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  LOG_ASSERT(adapter_creative_check_types.count(msg_type) != 0)
      << "unexpected_type: " << msg_type;
  auto* pb = ad;
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    const Creative& creative = pb->GetExtension(Creative::creative_old);
    auto derivative_creative_flag = creative.derivative_creative_flag();
    if (!derivative_creative_flag) {
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::MATERIAL) {
    const Material& material = pb->GetExtension(Material::material_old);
    auto derivative_creative_flag = material.derivative_creative_flag();
    if (!derivative_creative_flag) {
      return false;
    }
  } else if (msg_type ==  kuaishou::ad::AdEnum::PHOTO_STATUS) {
    const PhotoStatus& photo_status = pb->GetExtension(PhotoStatus::photo_status_old);
    auto derivative_creative_flag = photo_status.derivative_creative_flag();
    if (!derivative_creative_flag) {
      return false;
    }
  }
  return true;
}

bool IsAdapterCreative(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  bool ret = true;
  if (ret && adapter_creative_check_types.count(msg_type) != 0) {
    ret &= IsAdapterCreativeImp(ad);
  }
  return ret;
}

bool IsNotAdapterCreative(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  bool ret = true;
  if (ret && adapter_creative_check_types.count(msg_type) != 0) {
    ret &= !IsAdapterCreativeImp(ad);
  }
  return ret;
}
bool IsAdapterCreativeOnly(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (adapter_creative_check_types_sf.count(table_name)) {
    return IsAdapterCreativeImp(table_row);
  }
  return false;
}
bool IsAdapterCreativeOnly(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (adapter_creative_check_types.count(msg_type)) {
    return IsAdapterCreativeImp(ad);
  }
  return false;
}

bool IsAdapterCreativeKnewOnly(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type != kuaishou::ad::AdEnum::CREATIVE) {
    return false;
  }
  const Creative& creative = ad->GetExtension(Creative::creative_old);
  auto derivative_creative_flag = creative.derivative_creative_flag();
  auto biz_id = creative.biz_id();
  static const std::unordered_set<int> biz_id_type_set{2, 4, 5};
  return derivative_creative_flag && biz_id_type_set.count(biz_id) > 0;
}

bool IsAdapterCreativeUniverseOnly(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type != kuaishou::ad::AdEnum::CREATIVE) {
    return false;
  }
  const Creative& creative = ad->GetExtension(Creative::creative_old);
  auto derivative_creative_flag = creative.derivative_creative_flag();
  auto biz_id = creative.biz_id();
  // TODO(caoyifan03): 确认小程序应该走联盟还是 knews
  return derivative_creative_flag && biz_id == 1;
}

}  // namespace index_builder
}  // namespace ks
