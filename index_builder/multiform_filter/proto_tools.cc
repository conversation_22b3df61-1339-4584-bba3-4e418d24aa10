#include "teams/ad/index_builder/multiform_filter/proto_tools.h"

#include <vector>
#include <unordered_map>
#include <set>
#include <memory>
#include "base/hash_function/city.h"
#include "glog/logging.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/multiform_filter/multiform_kconf.h"

namespace ks {
namespace index_builder {

using kuaishou::ad::AdEnum;

static std::unordered_map<int, std::string> enum2type{};
static std::unordered_map<std::string, int> type2enum{};

// 为了减少依赖，直接 copy 一份代码
class TargetKeyConvertor {
 public:
  int64_t operator()(const std::string& str) const {
    uint64_t uint64_sign = base::CityHash64(str.data(), str.size());
    return *(reinterpret_cast<int64_t*>(&uint64_sign));
  }
};
static const TargetKeyConvertor inner_convertor;

// 为了减少依赖，直接 copy 一份代码
static google::protobuf::Message* GetExtensionField(kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  auto* ad_desc = ad->GetDescriptor();
  int field_number = ad_desc->extension_range(0)->start + msg_type;
  auto* desc_pool = google::protobuf::DescriptorPool::generated_pool();
  auto* field_desc = desc_pool->FindExtensionByNumber(ad_desc, field_number);
  if (!field_desc) {
    return nullptr;
  }
  auto* ref = ad->GetReflection();
  return ref->MutableMessage(ad, field_desc);
}

static void ClearExtensionField(kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  auto* ad_desc = ad->GetDescriptor();
  int field_number = ad_desc->extension_range(0)->start + msg_type;
  auto* desc_pool = google::protobuf::DescriptorPool::generated_pool();
  auto* field_desc = desc_pool->FindExtensionByNumber(ad_desc, field_number);
  if (!field_desc) {
    return;
  }
  auto* ad_ref = ad->GetReflection();
  ad_ref->ClearField(ad, field_desc);
}

// 为了减少依赖，直接 copy 一份代码
static int32_t RemoveUselessField(kuaishou::ad::AdInstance* ad_instance,
                                  std::function<bool(std::string)> expected_func) {
  int32_t ret = 0;
  auto* message = GetExtensionField(ad_instance);
  if (!message) {
    return ret;
  }
  auto* ref = message->GetReflection();
  auto* desc = message->GetDescriptor();
  for (int32_t i = 0; i < desc->field_count(); ++i) {
    auto* field_desc = desc->field(i);
    if (!expected_func(field_desc->name())) {
      ref->ClearField(message, field_desc);
      ret++;
    }
  }
  return ret;
}

std::string GetPbNameByAdInstanceType(const std::string& ad_instance_type) {
  int enum_int = GetAdInstanceType(ad_instance_type);
  if (enum_int == 0) {
    return std::string();
  }
  auto pb_type_it = enum2type.find(enum_int);
  if (pb_type_it != enum2type.end()) {
    return pb_type_it->second;
  }
  return std::string();
}

std::string GetEnumNameByPbName(const std::string& pb_name) {
  auto pb_type_it = type2enum.find(pb_name);
  if (pb_type_it != type2enum.end()) {
    return kuaishou::ad::AdEnum::AdInstanceType_Name(
        static_cast<kuaishou::ad::AdEnum::AdInstanceType>(pb_type_it->second));
  } else {
    return std::string();
  }
}

void InitEnum2Type() {
  std::set<int> valid_message_type_set{};
  {
    auto * desc = google::protobuf::GetEnumDescriptor<kuaishou::ad::AdEnum::AdInstanceType>();
    for (int i = 0; i < desc->value_count(); ++i) {
      valid_message_type_set.insert(desc->value(i)->number());
    }
  }

  kuaishou::ad::AdInstance ad;
  auto* desc = ad.GetDescriptor();
  auto* ref = ad.GetReflection();
  static const auto* desc_pool = google::protobuf::DescriptorPool::generated_pool();
  for (auto valid_type : valid_message_type_set) {
    // LOG(INFO) << "valid_type: " << valid_type;
    auto* extension_desc =
        desc_pool->FindExtensionByNumber(desc, valid_type + desc->extension_range(0)->start);
    if (!extension_desc) {
      continue;
    }
    auto sub_message = ref->MutableMessage(&ad, extension_desc);
    std::string type_name = sub_message->GetTypeName();
    enum2type.emplace(valid_type, type_name);
    type2enum.emplace(type_name, valid_type);
    // LOG(INFO) << valid_type << " type: " << type_name;
  }
}

std::vector<std::shared_ptr<kuaishou::ad::AdInstance>> AdInstanceSplit(
    const kuaishou::ad::AdInstance* ad_instance,
    const std::unordered_map<std::string, std::string>& convert_config_map) {
  std::vector<std::shared_ptr<kuaishou::ad::AdInstance>> ret;
  std::shared_ptr<kuaishou::ad::AdInstance> tmp_ad_instance =
      std::shared_ptr<kuaishou::ad::AdInstance>(new kuaishou::ad::AdInstance);
  tmp_ad_instance->CopyFrom(*ad_instance);
  auto* message = GetExtensionField(tmp_ad_instance.get());
  const auto* actual_ref = message->GetReflection();
  const auto* actual_desc = message->GetDescriptor();
  ret.push_back(tmp_ad_instance);
  for (int i = 0; i < actual_desc->field_count(); i++) {
    auto* field_desc = actual_desc->field(i);
    if (field_desc->is_repeated()) continue;
    if (field_desc->cpp_type() != google::protobuf::FieldDescriptor::CPPTYPE_MESSAGE) continue;
    auto full_name = field_desc->full_name();
    if (convert_config_map.count(full_name)) {
      auto detail_type_str = convert_config_map.at(full_name);
      std::shared_ptr<kuaishou::ad::AdInstance> split_sub_ad =
          std::shared_ptr<kuaishou::ad::AdInstance>(new kuaishou::ad::AdInstance);
      split_sub_ad->CopyFrom(*tmp_ad_instance);
      kuaishou::ad::AdEnum::AdInstanceType type =
          static_cast<kuaishou::ad::AdEnum::AdInstanceType>(type2enum[detail_type_str]);
      ClearExtensionField(split_sub_ad.get());
      split_sub_ad->set_type(type);
      auto* sub_message = GetExtensionField(split_sub_ad.get());
      auto* detail_message = actual_ref->MutableMessage(message, field_desc);
      sub_message->CopyFrom(*detail_message);
      actual_ref->ClearField(message, field_desc);
      ret.push_back(split_sub_ad);
    }
  }
  TargetIdCalculate(&ret);
  return ret;
}

void TargetIdCalculate(std::vector<std::shared_ptr<kuaishou::ad::AdInstance>>* ptr_vec) {
  if (!ptr_vec || ptr_vec->size() < 2) {
    return;
  }
  if (ptr_vec->front()->type() != AdEnum::UNIT) {
    return;
  }
  std::shared_ptr<kuaishou::ad::AdInstance> unit_ptr;
  std::shared_ptr<kuaishou::ad::AdInstance> target_ptr;
  for (auto ad_ptr : *ptr_vec) {
    if (ad_ptr->type() == AdEnum::UNIT) {
      unit_ptr = ad_ptr;
    }
    if (ad_ptr->type() == AdEnum::TARGET) {
      target_ptr = ad_ptr;
    }
  }
  std::function<bool(std::string)> white_list_func = [](std::string field_name) {
    return MultiFormKconfUtil::targetWhiteList()->count(field_name);
  };
  if (!target_ptr || !unit_ptr) {
    return;
  }
  // TargetWhitList
  RemoveUselessField(target_ptr.get(), white_list_func);
  auto* target_message = GetExtensionField(target_ptr.get());
  auto* target = dynamic_cast<kuaishou::ad::tables::Target*>(target_message);
  target->set_id(0);
  auto target_id = inner_convertor(target->SerializeAsString());
  target_id = (target_id > 0) ? target_id : -target_id;
  target->set_id(target_id);
  LOG_EVERY_N(INFO, 10000) << "target: " << target_ptr->ShortDebugString();
  auto* unit_message = GetExtensionField(unit_ptr.get());
  auto* unit = dynamic_cast<kuaishou::ad::tables::Unit*>(unit_message);
  unit->set_origin_target_id(unit->target_id());
  unit->set_target_id(target_id);
}

}  // namespace index_builder
}  // namespace ks
