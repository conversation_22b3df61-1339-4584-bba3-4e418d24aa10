
#pragma once
#include <string>
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/index_builder/multiform_filter/multiform_kconf.pb.h"
namespace ks {
namespace index_builder {

using namespace ks::ad_base::kconf;  // NOLINT

class MultiFormKconfUtil {
 public:
  // target 白名单
  DEFINE_SET_NODE_KCONF(std::string, ad.index_message_proxy, targetWhiteList);

  // b 端粉条白名单
  DEFINE_SET_NODE_KCONF(int64_t, ad.index_message_proxy, BusinessFanstopAccountWhitelist);
  DEFINE_INT64_KCONF_NODE(ad.index_message_proxy, businessFanstopAccountAdmitTailNum, 0)  // 100 取模准入

  // 每个 proto type 的黑白名单
  DEFINE_PROTOBUF_NODE_KCONF(ProtoList, ad.index_message_proxy, protoWBList)

  // 归拢黑白名单类型的 ProtoList 配置
  DEFINE_PROTOBUF_NODE_KCONF(MultiProtoList, ad.index_message_proxy,
                             multiProtoList)

  DEFINE_BOOL_KCONF_NODE(
      ad.index_message_proxy, enableRtbWhiteList,
      false);  // rtb 在 splash 投放开关，数据 ready 之后再开启 否则会 core

  // 联盟暗投主站开屏广告
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableUniverseSupportSplashOnlyData, false);

  // 联盟支持磁力金牛移动端物料
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableUniverseSupportEspMobile, false);

  // 联盟支持搜索暗投广告
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableUniverseTinySearchData, false);

  // 联盟搜索暗投广告支持所有 campaign
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableUniverseSearchAllCampaign, false);

  // 联盟放开智能扩词
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableUniverseExtendSearch, false);

  // 联盟屏蔽 AIGC 微改素材
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableUniverseBlockAigcModifyPhoto, false);

  // 联盟支持短剧
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableUniverseSupportKwaiSerial, false);

  // 联盟过滤不支持物料类型
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableUniverseInvalidTypeFilter, false);
  // 联盟小系统过滤不支持物料数据
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableUniverseTinyFilter, false);
  // 联盟 DPA 账户白名单
  DEFINE_TAILNUMBERV2_KCONF(ad.universePlatform, allianceDpaUserAccountId, "");

  // 搜索暗投 target 放入 快投放物料
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableTargetSearchQuickSearchData, false);

  // 搜索暗投 target 放入 明投专业版 智能扩量 物料
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableTargetSearchExtendSearchData, false);

  // 搜索 target 放入 速推短视频
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableFlashPromotionPhotoInSearch, true);

  // 搜索明投暗投屏蔽 AIGC 微改素材
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableSearchBlockAigcModifyPhoto, true);

  // 开屏 RTB 屏蔽 AIGC 微改素材
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableSplashBlockAigcModifyPhoto, true);

  // 开屏、联盟过滤商品卡创意
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableUniverseFilterItemCard, true);

  // esp mobile 账户是否进入索引
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableEspMobileAccount, true);

  // 内循环部署是否准入外循环直播
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, admitOuterDirectLiveInLive, false);

  DEFINE_BOOL_KCONF_NODE(
      ad.index_message_proxy, forbidParser,
      false);  // 给 adcache 提供原始不解析数据

  // 信息流 budget 支持的订单类型
  DEFINE_SET_NODE_KCONF(std::string, ad.adFlowControl, dspBudgetOrderBizList);

  // 本地推账户白名单
  DEFINE_SET_NODE_KCONF(int64_t, ad.adtarget2, localLifeMigrateWhiteAccount);
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableBuildLocalPromoteForAmd, false);

  // style_server 分场景过滤数据
  DEFINE_PROTOBUF_NODE_KCONF(StyleServerIndexBlackFilter, ad.index_builder, styleServerIndexBlackFilter)

  // 联盟索引过滤
  DEFINE_PROTOBUF_NODE_KCONF(UniverseFilteredRoaringType,
                            ad.adtarget,
                            universeFilteredRoaringType)

  DEFINE_PROTOBUF_NODE_KCONF(UniverseSwiftIndexFilterConfig, ad.index_message_proxy,
                             universeSwiftIndexFilterConfig)

  // 内外循环部署 放入 搜索直投智能扩量
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableSearchMingtouToInnerOuter, true);
  // 内外循环部署 放入 搜索直投人群追投
  DEFINE_BOOL_KCONF_NODE(ad.index_message_proxy, enableSearchRetargetingToInnerOuter, true);
};

}  // namespace index_builder
}  // namespace ks
