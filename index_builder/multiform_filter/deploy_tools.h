#pragma once
#include <functional>
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <vector>
#include "absl/strings/substitute.h"
#include "glog/logging.h"
#include "google/protobuf/reflection.h"
#include "teams/ad/ad_base/src/common/basis_enum_types.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/multiform_filter/multiform_kconf.pb.h"
#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"

namespace ks {
namespace index_builder {

using ks::index_builder::GetExtensionField;

using DeployFunc = std::function<bool(kuaishou::ad::AdInstance*)>;
using DeployFuncOfMsg = std::function<bool(google::protobuf::Message*)>;

#define CREATIVE_EXT_FUNC(SEQ)                                                 \
  static bool CreativeScoreExt##SEQ(kuaishou::ad::AdInstance* ad) {            \
    auto msg_type = ad->type();                                                \
    if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {                          \
      auto* message = GetExtensionField(ad);                                   \
      auto* creative = dynamic_cast<kuaishou::ad::tables::Creative*>(message); \
      creative->set_creative_score(creative->creative_score_##SEQ());          \
    }                                                                          \
    return true;                                                               \
  }                                                                            \
  static bool CreativeScoreMsgExt##SEQ(google::protobuf::Message* msg) {       \
    auto type_name = msg->GetTypeName();                                       \
    if (type_name == "kuaishou.ad.tables.Creative") {                          \
      auto* creative = dynamic_cast<kuaishou::ad::tables::Creative*>(msg);     \
      creative->set_creative_score(creative->creative_score_##SEQ());          \
    }                                                                          \
    return true;                                                               \
  }

CREATIVE_EXT_FUNC(1);
CREATIVE_EXT_FUNC(2);
CREATIVE_EXT_FUNC(3);
CREATIVE_EXT_FUNC(4);

static bool CreativeScoreMsgExtAll(google::protobuf::Message* msg) {
  auto type_name = msg->GetTypeName();
  if (type_name == "kuaishou.ad.tables.Creative") {
    auto* creative = dynamic_cast<kuaishou::ad::tables::Creative*>(msg);
    std::vector<int64_t> scores;
    scores.push_back(creative->creative_score());
    scores.push_back(creative->creative_score_1());
    scores.push_back(creative->creative_score_2());
    scores.push_back(creative->creative_score_3());
    scores.push_back(creative->creative_score_4());
  }
  return true;
}

// 所有
static bool CreativeScoreExtAll(kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    auto* message = GetExtensionField(ad);
    CreativeScoreMsgExtAll(message);
  }
  return true;
}

// do nothing, use default
static bool CreativeScoreExtOri(kuaishou::ad::AdInstance* ad) { return true; }
static bool CreativeScoreMsgExtOri(google::protobuf::Message* msg) {
  return true;
}

static DeployFunc GetDeployFuncByEnum(DeployParam::Param deploy_param) {
  static std::unordered_map<DeployParam::Param, DeployFunc> inner_data_map = {
      {DeployParam::UNKNOWN_DEPLOY_PARAM, CreativeScoreExtOri},
      {DeployParam::ORIGINAL, CreativeScoreExtOri},
      {DeployParam::EXT1, CreativeScoreExt1},
      {DeployParam::EXT2, CreativeScoreExt2},
      {DeployParam::EXT3, CreativeScoreExt3},
      {DeployParam::EXT4, CreativeScoreExt4},
      {DeployParam::ALL, CreativeScoreExtAll}};
  auto map_it = inner_data_map.find(deploy_param);
  LOG_ASSERT(map_it != inner_data_map.end())
      << "unsupported deploy_param: " << deploy_param;
  return map_it->second;
}

static DeployFuncOfMsg GetDeployFuncByEnumOfMsg(
    DeployParam::Param deploy_param) {
  static std::unordered_map<DeployParam::Param, DeployFuncOfMsg>
      inner_data_map = {
          {DeployParam::UNKNOWN_DEPLOY_PARAM, CreativeScoreMsgExtOri},
          {DeployParam::ORIGINAL, CreativeScoreMsgExtOri},
          {DeployParam::EXT1, CreativeScoreMsgExt1},
          {DeployParam::EXT2, CreativeScoreMsgExt2},
          {DeployParam::EXT3, CreativeScoreMsgExt3},
          {DeployParam::EXT4, CreativeScoreMsgExt4},
          {DeployParam::ALL, CreativeScoreMsgExtAll}};
  auto map_it = inner_data_map.find(deploy_param);
  LOG_ASSERT(map_it != inner_data_map.end())
      << "unsupported deploy_param: " << deploy_param;
  return map_it->second;
}
}  // namespace index_builder
}  // namespace ks