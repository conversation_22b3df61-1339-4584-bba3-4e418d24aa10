#include "teams/ad/index_builder/multiform_filter/programmed_creative_filter.h"

#include <set>
#include <memory>
#include "perfutil/perfutil.h"
#include "teams/ad/ad_index/index/utils/ad_index_helper.h"
#include "teams/ad/index_builder/multiform_filter/common_tools.h"

namespace ks {
namespace index_builder {

using ks::infra::PerfUtil;
using kuaishou::ad::AdEnum;

using namespace kuaishou::ad::tables;  // NOLINT
using ks::ad_server::AdIndexHelper;
static std::set<AdEnum::AdInstanceType> programmed_check_types = {
    kuaishou::ad::AdEnum::CREATIVE};

bool IsProgrammedCreativeDataImp(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  LOG_ASSERT(programmed_check_types.count(msg_type) != 0)
      << "unexpected_type: " << msg_type;
  auto* pb = ad;
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    const Creative& creative = pb->GetExtension(Creative::creative_old);
    auto create_source_type = creative.create_source_type();
    if (creative.derivative_creative_flag()) {
      // 衍生创意不算程序化创意
      return false;
    }
    if (create_source_type !=
            kuaishou::ad::AdEnum::ADVANCED_PROGRAMMED_CREATIVE) {
      return false;
    }
  }
  return true;
}

bool IsProgrammedCreativeData(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  bool ret = true;
  if (ret && programmed_check_types.count(msg_type) != 0) {
    ret &= IsProgrammedCreativeDataImp(ad);
  }
  return ret;
}

bool IsNotProgrammedCreativeData(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  bool ret = true;
  if (ret && programmed_check_types.count(msg_type) != 0) {
    ret &= !IsProgrammedCreativeDataImp(ad);
  }
  return ret;
}

bool IsProgrammedCreativeOnly(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (programmed_check_types.count(msg_type) != 0) {
    return IsProgrammedCreativeDataImp(ad);
  }
  return false;
}

}  // namespace index_builder
}  // namespace ks
