#include "teams/ad/index_builder/multiform_filter/common_tools.h"

#include <unordered_set>
#include <unordered_map>
#include <set>
#include <memory>
#include "base/time/timestamp.h"
#include "perfutil/perfutil.h"
#include "absl/strings/str_split.h"
#include "absl/strings/str_join.h"
#include "teams/ad/index_builder/utils/parser/parser_common.h"
#include "teams/ad/engine_base/creative_score_util/creative_score_util.h"
#include "teams/ad/ad_base/src/better_enums/better_enums.h"
#include "teams/ad/index_builder/multiform_filter/programmed_creative_filter.h"
#include "teams/ad/ad_base/src/common/basis_enum_types.h"
#include "teams/ad/index_builder/multiform_filter/adapter_creative_filter.h"
#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"
#include "teams/ad/index_builder/multiform_filter/multiform_kconf.h"
#include "teams/ad/ad_index/framework/target/target_key.h"
#include "teams/ad/index_builder/multiform_filter/instance_filter.h"

namespace ks {
namespace index_builder {


using kuaishou::ad::AdEnum;

using namespace kuaishou::ad::tables;  // NOLINT
using ks::infra::PerfUtil;

static const std::unordered_set<AdEnum::AdInstanceType> type_set{
    AdEnum::CREATIVE, AdEnum::UNIT, AdEnum::CAMPAIGN, AdEnum::ACCOUNT};

int64_t GetValueByFieldName(kuaishou::ad::AdInstance* ad_instance,
                            std::string field_name) {
  auto* message = GetExtensionField(ad_instance);
  if (!message) return 0;
  auto& actual = *message;
  const auto* actual_ref = actual.GetReflection();
  const auto* actual_desc = actual.GetDescriptor();
  typedef std::vector<const google::protobuf::FieldDescriptor*> FieldList;
  FieldList field_list;
  actual_ref->ListFields(actual, &field_list);
  for (FieldList::const_iterator it = field_list.begin();
       it != field_list.end(); ++it) {
    const google::protobuf::FieldDescriptor* field = *it;
    if (field->is_repeated()) {
      continue;
    }
    #define CASE_READ_FIELD(CPPTYPE, func_suffix) \
      case google::protobuf::FieldDescriptor::CPPTYPE: \
        return actual_ref->Get##func_suffix(actual, field);
    if (field->name() == field_name) {
      switch (field->cpp_type()) {
        CASE_READ_FIELD(CPPTYPE_INT32, Int32);
        CASE_READ_FIELD(CPPTYPE_UINT32, UInt32);
        CASE_READ_FIELD(CPPTYPE_INT64, Int64);
        CASE_READ_FIELD(CPPTYPE_UINT64, UInt64);
        default:
          return 0;
      }
    }
    #undef CASE_READ_FIELD
  }
  return 0;
}

int64_t GetAccountIdOfAdInstanceV2(kuaishou::ad::AdInstance* ad_instance) {
  auto message_type = ad_instance->type();
  int64_t ret = -1;
  if (type_set.count(message_type)) {
    if (message_type == AdEnum::ACCOUNT) {
      ret = GetValueByFieldName(ad_instance, "id");
    } else {
      ret = GetValueByFieldName(ad_instance, "account_id");
    }
    return ret;
  }
  if (message_type == AdEnum::PHOTO_STATUS) {
    ret = GetValueByFieldName(ad_instance, "photo_id");
  }
  return ret;
}
int64_t GetAccountIdOfAdInstanceV3(kuaishou::ad::AdInstance* ad_instance) {
  auto message_type = ad_instance->type();
  int64_t ret = -1;
  if (type_set.count(message_type)) {
    if (message_type == AdEnum::ACCOUNT) {
      ret = -1;  // CreativeServer 获取全局 account ，暂不分片 @fengzezhao
    } else {
      ret = GetValueByFieldName(ad_instance, "account_id");
    }
    return ret;
  }
  return ret;
}
int64_t GetValueByFieldNameV2(kuaishou::ad::AdInstance* ad_instance,
                            std::string field_name) {
  auto* message = GetExtensionField(ad_instance);
  if (!message) return 0;
  auto& actual = *message;
  const auto* actual_ref = actual.GetReflection();
  const auto* actual_desc = actual.GetDescriptor();

  auto field = actual_desc->FindFieldByName(field_name);

  if (field != nullptr) {
    if (field->is_repeated()) {
      return 0;
    }
#define CASE_READ_FIELD(CPPTYPE, func_suffix)      \
  case google::protobuf::FieldDescriptor::CPPTYPE: \
    return actual_ref->Get##func_suffix(actual, field);

    switch (field->cpp_type()) {
      CASE_READ_FIELD(CPPTYPE_INT32, Int32);
      CASE_READ_FIELD(CPPTYPE_UINT32, UInt32);
      CASE_READ_FIELD(CPPTYPE_INT64, Int64);
      CASE_READ_FIELD(CPPTYPE_UINT64, UInt64);
      default:
        return 0;
    }
#undef CASE_READ_FIELD
  }
  return 0;
}

void GetAllFieldNames(kuaishou::ad::AdInstance* ad_instance, std::set<std::string>* name_set) {
  name_set->clear();
  auto* message = GetExtensionField(ad_instance);
  if (!message) return;
  auto& actual = *message;
  const auto* actual_ref = actual.GetReflection();
  const auto* actual_desc = actual.GetDescriptor();
  typedef std::vector<const google::protobuf::FieldDescriptor*> FieldList;
  FieldList field_list;
  actual_ref->ListFields(actual, &field_list);
  for (FieldList::const_iterator it = field_list.begin();
       it != field_list.end(); ++it) {
    const google::protobuf::FieldDescriptor* field = *it;
    name_set->insert(field->name());
  }
}

int64_t GetAccountIdOfAdInstance(kuaishou::ad::AdInstance* ad_instance) {
  int64_t ret = 0;
  auto message_type = ad_instance->type();
  if (type_set.count(message_type)) {
    if (message_type == AdEnum::ACCOUNT) {
      ret = GetValueByFieldName(ad_instance, "id");
    } else {
      ret = GetValueByFieldName(ad_instance, "account_id");
    }
    return ret;
  }

  std::set<std::string> field_names;
  GetAllFieldNames(ad_instance, &field_names);
  std::string expected_field_name{};
  if (field_names.count("id")) {
    expected_field_name = "id";
  } else if (field_names.count("cover_id")) {
    expected_field_name = "account_id";
  } else if (field_names.count("creative_id")) {
    expected_field_name = "creative_id";
  } else if (field_names.count("unit_id")) {
    expected_field_name = "unit_id";
  } else if (field_names.count("account_id")) {
    expected_field_name = "account_id";
  }
  ret = GetValueByFieldName(ad_instance, expected_field_name);
  return ret;
}

void TableParserProcess(kuaishou::ad::AdInstance* ad) {
  /*
    已经横级联 unit: target unit_support_info unit_fanstop_support_info
             campaign: campaign_fanstop_support_info
             creative: creative_fanstop_support_info
    横级联的需要特殊处理,使扩展的 parser 能起作用
  */
  if (MultiFormKconfUtil::forbidParser()) {
    return;
  }
  auto* message = GetExtensionField(ad);
  ParseManagerByTypeName::GetInstance()->Process(message);
  return;
}

int64_t GetCreativeBeginTime(const kuaishou::ad::tables::Creative* creative) {
  int64_t creative_id = creative->id();
  int64_t tmp_value;
  int64_t* valid_time = &tmp_value;
  auto& valid_time_ref = *valid_time;
  valid_time_ref = creative->first_audit_passtime();
  if (valid_time_ref < creative->create_time()) {
    valid_time_ref = creative->create_time();
  }
  return tmp_value;
}

bool IsCreativeHotOnly(kuaishou::ad::AdInstance* ad) {
  if (ad->type() != AdEnum::CREATIVE) {
    return false;
  }
  const Creative& creative = ad->GetExtension(Creative::creative_old);
  if (creative.create_source_type() ==
          kuaishou::ad::AdEnum::ADVANCED_PROGRAMMED_CREATIVE ||
      creative.live_creative_type() ==
          kuaishou::ad::AdEnum::LIVE_STREAM_CREATIVE_TYPE ||
      creative.live_creative_type() ==
          kuaishou::ad::AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE) {
    return true;
  }
  return false;
}

bool IsCreativeOffline(kuaishou::ad::AdInstance* ad_instance) {
  auto* message = GetExtensionField(ad_instance);
  if (!message) return false;
  auto* creative = dynamic_cast<kuaishou::ad::tables::Creative*>(message);
  auto put_status = creative->put_status();
  if (put_status != kuaishou::ad::AdEnum::PUT_STATUS_OPEN) return true;
  auto review_status = creative->review_status();
  auto community_review_status = creative->community_review_status();
  static std::set<kuaishou::ad::AdEnum::ReviewStatus> review_pass_status{
      kuaishou::ad::AdEnum::REVIEW_THROUGH,
      kuaishou::ad::AdEnum::REVIEW_BASIC_THROUGH,
      kuaishou::ad::AdEnum::REVIEW_NEBULA_THROUGH};
  if (review_pass_status.count(static_cast<kuaishou::ad::AdEnum::ReviewStatus>(
          review_status)) == 0 &&
      review_pass_status.count(static_cast<kuaishou::ad::AdEnum::ReviewStatus>(
          community_review_status)) == 0) {
    return true;
  }
  return false;
}

bool IsCreativeProgrammedOffline(kuaishou::ad::AdInstance* ad_instance) {
  if (IsCreativeOffline(ad_instance)) return true;
  auto* message = GetExtensionField(ad_instance);
  if (!message) return false;
  auto* creative = dynamic_cast<kuaishou::ad::tables::Creative*>(message);
  auto create_source_type = creative->create_source_type();
  if (create_source_type != kuaishou::ad::AdEnum::ADVANCED_PROGRAMMED_CREATIVE)
    return false;
  auto creative_score = creative->creative_score();
  PerfUtil::CountLogStash(1, "ad.index_message_proxy", "programmed_score",
                          DeployVariable::GetKwsName(),
                          std::to_string(creative_score),
                          DeployVariable::GetPodName());
  if (!ks::engine_base::CheckCreativeScore(creative_score)) {
    return true;
  }
  return false;
}

std::set<std::string> GetAllowedDeploy(const kuaishou::ad::AdInstance* ad_instance) {
  std::set<std::string> allowed_deploy;
  if (ad_instance->type() != AdEnum::CREATIVE) {
    return allowed_deploy;
  }
  const Creative& creative = ad_instance->GetExtension(Creative::creative_old);
  // 见 teams/ad/engine_base/creative_score_util/creative_score_util.h
  // https://docs.corp.kuaishou.com/d/home/<USER>
  static const std::unordered_map<int, int> deploy_type_map{
      {+DeployType::Default, 2},      {+DeployType::thanos, 1},
      {+DeployType::universe, 4},     {+DeployType::detail, 3},
      {+DeployType::search, 5},       {+DeployType::knews, 6},
      {+DeployType::universe_feed, 7}};
  static const std::unordered_map<int, std::string> deploy_type_str_map{
      {+DeployType::Default,
       DeployType::_from_integral_nothrow(+DeployType::Default)->_to_string()},  // NOLINT
      {+DeployType::thanos,
       DeployType::_from_integral_nothrow(+DeployType::thanos)->_to_string()},  // NOLINT
      {+DeployType::universe,
       DeployType::_from_integral_nothrow(+DeployType::universe)->_to_string()},  // NOLINT
      {+DeployType::detail,
       DeployType::_from_integral_nothrow(+DeployType::detail)->_to_string()},  // NOLINT
      {+DeployType::search,
       DeployType::_from_integral_nothrow(+DeployType::search)->_to_string()},  // NOLINT
      {+DeployType::knews,
       DeployType::_from_integral_nothrow(+DeployType::knews)->_to_string()},  // NOLINT
      {+DeployType::universe_feed,
       DeployType::_from_integral_nothrow(+DeployType::universe_feed)->_to_string()}};  // NOLINT
  auto creative_score = creative.creative_score();
  if (IsProgrammedCreativeOnly(ad_instance)) {
    // 程序化创意
    for (const auto& deploy_it : deploy_type_map) {
      if (ks::engine_base::CheckCreativeScore(creative_score,
                                              deploy_it.second)) {
        allowed_deploy.insert(deploy_type_str_map.at(deploy_it.first));
      }
    }
    return allowed_deploy;
  } else if (IsAdapterCreativeOnly(ad_instance)) {
    // 多端适配 目前是通投，待后续填充上场景位
    for (const auto& deploy_it : deploy_type_map) {
      if (ks::engine_base::CheckCreativeScore(creative_score,
                                              deploy_it.second)) {
        allowed_deploy.insert(deploy_type_str_map.at(deploy_it.first));
      }
    }
    return allowed_deploy;
  } else {  // 其他情况算下发所有部署
    for (const auto& deploy_it : deploy_type_map) {
      allowed_deploy.insert(deploy_type_str_map.at(deploy_it.first));
    }
    return allowed_deploy;
  }
}

std::string GetTypeString(kuaishou::ad::AdInstance* ad) {
  auto* message = GetExtensionField(ad);
  const auto* actual_desc = message->GetDescriptor();
  return actual_desc->full_name();
}



}  // namespace index_builder
}  // namespace ks
