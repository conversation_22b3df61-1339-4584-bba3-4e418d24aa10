#pragma once

#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
namespace ks {
namespace index_builder {

/*
  统一转成 instance 的 extention 处理
*/
bool IsProgrammedCreativeData(const kuaishou::ad::AdInstance* ad);
bool IsNotProgrammedCreativeData(const kuaishou::ad::AdInstance* ad);
// 标记为程序化创意独有数据
bool IsProgrammedCreativeOnly(const kuaishou::ad::AdInstance* ad);

}  // namespace index_builder
}  // namespace ks
