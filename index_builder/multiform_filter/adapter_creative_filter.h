#pragma once

#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/data_build/schemafree/table_row.h"
namespace ks {
namespace index_builder {

/*
  统一转成 instance 的 extention 处理
*/
// 只 check CREATIVE, MATERIAL, PHOTO_STATUS 是多端适配。别的 type 都 return true。
bool IsAdapterCreative(const kuaishou::ad::AdInstance* ad);
// 只 check CREATIVE, MATERIAL, PHOTO_STATUS 不是多端适配。别的 type 都 return true。
bool IsNotAdapterCreative(const kuaishou::ad::AdInstance* ad);

// 标记为多端适配 only 的数据
bool IsAdapterCreativeOnly(const ks::ad::build_service::TableRow* table_row);
bool IsAdapterCreativeOnly(const kuaishou::ad::AdInstance* ad);

bool IsAdapterCreativeKnewOnly(const kuaishou::ad::AdInstance* ad);

bool IsAdapterCreativeUniverseOnly(const kuaishou::ad::AdInstance* ad);
}  // namespace index_builder
}  // namespace ks
