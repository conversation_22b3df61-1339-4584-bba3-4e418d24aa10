#pragma once

#include "teams/ad/ad_base/src/better_enums/better_enums.h"
#include "teams/ad/data_build/schemafree/table_row.h"

namespace ks {
namespace index_builder {

BETTER_ENUM(AdType, int32_t, dsp = 0, search)

// 搜索明投专业版
bool IsSearchOnlyData(const ks::ad::build_service::TableRow* table_row);
bool IsSearchOnlyData(const kuaishou::ad::AdInstance* ad);
// 搜索明投专业版智能扩量
bool IsExtendSearchData(const ks::ad::build_service::TableRow* table_row);
bool IsExtendSearchData(const kuaishou::ad::AdInstance* ad);
// 搜索明投专业版人群追投
bool IsSearchPopulationRetargeting(const ks::ad::build_service::TableRow* table_row);
bool IsSearchPopulationRetargeting(const kuaishou::ad::AdInstance* ad);

}  // namespace index_builder
}  // namespace ks
