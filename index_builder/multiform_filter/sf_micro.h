#pragma once
namespace ks {
namespace index_builder {
#define CHECKTABLEROW(table_row)                         \
  if (table_row == nullptr) {                            \
    return false;                                        \
  }                                                      \
  auto table_name = table_row->GetTableName();           \
  if (table_name.empty()) {                                \
    LOG_EVERY_N(WARNING, 1000) << "get table name fail"; \
    return false;                                        \
  }
}  // namespace index_builder
}  // namespace ks
