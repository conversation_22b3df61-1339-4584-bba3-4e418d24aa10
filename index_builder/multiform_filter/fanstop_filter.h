#pragma once

#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/data_build/schemafree/table_row.h"
namespace ks {
namespace index_builder {

/*
  统一转成 instance 的 extention 处理
*/
bool IsFansTopData(const ks::ad::build_service::TableRow* table_row);
bool IsFansTopData(const kuaishou::ad::AdInstance* ad);
bool IsNotFanstopData(const ks::ad::build_service::TableRow* table_row);
bool IsNotFanstopData(const kuaishou::ad::AdInstance* ad);
// 标记为 fanstop 独有数据
bool IsFansTopOnly(const ks::ad::build_service::TableRow* table_row);
bool IsFansTopOnly(const kuaishou::ad::AdInstance* ad);

// 粉条 budget 只要粉条数据
bool IsFanstopBudget(const kuaishou::ad::AdInstance* ad);

bool IsFanstopLiveCampaign(const ks::ad::build_service::TableRow* table_row);
bool IsFanstopLiveCampaign(const kuaishou::ad::tables::Campaign& campaign);

// 是否是内部粉条数据
bool IsInnerFanstop(const kuaishou::ad::AdInstance* ad);
// 是否粉条需要的 wt_ad_budget_status
bool IsNotFanstopBudget(const ks::ad::build_service::TableRow* table_row);
// 是否粉条需要 auto_bid 数据
bool IsNotFanstopWtAutoBidData(const ks::ad::build_service::TableRow* table_row);
}  // namespace index_builder
}  // namespace ks
