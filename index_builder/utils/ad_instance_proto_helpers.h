// A few helpers dealing with HDFS ad instance proto files.
#pragma once
#include <memory>

#include <string>
#include <vector>

#include "absl/container/flat_hash_map.h"
#include "absl/types/optional.h"
#include "google/protobuf/util/message_differencer.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace ks {
namespace index_builder {

// Get the extension message from an AdInstance.
google::protobuf::Message* GetExtensionField(
    kuaishou::ad::AdInstance *ad_instance);

// Clear fields from fields_to_clear.
void ClearFields(google::protobuf::Message *msg,
                 const std::vector<std::string> &fields_to_clear);

// Convert table name to full proto type, e.g. ad_dsp_unit_target to
// kuaishou.ad.tables.UnitTarget.
absl::optional<std::string> TableToProtoType(const std::string &table_name);

// 默认主键是第一个字段
int64_t GetPrimaryKey(kuaishou::ad::AdInstance* ad_instance);

int64_t GetPrimaryKey(google::protobuf::Message *msg);

// Writes the diff of 2 AdInstance protos into diff_report using
// MessageDifferencer.
bool DiffRawAdInstances(kuaishou::ad::AdInstance *test,
                        kuaishou::ad::AdInstance *base,
                        std::string *diff_report);
}  // namespace index_builder
}  // namespace ks
