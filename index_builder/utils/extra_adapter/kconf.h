#pragma once
#include <string>
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/index_builder/utils/extra_adapter/kconf.pb.h"
namespace ks {
namespace index_builder {

using namespace ks::ad_base::kconf;  // NOLINT

class CreativeAdapterKconf {
 public:
  DEFINE_PROTOBUF_NODE_KCONF(CreativeAdapterParam, ad.index_builder,
                             creativeAdapterParam);
  DEFINE_PROTOBUF_NODE_KCONF(CreativeAdapterConfigs, ad.index_builder,
                             creativeAdapterConfigs);
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, sdpaCreativeSetOnline,
                         false);  // sdpa 默认置为上线
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, proxyLoadSnap,
                         false);  // proxy 加载快照兜底
  DEFINE_PROTOBUF_NODE_KCONF(LabelAdapterParam, ad.index_builder,
                             labelAdapterParam);
};

}  // namespace index_builder
}  // namespace ks
