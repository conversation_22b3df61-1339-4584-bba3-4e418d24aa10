syntax = "proto3";

package ks.index_builder;
import "teams/ad/ad_proto/kuaishou/ad/common/enums.proto";
import "teams/ad/engine_base/utils/kconf.proto";

message CreativeScorePathInfo {
  kuaishou.ad.AdEnum.CreativeServerBizType scene = 1;
  repeated string data_paths = 2;
  string prefix = 3;
}

message CreativeAdapterParam {
  string kcc_name = 1;
  string prefix = 2;
  kuaishou.ad.AdEnum.CreativeServerBizType scene = 3;
  // 加载的creative_dump 数据
  repeated string data_paths = 4;
  ks.engine_base.SceneParam.SceneForCreative creative_scene = 5;
  // 全场景使用：产出 ktable 格式数据
  bool enable_all_scene = 6;
  // <kuaishou.ad.AdEnum.CreativeServerBizType, data_paths>
  repeated CreativeScorePathInfo score_path_info = 7;
  bool auto_lock_version = 8;
}

message CreativeAdapterConfigs {
  map<string, CreativeAdapterParam> params = 1;
}

message LabelAdapterParam {
  message Paths {
    repeated string data_path = 1;
  }
  map<string, Paths> label_config = 1;
  string prefix = 2;
}
