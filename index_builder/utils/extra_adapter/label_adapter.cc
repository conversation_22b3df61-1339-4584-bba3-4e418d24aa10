#include "teams/ad/index_builder/utils/extra_adapter/label_adapter.h"
#include <cstddef>
#include <functional>
#include <mutex>
#include <sstream>
#include <string>
#include <vector>
#include "absl/container/flat_hash_map.h"
#include "base/common/closure.h"
#include "base/thread/thread_pool.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "teams/ad/ad_creative_server/proto/creative_score.pb.h"
#include "teams/ad/ad_label_server/proto/label_server.pb.h"
#include "teams/ad/ad_label_server/utils/common/common.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_adapter/utils/hdfs_util/hdfs_reader.h"
#include "teams/ad/engine_base/utils/deploy_variable.h"
#include "teams/ad/index_adapter/utils/perf_manager/perf_manager.h"
#include "teams/ad/index_adapter/utils/stream_map/stream_map_maker.h"
#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"
#include "teams/ad/index_builder/utils/extra_adapter/kconf.h"

/*
  对接分部署的 creative_server 产出的 creative_db
*/

using ks::ad_label_server::GetPbFromRedis;
using kuaishou::ad::tables::LevelLabelData;
using ks::index_adapter::FindLastestSuccessVersionOnHdfs;
using ks::index_adapter::GetAllFileOnPath;
using ks::index_builder::GetExtensionField;
using ks::ad_label_server::TGetFromEncodeString;
namespace ks {
namespace index_builder {

static void LabelParser(kuaishou::ad::tables::Creative* creative) {
  using LabelFunction =
    std::function<void(kuaishou::ad::tables::Creative*, const kuaishou::ad::tables::OneChannel&)>;
  LabelFunction dup_creative_id =
      [](kuaishou::ad::tables::Creative* creative,
         const kuaishou::ad::tables::OneChannel& one_channel) {
        if (one_channel.label_data().label_data().r_i64_size() > 0) {
          auto* extend = creative->mutable_extend_fields();
          extend->set_dup_creative_id(one_channel.label_data().label_data().r_i64(0));
        }
        LOG_FIRST_N(INFO, 100) << creative->extend_fields().ShortDebugString();
      };
  LabelFunction ecom_statistics_cost =
      [](kuaishou::ad::tables::Creative* creative,
         const kuaishou::ad::tables::OneChannel& one_channel) {
        if (one_channel.label_data().label_data().r_f_size() > 0) {
          auto* extend = creative->mutable_extend_fields();
          extend->set_statistics_cost(one_channel.label_data().label_data().r_f(0));
        }
        if (one_channel.label_data().label_data().r_i64_size() > 0) {
          auto* extend = creative->mutable_extend_fields();
          extend->set_statistics_merchant_order_num(one_channel.label_data().label_data().r_i64(0));
        }
        LOG_FIRST_N(INFO, 100) << creative->extend_fields().ShortDebugString();
      };
  LabelFunction pla_creative_id =
      [](kuaishou::ad::tables::Creative* creative,
         const kuaishou::ad::tables::OneChannel& one_channel) {
        if (one_channel.label_data().label_data().r_i64_size() > 0) {
          auto* extend = creative->mutable_extend_fields();
          extend->set_pla_creative_id(one_channel.label_data().label_data().r_i64(0));
        }
        LOG_FIRST_N(INFO, 100) << creative->extend_fields().ShortDebugString();
      };
  static std::map<std::string, LabelFunction> funtion_map{
      {"pla_creative_id", pla_creative_id},
      {"ecom_statistics_cost", ecom_statistics_cost},
      {"dup_creative_id", dup_creative_id}};
  for (const auto& one_channel :
       creative->extend_fields().level_label_data().channel_data()) {
    auto channel_name = one_channel.name();
    auto map_it = funtion_map.find(channel_name);
    if (map_it != funtion_map.end()) {
      auto func = map_it->second;
      func(creative, one_channel);
    }
  }
  // 已解析则去掉
  creative->mutable_extend_fields()->clear_level_label_data();
}

void AddLabelData(kuaishou::ad::AdInstance* ad_instance,
                  LevelLabelData label_data) {
  auto msg_type = ad_instance->type();
  auto* message = GetExtensionField(ad_instance);
  if (msg_type == kuaishou::ad::AdEnum::PHOTO_STATUS) {
    auto* photo_status = dynamic_cast<kuaishou::ad::tables::PhotoStatus*>(message);
    photo_status->mutable_extend_fields()->mutable_level_label_data()->CopyFrom(label_data);
    LOG_FIRST_N(INFO, 100) << photo_status->ShortDebugString();
  } else if (msg_type == kuaishou::ad::AdEnum::ACCOUNT) {
    auto* account = dynamic_cast<kuaishou::ad::tables::Account*>(message);
    account->mutable_extend_fields()->mutable_level_label_data()->CopyFrom(label_data);
    LOG_FIRST_N(INFO, 100) << account->ShortDebugString();
  } else if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    auto* creative = dynamic_cast<kuaishou::ad::tables::Creative*>(message);
    creative->mutable_extend_fields()->mutable_level_label_data()->CopyFrom(label_data);
    if (AdKconfUtil::enableParseLabelWhenMerge()) {
      LabelParser(creative);
    }
    LOG_FIRST_N(INFO, 100) << creative->ShortDebugString();
  }
}

std::string GetRedisKey(kuaishou::ad::AdInstance* ad_instance) {
  static std::string prefix =
      CreativeAdapterKconf::labelAdapterParam()->data().prefix();
  auto type_str = AdEnum_AdInstanceType_Name(ad_instance->type());
  /*
  std::string key = absl::Substitute("$0_$1_$2", prefix, type_str,
                                     GetPrimaryKey(ad_instance));
  */
  std::string key = absl::Substitute("$0_$1_$2", prefix, type_str,
                                     GetPrimaryKey(ad_instance));
  LOG_FIRST_N(INFO, 10) << "key: " << key
                        << " ad_instance: " << ad_instance->ShortDebugString();
  return key;
}

class SnapHdfsReader {
 public:
  explicit SnapHdfsReader(std::function<void(std::string*)> func,
                          std::string type)
      : func_(func), type_(type) {}
  void DoReadFile() {
    auto label_config =
        CreativeAdapterKconf::labelAdapterParam()->data().label_config();
    std::string suc_flag = "_SUCCESS";
    thread::ThreadPool dump_pool{20};

    std::vector<std::string> files;
    if (label_config.find(type_) != label_config.end()) {
      std::string version;
      auto paths = label_config.at(type_).data_path();
      for (auto path : paths) {
        if (FindLastestSuccessVersionOnHdfs(path, &version, suc_flag)) {
          ks::index_adapter::PerfManager::GetInstance()->UpdateLabelShot(
              path, version, type_);
          auto act_path = base::FilePath(path).Append(version).ToString();
          GetAllFileOnPath(act_path, &files);
        }
      }
    }
    for (auto file : files) {
      dump_pool.AddTask(::NewCallback(this, &SnapHdfsReader::Read, file));
    }
    dump_pool.JoinAll();
  }

  void Read(std::string hdfs_file) {
    auto reader = ks::index_adapter::HDFSReader(hdfs_file);
    std::string content;
    int64_t record_num = 0;
    while (true) {
      reader.ReadLine(content);
      // LOG(INFO) << content;
      if (content.empty()) break;
      func_(&content);
      record_num++;
    }
    LOG(INFO) << "hdfs_file " << hdfs_file << " record_num " << record_num;
  }

 private:
  std::string type_;
  std::function<void(std::string*)> func_;
};

MaterialLableSnap::MaterialLableSnap(std::string type) : type_(type) {
  std::string product_name;
  if (getenv("KWS_PRODUCT_NAME") != NULL) {
    product_name = getenv("KWS_PRODUCT_NAME");
  } else {
    non_snap_load_ = true;  // 数据构建层 flink 任务没有环境变量，强制设为不加载 snap
  }

  if ("ad_index_message_proxy" == product_name &&
      !CreativeAdapterKconf::proxyLoadSnap()) {
    non_snap_load_ = true;
  }
  {
    kcc_name_ = "adLabelServer";
    redis_client_ =
        infra::RedisProxyClient::GetRedisClientByKccFromKconf(kcc_name_, 10);
    LOG_ASSERT(redis_client_ != nullptr)
        << "kcc_name: " << kcc_name_ << " empty or kcc init fail";
  }
}

MaterialLableSnap::~MaterialLableSnap() {}

LevelLabelData MaterialLableSnap::Process(
    kuaishou::ad::AdInstance* ad_instance) {
  LevelLabelData ret;
  if (non_snap_load_) {
    std::string key = GetRedisKey(ad_instance);
    if (GetPbFromRedis(redis_client_, key, &ret)) {
      PerfUtil::CountLogStash(1, "ad.index_builder", "label_redis_load_success",
                              type_,
                              ks::engine_base::DeployVariable::GetKwsName(),
                              ks::engine_base::DeployVariable::GetPodName());
    } else {
      PerfUtil::CountLogStash(1, "ad.index_builder", "label_redis_load_fail",
                              type_,
                              ks::engine_base::DeployVariable::GetKwsName(),
                              ks::engine_base::DeployVariable::GetPodName());
    }
  } else {
    int64_t key = GetPrimaryKey(ad_instance);
    LOG_FIRST_N(INFO, 100) << key;
    int64_t read_index = 1 - write_index_.load();
    if (data_[read_index].find(key) == data_[read_index].end()) {
      PerfUtil::CountLogStash(1, "ad.index_builder", "label_snap_load_success",
                              type_,
                              ks::engine_base::DeployVariable::GetKwsName(),
                              ks::engine_base::DeployVariable::GetPodName());
      return ret;
    }
    PerfUtil::CountLogStash(1, "ad.index_builder", "label_snap_load_fail",
                            type_,
                            ks::engine_base::DeployVariable::GetKwsName(),
                            ks::engine_base::DeployVariable::GetPodName());
    TGetFromEncodeString(data_[read_index].at(key), &ret);
    LOG_FIRST_N(INFO, 100) << ret.ShortDebugString();
  }
  return ret;
}

void MaterialLableSnap::DoReadFile() {
  if (non_snap_load_) {
    LOG(INFO) << "non_snap_load_: " << non_snap_load_ << " skiped hdfs file";
    return;
  }
  std::function<void(std::string*)> func = [&](std::string* line) {
    std::lock_guard<std::mutex> lock(mtx_);
    LevelLabelData level_label_data;
    TGetFromEncodeString(*line, &level_label_data);
    data_[write_index_.load()].emplace(level_label_data.key(), *line);
  };
  ClearWriteSpace();
  SnapHdfsReader snap_hdfs_reader(func, type_);
  snap_hdfs_reader.DoReadFile();
  PerfUtil::SetLogStash(data_[write_index_.load()].size(), "ad.index_builder",
                        "label_snap_size", type_,
                        ks::engine_base::DeployVariable::GetKwsName(),
                        ks::engine_base::DeployVariable::GetPodName());

  LOG(INFO) << "snap total size: " << data_[write_index_.load()].size();

  for (auto item : data_[write_index_.load()]) {
    LevelLabelData level_label_data;
    TGetFromEncodeString(item.second, &level_label_data);
    LOG_FIRST_N(INFO, 100) << item.first << " "
                           << level_label_data.ShortDebugString();
  }
  Swap();
}

LabelAdapter::LabelAdapter() {
  LOG(INFO)
      << CreativeAdapterKconf::labelAdapterParam()->data().ShortDebugString();
  auto label_config =
      CreativeAdapterKconf::labelAdapterParam()->data().label_config();
  std::stringstream ss;
  for (auto item : label_config) {
    snap_map_[item.first] = new MaterialLableSnap(item.first);
    ss << item.first << " ";
  }
  LOG(INFO) << ss.str() << "load label";
  update_thread_ = std::thread([this]() { UpdateThread(); });
}

LabelAdapter::~LabelAdapter() {
  running_.store(false);
  update_thread_.join();
  for (auto item : snap_map_) {
    delete item.second;
  }
}

bool LabelAdapter::ProcessForInc(kuaishou::ad::AdInstance* ad_instance) {
  std::string type = AdEnum_AdInstanceType_Name(ad_instance->type());
  // 非打标数据
  if (snap_map_.find(type) == snap_map_.end()) {
    return true;
  }
  auto level_label_data = snap_map_[type]->Process(ad_instance);
  if (level_label_data.channel_data_size() > 0) {
    AddLabelData(ad_instance, level_label_data);
  }
  return true;
}

bool LabelAdapter::ProcessForFull(kuaishou::ad::AdInstance* ad_instance) {
  return ProcessForInc(ad_instance);
}

void LabelAdapter::UpdateThread() {
  running_.store(true);
  while (running_.load()) {
    for (auto item : snap_map_) {
      auto type = item.first;
      auto label_snap = item.second;
      label_snap->DoReadFile();
    }
    LOG(INFO) << "finish one turn";
    ready_.store(true);
    std::this_thread::sleep_for(std::chrono::seconds(600));
  }
}

}  // namespace index_builder
}  // namespace ks
