#include "teams/ad/index_builder/utils/extra_adapter/creative_adapter.h"
#include <algorithm>
#include <atomic>
#include <chrono>
#include <cstddef>
#include <set>
#include <thread>
#include <unordered_map>
#include <vector>
#include "absl/strings/str_split.h"
#include "absl/strings/substitute.h"
#include "absl/time/time.h"
#include "base/common/closure.h"
#include "base/encoding/base64.h"
#include "base/file/file_path.h"
#include "base/thread/thread_pool.h"
#include "glog/logging.h"
#include "pub/src/base/strings/string_util.h"
#include "serving_base/hdfs_read/hdfs_file_util.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_creative_server/proto/creative_score.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_adapter/utils/hdfs_util/hdfs_reader.h"
#include "teams/ad/engine_base/utils/deploy_variable.h"
#include "teams/ad/index_adapter/utils/perf_manager/perf_manager.h"
#include "teams/ad/index_adapter/utils/stream_map/stream_map_maker.h"
#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"
#include "teams/ad/index_builder/utils/extra_adapter/kconf.h"

namespace ks {
namespace index_builder {

using kuaishou::ad::AdEnum;
static void CreativeTagInfoCopy(
    const kuaishou::ad::ad_creative_server::CreativeExtendData&
        creative_extend_data,
    kuaishou::ad::tables::Creative* creative) {
  creative->set_creative_score(creative_extend_data.creative_score());
  for (const auto& creative_label : creative_extend_data.creative_label()) {
    kuaishou::ad::tables::CreativeExtendScore::CreativeLabel tmp_creative_label;
    auto dest = creative->mutable_extend_fields()
        ->mutable_creative_extend_score()
        ->mutable_creative_label()->Add();
    dest->set_exp_id(creative_label.exp_id());
    dest->set_label(creative_label.label());
  }
  auto* creative_ext_data = creative->mutable_extend_fields()->mutable_creative_extend_score();
  for (const auto& new_creative_tag :
       creative_extend_data.new_creative_tags()) {
    auto* map_ptr = creative_ext_data->mutable_new_creative_tag();
    (*map_ptr)[new_creative_tag.index()] = new_creative_tag.value();
  }
  creative_ext_data->set_circulation_type(creative_extend_data.circulation_type());
  for (auto unit_exp_feature_id : creative_extend_data.unit_exp_feature_id()) {
    creative_ext_data->add_unit_exp_feature_id(unit_exp_feature_id);
  }
  auto* similar_info = creative_ext_data->mutable_internal_photo_similar_info();
  similar_info->CopyFrom(creative_extend_data.internal_photo_similar_info());
}

static void CreativeTagInfoCopy(const std::string& pb_str,
                                kuaishou::ad::tables::Creative* creative) {
  kuaishou::ad::ad_creative_server::CreativeExtendData creative_extend_data;
  creative_extend_data.ParseFromString(pb_str);
  CreativeTagInfoCopy(creative_extend_data, creative);
}

static void Transfer(const kuaishou::ad::ad_creative_server::CreativeExtendData& creative_extend_data,
                     kuaishou::ad::tables::CreativeExtendScore* creative_extend_score) {
  if (!creative_extend_score) {
    return;
  }
  for (const auto& creative_label : creative_extend_data.creative_label()) {
    kuaishou::ad::tables::CreativeExtendScore::CreativeLabel tmp_creative_label;
    auto* dest = creative_extend_score->mutable_creative_label()->Add();
    dest->set_exp_id(creative_label.exp_id());
    dest->set_label(creative_label.label());
  }
  for (const auto& new_creative_tag :
       creative_extend_data.new_creative_tags()) {
    auto* map_ptr = creative_extend_score->mutable_new_creative_tag();
    (*map_ptr)[new_creative_tag.index()] = new_creative_tag.value();
  }
  creative_extend_score->set_circulation_type(creative_extend_data.circulation_type());
  for (auto unit_exp_feature_id : creative_extend_data.unit_exp_feature_id()) {
    creative_extend_score->add_unit_exp_feature_id(unit_exp_feature_id);
  }
  auto* similar_info = creative_extend_score->mutable_internal_photo_similar_info();
  similar_info->CopyFrom(creative_extend_data.internal_photo_similar_info());
}

static void AllSceneCreativeTagInfoCopy(const absl::flat_hash_map<int32_t, std::string>& score_map,
                                        kuaishou::ad::tables::Creative* creative) {
  base::JsonObject json;
  for (const auto& [scene, pb_str] : score_map) {
    kuaishou::ad::ad_creative_server::CreativeExtendData creative_extend_data;
    creative_extend_data.ParseFromString(pb_str);
    kuaishou::ad::tables::CreativeExtendScore creative_extend_score;
    Transfer(creative_extend_data, &creative_extend_score);
    std::string str;
    creative_extend_score.SerializeToString(&str);
    std::string encode_str;
    base::Base64Encode(str, &encode_str);
    if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_EXTERNAL_CIRCULATION_UNIT) { // NOLINT
     creative->set_creative_score_1(creative_extend_data.creative_score());
     json.set("creative_score_ext_1", encode_str);
    } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_DEFAULT) {
     creative->set_creative_score_2(creative_extend_data.creative_score());
     json.set("creative_score_ext_2", encode_str);
    } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_SEARCH) {
     creative->set_creative_score_3(creative_extend_data.creative_score());
     json.set("creative_score_ext_3", encode_str);
    } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_UNIVERSE_DEFAULT) {  // NOLINT
     creative->set_creative_score_4(creative_extend_data.creative_score());
     json.set("creative_score_ext_4", encode_str);
    }
  }
  std::string creative_score_ext = json.ToString();
  LOG_EVERY_N(INFO, 1000000) << "vanke test, creative_score_1=" << creative->creative_score_1()
                             << ", creative_score_2=" << creative->creative_score_2()
                             << ", creative_score_3=" << creative->creative_score_3()
                             << ", creative_score_4=" << creative->creative_score_4()
                             << ", creative_score_ext=" << creative_score_ext;
  creative->set_creative_score_ext(creative_score_ext);
}

static void CreativeTagInfoCopy(
           int32_t scene,
           const kuaishou::ad::ad_creative_server::CreativeExtendData& creative_extend_data,
           kuaishou::ad::tables::Creative* creative) {
  if (!creative) {
    return;
  }
  base::JsonObject json;
  kuaishou::ad::tables::CreativeExtendScore creative_extend_score;
  Transfer(creative_extend_data, &creative_extend_score);
  std::string str;
  creative_extend_score.SerializeToString(&str);
  std::string encode_str;
  base::Base64Encode(str, &encode_str);
  if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_EXTERNAL_CIRCULATION_UNIT) { // NOLINT
    creative->set_creative_score_1(creative_extend_data.creative_score());
    json.set("creative_score_ext_1", encode_str);
  } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_DEFAULT) {
    creative->set_creative_score_2(creative_extend_data.creative_score());
    json.set("creative_score_ext_2", encode_str);
  } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_SEARCH) {
    creative->set_creative_score_3(creative_extend_data.creative_score());
    json.set("creative_score_ext_3", encode_str);
  } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_UNIVERSE_DEFAULT) {  // NOLINT
    creative->set_creative_score_4(creative_extend_data.creative_score());
    json.set("creative_score_ext_4", encode_str);
  }
  std::string creative_score_ext = json.ToString();
  LOG_EVERY_N(INFO, 10000) << "vanke test, creative_score_1=" << creative->creative_score_1()
                           << ", creative_score_2=" << creative->creative_score_2()
                           << ", creative_score_3=" << creative->creative_score_3()
                           << ", creative_score_4=" << creative->creative_score_4()
                           << ", creative_score_ext=" << creative_score_ext;
  creative->set_creative_score_ext(creative_score_ext);
}

static std::string SingleCreativeTagInfoCopy(
           int32_t scene,
           const kuaishou::ad::ad_creative_server::CreativeExtendData& creative_extend_data,
           kuaishou::ad::tables::Creative* creative) {
  std::string encode_str;
  if (!creative) {
    return encode_str;
  }
  kuaishou::ad::tables::CreativeExtendScore creative_extend_score;
  Transfer(creative_extend_data, &creative_extend_score);
  std::string str;
  creative_extend_score.SerializeToString(&str);
  base::Base64Encode(str, &encode_str);
  if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_EXTERNAL_CIRCULATION_UNIT) { // NOLINT
    creative->set_creative_score_1(creative_extend_data.creative_score());
  } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_DEFAULT) {
    creative->set_creative_score_2(creative_extend_data.creative_score());
  } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_SEARCH) {
    creative->set_creative_score_3(creative_extend_data.creative_score());
  } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_UNIVERSE_DEFAULT) {  // NOLINT
    creative->set_creative_score_4(creative_extend_data.creative_score());
  }
  return encode_str;
}

// 查 redis 重试失败时，设置默认值
static void CreativeTagInfoCopy(int32_t scene, kuaishou::ad::tables::Creative* creative) {
  if (!creative) {
    return;
  }
  if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_EXTERNAL_CIRCULATION_UNIT) { // NOLINT
    creative->set_creative_score_1(60);
  } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_DEFAULT) {
    creative->set_creative_score_2(60);
  } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_SEARCH) {
    creative->set_creative_score_3(60);
  } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_UNIVERSE_DEFAULT) {  // NOLINT
    creative->set_creative_score_4(60);
  }
}


static void CreativeTagInfoCopyFromMergeData(const std::string& pb_str,
                                             kuaishou::ad::tables::Creative* creative,
                                             const std::string& scene) {
  kuaishou::ad::ad_creative_server::AggrCreativeExtendData aggr_creative_extend_data;
  if (!aggr_creative_extend_data.ParseFromString(pb_str)) {
    LOG_EVERY_N(ERROR, 10000) << "Parsing score data error!";
    return;
  }
  static const google::protobuf::Descriptor* desc = aggr_creative_extend_data.GetDescriptor();
  static const google::protobuf::Reflection* refl = aggr_creative_extend_data.GetReflection();
  const auto* field = desc->FindFieldByName(scene);
  if (!field) {
    LOG_EVERY_N(INFO, 10000) << "not field name " << scene << " found!";
    return;
  }
  const auto& creative_extend_data =
      dynamic_cast<const kuaishou::ad::ad_creative_server::CreativeExtendData&>(
          refl->GetMessage(aggr_creative_extend_data, field));
  CreativeTagInfoCopy(creative_extend_data, creative);
}

using ks::infra::PerfUtil;
using kuaishou::ad::AdEnum;
using namespace kuaishou::ad::tables;  //  NOLINT
// using ks::engine_base::DeployVariable;

using ks::index_adapter::FindLastestSuccessVersionOnHdfs;
// 返回从指定时间开始的，最老的版本。并且要求有比较新版本出现
bool FindOldVersionFromTime(const std::string& hdfs_path, int64_t start_tm, std::string* ret_version,
                            const std::string& suc_file) {
  if (ret_version == nullptr) return false;
  if (start_tm == 0) {
    LOG_EVERY_N(INFO, 40) << "invalid start_tm FindOldVersionFromTime fail, hdfs_path=" << hdfs_path;
    return false;
  }
  std::vector<hadoop::HDFSPathInfo> hdfs_files;
  if (!hadoop::HDFSListDirectory(hdfs_path.c_str(), &hdfs_files)) {
    LOG(ERROR) << "hadoop::HDFSListDirectory(" << hdfs_path << ") failed.";
    return false;
  }
  std::sort(hdfs_files.begin(), hdfs_files.end(),
            [](const hadoop::HDFSPathInfo& a, const hadoop::HDFSPathInfo& b) {
              return a.type > b.type || (a.type == b.type && a.name < b.name);
            });
  std::string old_version{""};
  for (auto hdfs_file : hdfs_files) {
    if (hdfs_file.type != hadoop::kDirectory) {
      LOG(ERROR) << "Invalid latest path: " << hdfs_file.name
                 << ", it's not a directory.";
      continue;
    }

    std::string path = hdfs_file.name;
    if (path.back() == '/') {
      path.pop_back();
    }
    std::string flag_file = path + "/" + suc_file;
    if (!hadoop::HDFSExists(flag_file.c_str())) {
      LOG(ERROR) << "flag_file: " << flag_file
                 << " does not exist, maybe not prepared.";
      continue;
    }
    size_t pos = path.rfind('/');
    auto version = path.substr(pos + 1);
    absl::Time t;
    std::string err;
    if (!absl::ParseTime("%Y-%m-%d_%H%M", version, absl::LocalTimeZone(), &t, &err)) {
      LOG(ERROR) << "absl::ParseTime(" << version << ") failed. err: " << err;
      continue;
    }
    int64_t ts = absl::ToUnixSeconds(t);
    // LOG(INFO) << "debugcc, prepare version, path=" << path << " start_tm=" << start_tm
    //           << " version=" << version << " ts=" << ts;
    if (ts <= start_tm) continue;
    if (old_version == "") {
      old_version = version;
      continue;
    }
    *ret_version = old_version;
    break;
  }
  LOG(INFO) << "prepare version, path=" << hdfs_path << " start_tm=" << start_tm
            << " version=" << *ret_version;
  return ret_version->empty() ? false : true;
}

bool FindVersionOnHdfs(const std::string& hdfs_path, std::string* version, const std::string& suc_flag,
                       bool auto_lock_version, int64_t start_tm) {
  if (version == nullptr) return false;
  LOG(INFO) << "find version on hdfs, hdfs_path=" << hdfs_path << " suc_flag=" << suc_flag
            << " auto_lock_version=" << auto_lock_version << " start_tm=" << start_tm;
  if (auto_lock_version) {
    return FindOldVersionFromTime(hdfs_path, start_tm, version, suc_flag);
  } else {
    return FindLastestSuccessVersionOnHdfs(hdfs_path, version, suc_flag);
  }
  return true;
}

std::string ScoreHdfsReader::DoReadFile() {
  std::vector<std::string> dest_files;
  std::vector<std::string> invalid_path;
  std::string min_version = "5999-12-31_2359";
  for (auto path : params_.data_paths()) {
    std::string version;
    std::string suc_flag = "_SUCCESS";
    if (FindVersionOnHdfs(path, &version, suc_flag, params_.auto_lock_version(), das_vesion_)) {
      min_version = std::min(version, min_version);
      ks::index_adapter::PerfManager::GetInstance()->UpdateSnapShot(path, version);
      std::vector<hadoop::HDFSPathInfo> hdfs_files;
      std::string dist_path = base::FilePath(path).Append(version).ToString();
      std::string suc_file =
          base::FilePath(path).Append(version).Append(suc_flag).ToString();
      LOG(INFO) << "detect path: " << dist_path;
      if (hadoop::HDFSListDirectory(dist_path.c_str(), &hdfs_files)) {
        for (auto hdfs_file : hdfs_files) {
          std::vector<std::string> words = absl::StrSplit(hdfs_file.name, "/", absl::SkipEmpty());
          if (words.empty()) {
            continue;
          }
          if (words.back() == suc_flag) {
            continue;
          }
          std::string target_file = base::FilePath(path)
                                        .Append(version)
                                        .Append(words.back())
                                        .ToString();
          dest_files.push_back(target_file);
          LOG(INFO) << target_file << " add to dest_files";
        }
      }
    } else {
      invalid_path.push_back(path);
    }
  }
  if (invalid_path.size() > 0) {
    LOG(ERROR) << "invalid path, size=" << invalid_path.size()
               << " path=" << absl::StrJoin(invalid_path, ",");
    return "";
  }
  thread::ThreadPool dump_pool{20};
  for (const std::string dest_file : dest_files) {
    dump_pool.AddTask(::NewCallback(this, &ScoreHdfsReader::Read, dest_file));
  }
  dump_pool.JoinAll();
  return min_version;
}

void ScoreHdfsReader::Read(std::string hdfs_file) {
  auto reader = ks::index_adapter::HDFSReader(hdfs_file);
  std::string content;
  int64_t record_num = 0;
  while (true) {
    reader.ReadLine(content);
    // LOG(INFO) << content;
    if (content.empty()) break;
    func_(&content);
    record_num++;
  }
  LOG(INFO) << "hdfs_file " << hdfs_file << " record_num " << record_num;
}

std::string AllSceneScoreHdfsReader::DoReadFile() {
  std::unordered_map<int32_t, std::vector<std::string>> scene_dest_files;
  std::vector<std::string> invalid_path;
  std::string min_version = "5999-12-31_2359";
  for (auto& info : params_.score_path_info()) {
    auto scene = info.scene();
    auto& data_paths = info.data_paths();
    std::vector<std::string> dest_files;
    for (auto path : data_paths) {
      std::string version;
      std::string suc_flag = "_SUCCESS";
      if (FindVersionOnHdfs(path, &version, suc_flag, params_.auto_lock_version(), das_version_)) {
        min_version = std::min(version, min_version);
        ks::index_adapter::PerfManager::GetInstance()->UpdateSnapShot(path, version);
        std::vector<hadoop::HDFSPathInfo> hdfs_files;
        std::string dist_path = base::FilePath(path).Append(version).ToString();
        std::string suc_file = base::FilePath(path).Append(version).Append(suc_flag).ToString();
        LOG(INFO) << "detect path: " << dist_path;
        if (hadoop::HDFSListDirectory(dist_path.c_str(), &hdfs_files)) {
          for (auto hdfs_file : hdfs_files) {
            std::vector<std::string> words = absl::StrSplit(hdfs_file.name, "/", absl::SkipEmpty());
            if (words.empty()) {
              continue;
            }
            if (words.back() == suc_flag) {
              continue;
            }
            std::string target_file = base::FilePath(path).Append(version).Append(words.back()).ToString();
            dest_files.push_back(target_file);
            LOG(INFO) << target_file << " add to dest_files";
          }
        }
      } else {
        invalid_path.push_back(path);
      }
    }
    scene_dest_files.emplace(scene, dest_files);
  }
  if (invalid_path.size() > 0) {
    LOG(ERROR) << "creative server file not ready, size=" << invalid_path.size()
               << " path=" << absl::StrJoin(invalid_path, ",");
    return "";
  }
  thread::ThreadPool dump_pool{20};
  for (auto& val : scene_dest_files) {
    int32_t scene = val.first;
    for (const std::string dest_file : val.second) {
      dump_pool.AddTask(::NewCallback(this, &AllSceneScoreHdfsReader::Read, dest_file, scene));
    }
  }
  dump_pool.JoinAll();
  return min_version;
}

void AllSceneScoreHdfsReader::Read(std::string hdfs_file, int32_t scene) {
  auto reader = ks::index_adapter::HDFSReader(hdfs_file);
  std::string content;
  int64_t record_num = 0;
  while (true) {
    reader.ReadLine(content);
    // LOG(INFO) << content;
    if (content.empty()) break;
    func_(&content, scene);
    record_num++;
  }
  LOG(INFO) << "scene= " << scene << ", hdfs_file " << hdfs_file << " record_num " << record_num;
}


void AdapterForCreativeServer::OldInit() {
  creative_adapter_param_ =
      CreativeAdapterKconf::creativeAdapterParam()->data();
  InitImpl();
}
void AdapterForCreativeServer::Init(const std::string& key, bool use_aggr_data, BuildJobMode mode) {
  if (mode == BuildJobMode::UNKNOWN) {
    std::string product_name;
    if (getenv("KWS_PRODUCT_NAME") != NULL) {
      product_name = getenv("KWS_PRODUCT_NAME");
    }
    LOG(INFO) << product_name;
    /*
      proxy 兜底情况下可加载 snap
    */
    if ("ad_index_message_proxy" == product_name &&
        !CreativeAdapterKconf::proxyLoadSnap()) {
      non_snap_load_ = true;
    }
  } else if (mode == BuildJobMode::INC) {
    non_snap_load_ = true;
  }
  use_aggr_data_ = use_aggr_data;
  if (key.empty()) {
    OldInit();
    return;
  }
  auto kvs = CreativeAdapterKconf::creativeAdapterConfigs()->data();
  LOG(INFO) << "vanke test creative_adapter_key=" << key;
  LOG_ASSERT(kvs.params().find(key) != kvs.params().end());
  creative_adapter_param_ = kvs.params().find(key)->second;
  InitImpl();
}

void AdapterForCreativeServer::InitImpl() {
  LOG(INFO) << creative_adapter_param_.ShortDebugString();
  all_scene_ = creative_adapter_param_.enable_all_scene();
  kcc_name_ = creative_adapter_param_.kcc_name();
  prefix_ = creative_adapter_param_.prefix();
  scene_ = creative_adapter_param_.scene();
  if (all_scene_) {
    // 全场景增量打分需要
    for (const auto& info : creative_adapter_param_.score_path_info()) {
      redis_prefix_map_[info.scene()] = info.prefix();
    }
  }
  if (use_aggr_data_) {
    creative_adapter_param_.clear_data_paths();
    for (int shard = 0; shard < 10; ++shard) {
      creative_adapter_param_.add_data_paths(
          absl::StrCat("/home/<USER>/ad_cache/creative_score_snapshot/PROD/", shard));
    }
  }

  if (!all_scene_) {
    if (creative_adapter_param_.data_paths().empty()) {
      non_snap_load_ = true;
    }
    update_thread_ = std::thread([this]() { UpdateThread(); });
  } else {
    update_thread_ = std::thread([this]() { UpdateAllSceneThread(); });
  }

  LOG(INFO) << "snap function forbid: " << (non_snap_load_ ? "true" : "false");

  if (kcc_name_.empty()) {
    redis_client_ = nullptr;
    LOG(INFO) << "non kcc_name, non scene!";
  } else {
    redis_client_ =
        infra::RedisProxyClient::GetRedisClientByKccFromKconf(kcc_name_, 10);
    LOG_ASSERT(redis_client_ != nullptr)
        << "kcc_name: " << kcc_name_ << " empty or kcc init fail";
  }
}
AdapterForCreativeServer::~AdapterForCreativeServer() {
  if (redis_client_) {
    delete redis_client_;
    redis_client_ = nullptr;
  }
  running_.store(false);
  update_thread_.join();
}

void AdapterForCreativeServer::UpdateThread() {
  if (non_snap_load_) {
    LOG(INFO) << "creative_adapter_param: data_paths empty update_thread end";
    return;
  }
  while (running_.load()) {
    std::function<void(std::string*)> func = [&](std::string* line) {
      // DO NOTING AT PRESENT
      std::lock_guard<std::mutex> guard(mtx_);
      // LOG(INFO) << *line;
      std::vector<std::string> words =
          absl::StrSplit(*line, "\t", absl::SkipEmpty());
      if (words.size() != 2) {
        return;
      }
      int64_t creative_id = std::stoll(words[0]);
      std::string decode_string;
      if (!base::Base64Decode(words[1], &decode_string)) {
        LOG_EVERY_N(INFO, 1000) << creative_id << " parse fails " << words[1];
        return;
      }
      data_[write_index_.load()].emplace(creative_id, decode_string);
    };
    data_[write_index_.load()].clear();
    LOG(INFO) << "Begin to Update Score From hdfs, das_version_=" << das_version_
              << " auto_load=" << creative_adapter_param_.auto_lock_version();
    if (creative_adapter_param_.auto_lock_version() && das_version_ == 0) {
      LOG(INFO) << "autoload is true, and das_version is zero, skip";
      std::this_thread::sleep_for(std::chrono::seconds{20});
      continue;
    }
    ScoreHdfsReader score_reader(func, creative_adapter_param_, das_version_);
    auto version = score_reader.DoReadFile();
    PerfUtil::SetLogStash(data_[write_index_.load()].size(), "ad.index_builder",
                          "snap_total_size",
                          ks::engine_base::DeployVariable::GetKwsName(),
                          ks::engine_base::DeployVariable::GetPodName());
    if (version == "") {
      LOG(ERROR) << "update score from hdfs fail";
      std::this_thread::sleep_for(std::chrono::seconds{20});
      continue;
    }
    LOG(INFO) << "snap total size: " << data_[write_index_.load()].size();
    absl::Time t;
    std::string err;
    if (absl::ParseTime("%Y-%m-%d_%H%M", version, absl::LocalTimeZone(), &t, &err)) {
      buffer_ts_[write_index_] = absl::ToUnixSeconds(t);
    } else {
      LOG(ERROR) << "absl::ParseTime(" << version << ") failed. err: " << err;
    }
    // 切换 buffer
    write_index_ = 1 - write_index_;
    std::this_thread::sleep_for(std::chrono::seconds{20});
  }
}

void AdapterForCreativeServer::UpdateAllSceneThread() {
  if (non_snap_load_) {
    LOG(INFO) << "creative_adapter_param: data_paths empty update_thread end";
    return;
  }
  while (running_.load()) {
    std::function<void(std::string*, int32_t)> func = [&](std::string* line, int32_t scene) {
      // DO NOTING AT PRESENT
      std::lock_guard<std::mutex> guard(mtx_);
      // LOG(INFO) << *line;
      std::vector<std::string> words =
          absl::StrSplit(*line, "\t", absl::SkipEmpty());
      if (words.size() != 2) {
        return;
      }
      int64_t creative_id = std::stoll(words[0]);
      std::string decode_string;
      if (!base::Base64Decode(words[1], &decode_string)) {
        LOG_EVERY_N(INFO, 1000) << creative_id << " parse fails " << words[1];
        return;
      }
      auto& map = all_data_[write_index_.load()];
      auto itr = map.find(creative_id);
      if (itr != map.end()) {
        itr->second.emplace(scene, decode_string);
      } else {
        auto& inner_map = map[creative_id];
        inner_map.emplace(scene, decode_string);
      }
    };
    all_data_[write_index_.load()].clear();
    LOG(INFO) << "Begin to Update Score From hdfs, das_version_=" << das_version_
              << " auto_lock=" << creative_adapter_param_.auto_lock_version();
    if (creative_adapter_param_.auto_lock_version() && das_version_ == 0) {
      LOG(INFO) << "autolock is true, and das_version is zero, skip";
      std::this_thread::sleep_for(std::chrono::seconds{20});
      continue;
    }
    AllSceneScoreHdfsReader score_reader(func, creative_adapter_param_, das_version_);
    auto version = score_reader.DoReadFile();
    PerfUtil::SetLogStash(all_data_[write_index_.load()].size(), "ad.index_builder",
                          "snap_total_size",
                          ks::engine_base::DeployVariable::GetKwsName(),
                          ks::engine_base::DeployVariable::GetPodName());
    if (version == "") {
      LOG(ERROR) << "update score from hdfs fail";
      std::this_thread::sleep_for(std::chrono::seconds{20});
      continue;
    }
    LOG(INFO) << "snap total size: " << all_data_[write_index_.load()].size();
    absl::Time t;
    std::string err;
    if (absl::ParseTime("%Y-%m-%d_%H%M", version, absl::LocalTimeZone(), &t, &err)) {
      buffer_ts_[write_index_] = absl::ToUnixSeconds(t);
    } else {
      LOG(ERROR) << "absl::ParseTime(" << version << ") failed. err: " << err;
    }
    // 切换 buffer
    write_index_ = 1 - write_index_;
    std::this_thread::sleep_for(std::chrono::seconds{20});
  }
}

void AdapterForCreativeServer::WaitForVersionReady(int64_t ts) {
  if (creative_adapter_param_.auto_lock_version()) {
    while (std::min(buffer_ts_[0], buffer_ts_[1]) < ts) {
      LOG_EVERY_N(INFO, 1) << "Creative Server not ready for " << ts;
      std::this_thread::sleep_for(std::chrono::seconds(2));
    }
    LOG(INFO) << "Creative SnapShot version " << std::min(buffer_ts_[0], buffer_ts_[1])
              << "  Build Index version " << ts;
    return;
  }
  // while (std::max(buffer_ts_[0], buffer_ts_[1]) != 0 && std::min(buffer_ts_[0], buffer_ts_[1]) < ts) {
  while (std::max(buffer_ts_[0], buffer_ts_[1]) == 0) {
    LOG_EVERY_N(INFO, 1) << "Creative Server not ready for " << ts;
    std::this_thread::sleep_for(std::chrono::seconds(2));
  }
  LOG_EVERY_N(INFO, 1) << "Creative SnapShot version " << std::min(buffer_ts_[0], buffer_ts_[1])
                       << "  Build Index version " << ts;
  return;
}
bool AdapterForCreativeServer::ProcessForInc(
    kuaishou::ad::AdInstance* ad_instance) {
  if (!all_scene_)  {
    auto ret = ProcessForIncImp(ad_instance);
    UpdateCreativeScoreByConfig(ad_instance);
    return ret;
  } else {
    auto ret = ProcessForIncImpAllScene(ad_instance);
    return ret;
  }
}

bool AdapterForCreativeServer::ProcessForIncImp(
    kuaishou::ad::AdInstance* ad_instance) {
  auto type = ad_instance->type();
  if (type == AdEnum::CREATIVE && !kcc_name_.empty()) {
    auto* creative = ad_instance->MutableExtension(
        kuaishou::ad::tables::Creative::creative_old);
    auto match_fuc = [](const std::string& field_name) {
      static std::set<std::string> creative_server_modify_fields{
          "creative_score", "creative_score_ext"};
      return creative_server_modify_fields.count(field_name) > 0;
    };
    // 来自本场景的 CreativeServer 打分直接赋值分数
    if (creative->creative_score_3() == static_cast<int64>(scene_)) {
      ad_instance->add_modify_field_list("creative_score");
      creative->set_creative_score(creative->creative_score_4());
      auto* extend_fields = creative->mutable_extend_fields();
      auto* creative_extend_score =
          extend_fields->mutable_creative_extend_score();
      std::string string_value = creative->creative_score_ext();
      google::protobuf::util::JsonParseOptions options;
      options.ignore_unknown_fields = true;
      auto status = google::protobuf::util::JsonStringToMessage(
          string_value, creative_extend_score, options);
      if (!status.ok()) {
        LOG_EVERY_N(WARNING, 1000) << string_value;
      }
      return true;
    }
    /*
      note:
        其他 creative_server 打分有 2 个来源：
        默认的 creative （modify_field 有 creative_score ）
        分场景的其他场景的 creative_score_3 不为 0
    */
    // 其他场景的 CreativeServer 打分过滤掉
    if (creative->creative_score_3() != 0) {
      return false;
    }
    const auto& modify_field_list = ad_instance->modify_field_list();
    // 其他场景 CreativeServer 打分
    if (std::any_of(modify_field_list.cbegin(), modify_field_list.cend(),
                    match_fuc)) {
      return false;
    }
    LoadSceneInfoFromRedisData(ad_instance);
  }
  return true;
}

bool AdapterForCreativeServer::ProcessForIncImpAllScene(
    kuaishou::ad::AdInstance* ad_instance) {
  auto type = ad_instance->type();
  if (type != AdEnum::CREATIVE) {
    return true;
  }
  auto* creative = ad_instance->MutableExtension(
      kuaishou::ad::tables::Creative::creative_old);
  auto match_fuc = [](const std::string& field_name) {
    static std::set<std::string> creative_server_modify_fields{
        "creative_score", "creative_score_ext"};
    return creative_server_modify_fields.count(field_name) > 0;
  };
  auto fill_func = [&](const std::string& key) {
    auto* extend_fields = creative->mutable_extend_fields();
    auto* creative_extend_score =
        extend_fields->mutable_creative_extend_score();
    std::string string_value = creative->creative_score_ext();
    google::protobuf::util::JsonParseOptions options;
    options.ignore_unknown_fields = true;
    auto status = google::protobuf::util::JsonStringToMessage(
        string_value, creative_extend_score, options);
    if (!status.ok()) {
      LOG_EVERY_N(WARNING, 1000) << string_value;
    }
    std::string encode_str;
    std::string str;
    creative_extend_score->SerializeToString(&str);
    base::Base64Encode(str, &encode_str);
    base::JsonObject json;
    json.set(key, encode_str);
    std::string creative_score_ext = json.ToString();
    LOG_EVERY_N(INFO, 10000) << "vanke test, creative_score_1=" << creative->creative_score_1()
                             << ", creative_score_2=" << creative->creative_score_2()
                             << ", creative_score_3=" << creative->creative_score_3()
                             << ", creative_score_4=" << creative->creative_score_4()
                             << ", creative_score_ext=" << creative_score_ext;
    creative->set_creative_score_ext(creative_score_ext);
  };
  auto scene = creative->creative_score_3();
  PerfUtil::CountLogStash(1, "ad.index_builder", "creative_scene", std::to_string(scene),
                          ks::engine_base::DeployVariable::GetKwsName(),
                          ks::engine_base::DeployVariable::GetPodName());

  // 外循环直投直播附上全场景分数
  if (creative->live_creative_type() == AdEnum::DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
    LoadAllSceneInfoFromRedisData(ad_instance);
    return true;
  } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_EXTERNAL_CIRCULATION_UNIT) { // NOLINT
    ad_instance->add_modify_field_list("score_external");
    creative->set_creative_score_1(creative->creative_score_4());
    fill_func("creative_score_ext_1");
    return true;
  } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_DEFAULT) {
    ad_instance->add_modify_field_list("score_internal");
    creative->set_creative_score_2(creative->creative_score_4());
    fill_func("creative_score_ext_2");
    return true;
  } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_SEARCH) {
    ad_instance->add_modify_field_list("score_search");
    creative->set_creative_score_3(creative->creative_score_4());
    fill_func("creative_score_ext_3");
    return true;
  } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_UNIVERSE_DEFAULT) {  // NOLINT
    ad_instance->add_modify_field_list("score_universe");
    creative->set_creative_score_4(creative->creative_score_4());
    fill_func("creative_score_ext_4");
    return true;
  } else {
    // DAS creative 增量
    // 查 redis
    PerfUtil::CountLogStash(1, "ad.index_builder", "das_inc_qps",
                            ks::engine_base::DeployVariable::GetKwsName(),
                            ks::engine_base::DeployVariable::GetPodName());
    LOG_EVERY_N(INFO, 10000) << "vanke test, creative_scene=" << scene;
    LoadAllSceneInfoFromRedisData(ad_instance);
  }
  return true;
}

// dpa 场景都置为 true
bool AdapterForCreativeServer::DpaSpecailProcess(
    kuaishou::ad::tables::Creative* creative) {
  // dpa 没有打分记录
  if (creative->create_source_type() == AdEnum::DPA_SOURCE_CREATIVE) {
    creative->set_creative_score(60);
    return true;
  }
  // sdpa 在 dpa 场景没有打分，都置为上线
  if (CreativeAdapterKconf::sdpaCreativeSetOnline() &&
      creative->create_source_type() == AdEnum::SDPA_SOURCE_CREATIVE) {
    creative->set_creative_score(60);
    return true;
  }
  return false;
}

bool AdapterForCreativeServer::LoadAllSceneInfoFromSnap(
    kuaishou::ad::tables::Creative* creative) {
  auto creative_id = creative->id();
  // dpa 没有打分记录
  if (DpaSpecailProcess(creative)) {
    return true;
  }
  int64_t read_index = 1 - write_index_.load();
  // 有快照则读取快照，读快照不成功置为 0
  if (creative_adapter_param_.score_path_info_size() > 0) {
    if (all_data_[read_index].find(creative_id) == all_data_[read_index].end()) {
      PerfUtil::CountLogStash(1, "ad.index_builder", "scene_get_from_snap_fail",
                              ks::engine_base::DeployVariable::GetKwsName(),
                              ks::engine_base::DeployVariable::GetPodName());
      // load 失败 则置为下线
      creative->set_creative_score(0);
      return false;
    }
    auto& score_map = all_data_[read_index].at(creative_id);
    AllSceneCreativeTagInfoCopy(score_map, creative);
    PerfUtil::CountLogStash(1, "ad.index_builder", "scene_get_from_snap_suc",
                            ks::engine_base::DeployVariable::GetKwsName(),
                            ks::engine_base::DeployVariable::GetPodName());
  }
  return true;
}

bool AdapterForCreativeServer::LoadSceneInfoFromSnap(
    kuaishou::ad::tables::Creative* creative) {
  auto creative_id = creative->id();
  // dpa 没有打分记录
  if (DpaSpecailProcess(creative)) {
    return true;
  }
  int64_t read_index = 1 - write_index_.load();
  // 有快照则读取快照，读快照不成功置为 0

  if (creative_adapter_param_.data_paths().size() > 0) {
    if (data_[read_index].find(creative_id) == data_[read_index].end()) {
      PerfUtil::CountLogStash(1, "ad.index_builder", "scene_get_from_snap_fail",
                              ks::engine_base::DeployVariable::GetKwsName(),
                              ks::engine_base::DeployVariable::GetPodName());
      // load 失败 则置为下线
      creative->set_creative_score(0);
      return false;
    }
    std::string tmp_score_data_str = data_[read_index].at(creative_id);
    if (use_aggr_data_) {
      CreativeTagInfoCopyFromMergeData(tmp_score_data_str, creative, prefix_);
    } else {
      CreativeTagInfoCopy(tmp_score_data_str, creative);
    }
    PerfUtil::CountLogStash(1, "ad.index_builder", "scene_get_from_snap_suc",
                            ks::engine_base::DeployVariable::GetKwsName(),
                            ks::engine_base::DeployVariable::GetPodName());
  }
  return true;
}

bool AdapterForCreativeServer::LoadSceneInfoFromSnap(
    kuaishou::ad::AdInstance* ad_instance) {
  auto type = ad_instance->type();
  if (type != AdEnum::CREATIVE) {
    return true;
  }
  auto* creative = ad_instance->MutableExtension(
      kuaishou::ad::tables::Creative::creative_old);
  if (all_scene_) {
    return LoadAllSceneInfoFromSnap(creative);
  } else {
    return LoadSceneInfoFromSnap(creative);
  }
}

bool AdapterForCreativeServer::ProcessForFull(
    kuaishou::ad::AdInstance* ad_instance) {
  auto ret = LoadSceneInfoFromSnap(ad_instance);
  if (!all_scene_) {
    UpdateCreativeScoreByConfig(ad_instance);
  }
  return ret;
}

void AdapterForCreativeServer::LoadSceneInfoFromRedisData(
    kuaishou::ad::AdInstance* ad_instance) {
  auto type = ad_instance->type();

  if (type == AdEnum::CREATIVE) {
    auto* creative = ad_instance->MutableExtension(
        kuaishou::ad::tables::Creative::creative_old);
    if (DpaSpecailProcess(creative)) {
      return;
    }
  }

  if (type == AdEnum::CREATIVE && !kcc_name_.empty()) {
    auto* creative = ad_instance->MutableExtension(
        kuaishou::ad::tables::Creative::creative_old);
    auto creative_id = creative->id();

    std::string key = absl::Substitute("$0_$1", prefix_, creative_id);
    LOG_FIRST_N(INFO, 50) << "key: " << key;
    std::string value;
    /*
      重试只有一次
    */
    if (redis_client_) {
      int64_t retry_times = 1;
      kuaishou::ad::ad_creative_server::CreativeExtendData creative_extend_data;
      while (retry_times > 0) {
        auto error_code = redis_client_->Get(key, &value, 5);
        if (error_code == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
          creative_extend_data.ParseFromString(value);
          CreativeTagInfoCopy(creative_extend_data, creative);
          break;
        } else {
          retry_times--;
        }
      }
      if (retry_times == 0) {
        PerfUtil::CountLogStash(1, "ad.index_builder", "scene_get_fail",
                                ks::engine_base::DeployVariable::GetKwsName(),
                                ks::engine_base::DeployVariable::GetPodName());
        LOG_EVERY_N(INFO, 10000) << "key: " << key << " fails";
        creative->set_creative_score(60);
        // LoadSceneInfoFromSnap(ad_instance);
      } else {
        PerfUtil::CountLogStash(1, "ad.index_builder", "scene_get_suc",
                                ks::engine_base::DeployVariable::GetKwsName(),
                                ks::engine_base::DeployVariable::GetPodName());
        LOG_EVERY_N(INFO, 10000) << "key: " << key << " creative_extend_data:"
                                 << creative_extend_data.ShortDebugString();
      }
    }
  }
}

void AdapterForCreativeServer::LoadAllSceneInfoFromRedisData(
    kuaishou::ad::AdInstance* ad_instance) {
  auto type = ad_instance->type();

  if (type == AdEnum::CREATIVE) {
    auto* creative = ad_instance->MutableExtension(
        kuaishou::ad::tables::Creative::creative_old);
    if (DpaSpecailProcess(creative)) {
      return;
    }
  }

  if (type == AdEnum::CREATIVE && !kcc_name_.empty()) {
    auto* creative = ad_instance->MutableExtension(
        kuaishou::ad::tables::Creative::creative_old);
    auto creative_id = creative->id();
    base::JsonObject json;
    for (const auto& [scene, prefix] : redis_prefix_map_) {
      std::string key = absl::Substitute("$0_$1", prefix, creative_id);
      LOG_FIRST_N(INFO, 50) << "key: " << key;
      std::string value;
      if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_EXTERNAL_CIRCULATION_UNIT) { // NOLINT
        ad_instance->add_modify_field_list("score_external");
      } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_DEFAULT) {
        ad_instance->add_modify_field_list("score_internal");
      } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_SEARCH) {
        ad_instance->add_modify_field_list("score_search");
      } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_UNIVERSE_DEFAULT) {  // NOLINT
        ad_instance->add_modify_field_list("score_universe");
      }
      /*
        重试只有一次
      */
      if (redis_client_) {
        int64_t retry_times = 1;
        kuaishou::ad::ad_creative_server::CreativeExtendData creative_extend_data;
        while (retry_times > 0) {
          auto error_code = redis_client_->Get(key, &value, 5);
          if (error_code == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
            creative_extend_data.ParseFromString(value);
            std::string encode_str = SingleCreativeTagInfoCopy(scene, creative_extend_data, creative);
            LOG_FIRST_N(INFO, 50) << "vanke key: " << key << ", scene=" << scene
                                  << ", encode_str=" << encode_str;
            if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_EXTERNAL_CIRCULATION_UNIT) { // NOLINT
              json.set("creative_score_ext_1", encode_str);
            } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_DEFAULT) {
              json.set("creative_score_ext_2", encode_str);
            } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_SEARCH) {
              json.set("creative_score_ext_3", encode_str);
            } else if (scene == kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_UNIVERSE_DEFAULT) {  // NOLINT
              json.set("creative_score_ext_4", encode_str);
            }
            break;
          } else {
            retry_times--;
          }
        }
        if (retry_times == 0) {
          PerfUtil::CountLogStash(1, "ad.index_builder", "scene_get_fail",
                                  ks::engine_base::DeployVariable::GetKwsName(),
                                  ks::engine_base::DeployVariable::GetPodName());
          LOG_EVERY_N(INFO, 10000) << "key: " << key << " fails";
          CreativeTagInfoCopy(scene, creative);
        } else {
          PerfUtil::CountLogStash(1, "ad.index_builder", "scene_get_suc",
                                  ks::engine_base::DeployVariable::GetKwsName(),
                                  ks::engine_base::DeployVariable::GetPodName());
          LOG_EVERY_N(INFO, 10000) << "key: " << key << " creative_extend_data:"
                                   << creative_extend_data.ShortDebugString();
        }
      }
    }
    // 填充打分数据
    std::string creative_score_ext = json.ToString();
    LOG_EVERY_N(INFO, 10000) << "vanke test, creative_score_1=" << creative->creative_score_1()
                             << ", creative_score_2=" << creative->creative_score_2()
                             << ", creative_score_3=" << creative->creative_score_3()
                             << ", creative_score_4=" << creative->creative_score_4()
                             << ", creative_score_ext=" << creative_score_ext;
    creative->set_creative_score_ext(creative_score_ext);
  }
}

bool AdapterForCreativeServer::ProcessForFull(
    google::protobuf::Message* message) {
  if (message->GetTypeName() == "kuaishou.ad.tables.Creative") {
    auto* creative = dynamic_cast<kuaishou::ad::tables::Creative*>(message);
    if (creative) {
      LoadSceneInfoFromSnap(creative);
      UpdateCreativeScoreByConfig(creative);
    }
  }
  return true;
}

void AdapterForCreativeServer::UpdateCreativeScoreByConfig(
    kuaishou::ad::AdInstance* ad_instance) {
  auto msg = GetExtensionField(ad_instance);
  UpdateCreativeScoreByConfig(msg);
}

void AdapterForCreativeServer::UpdateCreativeScoreByConfig(
    google::protobuf::Message* message) {
  static auto creative_scene = creative_adapter_param_.creative_scene();
  if (message->GetTypeName() == "kuaishou.ad.tables.Creative") {
    auto* creative = dynamic_cast<kuaishou::ad::tables::Creative*>(message);
    int64_t creative_score = creative->creative_score();
    if (ks::engine_base::SceneParam::HOT_AND_COLD == creative_scene) {
      if (creative_score == 40) {
        creative->set_creative_score(60);
      }
    } else if (ks::engine_base::SceneParam::ALL == creative_scene) {
      if (creative_score < 60) {
        creative->set_creative_score(60);
      }
    } else if (ks::engine_base::SceneParam::COLD_ONLY == creative_scene) {
      if (creative_score == 40) {
        creative->set_creative_score(60);
      } else {
        creative->set_creative_score(0);
      }
    }
  }
}

}  // namespace index_builder
}  // namespace ks
