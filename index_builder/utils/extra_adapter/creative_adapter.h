#pragma once
#include <sys/types.h>
#include <array>
#include <functional>
#include <string>
#include <unordered_map>
#include <vector>
#include <mutex>
#include "absl/container/flat_hash_map.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_creative_server/proto/creative_score.pb.h"
#include "teams/ad/index_builder/utils/extra_adapter/kconf.h"
#include "teams/ad/data_build/utils/build_job_config.pb.h"
/*
  对接分部署的 creative_server 产出的 creative_db
*/
namespace ks {

using ks::ad::build_service::BuildJobMode;
using kuaishou::ad::ad_creative_server::CreativeExtendData;
namespace index_builder {
class ScoreHdfsReader {
 public:
  ScoreHdfsReader(std::function<void(std::string*)> func, CreativeAdapterParam param, int64_t das_version)
      : func_(func), params_(param), das_vesion_(das_version) {}
  std::string DoReadFile();
  void Read(std::string hdfs_file);

 private:
  std::function<void(std::string*)> func_;
  CreativeAdapterParam params_;
  int64_t das_vesion_{0};
};
// 全场景
class AllSceneScoreHdfsReader {
 public:
  AllSceneScoreHdfsReader(std::function<void(std::string*, int32_t)> func, CreativeAdapterParam param,
                          int64_t das_version)
      : func_(func), params_(param), das_version_(das_version) {}
  std::string DoReadFile();
  void Read(std::string hdfs_file, int32_t scene);

 private:
  std::function<void(std::string*, int32_t)> func_;
  CreativeAdapterParam params_;
  int64_t das_version_{0};
};

class AdapterForCreativeServer {
 public:
  AdapterForCreativeServer() {}
  ~AdapterForCreativeServer();
  void Init(const std::string & key, bool use_aggr_data = false,
    BuildJobMode mode = BuildJobMode::UNKNOWN);
  static AdapterForCreativeServer* GetInstance() {
    static AdapterForCreativeServer inst;
    return &inst;
  }
  void SetDasVersion(int64_t das_version) {
    das_version_ = das_version;
    LOG(INFO) << "ccdebug, set das version, " << das_version;
  }
  /*
    考虑到增量,如果是新数据 & 字段由其他场景 CreativeServer触发，则过滤
    读 redis 失败则读通过快照兜底
  */
  bool ProcessForInc(kuaishou::ad::AdInstance* ad_instance);
  /*
    全量则都要处理.
    先从 snap 尝试读取, 不成功则僵尸直接跳过
  */
  bool ProcessForFull(kuaishou::ad::AdInstance* ad_instance);
  void WaitForVersionReady(int64_t ts);

  // 加载成功 or 没有快照 or message_proxy
  bool IsReady() {
    if (creative_adapter_param_.auto_lock_version()) {
      LOG(INFO) << "auto lock is enable, skip wait ready";
      return true;
    }
    if (!all_scene_) {
      return data_[1 - write_index_.load()].size() != 0 || non_snap_load_;
    } else {
      return all_data_[1 - write_index_.load()].size() != 0 || non_snap_load_;
    }
  }

  bool ProcessForFull(google::protobuf::Message* messag);

 private:
  // todo(zhangruyuan): 老的构造逻辑，迁移后删除
  void OldInit();
  void InitImpl();
  bool ProcessForIncImp(kuaishou::ad::AdInstance* ad_instance);
  void LoadSceneInfoFromRedisData(kuaishou::ad::AdInstance* ad_instance);
  bool LoadSceneInfoFromSnap(kuaishou::ad::AdInstance* ad_instance);
  bool LoadSceneInfoFromSnap(kuaishou::ad::tables::Creative* creative);
  // dpa 场景都置为 true
  bool DpaSpecailProcess(kuaishou::ad::tables::Creative* creative);
  void UpdateThread();
  // 全量全场景打分
  bool LoadAllSceneInfoFromSnap(kuaishou::ad::tables::Creative* creative);
  void UpdateAllSceneThread();
  // 增量全场景打分
  bool ProcessForIncImpAllScene(kuaishou::ad::AdInstance* ad_instance);
  void LoadAllSceneInfoFromRedisData(kuaishou::ad::AdInstance* ad_instance);

  /*
    ALL = score < 60 set 60
    COLDANDHOT = score = 40 set 60
    COLD_ONLY = score = 40 set 60, other set to 0
  */
  void UpdateCreativeScoreByConfig(kuaishou::ad::AdInstance* ad_instance);
  void UpdateCreativeScoreByConfig(google::protobuf::Message* message);

  ::ks::infra::RedisClient *redis_client_;
  std::string prefix_;
  std::string kcc_name_;
  kuaishou::ad::AdEnum::CreativeServerBizType scene_;
  CreativeAdapterParam creative_adapter_param_;
  bool use_aggr_data_{false};
  // 没有 snap 数据或者是 message_proxy
  bool non_snap_load_{false};
  // for read score from hdfs
  std::mutex mtx_;
  std::thread update_thread_;
  std::thread all_scene_update_thread_;
  std::atomic_bool running_{true};
  std::atomic_int write_index_{0};
  absl::flat_hash_map<int64_t, std::string> data_[2];
  // 全场景打分
  bool all_scene_ {false};
  absl::flat_hash_map<int64_t, absl::flat_hash_map<int32_t, std::string>> all_data_[2];
  std::array<int64_t, 2> buffer_ts_{};
  int64_t das_version_{0};
  std::unordered_map<int32_t, std::string> redis_prefix_map_;
};
}  // namespace index_builder
}  // namespace ks
