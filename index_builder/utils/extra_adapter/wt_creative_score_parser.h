#pragma once
#include <functional>
#include <string>
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/extra_adapter/kconf.h"

namespace ks {
namespace index_builder {

class WTCreativeScoreParser {
 public:
  WTCreativeScoreParser() {}
  ~WTCreativeScoreParser();
  void Init(const std::string& key);
  static WTCreativeScoreParser& GetInstance() {
    static WTCreativeScoreParser instance;
    return instance;
  }
  bool ProcessWTCreativeFull(kuaishou::ad::AdInstance* ad_instance);
  bool ProcessWTCreativeInc(kuaishou::ad::AdInstance* ad_instance);


 private:
  bool ProcessWTCreativeImp(kuaishou::ad::AdInstance* ad_instance);
  bool DpaSpecailProcess(kuaishou::ad::tables::Creative* creative);
  kuaishou::ad::AdEnum::CreativeServerBizType scene_;
  CreativeAdapterParam creative_adapter_param_;
};


}  // namespace index_builder
}  // namespace ks
