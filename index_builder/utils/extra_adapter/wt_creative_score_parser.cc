#include "teams/ad/index_builder/utils/extra_adapter/wt_creative_score_parser.h"
#include <unordered_map>
#include <unordered_set>
#include "teams/ad/index_adapter/utils/perf_manager/perf_manager.h"
#include "base/encoding/base64.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/engine_base/utils/deploy_variable.h"
#include "teams/ad/engine_base/material_feature_type/material_feature_type.h"
#include "teams/ad/index_builder/utils/extra_adapter/kconf.h"


namespace ks {
namespace index_builder {
using ks::infra::PerfUtil;
using kuaishou::ad::AdEnum;

static const std::unordered_map<kuaishou::ad::AdEnum::CreativeServerBizType, std::string> SceneMap = {
  {kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_EXTERNAL_CIRCULATION_UNIT, "score_external"}, // NOLINT
  {kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_DEFAULT, "score_internal"},
  {kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_SEARCH, "score_search"},
  {kuaishou::ad::AdEnum_CreativeServerBizType_CREATIVE_SERVER_BIZ_TYPE_UNIVERSE_DEFAULT, "score_universe"}
};

WTCreativeScoreParser::~WTCreativeScoreParser() {}

void WTCreativeScoreParser::Init(const std::string& key) {
  auto kvs = CreativeAdapterKconf::creativeAdapterConfigs()->data();
  std::string adapter_key = key;
  if (adapter_key.empty()) {
    adapter_key = "dummy";
  }
  LOG_ASSERT(kvs.params().find(adapter_key) != kvs.params().end());
  creative_adapter_param_ = kvs.params().find(adapter_key)->second;
  scene_ = creative_adapter_param_.scene();
}

bool WTCreativeScoreParser::ProcessWTCreativeInc(kuaishou::ad::AdInstance* ad_instance) {
  auto type = ad_instance->type();
  if (type == AdEnum::WT_CREATIVE) {
    auto scene_itr = SceneMap.find(scene_);
    if (scene_itr == SceneMap.end()) {
      return false;
    }
    std::string score_scene = scene_itr->second;
    // 增量处理逻辑
    const auto& modify_field_list = ad_instance->modify_field_list();
    std::unordered_set<std::string> modify_field_set(modify_field_list.begin(), modify_field_list.end());
    // 每条记录的 modify_field_list 中只会有一个标记
    auto exist = [&](const std::string& scene) -> bool {
      return modify_field_set.count(scene) > 0;
    };
    bool score_inc = exist("score_internal") || exist("score_external") || exist("score_search")
                    || exist("score_universe");
    if (!score_inc) {
      // 若 4 个打分标都没有，则正常下发
      PerfUtil::CountLogStash(1, "ad.index_builder", "no_score_inc",
                              ks::engine_base::DeployVariable::GetKwsName(),
                              ks::engine_base::DeployVariable::GetPodName());
      return true;
    }
    // 只下发本场景增量
    bool allow = exist(score_scene);
    if (allow) {
      return ProcessWTCreativeImp(ad_instance);
    } else {
      return false;
    }
  }
  return true;
}

bool WTCreativeScoreParser::ProcessWTCreativeFull(kuaishou::ad::AdInstance* ad_instance) {
  auto type = ad_instance->type();
  if (type == AdEnum::WT_CREATIVE) {
    return ProcessWTCreativeImp(ad_instance);
  }
  return true;
}

bool WTCreativeScoreParser::ProcessWTCreativeImp(kuaishou::ad::AdInstance* ad_instance) {
  auto type = ad_instance->type();
  if (type == AdEnum::WT_CREATIVE) {
    auto* wt_creative = ad_instance->MutableExtension(kuaishou::ad::tables::WTCreative::wt_creative_old);
    auto scene_itr = SceneMap.find(scene_);
    if (scene_itr == SceneMap.end()) {
      LOG(ERROR) << "Unknown score scene, scene=" << scene_;
      return false;
    }
    // 只保留改场景分数，其它清空
    std::string scene = scene_itr->second;
    if (scene == "score_external") {
      wt_creative->clear_score_internal();
      wt_creative->clear_score_search();
      wt_creative->clear_score_universe();
    } else if (scene == "score_internal") {
      wt_creative->clear_score_external();
      wt_creative->clear_score_search();
      wt_creative->clear_score_universe();
      if (!ks::engine_base::IsCreativeInternalCirculation(wt_creative->creative_feature())) {
        // 清空全部得分
        wt_creative->clear_score_internal();
      }
    } else if (scene == "score_search") {
      wt_creative->clear_score_internal();
      wt_creative->clear_score_external();
      wt_creative->clear_score_universe();
    } else if (scene == "score_universe") {
      wt_creative->clear_score_internal();
      wt_creative->clear_score_search();
      wt_creative->clear_score_external();
    }
  }
  return true;
}

}  // namespace index_builder
}  // namespace ks
