#pragma once
#include <functional>
#include <map>
#include <mutex>
#include <string>
#include <vector>
#include "absl/container/flat_hash_map.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "teams/ad/ad_creative_server/proto/creative_score.pb.h"
#include "teams/ad/ad_label_server/proto/label_server.pb.h"
#include "teams/ad/ad_label_server/utils/common/common.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/extra_adapter/creative_adapter.h"
#include "teams/ad/index_builder/utils/extra_adapter/kconf.h"
/*
  对接分部署的 creative_server 产出的 creative_db
*/

namespace ks {
namespace index_builder {

class MaterialLableSnap {
 public:
  explicit MaterialLableSnap(std::string type);
  ~MaterialLableSnap();

  void Swap() { write_index_ = 1 - write_index_; }
  void ClearWriteSpace() { data_[write_index_].clear(); }
  void DoReadFile();
  ks::ad_label_server::LevelLabelData Process(
      kuaishou::ad::AdInstance* ad_instance);

 private:
  std::mutex mtx_;
  std::string type_;
  bool non_snap_load_{false};
  absl::flat_hash_map<int64_t, std::string> data_[2];
  std::atomic_int write_index_{0};
  ::ks::infra::RedisClient* redis_client_;
  std::string kcc_name_;
};

class LabelAdapter {
 public:
  LabelAdapter();
  ~LabelAdapter();

  static LabelAdapter* GetInstance() {
    static LabelAdapter instance;
    return &instance;
  }
  bool ProcessForInc(kuaishou::ad::AdInstance* ad_instance);
  bool ProcessForFull(kuaishou::ad::AdInstance* ad_instance);
  void UpdateThread();

  bool IsReady() { return ready_.load(); }

 private:
  std::mutex mtx_;
  std::map<std::string, MaterialLableSnap*> snap_map_;
  std::thread update_thread_;
  std::atomic<bool> ready_{false};
  std::atomic<bool> running_{false};
};
}  // namespace index_builder
}  // namespace ks
