#pragma once

#include <string>
#include <unordered_map>

#include "teams/ad/index_builder/utils/builder.pb.h"

namespace ks {
namespace index_builder {

class PattenParser {
 public:
  PattenParser();

  int64_t GetCurrentDayMs();

  bool ParsePattenValue(const std::string& patten, int64_t* value);

  std::string ParseQuery(const std::string& query, const std::string& shard_name);

 private:
  std::unordered_map<std::string, int> relate_time_pattens_;
};

}  // namespace index_builder
}  // namespace ks

