#pragma once

#include <memory>
#include <mutex>
#include <string>
#include <map>
#include <vector>
#include <utility>
#include <fstream>
#include <iostream>
#include "absl/strings/str_split.h"
#include "absl/strings/str_join.h"
#include "base/file/file_util.h"
#include "base/common/closure.h"
#include "base/thread/thread_pool.h"
#include "serving_base/util/hdfs.h"
#include "serving_base/hdfs_read/hdfs_file_util.h"
#include "serving_base/hdfs_read/hdfs_handle.h"

#include "teams/ad/index_builder/utils/md5_helper.h"

namespace ks {
namespace index_builder {

class HdfsUploader {
 public:
  static HdfsUploader* Instance() {
    static HdfsUploader instance;
    return &instance;
  }

  void EnableUpload() {enable_upload_ = true;}
  void DisableUpload() {enable_upload_ = false;}

  void SetUploadThreadPool(thread::ThreadPool* pool) {
    upload_pool_ = pool;
    EnableUpload();
  }

  void AddUploadTask(std::string local_file, std::string hdfs_path) {
    if (!enable_upload_ || !upload_pool_) {
      return;
    }

    std::lock_guard<std::mutex> guard(lock_);
    upload_pool_->AddTask(::NewCallback(Instance(), &HdfsUploader::PutFileToHdfs, local_file, hdfs_path));
  }

  void PutFileToHdfs(std::string local_file, std::string hdfs_path) {
    int start = time(nullptr);
    std::string hdfs_file;
    size_t pos = local_file.rfind('/');
    if (pos == local_file.npos) {
      hdfs_file = hdfs_path + "/" + local_file;
    } else {
      hdfs_file = hdfs_path + "/" + local_file.substr(pos + 1);
    }

    if (hadoop::HDFSExists(hdfs_file.c_str())) {
      LOG(INFO) << "dst hdfs_file: " << hdfs_file << " has been exists, first remove it";
      if (!hadoop::HDFSRmr(hdfs_file.c_str())) {
        LOG(ERROR) << "hadoop::HDFSRmr(" << hdfs_file << ") failed.";
      }
    }

    LOG(INFO) << "start HDFSPut| " << local_file << " -> " << hdfs_file;
    int ret = hadoop::HDFSPut(local_file.c_str(), hdfs_file.c_str());
    if (ret == 0) {
      LOG(INFO) << "finish HDFSPut| " << local_file << " -> " << hdfs_file
                << " success. cost: " << (time(nullptr) - start) << "s";
    } else {
      LOG(ERROR) << "finish HDFSPut| " << local_file << " -> " << hdfs_file
                 << " failed. ret: " << ret;
    }
  }

  void TouchSuccess(const std::string& hdfs_path) {
    std::string flag_file = hdfs_path + "/_SUCCESS";
    if (!hadoop::HDFSExists(flag_file.c_str())) {
      if (hdfs_.Touch(flag_file)) {
        LOG(INFO) << "hdfs_.Touch(" << flag_file << ") success.";
      } else {
        LOG(ERROR) << "hdfs_.Touch(" << flag_file << ") failed.";
      }
    }
  }

  bool TouchPending(const std::string& pending_file) {
    bool touch_result = hdfs_.Touch(pending_file);
    if (touch_result) {
      LOG(INFO) << "hdfs touch pending file " << pending_file << " succeeded.";
    } else {
      LOG(ERROR) << "hdfs touch pending file " << pending_file << " failed.";
    }
    return touch_result;
  }

  bool DeletePending(const std::string& pending_file) {
    bool delete_result = hadoop::HDFSRmr(pending_file.c_str());
    if (delete_result) {
      LOG(INFO) << "hdfs delete pending file " << pending_file << " succeeded.";
    } else {
      LOG(ERROR) << "hdfs delete pending file " << pending_file << " failed.";
    }
    return delete_result;
  }

  bool DumpTimetExists(const std::string& hdfs_path) {
    std::string dump_timet_file = hdfs_path + "/dump_timet";
    return hadoop::HDFSExists(dump_timet_file.c_str());
  }

  bool Mkdir(const std::string& hdfs_path) {
    if (!hadoop::HDFSExists(hdfs_path.c_str())) {
      if (!hadoop::HDFSMkdir(hdfs_path.c_str())) {
        LOG(ERROR) << "hadoop::HDFSMkdir(" << hdfs_path << ") failed.";
        return false;
      }
    }
    return true;
  }

  void CleanLastTimeTask() {
    std::lock_guard<std::mutex> guard(lock_);
    uploadfile_trace_by_index_type_.clear();
    record_nums_trace_by_index_type_.clear();
    rn_local_dir_by_index_type_.clear();
    rn_hdfs_dir_by_index_type_.clear();
  }

  void AddUploadTask(std::string local_file, std::string hdfs_path, std::string index_type) {
    if (!enable_upload_ || !upload_pool_) {
      return;
    }
    LOG(INFO) << "local_file: " << local_file << " hdfs_path: " << hdfs_path << " index_type: " << index_type;
    std::lock_guard<std::mutex> guard(lock_);
    uploadfile_trace_by_index_type_[index_type].emplace_back(local_file, hdfs_path);
    upload_pool_->AddTask(::NewCallback(
        Instance(), &HdfsUploader::PutFileToHdfs, local_file, hdfs_path));
  }

  void Md5FileGen() {
    LOG(INFO) << "HdfsUploader Md5FileGen";
    for (const auto& index_type_pair : uploadfile_trace_by_index_type_) {
      thread::ThreadPool md5_thread_pool(20);
      std::mutex md5_mtx;

      const auto& index_type = index_type_pair.first;
      std::vector<std::pair<std::string, std::string>> file_md5_vec;
      for (const auto& index_pair : index_type_pair.second) {
        // 这里拆开做并行。。。
        LOG(INFO) << "index_type: " << index_type << " local_file: " << index_pair.first
            << " hdfs_path: " << index_pair.second;
        md5_thread_pool.AddTask(
            ::NewCallback(Instance(), &HdfsUploader::md5_func, index_pair.first, &file_md5_vec, &md5_mtx));
      }
      md5_thread_pool.JoinAll();
      for (const auto& md5_pair : file_md5_vec) {
        LOG(INFO) << "file_name: " << md5_pair.first << " md5: " << md5_pair.second;
      }
      std::string md5_update_filename;
      std::string md5_hdfs_filename;
      std::vector<std::string> split_dirs;
      if (index_type_pair.second.size() > 0) {
        split_dirs = absl::StrSplit(index_type_pair.second.front().first, "/");
        if (split_dirs.size() > 0) {
          split_dirs.pop_back();
          md5_update_filename = absl::StrJoin(split_dirs, "/") + "/md5_check";
        } else {
          continue;
        }
        md5_hdfs_filename = index_type_pair.second.front().second;
        std::ofstream md5_output(md5_update_filename);
        for (const auto& md5_pair : file_md5_vec) {
          md5_output << md5_pair.first << "\t" << md5_pair.second << "\n";
        }
        md5_output.close();
        LOG(INFO) << "md5upload: " << md5_update_filename << " " << md5_hdfs_filename;
        PutFileToHdfs(md5_update_filename, md5_hdfs_filename);
      }
    }
    uploadfile_trace_by_index_type_.clear();
  }

  void md5_func(
      const std::string file_dir,
      std::vector<std::pair<std::string, std::string>>* file_md5_vec_ptr,
      std::mutex* md5_mutex_ptr) {
    std::vector<std::string> split_dirs;
    std::string md5_str = MD5SumFile(file_dir);
    split_dirs = absl::StrSplit(file_dir, "/");
    std::string file_name = (split_dirs.size() > 0) ? split_dirs.back() : "";
    std::lock_guard<std::mutex> guard(*md5_mutex_ptr);
    file_md5_vec_ptr->emplace_back(file_name, md5_str);
  }

  void AddUploadRecordNum(const std::string& local_file,
                          const std::string& hdfs_path,
                          std::string index_type, int64 record_num) {
    std::vector<std::string> split_dirs = absl::StrSplit(local_file, "/");
    std::lock_guard<std::mutex> guard(lock_);
    const std::string table_name = split_dirs.back();
    if (rn_hdfs_dir_by_index_type_.find(index_type) ==
        rn_hdfs_dir_by_index_type_.end()) {
      split_dirs.pop_back();
      const std::string& local_file_dir =
          absl::StrJoin(split_dirs, "/") + "/record_num";
      const std::string& hdfs_file_dir = hdfs_path;
      rn_hdfs_dir_by_index_type_[index_type] = hdfs_file_dir;
      rn_local_dir_by_index_type_[index_type] = local_file_dir;
      LOG(INFO) << "hdfs_file_dir: " << hdfs_file_dir
                << " local_file_dir: " << local_file_dir;
    }
    record_nums_trace_by_index_type_[index_type].emplace_back(
        table_name, std::to_string(record_num));
  }

  void RecordFileGen() {
    for (const auto& index_type_pair : record_nums_trace_by_index_type_) {
      const auto& table_recordnum = index_type_pair.second;
      const auto& index_type = index_type_pair.first;
      const std::string& rn_local_filename = rn_local_dir_by_index_type_[index_type];
      const std::string& rn_hdfs_filename = rn_hdfs_dir_by_index_type_[index_type];
      std::ofstream record_output(rn_local_filename);
      for (const auto& record_pair : index_type_pair.second) {
        const auto& table_name = record_pair.first;
        const auto& rn = record_pair.second;
        record_output << table_name << "\t" << rn << "\n";
        LOG(INFO) << table_name << "\t" << rn;
      }
      record_output.close();
      LOG(INFO) << "recordNum upload: " << rn_local_filename << " "
                << rn_hdfs_filename;
      PutFileToHdfs(rn_local_filename, rn_hdfs_filename);
    }
  }

  std::map<std::string, std::vector<std::pair<std::string, std::string>>> GetIndexRecordMap() {
    return record_nums_trace_by_index_type_;
  }

 private:
  HdfsUploader() : hdfs_(hadoop::FLAGS_hadoop_namenode_ip,
      hadoop::FLAGS_hadoop_namenode_port, "ad") {}
  ~HdfsUploader() = default;

 private:
  bool enable_upload_ {false};
  thread::ThreadPool* upload_pool_ {nullptr};

  std::map<std::string, std::vector<std::pair<std::string, std::string>>> uploadfile_trace_by_index_type_;
  // pair = <file, record_num> rn = recordnum
  std::map<std::string, std::vector<std::pair<std::string, std::string>>> record_nums_trace_by_index_type_;
  std::map<std::string, std::string> rn_local_dir_by_index_type_;
  std::map<std::string, std::string> rn_hdfs_dir_by_index_type_;
  std::mutex lock_;

  base::HDFSWrapper hdfs_;
};

}  // namespace index_builder
}  // namespace ks

