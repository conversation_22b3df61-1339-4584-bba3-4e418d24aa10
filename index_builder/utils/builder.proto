syntax = "proto3";

package ks.index_builder;
import "teams/ad/ad_proto/kuaishou/ad/ad_inc.proto";
import "teams/ad/index_builder/multiform_filter/multiform_kconf.proto";

/////////////////////////////////
// mysql connect param
message MysqlAccount {
  string host = 1;  // host:port
  string user = 2;
  string password = 3;
  string database = 4;
}

message MysqlConf {
  repeated MysqlAccount accounts = 1;
}

message TableDumpConfig {
  string source_name = 1;
  string type = 2;
  string select_part = 3;
  string table_name = 4;
  string condition = 5;
  string limit = 6;             // 设置limit xxx，测试时开启
  int64  fixed_shard_num = 7;   // 预先设置分片数，导出结果分片数不为该值则认为出错
  bool   merge_shards = 8;       // 是否将所有分片数据合并成一个文件
  repeated string server_name = 9;
}

message DumpConfig {
  string                  output = 1;
  int64                   index_build_interval = 2;
  map<string, MysqlConf>             data_source = 3; // name, connect
  repeated TableDumpConfig table_dump_configs = 4;
  int64 limit = 5;    // 值大于0时为测试流程，限定sql查询条数上限
}

/////////////////////////////////
// index info
message ShardDumpInfo {
  int64 shard_id = 1;
  int64 record_num = 2;
  int64 data_size = 3;
  int64 compress_size = 4;
  string compress_md5 = 5;
}

message TableDumpInfo {
  int64  shard_num = 1;
  int64  total_record_num = 2;
  int64  total_data_size = 3;
  int64  total_compress_size = 4;
  repeated ShardDumpInfo shards = 5;
}

message IndexInfo {
  int64  time  = 1;       // time(0)
  bool   force = 2;       // 临时制作
  int64  cost_time = 3;   // 制作索引花费时间(秒数)
  map<string, TableDumpInfo> detail = 4;  // table, DumpInfo
}

message IndexInfos {
  map<string, IndexInfo> infos = 1;   // version, IndexInfo
}

/////////// kconf
enum  IndexBuilderInputType {  // 输入数据类型
  INPUT_USE_MYSQL = 0;
  INPUT_USE_HDFS  = 1;
}
enum  IndexBuilderOutputType {  // 输出类型
  OUT_PUT_FORWARD_INDEX = 0;
}

message IndexBuilderConf {
  string path_suffix = 1;  // for hdfs_path
  IndexBuilderInputType   input_type = 2;
  IndexBuilderOutputType  output_type = 3;
  // Deprecated. Use ad.index_builder.streamMapConfig.input_full_hdfs_paths instead.
  string benchmark_path = 4 [deprecated = true];
  repeated string support_servers = 5;
  int64 total_creative_limit = 8;  // 总创意数限制,超过则丢创意
  int64 throw_zom_limit = 10;  // 僵尸创意阈值，超过则开始丢僵尸创意
  int64 total_valid_limit = 11;  //  活跃创意限制，超过则丢弃
  bool delete_not_pass = 12;  //  delete操作的创意是否进入索引[deprecated]
  bool is_ai_hosting = 13;  //  是否是智能托管项目
  repeated string p2p_dirs = 14;  //  额外p2p文件目录和 index 一起推送
  string stream_maker_type = 15;  //  新的 stream_map 类型设定 如 ad_server/aihosting/dpa
  // 对齐枚举值 teams/ad/ad_base/src/common/basis_enum_types.h deploy_type
  repeated string deploy_types = 16;  //  根据部署对 creative 进行过滤  空为不过滤
  int64 customized_creative_limit = 17;  //  自定义创意限制
  int64 programmed_creative_limit = 18;  //  程序化限制

  // creative_server 分场景 使用不同的 creative_score_X
  ks.index_builder.DeployParam.Param deploy_param = 19;
}

message FilterTables {
  repeated string tables = 1;
}

message StreamMap {
  // 重定向 如 ad_dsp_creative_advanced_programmed->ad_dsp_creative 进行输出
  map<string, string> redirections = 2;
  // 输入文件 HDFS 路径  如 ad_server->[/home/<USER>/das/benchmark_online_platform/base, /home/<USER>/benchmark/charge]
  repeated string input_full_hdfs_paths = 4;
  // 封禁某些目录文件输入 如 "/home/<USER>/das/benchmark_online_platform/base" : [ad_dsp_creative, ad_dsp_target]
  map<string, FilterTables> forbid_input_hdfs = 5;
}

// 根据类型选种类
message StreamMaps {
  map<string, StreamMap> name2map = 1;
}

// server 特化删除字段 配置
message TableDeleteFields {
  message TableField {
    repeated string field = 1;
  }
  message TableFields {
    map<string,TableField> table_field = 1;
  }
  map<string, TableFields> server_to_table_fields = 1;
}

message StreamAdvancedConfig {
  bool is_instance_split_enable = 1;  //  是否是 adInstance 拆分
  //  拆分时被拆分 adInstance 掩盖的表 如 unit-target拆分后 ad_dsp_target 原始输入可以不要
  repeated string forbid_table_name = 2;
  //  拆分规则 如 ["kuaishou.ad.tables.Unit.unit_support_info" : "kuaishou.ad.tables.UnitSupportInfo"]
  map<string, string> split_config_map = 3;
}

// 分片配置，后续msg_proxy也用这个配置管理增量分片
message IndexShardConfig {
  bool enable = 1;
  int32 shard_num = 2;       // 分片数量
  int32 shard_num_bak = 3;   // 第二分片数量，用于分片调整
  map<string, string> table_shardkey = 4;      // table=>shardkey 这里列出的 table 才会分片，并且对 shard_key 取模
  repeated string builder_enable_servers = 5;  // indexbuilder 中生效的 server，如果为空，则全部 server 生效。
}
