#pragma once

#include <glog/logging.h>
#include <fstream>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>
#include "perfutil/perfutil.h"
#include "base/time/timestamp.h"
#include "serving_base/hdfs_read/hdfs_file_stream.h"
#include "base/file/file_stream.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/meta.pb.h"
#include "teams/ad/index_builder/utils/hdfs_valid_checker/hdfs_valid_checker.h"
namespace ks {
namespace index_builder {

using ks::infra::PerfUtil;

class MessageStream {
 public:
  MessageStream() = default;
  virtual ~MessageStream() {
  }

  void SetBaseMessage(kuaishou::ad::AdEnum::AdInstanceType type);

  void SetBaseMessage(const std::string& proto_name);

  kuaishou::ad::AdEnum::AdInstanceType MessageType() {
    return msg_type_;
  }

  std::string MessageName() {
    return base_msg_->GetTypeName();
  }

  kuaishou::ad::AdEnum::AdInstanceType DetermineMessageType();

  virtual bool Valid() {return false;}

  virtual google::protobuf::Message* NextMessage() {
    return base_msg_;
  }

  virtual const kuaishou::ad::AdInstance* NextAdInstance() {
    return &default_instance_;
  }

  virtual std::string DebugInfo() {return "";}

  virtual void Clear() {}

  virtual int64_t GetOutputRecordNum() = 0;

  virtual std::string GetFileName() = 0;

  virtual std::string GetTableName() = 0;

 protected:
  kuaishou::ad::AdEnum::AdInstanceType msg_type_;
  google::protobuf::Message* base_msg_{nullptr};
  kuaishou::ad::AdInstance default_instance_;
};

/*
  read hdfs file for das
*/
class HdfsMessageStream : public MessageStream {
 public:
  explicit HdfsMessageStream(const std::string& table_name,
                             const std::string& hdfs_file)
      : table_name_(table_name), hdfs_file_(hdfs_file) {}

  ~HdfsMessageStream() {
    if (record_num_ != 0) {
      LOG(INFO) << "file: " << hdfs_file_ << " read " << record_num_
                << "records";
      LOG_ASSERT(HdfsValidChecker::GetInstance()->OutputValidCheck(hdfs_file_,
                                                                   record_num_))
          << hdfs_file_ << " read record not meet expect :" << record_num_;
      PerfUtil::SetLogStash(end_time_ - start_time_, "ad.index_builder",
                            "dump_time", hdfs_file_);
      PerfUtil::SetLogStash(record_num_, "ad.index_builder", "record_num",
                            hdfs_file_);
    }
  }

  bool Valid() override;

  void Clear() override {
    hdfs_stream_.reset();
  }

  std::string DebugInfo() override {return hdfs_file_;}

  int64_t GetOutputRecordNum() override { return record_num_; }

  std::string GetFileName() override { return hdfs_file_; }

  std::string GetTableName() override { return table_name_; }

 protected:
  std::string table_name_;
  std::string hdfs_file_;

  int record_num_{0};
  int read_offset_{0};
  int cache_size_{0};
  std::vector<char> buffer_;

  int64_t start_time_{base::GetTimestamp() / 1000};
  int64_t end_time_{0};

  std::unique_ptr<hadoop::HDFSFileStream> hdfs_stream_;
};

/*
  read hdfs file for adinstance type
*/
class HdfsMessageStreamForAdInstance : public HdfsMessageStream {
 public:
  explicit HdfsMessageStreamForAdInstance(const std::string& table_name,
                                          const std::string& hdfs_file)
      : HdfsMessageStream(table_name, hdfs_file) {}

  ~HdfsMessageStreamForAdInstance() = default;

  bool Valid() override;
};

/*
  read hdfs file for das on kfs
*/
class KfsMessageStream : public HdfsMessageStream {
 public:
  explicit KfsMessageStream(const std::string& table_name,
                            const std::string& hdfs_file)
      : HdfsMessageStream(table_name, hdfs_file) {}
  ~KfsMessageStream() = default;
  bool Valid() override;
  std::unique_ptr<base::FileStream> kfs_stream_;  // 更换 kfs
};

/*
  read hdfs file for adinstance typeon Kfs
*/
class HdfsMessageStreamOnKfsForAdInstance
    : public HdfsMessageStreamForAdInstance {
 public:
  explicit HdfsMessageStreamOnKfsForAdInstance(const std::string& table_name,
                                          const std::string& hdfs_file)
      : HdfsMessageStreamForAdInstance(table_name, hdfs_file) {}

  ~HdfsMessageStreamOnKfsForAdInstance() = default;
  bool Valid() override;
  std::unique_ptr<base::FileStream> kfs_stream_;  // 更换 kfs
};

/*
 * read hdfs file for table_lite
 * one stream design to one table
 */
class HdfsLiteIndexStream : public HdfsMessageStreamForAdInstance {
 public:
  explicit HdfsLiteIndexStream(const std::string& table_name,
                               const std::string& hdfs_file)
      : HdfsMessageStreamForAdInstance(table_name, hdfs_file) {}

  ~HdfsLiteIndexStream() = default;
  void InitMultiFile(const std::vector<std::string>& files) {
    paths_ = files;
    curr_read_index_ = 0;
  }
  bool Valid() override;
  std::vector<std::string> paths_;
  int32_t curr_read_index_{0};
};

/*
 * read hdfs file for table_lite on kfs
 * one stream design to one table
 */
class HdfsLiteIndexStreamOnKfs : public HdfsMessageStreamOnKfsForAdInstance {
 public:
  explicit HdfsLiteIndexStreamOnKfs(const std::string& table_name,
                                    const std::string& hdfs_file)
      : HdfsMessageStreamOnKfsForAdInstance(table_name, hdfs_file) {}

  ~HdfsLiteIndexStreamOnKfs() = default;
  void InitMultiFile(const std::vector<std::string>& files) {
    paths_ = files;
    curr_read_index_ = 0;
  }
  bool Valid() override;
  std::vector<std::string> paths_;
  int32_t curr_read_index_{0};
};

using MessageStreamPtr = std::shared_ptr<MessageStream>;
using MessageStreamMap =
    std::unordered_map<std::string, std::vector<MessageStreamPtr>>;

using InstanceStreamPtr = std::shared_ptr<HdfsMessageStreamForAdInstance>;
using InstanceStreamMap =
    std::unordered_map<std::string,
                       std::vector<InstanceStreamPtr>>;

}  // namespace index_builder
}  // namespace ks

