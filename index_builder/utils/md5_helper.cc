#include <boost/filesystem.hpp>
#include <string>
#include <algorithm>
#include <fstream>
#include <vector>
#include "base/hash_function/md5.h"
#include "teams/ad/index_builder/utils/md5_helper.h"

namespace ks {
namespace index_builder {

std::string MD5SumFile(const std::string &file_path) {
  MD5Context ctx;
  MD5Init(&ctx);
  std::ifstream fs(file_path.c_str());
  char line[1024];
  while (true) {
    fs.read(line, 1024);
    if (fs) {
      MD5Update(&ctx, line, 1024);
    } else {
      MD5Update(&ctx, line, fs.gcount());
      break;
    }
  }
  MD5Digest digest;  // the result of the computation
  MD5Final(&digest, &ctx);
  fs.close();
  return MD5DigestToBase16(digest);
}

std::string MD5SumString(const std::string& str) {
  MD5Context ctx;
  MD5Init(&ctx);
  MD5Update(&ctx, str.c_str(), str.size());
  MD5Digest digest;  // the result of the computation
  MD5Final(&digest, &ctx);
  return MD5DigestToBase16(digest);
}

std::string MD5SumPath(const std::string &file_path) {
  // 对路径下的所有文件遍历，计算 md5
  namespace fs = boost::filesystem;
  fs::path fp(file_path);
  if (!fs::is_directory(fp)) {
    return "";
  }
  std::vector<std::string> file_list;
  fs::directory_iterator itr(fp);
  fs::directory_iterator end;
  for (; itr != end; ++itr) {
    if (!fs::is_regular_file(itr->path())) {
      continue;
    }
    file_list.emplace_back(itr->path().string());
  }
  std::sort(file_list.begin(), file_list.end());
  MD5Context ctx;
  MD5Init(&ctx);
  for (auto &file_name : file_list) {
    std::ifstream fs(file_name.c_str());
    char line[1024];
    while (true) {
      fs.read(line, 1024);
      if (fs) {
        MD5Update(&ctx, line, 1024);
      } else {
        MD5Update(&ctx, line, fs.gcount());
        break;
      }
    }
    fs.close();
  }

  MD5Digest digest;  // the result of the computation
  MD5Final(&digest, &ctx);
  return MD5DigestToBase16(digest);
}
}  // namespace index_builder
}  // namespace ks
