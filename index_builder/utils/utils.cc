#include "ks/serving_util/dynamic_config.h"
#include "teams/ad/index_builder/utils/kconf.h"
#include "teams/ad/index_builder/utils/utils.h"

namespace ks {
namespace index_builder {

DECLARE_string(hdfs_base_index_path);

int32 GetShardId() {
  auto shard_id = ks::DynamicJsonConfig::GetConfig()->GetInt("shard_id", -1);
  VLOG_EVERY_N(33, 10) << "shard_id=" << shard_id;
  return shard_id;
}

std::string GetServerOutputPath(const DumpConfig &dump_conf, const std::string &server_name, const std::string &version) {
  std::string output_path = dump_conf.output() + "/" + server_name;
  if (AdKconfUtil::IsShardEnable(server_name)) {
    output_path = output_path + "/" + std::to_string(GetShardId());
  }
  if (!version.empty()) {
    output_path = output_path + "/" + version;
  }
  VLOG(33) << "get output_path=" << output_path;
  return output_path;
}

std::string GetServerUploadPath(const std::string &server_name, const std::string &version) {
  std::string suffix = AdKconfUtil::indexBuilderConf()->data().path_suffix();
  std::string hdfs_base_path = FLAGS_hdfs_base_index_path + suffix;
  std::string upload_path = hdfs_base_path + "/" + server_name;
  if (AdKconfUtil::IsShardEnable(server_name)) {
    upload_path = upload_path + "/" + std::to_string(GetShardId());
  }
  if (!version.empty()) {
    upload_path = upload_path + "/" + version;
  }
  VLOG(33) << "get upload_path=" << upload_path;
  return upload_path;
}

std::string GetPendingFileDir() {
  std::string pending_dir =
      FLAGS_hdfs_base_index_path + AdKconfUtil::indexBuilderConf()->data().path_suffix() + "/" + "_PENDING";
  if (AdKconfUtil::IsShardEnable()) {
    pending_dir = pending_dir + std::to_string(GetShardId());
  }
  VLOG(33) << "get pending_file_dir=" << pending_dir;
  return pending_dir;
}

}  // namespace index_builder
}  // namespace ks
