// Debug print protos created by DAS.
// Example usage (redirect stderr to file since it will be too large):
//  ./check_das_message --hadoop_namenode_ip=default --hadoop_namenode_port=0 \
//      --hdfs_url=/home/<USER>/benchmark/base/2021-04-06_160000/ad_dsp_unit-4000~4999-7-0 \
//      --pb_type=kuaishou.ad.tables.Unit --alsologtostderr &> /tmp/test_check_das_message_run.txt

// diff two base file diff
// ./check_das_message
// --hdfs_new_url=/home/<USER>/das/benchmark_online_platform/base/2021-04-28_100000/ad_dsp_target-81-0
// --hdfs_old_url=/home/<USER>/benchmark/base/2021-04-28_100000/ad_dsp_target-81-0
// --pb_type=kuaishou.ad.tables.Target --funct=compare
// --flagfile=../config/server_static.flags

#include <gflags/gflags.h>
#include <fstream>

#include "base/file/file_util.h"
#include "base/file/file_path.h"
#include "base/thread/thread_pool.h"
#include "ks/serving_util/server_base.h"
#include "teams/ad/index_builder/utils/message_stream.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/tables/data_gate/all_tables.h"
#include "google/protobuf/util/message_differencer.h"
#include "teams/ad/ad_index/framework/pb_reader/pb_file_loader.h"
#include "teams/ad/index_builder/multiform_filter/common_tools.h"
#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"

using kuaishou::ad::AdInstance;


DEFINE_string(hdfs_url, "", "hdfs base file url");
DEFINE_string(pb_type, "", "pb type in full path, e.g. kuaishou.ad.tables.Account");

DEFINE_string(funct, "", "function param");
DEFINE_string(hdfs_old_url, "", "old hdfs base file url");
DEFINE_string(hdfs_new_url, "", "new hdfs base file url");

void sigint_handler(int s) {
  printf("Caught signal %d , exit \n", s);
  exit(1);
}

void PrintDasInstance() {
  std::string hdfs_path = FLAGS_hdfs_url;
  std::string pb_type = FLAGS_pb_type;
  LOG(INFO) << "hdfs: " << hdfs_path << " pb_type: " << pb_type;
  std::shared_ptr<ks::index_builder::HdfsMessageStream> reader_ptr;
  reader_ptr.reset(
      new ks::index_builder::HdfsMessageStream(pb_type, hdfs_path));
  reader_ptr->SetBaseMessage(pb_type);
  while (reader_ptr->Valid()) {
    auto* msg = reader_ptr->NextMessage();
    LOG(INFO) << msg->ShortDebugString() << std::endl;
  }
}

void PrintDasAdInstance() {
  std::string hdfs_path = FLAGS_hdfs_url;
  std::string pb_type = FLAGS_pb_type;
  LOG(INFO) << "hdfs: " << hdfs_path << " pb_type: " << pb_type;
  std::shared_ptr<ks::index_builder::HdfsMessageStream> reader_ptr;
  reader_ptr.reset(new ks::index_builder::HdfsMessageStreamForAdInstance(
      pb_type, hdfs_path));
  reader_ptr->SetBaseMessage(pb_type);
  while (reader_ptr->Valid()) {
    auto* msg = reader_ptr->NextMessage();
    LOG(INFO) << msg->ShortDebugString() << std::endl;
  }
}

void LoadDasInstance(std::string path, std::map<int64_t, std::string> *dest_map) {
  std::string hdfs_path = path;
  std::string pb_type = FLAGS_pb_type;
  LOG(INFO) << "hdfs: " << hdfs_path << " pb_type: " << pb_type;
  std::shared_ptr<ks::index_builder::HdfsMessageStream> reader_ptr;
  reader_ptr.reset(
      new ks::index_builder::HdfsMessageStream(pb_type, hdfs_path));
  reader_ptr->SetBaseMessage(pb_type);
  while (reader_ptr->Valid()) {
    auto* msg = reader_ptr->NextMessage();
    auto primary_key = ks::index_builder::GetPrimaryKey(msg);
    dest_map->emplace(primary_key, msg->ShortDebugString());
    LOG_EVERY_N(INFO, 10000) << path << " -> " << msg->ShortDebugString();
    // LOG(INFO) << msg->ShortDebugString() << std::endl;
  }
}

int main(int argc, char* argv[]) {
  ::google::FlushLogFiles(::google::INFO);

  signal(SIGINT, sigint_handler);

  base::InitApp(&argc, &argv, "index_builder check_das_message");

  ks::index_builder::InitEnum2Type();

  if (FLAGS_funct == "compare") {
    std::map<int64_t, std::string> old_map, new_map;
    LoadDasInstance(FLAGS_hdfs_old_url, &old_map);
    LoadDasInstance(FLAGS_hdfs_new_url, &new_map);
    int64_t compare_count = 0;
    int64_t compare_diff_count = 0;
    for (auto& instance_pair : new_map) {
      int64_t primary_key = instance_pair.first;
      std::string new_str = instance_pair.second;
      auto old_map_it = old_map.find(primary_key);
      if (old_map_it != old_map.end()) {
        std::string old_str = old_map_it->second;
        if (new_str != old_str) {
          LOG_EVERY_N(INFO, 1)
              << "diff: \n"
              << " new_str: " << new_str << "\n old_str: " << old_str;
          compare_diff_count++;
        }
        compare_count++;
      }
    }
    LOG(INFO) << "compare_count: " << compare_count
              << " compare_diff_count: " << compare_diff_count;
  } else if (FLAGS_funct ==
             "read_hdfs_adinstance") {  // read target_index file on hdfs
    PrintDasAdInstance();
  } else {
    PrintDasInstance();
  }

  return 0;
}
