#include "teams/ad/index_builder/utils/message_stream.h"
#include <absl/time/time.h>
#include <absl/time/clock.h>
#include <algorithm>
#include <fstream>
#include "perfutil/perfutil.h"
#include "absl/strings/str_join.h"
#include "serving_base/hdfs_read/hdfs_file_util.h"
#include "base/strings/string_split.h"
#include "abseil/absl/strings/strip.h"
#include "abseil/absl/strings/numbers.h"
#include "google/protobuf/util/json_util.h"
#include "teams/ad/ad_index/framework/pb_reader/pb_header.h"
#include "teams/ad/index_builder/utils/kconf.h"
#include "teams/ad/index_builder/utils/patten_parser.h"
#include "teams/ad/index_builder/utils/table_config.h"
#include "teams/ad/index_builder/multiform_filter/common_tools.h"

using ks::infra::PerfUtil;

namespace ks {
namespace index_builder {

void MessageStream::SetBaseMessage(kuaishou::ad::AdEnum::AdInstanceType type) {
  msg_type_ = type;
  default_instance_.set_type(msg_type_);
  auto* ad_desc = default_instance_.GetDescriptor();

  int field_number = ad_desc->extension_range(0)->start + msg_type_;
  auto* desc_pool = google::protobuf::DescriptorPool::generated_pool();
  auto* field_desc = desc_pool->FindExtensionByNumber(ad_desc, field_number);
  if (!field_desc) {
    LOG(ERROR) << "AdInstanceType [" << type << "] maybe invalid";
    return;
  }

  auto* ad_reflect = default_instance_.GetReflection();
  base_msg_ = ad_reflect->MutableMessage(&default_instance_, field_desc);
  LOG(INFO) << "SetBaseMessage| type: " << msg_type_ << " -> " << base_msg_->GetTypeName();
}

void MessageStream::SetBaseMessage(const std::string& proto_name) {
  std::string ad_instance_type = GetEnumNameByPbName(proto_name);
  kuaishou::ad::AdEnum::AdInstanceType type;
  if (kuaishou::ad::AdEnum_AdInstanceType_Parse(ad_instance_type, &type)) {
    SetBaseMessage(type);
  } else {
    LOG_ASSERT(false) << "not supportted proto_name " << proto_name
                      << " found... Please Check InitEnum2Type is runned or "
                         "proto is deleted update code?";
  }
}

kuaishou::ad::AdEnum::AdInstanceType MessageStream::DetermineMessageType() {
  auto* desc_pool = google::protobuf::DescriptorPool::generated_pool();
  auto* ref = default_instance_.GetReflection();
  auto* desc = default_instance_.GetDescriptor();
  int begin = desc->extension_range(0)->start, end = desc->extension_range(0)->end;
  for (int i = begin; i < end; i++) {
    auto* field = desc_pool->FindExtensionByNumber(desc, i);
    if (!field) {
      continue;
    }

    auto* msg = ref->MutableMessage(&default_instance_, field);
    if (!msg) {
      continue;
    } else if (msg->GetDescriptor() == base_msg_->GetDescriptor()) {
      msg_type_ = (kuaishou::ad::AdEnum::AdInstanceType)(i - begin);
      return msg_type_;
    }
  }
  return msg_type_;
}

static const int CACHE_SIZE = 1 << 22;
bool HdfsMessageStream::Valid() {
  if (!base_msg_) {
    return false;
  }

  if (record_num_ % 100 == 0) {
    end_time_ = base::GetTimestamp() / 1000;
  }

  if (!hdfs_stream_) {
    hdfs_stream_.reset(new hadoop::HDFSFileStream(
      hadoop::FLAGS_hadoop_namenode_ip.c_str(), hadoop::FLAGS_hadoop_namenode_port));
    if (!hdfs_stream_->Open(hdfs_file_.c_str(), O_RDONLY)) {
      LOG(ERROR) << "hdfs_stream_->Open(" << hdfs_file_ << ") failed.";
      return false;
    }

    buffer_.resize(CACHE_SIZE);
    cache_size_ = hdfs_stream_->Read(buffer_.data(), CACHE_SIZE);
    if (cache_size_ <= 0) {
      return false;
    }
  }

  if (cache_size_ - read_offset_ > 4) {
    uint32_t pb_size = *reinterpret_cast<uint32_t*>(buffer_.data() + read_offset_);
    if (pb_size > (120 << 10)) {
      LOG(ERROR) << MessageName() << ", pb_size: " << pb_size << " is too large. ";
    }

    if (read_offset_ + 4 + pb_size <= cache_size_) {
      char* data = buffer_.data() + read_offset_ + 4;
      read_offset_ += 4 + pb_size;
      base_msg_->Clear();
      bool flag = base_msg_->ParseFromArray(data, pb_size);
      record_num_ += (int)flag;
      return flag;
    }
  }

  cache_size_ = cache_size_ - read_offset_;
  memcpy(buffer_.data(), buffer_.data() + read_offset_, cache_size_);
  int read_size = hdfs_stream_->Read(buffer_.data() + cache_size_, CACHE_SIZE - cache_size_);
  if (read_size <= 0) {
    return false;
  } else {
    cache_size_ += read_size;
  }

  read_offset_ = 0;
  return Valid();
}

bool KfsMessageStream::Valid() {
  if (!base_msg_) {
    return false;
  }

  if (record_num_ % 100 == 0) {
    end_time_ = base::GetTimestamp() / 1000;
  }

  if (!kfs_stream_) {
    kfs_stream_.reset(new base::FileStream());
    int flags = base::PLATFORM_FILE_OPEN | base::PLATFORM_FILE_READ;
    if (kfs_stream_->Open(hdfs_file_, flags) != 0) {
      LOG(ERROR) << "kfs_stream_->Open(" << hdfs_file_ << ") failed.";
      return false;
    }
    buffer_.resize(CACHE_SIZE);
    cache_size_ = kfs_stream_->Read(buffer_.data(), CACHE_SIZE);
    if (cache_size_ <= 0) {
      return false;
    }
  }

  if (cache_size_ - read_offset_ > 4) {
    uint32_t pb_size =
        *reinterpret_cast<uint32_t*>(buffer_.data() + read_offset_);
    if (pb_size > (120 << 10)) {
      LOG(ERROR) << MessageName() << ", pb_size: " << pb_size
                 << " is too large. ";
    }

    if (read_offset_ + 4 + pb_size <= cache_size_) {
      char* data = buffer_.data() + read_offset_ + 4;
      read_offset_ += 4 + pb_size;
      base_msg_->Clear();
      bool flag = base_msg_->ParseFromArray(data, pb_size);
      record_num_ += (int)flag;
      return flag;
    }
  }

  cache_size_ = cache_size_ - read_offset_;
  memcpy(buffer_.data(), buffer_.data() + read_offset_, cache_size_);
  int read_size = kfs_stream_->Read(buffer_.data() + cache_size_,
                                        CACHE_SIZE - cache_size_);
  if (read_size <= 0) {
    return false;
  } else {
    cache_size_ += read_size;
  }

  read_offset_ = 0;
  return Valid();
}

bool HdfsMessageStreamForAdInstance::Valid() {
  if (!base_msg_) {
    return false;
  }

  if (record_num_ % 100 == 0) {
    end_time_ = base::GetTimestamp() / 1000;
  }

  if (!hdfs_stream_) {
    hdfs_stream_.reset(new hadoop::HDFSFileStream(
      hadoop::FLAGS_hadoop_namenode_ip.c_str(), hadoop::FLAGS_hadoop_namenode_port));
    if (!hdfs_stream_->Open(hdfs_file_.c_str(), O_RDONLY)) {
      LOG(ERROR) << "hdfs_stream_->Open(" << hdfs_file_ << ") failed.";
      return false;
    }

    // 读取头部结构
    ks::ad_base::PbHeader header;
    cache_size_ = hdfs_stream_->Read(reinterpret_cast<char*>(&header), sizeof(header));
    if (cache_size_ != sizeof(header)) {
      LOG(ERROR) << "hdfs header read fail";
    }

    buffer_.resize(CACHE_SIZE);
    cache_size_ = hdfs_stream_->Read(buffer_.data(), CACHE_SIZE);
    if (cache_size_ <= 0) {
      return false;
    }
  }

  if (cache_size_ - read_offset_ > 4) {
    uint32_t pb_size = *reinterpret_cast<uint32_t*>(buffer_.data() + read_offset_);
    if (pb_size > (120 << 10)) {
      LOG(ERROR) << MessageName() << ", pb_size: " << pb_size << " is too large. ";
    }

    if (read_offset_ + 4 + pb_size <= cache_size_) {
      char* data = buffer_.data() + read_offset_ + 4;
      read_offset_ += 4 + pb_size;
      default_instance_.Clear();
      bool flag = default_instance_.ParseFromArray(data, pb_size);
      record_num_ += (int)flag;
      return flag;
    }
  }

  cache_size_ = cache_size_ - read_offset_;
  memcpy(buffer_.data(), buffer_.data() + read_offset_, cache_size_);
  int read_size = hdfs_stream_->Read(buffer_.data() + cache_size_, CACHE_SIZE - cache_size_);
  if (read_size <= 0) {
    return false;
  } else {
    cache_size_ += read_size;
  }



  read_offset_ = 0;
  return Valid();
}

bool HdfsMessageStreamOnKfsForAdInstance::Valid() {
  if (!base_msg_) {
    return false;
  }

  if (record_num_ % 100 == 0) {
    end_time_ = base::GetTimestamp() / 1000;
  }

  if (!kfs_stream_) {
    kfs_stream_.reset(new base::FileStream());
    int flags = base::PLATFORM_FILE_OPEN | base::PLATFORM_FILE_READ;
    if (kfs_stream_->Open(hdfs_file_, flags) != 0) {
      LOG(ERROR) << "kfs_stream_->Open(" << hdfs_file_ << ") failed.";
      return false;
    }

    // 读取头部结构
    ks::ad_base::PbHeader header;
    cache_size_ = kfs_stream_->Read(reinterpret_cast<char *>(&header), sizeof(header));
    if (cache_size_ != sizeof(header)) {
      LOG(ERROR) << "hdfs header read fail";
    }

    buffer_.resize(CACHE_SIZE);
    cache_size_ = kfs_stream_->Read(buffer_.data(), CACHE_SIZE);
    if (cache_size_ <= 0) {
      return false;
    }
  }

  if (cache_size_ - read_offset_ > 4) {
    uint32_t pb_size = *reinterpret_cast<uint32_t*>(buffer_.data() + read_offset_);
    if (pb_size > (120 << 10)) {
      LOG(ERROR) << MessageName() << ", pb_size: " << pb_size << " is too large. ";
    }

    if (read_offset_ + 4 + pb_size <= cache_size_) {
      char* data = buffer_.data() + read_offset_ + 4;
      read_offset_ += 4 + pb_size;
      default_instance_.Clear();
      bool flag = default_instance_.ParseFromArray(data, pb_size);
      record_num_ += (int)flag;
      return flag;
    }
  }

  cache_size_ = cache_size_ - read_offset_;
  memcpy(buffer_.data(), buffer_.data() + read_offset_, cache_size_);
  int read_size = kfs_stream_->Read(buffer_.data() + cache_size_, CACHE_SIZE - cache_size_);
  if (read_size <= 0) {
    return false;
  } else {
    cache_size_ += read_size;
  }

  read_offset_ = 0;
  return Valid();
}

bool HdfsLiteIndexStream::Valid() {
  if (!base_msg_) {
    return false;
  }

  if (record_num_ % 100 == 0) {
    end_time_ = base::GetTimestamp() / 1000;
  }

  if (!hdfs_stream_) {
    hdfs_stream_.reset(
        new hadoop::HDFSFileStream(hadoop::FLAGS_hadoop_namenode_ip.c_str(),
                                   hadoop::FLAGS_hadoop_namenode_port));
    auto real_path = base::FilePath(hdfs_file_)
                         .DirName()
                         .Append(paths_[curr_read_index_])
                         .ToString();
    if (!hdfs_stream_->Open(real_path.c_str(), O_RDONLY)) {
      LOG(ERROR) << "hdfs_stream_->Open(" << real_path << ") failed.";
      return false;
    }
    buffer_.resize(CACHE_SIZE);
    cache_size_ = hdfs_stream_->Read(buffer_.data(), CACHE_SIZE);
    if (cache_size_ <= 0) {
      return false;
    }
  }

  if (cache_size_ - read_offset_ > 4) {
    uint32_t pb_size =
        *reinterpret_cast<uint32_t*>(buffer_.data() + read_offset_);
    if (pb_size > (120 << 10)) {
      LOG(ERROR) << MessageName() << ", pb_size: " << pb_size
                 << " is too large. ";
    }

    if (read_offset_ + 4 + pb_size <= cache_size_) {
      char* data = buffer_.data() + read_offset_ + 4;
      read_offset_ += 4 + pb_size;
      base_msg_->Clear();
      bool flag = base_msg_->ParseFromArray(data, pb_size);
      record_num_ += (int)flag;
      return flag;
    }
  }

  cache_size_ = cache_size_ - read_offset_;
  memcpy(buffer_.data(), buffer_.data() + read_offset_, cache_size_);
  int read_size = hdfs_stream_->Read(buffer_.data() + cache_size_,
                                     CACHE_SIZE - cache_size_);
  if (read_size <= 0) {
    if (curr_read_index_ < paths_.size() - 1) {
      curr_read_index_++;
      hdfs_stream_.reset(
          new hadoop::HDFSFileStream(hadoop::FLAGS_hadoop_namenode_ip.c_str(),
                                     hadoop::FLAGS_hadoop_namenode_port));
      auto real_path = base::FilePath(hdfs_file_)
                           .DirName()
                           .Append(paths_[curr_read_index_])
                           .ToString();
      if (!hdfs_stream_->Open(real_path.c_str(), O_RDONLY)) {
        LOG(ERROR) << "hdfs_stream_->Open(" << real_path << ") failed.";
        return false;
      }
      buffer_.resize(CACHE_SIZE);
      read_offset_ = 0;
      cache_size_ = hdfs_stream_->Read(buffer_.data(), CACHE_SIZE);
      if (cache_size_ <= 0) {
        return false;
      }
      return Valid();
    } else {
      return false;
    }
  } else {
    cache_size_ += read_size;
  }

  read_offset_ = 0;
  return Valid();
}
bool HdfsLiteIndexStreamOnKfs::Valid() {
  if (!base_msg_) {
    return false;
  }

  if (record_num_ % 100 == 0) {
    end_time_ = base::GetTimestamp() / 1000;
  }

  if (!kfs_stream_) {
    kfs_stream_.reset(new base::FileStream());
    int flags = base::PLATFORM_FILE_OPEN | base::PLATFORM_FILE_READ;
    auto real_path = base::FilePath(hdfs_file_)
                         .DirName()
                         .Append(paths_[curr_read_index_])
                         .ToString();
    if (kfs_stream_->Open(real_path, flags) != 0) {
      LOG(ERROR) << "kfs_stream_->Open(" << real_path << ") failed.";
      return false;
    }

    buffer_.resize(CACHE_SIZE);
    cache_size_ = kfs_stream_->Read(buffer_.data(), CACHE_SIZE);
    if (cache_size_ <= 0) {
      return false;
    }
  }

  if (cache_size_ - read_offset_ > 4) {
    uint32_t pb_size =
        *reinterpret_cast<uint32_t*>(buffer_.data() + read_offset_);
    if (pb_size > (120 << 10)) {
      LOG(ERROR) << MessageName() << ", pb_size: " << pb_size
                 << " is too large. ";
    }

    if (read_offset_ + 4 + pb_size <= cache_size_) {
      char* data = buffer_.data() + read_offset_ + 4;
      read_offset_ += 4 + pb_size;
      base_msg_->Clear();
      bool flag = base_msg_->ParseFromArray(data, pb_size);
      record_num_ += (int)flag;
      return flag;
    }
  }

  cache_size_ = cache_size_ - read_offset_;
  memcpy(buffer_.data(), buffer_.data() + read_offset_, cache_size_);
  int read_size =
      kfs_stream_->Read(buffer_.data() + cache_size_, CACHE_SIZE - cache_size_);
  if (read_size <= 0) {
    if (curr_read_index_ < paths_.size() - 1) {
      curr_read_index_++;
      kfs_stream_.reset(new base::FileStream());
      int flags = base::PLATFORM_FILE_OPEN | base::PLATFORM_FILE_READ;
      auto real_path = base::FilePath(hdfs_file_)
                           .DirName()
                           .Append(paths_[curr_read_index_])
                           .ToString();
      if (kfs_stream_->Open(real_path, flags) != 0) {
        LOG(ERROR) << "kfs_stream_->Open(" << real_path << ") failed.";
        return false;
      }
      buffer_.resize(CACHE_SIZE);
      read_offset_ = 0;
      cache_size_ = kfs_stream_->Read(buffer_.data(), CACHE_SIZE);
      if (cache_size_ <= 0) {
        return false;
      }
      return Valid();
    } else {
      return false;
    }
  } else {
    cache_size_ += read_size;
  }

  read_offset_ = 0;
  return Valid();
}

}  // namespace index_builder
}  // namespace ks

