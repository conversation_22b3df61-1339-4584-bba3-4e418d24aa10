#pragma once

#include <string>

#include "absl/strings/match.h"
#include "teams/ad/index_builder/utils/kconf.h"
#include "teams/ad/index_builder/utils/common.pb.h"

namespace ks {
namespace index_builder {

int32 GetShardId();

std::string GetServerOutputPath(const DumpConfig &dump_conf,
                                const std::string &server_name,
                                const std::string &version = "");

std::string GetServerUploadPath(const std::string &server_name, const std::string &version = "");

std::string GetPendingFileDir();

}  // namespace index_builder
}  // namespace ks
