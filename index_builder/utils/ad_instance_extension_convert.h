
#pragma once

#include <string>
#include <utility>
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"

namespace ks {
namespace index_builder {

static void ConvertFieldsToMessageImpl(google::protobuf::Message *detail_message,
                                       kuaishou::ad::AdInstance *ad) {
  const google::protobuf::Reflection *detail_ref = detail_message->GetReflection();
  const google::protobuf::Descriptor *detail_desc = detail_message->GetDescriptor();
  auto *fields = ad->mutable_fields();
  for (int i = 0; i < fields->size(); ++i) {
    auto &field = fields->Get(i);
    const google::protobuf::FieldDescriptor *to_field = detail_desc->FindFieldByName(field.name());
    if (!to_field) { continue; }
    int64_t tmp_int = 0;
    double tmp_double = 0.0;
    std::string tmp_string;
    if (field.has_int_value()) {
      tmp_int = field.int_value();
    }
    if (field.has_double_value()) {
      tmp_double = field.double_value();
    }
    if (field.has_string_value()) {
      tmp_string = field.string_value();
    }
#define CASE_DEAL_FIELD(CPPTYPE, func_suffix, value)               \
  case google::protobuf::FieldDescriptor::CPPTYPE:                 \
    detail_ref->Set##func_suffix(detail_message, to_field, value); \
    break
    switch (to_field->cpp_type()) {
      CASE_DEAL_FIELD(CPPTYPE_INT32, Int32, tmp_int);
      CASE_DEAL_FIELD(CPPTYPE_UINT32, UInt32, tmp_int);
      CASE_DEAL_FIELD(CPPTYPE_INT64, Int64, tmp_int);
      CASE_DEAL_FIELD(CPPTYPE_UINT64, UInt64, tmp_int);
      CASE_DEAL_FIELD(CPPTYPE_DOUBLE, Double, tmp_double);
      CASE_DEAL_FIELD(CPPTYPE_FLOAT, Float, tmp_double);
      CASE_DEAL_FIELD(CPPTYPE_BOOL, Bool, tmp_int);
      CASE_DEAL_FIELD(CPPTYPE_ENUM, EnumValue, tmp_int);
      CASE_DEAL_FIELD(CPPTYPE_STRING, String, std::move(tmp_string));
      default:
        break;
    }
#undef CASE_DEAL_FIELD
  }
}

static void ConvertFieldsToMessage(google::protobuf::Message *message, kuaishou::ad::AdInstance *ad) {
  // 转换本层字段
  ConvertFieldsToMessageImpl(message, ad);
  const google::protobuf::Reflection *extend_ref = message->GetReflection();
  const google::protobuf::Descriptor *extend_desc = message->GetDescriptor();
  // 转换下层字段
  for (int i = 0; i < extend_desc->field_count(); ++i) {
    const google::protobuf::FieldDescriptor *to_field = extend_desc->field(i);
    if (!to_field || to_field->is_repeated() || to_field->is_map()) { continue; }
    if (to_field->cpp_type() == google::protobuf::FieldDescriptor::CPPTYPE_MESSAGE) {
      google::protobuf::Message *sub_message = extend_ref->MutableMessage(message, to_field);
      ConvertFieldsToMessage(sub_message, ad);
    }
  }
}

static bool ConvertFieldsToExtensions(kuaishou::ad::AdInstance *ad) {
  if (!ad->has_type()) { return false; }
  static const auto *ref = ad->GetReflection();
  static const auto *desc = ad->GetDescriptor();
  const google::protobuf::FieldDescriptor *field =
      desc->FindFieldByNumber(desc->extension_range(0)->end + ad->type());
  if (!field) {
    static const auto *desc_pool = google::protobuf::DescriptorPool::generated_pool();
    field = desc_pool->FindExtensionByNumber(desc, ad->type() + desc->extension_range(0)->start);
  }
  if (!field) { return false; }
  bool not_exists = !ref->HasField(*ad, field);
  google::protobuf::Message *extension = ref->MutableMessage(ad, field);
  if (!extension) { return false; }
  ConvertFieldsToMessage(extension, ad);
  ad->clear_fields();
  return true;
}

}  // namespace index_builder
}  // namespace ks
