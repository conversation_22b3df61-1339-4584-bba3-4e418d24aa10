syntax = "proto3";

package ks.index_builder;
import "teams/ad/index_builder/admit_filters/admit_filter.proto";
import "teams/ad/ad_proto/kuaishou/ad/common/enums.proto";

message HotDataConfig {
  kuaishou.ad.AdEnum.CreativeServerBizType scene = 1;
  string data_path = 2;
}

message AdapterConfig {
  message UpStream {
    string topic_name = 1;
    repeated string forbid_types = 2;
    bool filter_das_inc = 3;  // 过滤非打分增量，只 index-message-proxy-ktable 部署使用 
  }
  message DownStream {
    string topic_name = 1;
    repeated string forbid_types = 2;
    ks.index_builder.AdmitFilterEnum.Type admit_filter_type = 3;
    string white_list_config_key = 4;                             //  白名单配置文件
    bool force_clear_offline_fields = 5;                          // 清理下线消息冗余字段
    string scene_name = 6;                                        // 落 clickhouse 区分
    string kafka2hive_key = 7;                                    //  kv格式下的
  }
  string stream_map_type = 1;                                     // 输入流
  string creative_adapter_key = 2;                                // 创意打分
  bool valid_check_skip = 3;                                      // 是否跳过有效性检查
  repeated string tables = 4;                                     // 处理的表名
  repeated UpStream up_stream = 5;                                // 增量消费上游
  repeated DownStream down_stream = 6;                            // 增量产出下游
  bool preview_support = 7;                                       // 是否支持预览增量
  ks.index_builder.AdmitFilterEnum.Type admit_filter_type = 8;    // proxy processor 使用，后续迁移至 DownStream
  string wild_table_path = 9;                                     // kfs 大宽表导出数据路径
  string wild_table_hdfs_path = 10;                               // hdfs 大宽表导出数据路径
  // 是否放开全店虚拟创意（默认不放开，只有正排会打开）
  bool enable_virtual_creative = 11; 
  bool enable_ktable = 18;  // 是否产出 ktable 格式全量
  repeated HotDataConfig hot_data_config = 19; // lazy 表产出
}
