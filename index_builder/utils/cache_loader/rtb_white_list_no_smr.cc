#include "teams/ad/index_builder/utils/cache_loader/rtb_white_list_no_smr.h"

#include <cstring>
#include <string>
#include <vector>
#include <map>
#include "absl/strings/numbers.h"
#include "absl/strings/str_split.h"
#include "perfutil/perfutil.h"

#include "base/common/basic_types.h"
#include "ks/serving_util/dynamic_config.h"

#include "falcon/counter.h"

#include "teams/ad/ad_base/src/file/utility.h"
#include "teams/ad/data_push/p2p_data_version_manager.h"
#include "teams/ad/index_builder/utils/cache_loader/kconf.h"

namespace ks {
namespace index_builder {

constexpr int64_t kLogFrequency = 10000000;

RtbWhiteList::RtbWhiteList()
    : P2pCacheLoader(3 * 60 * 1000, *CacheLoaderKconfUtil::rtbWhiteList()) {
  version_tag_ = "ad_index_builder." + task_name_ + ".current_version";
}

void RtbWhiteList::StartPrepare() {
  LOG(INFO) << "enter StartPrepare!";
  static auto *p2p = ::ad::data_push::P2pDataVersionManager::GetInstance();
  if (!p2p) {
    LOG(ERROR) << "zombie_creative p2p init failed";
    return;
  }
  // 强制 p2p 完事才能进行后续
  while (!p2p->WaitForReady(task_name_)) {
    LOG(ERROR) << "p2p not ready: " << task_name_;
    std::this_thread::sleep_for(
        std::chrono::milliseconds(100));
  }
  LOG(INFO) << "p2p " << task_name_<< " start";
}

bool RtbWhiteList::LoadFromFile(bool *skipped, bool *degraded) {
  static auto *p2p = ::ad::data_push::P2pDataVersionManager::GetInstance();
  if (!p2p->WaitForReady(task_name_)) {
    LOG(WARNING) << "p2p not ready: " << task_name_;
    return false;
  }
  ::ad::data_push::VersionInfo info;
  if (!p2p->GetVersionInfoSafe(task_name_, &info)) {
    LOG(WARNING) << "p2p GetVersionInfoSafe failed: " << task_name_;
    return false;
  }

  if (info.version_name == version_) {
    *skipped = true;
    return true;
  }

  if (ad_base::LoadFromFile(info.full_version_dir,
                            std::bind(&RtbWhiteList::ParseLine, this, std::placeholders::_1))) {  // NOLINT
    version_ = info.version_name;
    version_timestamp_ = info.version;
    falcon::Set(version_tag_.c_str(), info.version, falcon::kNonAdditiveGauge);
    if (!p2p->Confirm(task_name_, info)) {
      LOG(WARNING) << "[" << task_name_ << "] Confirm failed.";
      falcon::Inc(absl::Substitute("data_push.confirm.failed.$0", task_name_).c_str());
    }
    return true;
  }

  return false;
}

bool RtbWhiteList::ParseLine(const std::string &line) {
  if (!process_data_) {
    return false;
  }

  // line = creative_id
  std::vector<std::string> tokens = absl::StrSplit(line, "\t", absl::SkipEmpty());
  if (tokens.size() != 1) {
    LOG(INFO) << line << " parsed failed";
    return false;
  }

  ignore_result(absl::StripAsciiWhitespace(tokens[0]));
  ino64_t creative_id = 0;
  if (!absl::SimpleAtoi(tokens[0], &creative_id)) {
    LOG_EVERY_N(WARNING, 10000)
        << "key:" << tokens[0] << ", is invalid, not integer!";
    return false;
  }

  (*process_data_).insert(creative_id);

  /*
  static const std::map<std::string, int64> string2enum{
      {"top", 1},       {"mid", 2},        {"bottom", 3},    {"middle_p0", 4},
      {"middle_p1", 5}, {"middle_p2", 6},  {"middle_p3", 7}, {"bottom_p0", 8},
      {"bottom_p1", 9}, {"bottom_p2", 10}, {"bottom_p3", 11}};

  auto map_it = string2enum.find(tokens[2]);
  int64_t mark = 0;  // unkown
  if (string2enum.count(tokens[2]) > 0) {
    mark = string2enum.at(tokens[2]);
  }

  (*process_data_)[licence_id] = mark;
  LOG_EVERY_N(INFO, 1000) << "key:" << tokens[0] << ", value:" << mark;
  */
  return true;
}

bool RtbWhiteList::IsInWhiteList(int64_t creative_id) {
  auto creative_data = GetData();
  if (creative_data->count(creative_id)) {
    return true;
  }
  return false;
}

void RtbWhiteList::DoTaskPostProc() {
  int64_t interval_ms = 60 * 1000;
  int64_t times = 3;
  int creative_count = GetData()->size();
  LOG(INFO) << "creative count: " << creative_count;
}

}  // namespace index_builder
}  // namespace ks
