#pragma once

#include <memory>
#include <string>
#include <unordered_set>
#include "teams/ad/index_builder/utils/cache_loader/search_winfo_backlist.h"
#include "teams/ad/index_builder/utils/cache_loader/account_mark_no_smr.h"
#include "teams/ad/index_builder/utils/cache_loader/rtb_white_list_no_smr.h"
#include "teams/ad/index_builder/utils/extra_adapter/creative_adapter.h"
#include "teams/ad/index_builder/utils/extra_adapter/label_adapter.h"
#include "serving_base/region/region_dict.h"
#include "serving_base/location/location.h"
#include "teams/ad/ad_base/src/geohash/ip.h"
#include "teams/ad/index_builder/utils/kconf.h"

namespace ks {
namespace index_builder {

// 索引模块 预加载统一
/*
  1. 账户打标
  2. rtb 白名单
  3. 创意打分
*/
void CacheCommonBegin(const std::string & creative_adapter_key, bool use_aggr_data = false) {
  auto* account_mark = AccountMarkTransform::GetInstance();
  account_mark->Start();

  ::base::RegionDict::Warmup();
  // ip lib manager
  ks::ad_base::IPUtil::GetIPUtilInstance() = nullptr;
  // location warm up
  CHECK_NOTNULL(base::Location::GetGlobalLocation());

  auto* rtb_white_list = RtbWhiteList::GetInstance();
  rtb_white_list->Start();
  while (!rtb_white_list->IsReady()) {
    LOG(INFO) << "rtb_white_list not ready sleep for 1s";
    sleep(1);
  }
  LOG(INFO) << "rtb_white_list ready!";

  if (AdKconfUtil::enableWinfoBlacklist()) {
    auto& search_winfo_backlist = SearchWinfoBlacklist::GetInstance();
    search_winfo_backlist.Start();
    while (!search_winfo_backlist.IsReady()) {
      LOG(INFO) << "search_winfo_blacklist not ready sleep for 1s";
      sleep(1);
    }
    LOG(INFO) << "search_winfo_blacklist ready!";
  }


  if (AdKconfUtil::enableLabel()) {
    auto label_adapter = LabelAdapter::GetInstance();
    while (!label_adapter->IsReady()) {
      LOG(INFO) << "label_adapter not ready sleep for 1s";
      sleep(1);
    }
    LOG(INFO) << "label_adapter ready!";
  }

  while (!account_mark->IsReady()) {
    LOG(INFO) << "account_mark not ready sleep for 1s";
    sleep(1);
  }
  LOG(INFO) << "account_mark ready!";

  if (AdKconfUtil::enableAdapterCreative()) {
    auto adapter_creative = AdapterForCreativeServer::GetInstance();
    adapter_creative->Init(creative_adapter_key, use_aggr_data);
    while (!adapter_creative->IsReady()) {
      LOG(INFO) << "adapter_creative not ready sleep for 1s";
      sleep(1);
    }
  }
  LOG(INFO) << "adapter_creative ready!";
}
}  // namespace index_builder
}  // namespace ks
