#pragma once

#include <memory>
#include <string>
#include <unordered_set>

#include "absl/container/flat_hash_map.h"

#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
#include "teams/ad/data_push/p2p_data_version_manager.h"

namespace ks {
namespace index_builder {

// account_id -> AccountMark 映射
class AccountMarkTransform
    : public ks::ad_base::P2pCacheLoader<absl::flat_hash_map<int64_t, int64_t>> {
 public:
  using ValueType = absl::flat_hash_map<int64_t, int64_t>;

  static AccountMarkTransform* GetInstance() {
    static AccountMarkTransform instance;
    return &instance;
  }

  int64_t GetAccountMarkByAccountId(int64_t account_id);
  void DoTaskPostProc();
 private:
  void StartPrepare() override;

  bool LoadFromFile(bool* skipped, bool* degraded);

  bool ParseLine(const std::string& line) override;

 private:
  AccountMarkTransform();

  std::string version_{};
  std::string version_tag_{};
  int64_t version_timestamp_ = -1;

 private:
  DISALLOW_COPY_AND_ASSIGN(AccountMarkTransform);
};

}  // namespace index_builder
}  // namespace ks
