#pragma once

#include <memory>
#include <string>
#include <unordered_set>

#include "absl/container/flat_hash_map.h"

#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace ks {
namespace index_builder {

/*
  15 天内活跃联盟创意
*/
class UniverseActiveCreative
    : public ks::ad_base::P2pCacheLoader<absl::flat_hash_map<int64_t, int64_t>> {
 public:
  using ValueType = absl::flat_hash_map<int64_t, int64_t>;

  static UniverseActiveCreative* GetInstance() {
    static UniverseActiveCreative instance;
    return &instance;
  }

  bool IsUniverseCreativeActive(int64_t creative_id);
  void DoTaskPostProc();
  bool Admit(const kuaishou::ad::AdInstance ad_instance);
 private:
  bool ParseLine(const std::string& line) override;

 private:
  UniverseActiveCreative();

 private:
  DISALLOW_COPY_AND_ASSIGN(UniverseActiveCreative);
};

}  // namespace index_builder
}  // namespace ks
