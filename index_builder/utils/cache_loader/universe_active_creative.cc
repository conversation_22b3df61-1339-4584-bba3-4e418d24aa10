#include "teams/ad/index_builder/utils/cache_loader/universe_active_creative.h"

#include <cstring>
#include <string>
#include <vector>

#include "absl/strings/numbers.h"
#include "absl/strings/str_split.h"
#include "perfutil/perfutil.h"

#include "base/common/basic_types.h"
#include "ks/serving_util/dynamic_config.h"
#include "teams/ad/ad_base/src/math/random/random_shuffle.h"
#include "falcon/counter.h"
#include "teams/ad/index_builder/utils/kconf.h"
#include "teams/ad/ad_base/src/file/utility.h"
#include "teams/ad/data_push/p2p_data_version_manager.h"

namespace ks {
namespace index_builder {

constexpr int64_t kLogFrequency = 10000000;

UniverseActiveCreative::UniverseActiveCreative()
    : P2pCacheLoader(3 * 60 * 1000,
                        "universe_active_creative") {}

bool UniverseActiveCreative::ParseLine(const std::string &line) {
  if (!process_data_) {
    return false;
  }

  std::vector<std::string> tokens = absl::StrSplit(line, "\t", absl::SkipEmpty());
  if (tokens.size() != 1) {
    LOG(INFO) << line << " parsed failed";
    return false;
  }

  ignore_result(absl::StripAsciiWhitespace(tokens[0]));
  ino64_t creative_id = 0;
  if (!absl::SimpleAtoi(tokens[0], &creative_id)) {
    LOG_EVERY_N(WARNING, 10000) << "key:" << tokens[0] << ", is invalid, not integer!";
    return false;
  }

  (*process_data_)[creative_id] = 0;
  LOG_EVERY_N(INFO, 1000) << "key:" << tokens[0] << ", value:" << 0;
  return true;
}

bool UniverseActiveCreative::IsUniverseCreativeActive(int64_t creative_id) {
  auto active_creatives = GetData();
  auto map_it = active_creatives->find(creative_id);
  return map_it != active_creatives->end();
}

void UniverseActiveCreative::DoTaskPostProc() {
  int64_t interval_ms = 60 * 1000;
  int64_t times = 3;
  int creative_count = GetData()->size();
  LOG(INFO) << "creative_count: " << creative_count;
}

bool UniverseActiveCreative::Admit(const kuaishou::ad::AdInstance ad_instance) {
  auto type = ad_instance.type();
  if (type != kuaishou::ad::AdEnum::AD_STYLE_MATERIAL_BIND) {
    return true;
  }
  auto style_material_bind = ad_instance.GetExtension(
      kuaishou::ad::tables::AdStyleMaterialBind::ad_style_material_bind_old);
  if (IsUniverseCreativeActive(style_material_bind.creative_id())) {
    return true;
  } else {
    // 非活跃创意控制退场比例
    if (AdKconfUtil::universeInactiveCreativeExitRatio() < ks::ad_base::AdRandom::GetInt(0, 99)) {
      return true;
    }
    return false;
  }
}

}  // namespace index_builder
}  // namespace ks
