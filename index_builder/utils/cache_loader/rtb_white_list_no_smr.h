#pragma once

#include <memory>
#include <string>
#include <unordered_set>

#include "absl/container/flat_hash_map.h"
#include "absl/container/flat_hash_set.h"

#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
#include "teams/ad/data_push/p2p_data_version_manager.h"

namespace ks {
namespace index_builder {

// rtb 白名单，在白名单之内则放过业务过滤
class RtbWhiteList
    : public ks::ad_base::P2pCacheLoader<absl::flat_hash_set<int64_t>> {
 public:
  using ValueType = absl::flat_hash_set<int64_t>;

  static RtbWhiteList* GetInstance() {
    static RtbWhiteList instance;
    return &instance;
  }

  bool IsInWhiteList(int64_t creative_id);

  void DoTaskPostProc();
 private:
  void StartPrepare() override;

  bool LoadFromFile(bool* skipped, bool* degraded);

  bool ParseLine(const std::string& line) override;

 private:
  RtbWhiteList();

  std::string version_{};
  std::string version_tag_{};
  int64_t version_timestamp_ = -1;

 private:
  DISALLOW_COPY_AND_ASSIGN(RtbWhiteList);
};

}  // namespace index_builder
}  // namespace ks
