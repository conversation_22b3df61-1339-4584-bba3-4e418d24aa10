#include "teams/ad/index_builder/utils/cache_loader/kconf.h"
#include "teams/ad/index_builder/utils/cache_loader/search_winfo_backlist.h"
namespace ks {
namespace index_builder {
SearchWinfoBlacklist::SearchWinfoBlacklist()
    : ks::ad_base::P2pCacheLoader<absl::flat_hash_set<int64_t>, ks::ad_base::DeserializeFileEnum::Basic>(
          3 * 60 * 1000, *CacheLoaderKconfUtil::searchWinfoBlacklist()) {}

void SearchWinfoBlacklist::StartPrepare() {
  LOG(INFO) << "enter StartPrepare!";
  static auto* p2p = ::ad::data_push::P2pDataVersionManager::GetInstance();
  if (!p2p) {
    LOG(ERROR) << "search winfo black list p2p init failed";
    return;
  }
  // 强制 p2p 完事才能进行后续
  while (!p2p->WaitForReady(task_name_)) {
    LOG(ERROR) << "p2p not ready: " << task_name_;
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
  }
  LOG(INFO) << "p2p " << task_name_ << " start";
}

bool SearchWinfoBlacklist::IsInBlacklist(int64_t winfo_id) {
  auto search_winfo_backlist = GetData();
  return search_winfo_backlist == nullptr ? false : search_winfo_backlist->count(winfo_id) > 0;
}
}  // namespace index_builder
}  // namespace ks
