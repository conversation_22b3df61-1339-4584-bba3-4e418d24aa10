#pragma once

#include "absl/container/flat_hash_set.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader_type_helper.h"
#include "teams/ad/data_push/p2p_data_version_manager.h"
namespace ks {
namespace index_builder {
class SearchWinfoBlacklist : public ks::ad_base::P2pCacheLoader<absl::flat_hash_set<int64_t>,
                                                                ks::ad_base::DeserializeFileEnum::Basic> {
 public:
  static SearchWinfoBlacklist& GetInstance() {
    static SearchWinfoBlacklist instance;
    return instance;
  }

  bool IsInBlacklist(int64_t winfo_id);

 private:
  void StartPrepare();

 private:
  SearchWinfoBlacklist();
};

}  // namespace index_builder
}  // namespace ks
