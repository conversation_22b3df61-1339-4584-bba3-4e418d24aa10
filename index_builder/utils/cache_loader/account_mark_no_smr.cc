#include "teams/ad/index_builder/utils/cache_loader/account_mark_no_smr.h"

#include <cstring>
#include <string>
#include <vector>
#include <map>
#include "absl/strings/numbers.h"
#include "absl/strings/str_split.h"
#include "perfutil/perfutil.h"

#include "base/common/basic_types.h"
#include "ks/serving_util/dynamic_config.h"

#include "falcon/counter.h"

#include "teams/ad/ad_base/src/file/utility.h"
#include "teams/ad/data_push/p2p_data_version_manager.h"
#include "teams/ad/index_builder/utils/cache_loader/kconf.h"

namespace ks {
namespace index_builder {

constexpr int64_t kLogFrequency = ********;

AccountMarkTransform::AccountMarkTransform()
    : P2pCacheLoader(3 * 60 * 1000,
                        *CacheLoaderKconfUtil::accountMarkDataName()) {
  version_tag_ = "ad_index_builder." + task_name_ + ".current_version";
}

void AccountMarkTransform::StartPrepare() {
  LOG(INFO) << "enter StartPrepare!";
  static auto *p2p = ::ad::data_push::P2pDataVersionManager::GetInstance();
  if (!p2p) {
    LOG(ERROR) << "zombie_creative p2p init failed";
    return;
  }
  // 强制 p2p 完事才能进行后续
  while (!p2p->WaitForReady(task_name_)) {
    LOG(ERROR) << "p2p not ready: " << task_name_;
    std::this_thread::sleep_for(
        std::chrono::milliseconds(100));
  }
  LOG(INFO) << "p2p " << task_name_<< " start";
}

bool AccountMarkTransform::LoadFromFile(bool *skipped, bool *degraded) {
  static auto *p2p = ::ad::data_push::P2pDataVersionManager::GetInstance();
  if (!p2p->WaitForReady(task_name_)) {
    LOG(WARNING) << "p2p not ready: " << task_name_;
    return false;
  }
  ::ad::data_push::VersionInfo info;
  if (!p2p->GetVersionInfoSafe(task_name_, &info)) {
    LOG(WARNING) << "p2p GetVersionInfoSafe failed: " << task_name_;
    return false;
  }

  if (info.version_name == version_) {
    *skipped = true;
    return true;
  }

  if (ad_base::LoadFromFile(info.full_version_dir,
                            std::bind(&AccountMarkTransform::ParseLine, this, std::placeholders::_1))) {  // NOLINT
    version_ = info.version_name;
    version_timestamp_ = info.version;
    falcon::Set(version_tag_.c_str(), info.version, falcon::kNonAdditiveGauge);
    if (!p2p->Confirm(task_name_, info)) {
      LOG(WARNING) << "[" << task_name_ << "] Confirm failed.";
      falcon::Inc(absl::Substitute("data_push.confirm.failed.$0", task_name_).c_str());
    }
    return true;
  }

  return false;
}

bool AccountMarkTransform::ParseLine(const std::string &line) {
  if (!process_data_) {
    return false;
  }

  // line = account_id + 中文公司名 + top/bottom/mid
  std::vector<std::string> tokens = absl::StrSplit(line, "\t", absl::SkipEmpty());
  if (tokens.size() != 3) {
    LOG(INFO) << line << " parsed failed";
    return false;
  }

  ignore_result(absl::StripAsciiWhitespace(tokens[0]));
  ino64_t account_id = 0;
  if (!absl::SimpleAtoi(tokens[0], &account_id)) {
    LOG_EVERY_N(WARNING, 10000) << "key:" << tokens[0] << ", is invalid, not integer!";
    return false;
  }

  static const std::map<std::string, int64> string2enum{
      {"top", 1},       {"mid", 2},        {"bottom", 3},    {"middle_p0", 4},
      {"middle_p1", 5}, {"middle_p2", 6},  {"middle_p3", 7}, {"bottom_p0", 8},
      {"bottom_p1", 9}, {"bottom_p2", 10}, {"bottom_p3", 11}, {"se_novice", 12},
      {"se_expert", 13}, {"plus_new_holdout", 14}, {"plus_new_not_holdout", 15}, {"plus_old_holdout", 16},
      {"plus_old_not_holdout", 17}};

  auto map_it = string2enum.find(tokens[2]);
  int64_t mark = 0;  // unkown
  if (string2enum.count(tokens[2]) > 0) {
    mark = string2enum.at(tokens[2]);
  }

  (*process_data_)[account_id] = mark;
  LOG_EVERY_N(INFO, 1000) << "key:" << tokens[0] << ", value:" << mark;
  return true;
}

int64_t AccountMarkTransform::GetAccountMarkByAccountId(int64_t account_id) {
  auto account_mark_data = GetData();
  auto map_it = account_mark_data->find(account_id);
  if (map_it != account_mark_data->end()) {
    return map_it->second;
  }
  return 0;
}

void AccountMarkTransform::DoTaskPostProc() {
  int64_t interval_ms = 60 * 1000;
  int64_t times = 3;
  int account_id_size = GetData()->size();
  LOG(INFO) << "account count: " << account_id_size;
}

}  // namespace index_builder
}  // namespace ks
