#pragma once
#include <string>
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
namespace ks {
namespace index_builder {

using namespace ks::ad_base::kconf;  // NOLINT

class CacheLoaderKconfUtil {
 public:
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.index_builder, accountMarkDataName,
                                        "account_mark_by_account_id");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.index_builder, rtbWhiteList,
                                        "rtb_white_list");  // rtb splash p2p
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.index_builder, searchWinfoBlacklist, "search_ads_inactive_winfo");
};
}  // namespace index_builder
}  // namespace ks
