#pragma once

#include <string>
#include <vector>
#include <utility>
#include <unordered_map>

#include "teams/ad/index_adapter/utils/kconf/kconf.pb.h"
#include "teams/ad/index_message_proxy/kconf/index_message_proxy.pb.h"
#include "teams/ad/index_builder/utils/adapter_config.pb.h"
#include "teams/ad/index_builder/utils/kconf.h"

namespace ks {
namespace index_builder {

class UnifiedAdapterConfigManager {
 public:
  static UnifiedAdapterConfigManager* GetInstanace() {
    static UnifiedAdapterConfigManager instance;
    return &instance;
  }
  // 迁移过程中的临时接口，迁移完成后删除
  void InitWithOldAdapterConfig(const ks::index_adapter::AdapterConfig& adapter_config);
  const ks::index_adapter::AdapterConfig& GetOldAdapterConfig() { return adapter_config_; }
  void InitWithOldProxyConfig(const ks::ad::index_message_proxy::ProxyConfig& proxy_config);
  const ks::ad::index_message_proxy::ProxyConfig& GetOldProxyConfig() { return proxy_config_; }
  // 获取具体配置项
  const std::string& GetStreamMapType() const;
  const std::string& GetCreativeAdapterKey() const;
  const std::string& GetKafka2hiveKey() const;
  const std::string& GetHotDataPath() const;
  const std::string& GetWhiteListKey() const;
  const bool IsSkipValidCheck() const;
  const bool IsDumpTableLite() const;
  const bool IsUseAggregationData() const;
  const std::vector<std::string>& GetTables();
  const std::vector<std::string>& GetP2pDirs();
  const std::vector<int64_t>& GetShardNo();
  AdmitFilterEnum::Type GetAdmitFilterType() const;
  index_adapter::ShardParam::Param GetShardParam() const;
  const bool IsPreviewSupport() const;
  const std::string& GetMainKafka2hiveKey() const;
  const bool EnableStoreWideVirtualCreative() const;
  const bool EnableKtableBuild() const;
  const std::vector<ks::index_builder::AdapterConfig::UpStream>& GetUpStreamInfo() {
    return up_stream_info_;
  }
  const std::vector<ks::index_builder::AdapterConfig::DownStream>& GetDownStreamInfo() {
    return down_stream_info_;
  }
  const std::vector<std::pair<ks::index_adapter::ShardParam::Param, int>>& GetShardParams() {
    return shard_param_;
  }

  const bool IsUseUnifiedAdapterConfig() const { return use_unified_config_; }
  const std::string& GetKconfName() const { return kconf_name_; }
  const int GetShardNumber() const { return 0; }
  const std::string& GetDownStreamTopic() const;
  AdmitFilterEnum::Type GetProxyAdmitFilterType() const;
  const std::unordered_map<int32_t, std::string>& GetAllHotDataPaths() {
    return all_hot_data_path_;
  }

  std::string GetWildTablePath();
  std::string GetWildTableHdfsPath();

 private:
  UnifiedAdapterConfigManager();
  bool is_adapter_{false};
  bool is_message_proxy_{false};
  bool use_unified_config_{false};
  ks::index_builder::AdapterConfig unified_adapter_config_;
  // 适配 adapter
  ks::index_adapter::AdapterConfig adapter_config_;
  std::vector<std::string> tables_;
  std::vector<std::string> p2p_dirs_;
  std::vector<int64_t> shard_no_vec_;
  // 适配 message proxy
  ks::ad::index_message_proxy::ProxyConfig proxy_config_;
  std::vector<ks::index_builder::AdapterConfig::UpStream> up_stream_info_;
  std::vector<ks::index_builder::AdapterConfig::DownStream> down_stream_info_;
  std::vector<std::pair<ks::index_adapter::ShardParam::Param, int>> shard_param_;
  std::unordered_map<int32_t, std::string> all_hot_data_path_;

  // 空结果
  std::vector<std::string> empty_;
  std::string empty_string_;
  // 读取的统一配置名称
  std::string kconf_name_;
  DISALLOW_COPY_AND_ASSIGN(UnifiedAdapterConfigManager);
};

}  // namespace index_builder
}  // namespace ks
