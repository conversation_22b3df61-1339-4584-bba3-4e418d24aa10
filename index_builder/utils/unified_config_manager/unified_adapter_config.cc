#include "teams/ad/index_builder/utils/unified_config_manager/unified_adapter_config.h"

#include "kenv/kenv.h"
#include "base/common/logging.h"
#include "teams/ad/index_adapter/utils/kconf/kconf.pb.h"
#include "teams/ad/index_builder/utils/adapter_config.pb.h"

namespace ks {
namespace index_builder {

UnifiedAdapterConfigManager::UnifiedAdapterConfigManager() {
  auto& ksn = ks::infra::KEnv::GetKWSInfo()->GetKSN();
  if (ksn.find("ad-index-adapter") != std::string::npos) {
    is_adapter_ = true;
    is_message_proxy_ = false;
  } else if (ksn.find("ad-index-message-proxy") != std::string::npos) {
    is_adapter_ = false;
    is_message_proxy_ = true;
  }
  unified_adapter_config_ = UnifiedAdapterConfig()->data();
  tables_.clear();
  p2p_dirs_.clear();
  shard_no_vec_.clear();
  shard_param_.clear();
  empty_.clear();
  empty_string_.clear();
  for (const auto& t : unified_adapter_config_.tables()) {
    tables_.emplace_back(t);
  }
  for (const auto& u : unified_adapter_config_.up_stream()) {
    up_stream_info_.emplace_back(u);
  }
  for (const auto& d : unified_adapter_config_.down_stream()) {
    down_stream_info_.emplace_back(d);
    shard_param_.emplace_back();
    auto& shard = shard_param_.back();
    shard.first = ks::index_adapter::ShardParam::NO_SHARD;
    shard.second = 0;
  }
  auto ksn_hub_type = GetKsnHubTypeBySuffix();
  kconf_name_ = absl::StrCat("ad.index_builder.AdapterConfig_", ksn_hub_type);
  use_unified_config_ = (AdKconfUtil::enableUnifedAdapterConfig() && !ksn_hub_type.empty());
  if (ksn_hub_type.empty()) {
    kconf_name_ = "null";
  }
  if (AdKconfUtil::enableTestUnifedAdapterConfig()) {
    use_unified_config_ = true;
    kconf_name_ = "ad.index_builder.AdapterConfig_test";
  }
  // 做一层保护，防止打开了开关忘了配对应的配置
  // 如果没有读取到配置，则不切换统一配置
  if (unified_adapter_config_.stream_map_type().empty()) {
    use_unified_config_ = false;
  }
  for (const auto& it : unified_adapter_config_.hot_data_config()) {
    all_hot_data_path_.emplace(it.scene(), it.data_path());
  }

  LOG(INFO) << "use_unified_config:" << use_unified_config_;
  LOG(INFO) << "unified_adapter_config:" << unified_adapter_config_.ShortDebugString();
}

void UnifiedAdapterConfigManager::InitWithOldAdapterConfig(
              const ks::index_adapter::AdapterConfig& adapter_config) {
  adapter_config_ = adapter_config;
  LOG(INFO) << "adapter_config:" << adapter_config_.ShortDebugString();
  // 若未使用统一配置，则使用独立配置初始化
  if (!use_unified_config_) {
    tables_.clear();
    for (const auto& t : adapter_config_.tables()) {
      tables_.emplace_back(t);
    }
    shard_no_vec_.clear();
    for (const auto shard : adapter_config_.shard_no_vec()) {
      shard_no_vec_.emplace_back(shard);
    }
    p2p_dirs_.clear();
    for (const auto& p : adapter_config_.p2p_dirs()) {
      p2p_dirs_.emplace_back(p);
    }
  }
}

void UnifiedAdapterConfigManager::InitWithOldProxyConfig(
              const ks::ad::index_message_proxy::ProxyConfig& proxy_config) {
  proxy_config_ = proxy_config;
  LOG(INFO) << "proxy_config:" << proxy_config_.ShortDebugString();
  // 若未使用统一配置，则使用独立配置初始化
  if (!use_unified_config_) {
    up_stream_info_.clear();
    down_stream_info_.clear();
    shard_param_.clear();
    for (const auto& u : proxy_config_.upstream_configs()) {
      up_stream_info_.emplace_back();
      up_stream_info_.back().set_topic_name(u.topic_name());
      up_stream_info_.back().mutable_forbid_types()->CopyFrom(u.forbid_types());
    }
    // 未配置多上游时，使用单上游配置初始化
    if (up_stream_info_.empty()) {
      up_stream_info_.emplace_back();
      up_stream_info_.back().set_topic_name(proxy_config_.upstream_topic_id());
    }
    for (const auto& d : proxy_config_.downstream_configs()) {
      down_stream_info_.emplace_back();
      auto& info = down_stream_info_.back();
      info.set_topic_name(d.topic_name());
      info.mutable_forbid_types()->CopyFrom(d.forbid_types());
      info.set_admit_filter_type(d.admit_filter_type());
      info.set_white_list_config_key(d.white_list_config_key());
      info.set_force_clear_offline_fields(d.force_clear_offline_fields());
      info.set_scene_name(d.scene_name());
      info.set_kafka2hive_key(d.kafka2hive_key());
      shard_param_.emplace_back();
      auto& shard = shard_param_.back();
      shard.first = d.shard_strategy_config().shard_param();
      shard.second = d.shard_strategy_config().shard_no();
    }
    // 未配置多下游时，使用单下游配置初始化
    if (down_stream_info_.empty()) {
      down_stream_info_.emplace_back();
      auto& info = down_stream_info_.back();
      info.set_topic_name(proxy_config_.downstream_topic_id());
      info.set_force_clear_offline_fields(false);
      info.set_white_list_config_key("empty");
      info.set_admit_filter_type(AdmitFilterEnum::ALL);
      shard_param_.emplace_back();
      auto& shard = shard_param_.back();
      shard.first = ks::index_adapter::ShardParam::NO_SHARD;
      shard.second = 0;
    }
    LOG_ASSERT(down_stream_info_.size() == shard_param_.size()) << "down_stream_info not match shard_param";
  }
}

const std::string& UnifiedAdapterConfigManager::GetStreamMapType() const {
  if (use_unified_config_) {
    return unified_adapter_config_.stream_map_type();
  } else {
    return adapter_config_.stream_map_type();
  }
}

const std::string& UnifiedAdapterConfigManager::GetCreativeAdapterKey() const {
  if (use_unified_config_) {
    return unified_adapter_config_.creative_adapter_key();
  } else {
    return adapter_config_.creative_adapter_key();
  }
}

const std::string& UnifiedAdapterConfigManager::GetKafka2hiveKey() const {
  if (use_unified_config_) {
    return empty_string_;
  } else {
    return adapter_config_.kafka2hive_key();
  }
}

const std::string& UnifiedAdapterConfigManager::GetHotDataPath() const {
  if (use_unified_config_) { return empty_string_; }
  return adapter_config_.hot_data_path();
}

const std::string& UnifiedAdapterConfigManager::GetWhiteListKey() const {
  if (use_unified_config_) { return empty_string_; }
  return adapter_config_.white_list_config_key();
}

const bool UnifiedAdapterConfigManager::IsSkipValidCheck() const {
  if (use_unified_config_) {
    return unified_adapter_config_.valid_check_skip();
  } else {
    return adapter_config_.valid_check_skip();
  }
}

const bool UnifiedAdapterConfigManager::IsDumpTableLite() const {
  if (use_unified_config_) {
    return false;
  }
  return adapter_config_.dump_table_lite();
}

const bool UnifiedAdapterConfigManager::IsUseAggregationData() const {
  if (use_unified_config_) {
    return false;
  }
  return adapter_config_.use_aggr_data();
}

const std::vector<std::string>& UnifiedAdapterConfigManager::GetP2pDirs() {
  if (use_unified_config_) {
    return empty_;
  }
  return p2p_dirs_;
}

const std::vector<std::string>& UnifiedAdapterConfigManager::GetTables() {
  return tables_;
}

const std::vector<int64_t>& UnifiedAdapterConfigManager::GetShardNo() {
  return shard_no_vec_;
}

AdmitFilterEnum::Type UnifiedAdapterConfigManager::GetAdmitFilterType() const {
  if (use_unified_config_) {
    return unified_adapter_config_.admit_filter_type();
  } else {
    return adapter_config_.filter_param();
  }
}

AdmitFilterEnum::Type UnifiedAdapterConfigManager::GetProxyAdmitFilterType() const {
  if (use_unified_config_ && is_message_proxy_) {
    return unified_adapter_config_.admit_filter_type();
  } else if (is_message_proxy_) {
    return proxy_config_.admit_filter_type();
  } else {
    return AdmitFilterEnum::ALL;
  }
}


index_adapter::ShardParam::Param UnifiedAdapterConfigManager::GetShardParam() const {
  if (use_unified_config_ || is_message_proxy_) {
    return index_adapter::ShardParam::NO_SHARD;
  }
  return adapter_config_.shard_param();
}

const bool UnifiedAdapterConfigManager::IsPreviewSupport() const {
  if (use_unified_config_) {
    return unified_adapter_config_.preview_support();
  } else if (is_message_proxy_) {
    return proxy_config_.preview_support();
  } else {
    return false;
  }
}

const bool UnifiedAdapterConfigManager::EnableStoreWideVirtualCreative() const {
  if (use_unified_config_) {
    return unified_adapter_config_.enable_virtual_creative();
  } else if (is_message_proxy_) {
    return proxy_config_.enable_virtual_creative();
  } else {
    return false;
  }
}

const bool UnifiedAdapterConfigManager::EnableKtableBuild() const {
  if (use_unified_config_) {
    return unified_adapter_config_.enable_ktable();
  }
  return adapter_config_.enable_ktable();
}

const std::string& UnifiedAdapterConfigManager::GetMainKafka2hiveKey() const {
  if (is_message_proxy_) {
    return proxy_config_.kafka2hive_key_master();
  } else {
    return empty_string_;
  }
}

const std::string& UnifiedAdapterConfigManager::GetDownStreamTopic() const {
  if (use_unified_config_) { return empty_string_; }
  if (is_message_proxy_) { return proxy_config_.downstream_topic_id(); }
  return empty_string_;
}

std::string UnifiedAdapterConfigManager::GetWildTablePath() {
  if (use_unified_config_) {
    return unified_adapter_config_.wild_table_path();
  }
  return empty_string_;
}

std::string UnifiedAdapterConfigManager::GetWildTableHdfsPath() {
  if (use_unified_config_) {
    return unified_adapter_config_.wild_table_hdfs_path();
  }
  return empty_string_;
}

}  // namespace index_builder
}  // namespace ks
