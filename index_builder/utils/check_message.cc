// Debug print AdInstance protos, i.e. index builder output, from a local file
// (or HDFS, very rarely used).
//
// Usage:
// 1. print all ACCOUNT/CAMPAIGN/UNIT protos (BE CAREFUL! It prints everything!)
//    ./check_message print_instance \
//       /data/web_server/data_push/ad_index_hot_index_default/2021-04-06_1400/ad_dsp_account.base
//
// 2. print a CREATIVE given an ID, e.g. for a CREATIVE ********** (only
// creative is supported for now)
//    ./check_message read_message \
//       /data/web_server/data_push/ad_index_hot_index_default/2021-04-06_1400/ad_dsp_creative.base
//       **********
//
// 3. print file size and other stats of every single index (BE CAREFUL! It
// prints everything!)
//    ./check_message check_index \
//       /data/web_server/data_push/ad_index_hot_index_default/2021-04-06_1400/ad_dsp_creative.base
//
// 4. print HDFS diffs (BE CAREFUL! It prints everything!)
//    ./check_message ad_dsp_account \
//        /home/<USER>/ad_mysql_dumper/hdfs_base_index_cascade_preonline/hot_index/\
//              2021-04-06_1600/ad_dsp_account.base \
//        /home/<USER>/ad_mysql_dumper/hdfs_base_index_tianchi/hot_index/\
//              2021-04-06_1600/ad_dsp_account.base hdfs
// 5. print container attach file
// ./check_message read_attach_message
// universe_collective_index/2021-05-07_1800/ad_dsp_campaign_attach.0.bin
// CAMPAIGN
// 6. check unit-target  old & new match
// unit-target 提供横级联版本检测
//  ./check_message unit-target ad_dsp_target.base ad_dsp_unit.base
//  ad_dsp_target_old.base ad_dsp_unit_old.base
// 7. diff_tables old new
// 对比同表 2 个不同来源的索引文件
//  ./check_message diff_tables online/ad_dsp_target.base preonline/ad_dsp_target.base
// 8. small_shop_valid unit.base small_shop.base
// 拆分 unit_small_shop 横级联部分于原始导出比对
#include <gflags/gflags.h>
#include <fstream>

#include "base/file/file_path.h"
#include "base/file/file_util.h"
#include "base/thread/thread_pool.h"
#include "google/protobuf/util/message_differencer.h"
#include "ks/serving_util/server_base.h"
#include "teams/ad/ad_index/index/utils/ad_index_helper.h"
#include "teams/ad/ad_index/framework/pb_reader/pb_file_loader.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/multiform_filter/common_tools.h"
#include "teams/ad/index_builder/tables/data_gate/all_tables.h"
#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"
#include "teams/ad/index_builder/utils/message_stream.h"

using kuaishou::ad::AdInstance;

using kuaishou::ad::AdEnum;
using namespace kuaishou::ad::tables;  // NOLINT
using namespace ks::index_builder;  // NOLINT
void sigint_handler(int s) {
  printf("Caught signal %d , exit \n", s);
  exit(1);
}

int64_t GetKey(const std::string& table, AdInstance* ad) {
  std::string key_name = "id";
  int64_t key = 0;

  auto* ad_desc = ad->GetDescriptor();
  auto* ad_reflect = ad->GetReflection();
  auto* desc_pool = google::protobuf::DescriptorPool::generated_pool();
  if (table == "ad_dsp_creative") {
    int field_number = 1004;
    auto* field_desc = desc_pool->FindExtensionByNumber(ad_desc, field_number);
    auto* msg = ad_reflect->MutableMessage(ad, field_desc);
    return (dynamic_cast<kuaishou::ad::tables::Creative *>(msg))->id();
  } else if (table == "ad_dsp_account") {
    int field_number = 1001;
    auto* field_desc = desc_pool->FindExtensionByNumber(ad_desc, field_number);
    auto* msg = ad_reflect->MutableMessage(ad, field_desc);
    return (dynamic_cast<kuaishou::ad::tables::Account *>(msg))->id();
  } else if (table == "ad_dsp_unit") {
    int field_number = 1003;
    auto* field_desc = desc_pool->FindExtensionByNumber(ad_desc, field_number);
    auto* msg = ad_reflect->MutableMessage(ad, field_desc);
    return (dynamic_cast<kuaishou::ad::tables::Unit *>(msg))->id();
  } else if (table == "ad_dsp_target") {
    int field_number = 1006;
    auto* field_desc = desc_pool->FindExtensionByNumber(ad_desc, field_number);
    auto* msg = ad_reflect->MutableMessage(ad, field_desc);
    return (dynamic_cast<kuaishou::ad::tables::Target *>(msg))->id();
  } else if (table == "ad_dsp_creative_daily_charge") {
    int field_number = 1024;
    auto* field_desc = desc_pool->FindExtensionByNumber(ad_desc, field_number);
    auto* msg = ad_reflect->MutableMessage(ad, field_desc);
    return (dynamic_cast<kuaishou::ad::tables::CreativeDayCharge *>(msg))->key();
  } else if (table == "ad_dsp_account_daily_charge") {
    key_name = "account_id";
  } else if (table == "ad_dsp_campaign_daily_charge") {
    key_name = "campaign_id";
  } else if (table == "ad_dsp_unit_daily_charge") {
    key_name = "unit_id";
  } else if (table == "ad_dsp_account_balance") {
    key_name = "account_id";
  } else if (table == "ad_dsp_app") {
    key_name = "app_id";
  } else if (table == "ad_dsp_agent") {
    key_name = "agent_id";
  } else if (table == "ad_dsp_agent_account") {
    key_name = "account_id";
  } else if (table == "ad_dsp_photo") {
    key_name = "photo_id";
  } else if (table == "ad_dsp_package_bg") {
    key_name = "bg_id";
  } else if (table == "ad_risk_account_initiative") {
    key_name = "account_id";
  } else if (table == "ad_risk_industry_initiative") {
    key_name = "second_industry_id";
  } else if (table == "ad_dsp_upload_population_orientation") {
    key_name = "orientation_id";
  } else if (table == "factory_creative_info") {
    key_name = "creative_id";
  } else if (table == "ad_dsp_account_support_info") {
    key_name = "account_id";
  } else if (table == "ad_dsp_unit_support_info") {
    key_name = "unit_id";
  } else if (table == "ad_dsp_unit_small_shop") {
    key_name = "unit_id";
  }

  for (auto& field : ad->fields()) {
    if (field.name() == key_name) {
      key = field.int_value();
      break;
    }
  }

  return key;
}

bool CompareInstance(AdInstance* hdfs_ad, AdInstance* mysql_ad, std::string* diff_report) {
  ks::ad_server::AdIndexHelper::ConvertFieldsToExtensions(hdfs_ad);
  ks::ad_server::AdIndexHelper::ConvertFieldsToExtensions(mysql_ad);

  auto* ad_desc = hdfs_ad->GetDescriptor();
  int field_number = ad_desc->extension_range(0)->start + hdfs_ad->type();
  auto* desc_pool = google::protobuf::DescriptorPool::generated_pool();
  auto* field_desc = desc_pool->FindExtensionByNumber(ad_desc, field_number);
  auto* ad_reflect = hdfs_ad->GetReflection();
  auto* had = ad_reflect->MutableMessage(hdfs_ad, field_desc);

  ad_desc = mysql_ad->GetDescriptor();
  field_number = ad_desc->extension_range(0)->start + mysql_ad->type();
  desc_pool = google::protobuf::DescriptorPool::generated_pool();
  field_desc = desc_pool->FindExtensionByNumber(ad_desc, field_number);
  ad_reflect = mysql_ad->GetReflection();
  auto* mad = ad_reflect->MutableMessage(mysql_ad, field_desc);

  google::protobuf::util::MessageDifferencer differ;
  differ.ReportDifferencesToString(diff_report);
  differ.set_message_field_comparison(google::protobuf::util::MessageDifferencer::EQUIVALENT);
  differ.set_float_comparison(google::protobuf::util::MessageDifferencer::APPROXIMATE);
  differ.set_repeated_field_comparison(google::protobuf::util::MessageDifferencer::AS_SET);
  if (!differ.Compare(*had, *mad)) {
    return false;
  }

  return true;
}

bool CompareAdInstance(const AdInstance& hdfs_ad, const AdInstance& mysql_ad, std::string* diff_report) {
  bool has_diff = false;
  std::stringstream ss;
  std::unordered_set<std::string> keys;
  std::unordered_map<std::string, kuaishou::ad::AdInstanceField> hdfs_fields, mysql_fields;
  for (auto& field : hdfs_ad.fields()) {
    keys.insert(field.name());
    hdfs_fields[field.name()] = field;
  }

  for (auto& field : mysql_ad.fields()) {
    keys.insert(field.name());
    mysql_fields[field.name()] = field;
  }

  for (auto& key : keys) {
    auto hdfs_it = hdfs_fields.find(key);
    auto mysql_it = mysql_fields.find(key);
    if (hdfs_it != hdfs_fields.end() && mysql_it != mysql_fields.end()) {
      std::string diff_report;
      google::protobuf::util::MessageDifferencer differ;
      differ.ReportDifferencesToString(&diff_report);
      differ.set_message_field_comparison(google::protobuf::util::MessageDifferencer::EQUIVALENT);
      differ.set_float_comparison(google::protobuf::util::MessageDifferencer::APPROXIMATE);
      differ.set_repeated_field_comparison(google::protobuf::util::MessageDifferencer::AS_SET);
      if (!differ.Compare(hdfs_it->second, mysql_it->second)) {
        has_diff = true;
        ss << key << "| " << diff_report;
      }
    } else if (hdfs_it != hdfs_fields.end()) {
      has_diff = true;
      ss << key << "| " << hdfs_it->second.ShortDebugString() << " -> NULL" << std::endl;
    } else {
      has_diff = true;
      ss << key << "| NULL -> " << mysql_it->second.ShortDebugString() << std::endl;
    }
  }

  *diff_report = ss.str();
  return has_diff;
}

void LoadBaseFile(std::string table, std::string base_file,
                  std::unordered_map<int64_t, AdInstance*>* ad_map) {
  std::cout << "base_file: " << base_file << std::endl;
  auto reader = std::make_shared<ks::ad_base::PbReader<AdInstance>>();
  int ret = reader->Open(base_file.c_str());
  if (ret != 0) {
    std::cout << "hdfs_reader.Open(" << base_file << ") failed. ret: " << ret << std::endl;
    return;
  }

  while (!reader->Eof()) {
    auto* ad = reader->Read();
    if (ad == nullptr) {
      continue;
    }
    auto* pb = new AdInstance;
    pb->CopyFrom(*ad);
    int64_t key = GetKey(table, pb);
    (*ad_map)[key] = pb;
  }

  std::cout << "table: " << table << ", record num: " << ad_map->size()
            << ", file: " << base_file << std::endl;
}

void GetDiff(std::string base_file, std::string target_file) {
  std::unordered_map<int64_t, AdInstance*> base_map;
  {
    auto reader = std::make_shared<ks::ad_base::PbReader<AdInstance>>();
    int ret = reader->Open(base_file.c_str());
    if (ret != 0) {
      std::cout << "hdfs_reader.Open(" << base_file << ") failed. ret: " << ret
                << std::endl;
      return;
    }

    while (!reader->Eof()) {
      auto* ad = reader->Read();
      if (ad == nullptr) {
        continue;
      }
      auto* pb = new AdInstance;
      pb->CopyFrom(*ad);
      int64_t key = GetPrimaryKey(pb);
      base_map[key] = pb;
    }
  }
  int64_t target_record = 0;
  int64_t miss = 0;

  {
    auto reader = std::make_shared<ks::ad_base::PbReader<AdInstance>>();
    int ret = reader->Open(target_file.c_str());
    if (ret != 0) {
      std::cout << "hdfs_reader.Open(" << target_file
                << ") failed. ret: " << ret << std::endl;
      return;
    }
    std::cout << "target->base diff record" << std::endl;
    while (!reader->Eof()) {
      auto* ad = reader->Read();
      if (ad == nullptr) {
        continue;
      }
      auto* inst = new AdInstance;
      inst->CopyFrom(*ad);
      int64_t key = GetPrimaryKey(inst);
      auto map_it = base_map.find(key);
      if (map_it != base_map.end()) {
        auto* inst_base = map_it->second;
        std::string report;
        if (!DiffRawAdInstances(inst_base, inst, &report)) {
          std::cout << "get a diff record: " << inst_base->ShortDebugString()
                    << " -> " << inst->ShortDebugString() << std::endl;
          std::cout << "report: " << report << std::endl;
        }
      } else {
        miss++;
      }
      delete inst;
      target_record++;
    }
  }

  std::cout << "base record: " << base_map.size()
            << " target record: " << target_record << std::endl;
  std::cout << "miss record: " << miss << std::endl;
  for (auto item : base_map) {
    delete item.second;
  }
}

/*
template<typename T, typename PB>
void CheckContainerDiff(int argc, char* argv[]) {
  if (argc < 4) {
    std::cout << "no enough args" << std::endl;
    return;
  }

  ks::ad_table_lite::Container<T> hdfs_container, mysql_container;
  mysql_container.Init("", true);
  std::string hdfs_file = std::string(argv[2]) + "/" + std::string(argv[1]);
  if (!hdfs_container.LoadFromFile(hdfs_file)) {
    std::cout << "hdfs_container.LoadFromFile(" << argv[2] << ") failed" << std::endl;
    return;
  }

  std::string mysql_file = std::string(argv[3]) + "/" + std::string(argv[1]);
  if (!mysql_container.LoadFromFile(mysql_file)) {
    std::cout << "mysql_container.LoadFromFile(" << argv[3] << ") failed" << std::endl;
    return;
  }

  std::cout << "after LoadFromFile, hdfs_container.size: " << hdfs_container.size()
            << ", mysql_container.size: " << mysql_container.size() << std::endl;

  std::unordered_set<uint64_t> keys;
  hdfs_container.ExecuteAll([&](uint64_t key, const T& creative,
      const char* data, size_t size) -> bool {
    keys.insert(key);
    return true;
  });

  int same_num = 0, diff_num = 0;
  std::cout << "after hdfs_container.ExecuteAll, keys.size: " << keys.size() << std::endl;
  for (auto key : keys) {
    std::string hdfs_value = hdfs_container.GetAttachData(key);
    std::string mysql_value = mysql_container.GetAttachData(key);
    if (hdfs_value.empty() || mysql_value.empty()) {
      continue;
    }

    std::string diff_report;
    PB hdfs_pb, mysql_pb;
    hdfs_pb.ParseFromString(hdfs_value);
    mysql_pb.ParseFromString(mysql_value);
    google::protobuf::util::MessageDifferencer differ;
    differ.ReportDifferencesToString(&diff_report);
    differ.set_message_field_comparison(google::protobuf::util::MessageDifferencer::EQUIVALENT);
    differ.set_float_comparison(google::protobuf::util::MessageDifferencer::APPROXIMATE);
    differ.set_repeated_field_comparison(google::protobuf::util::MessageDifferencer::AS_SET);
    if (!differ.Compare(hdfs_pb, mysql_pb)) {
      if (diff_num++ < 100) {
        std::cout << diff_num << ") id: " << key << ", diff report: " << diff_report;
        std::cout << "hdfs_pb: " << hdfs_pb.ShortDebugString() << std::endl;
        std::cout << "mysql_pb: " << mysql_pb.ShortDebugString() << std::endl << std::endl;
      }
    } else {
      same_num++;
    }
  }

  std::cout << "check: " << argv[1] << ", same_num: " << same_num << ", diff_num: " << diff_num << std::endl;
}

int CheckContainerDiff(int argc, char* argv[]) {
  std::string check_table = std::string(argv[1]);
  std::cout << "table: " << check_table << std::endl;
  if (check_table == "ad_dsp_account_daily_charge") {
    CheckContainerDiff<tables::data_gate::AccountDayCharge,
      kuaishou::ad::tables::AccountDayCharge>(argc, argv);
  } else if (check_table == "ad_dsp_campaign_daily_charge") {
    CheckContainerDiff<tables::data_gate::CampaignDayCharge,
      kuaishou::ad::tables::CampaignDayCharge>(argc, argv);
  } else if (check_table == "ad_dsp_unit_daily_charge") {
    CheckContainerDiff<tables::data_gate::UnitDayCharge, kuaishou::ad::tables::UnitDayCharge>(argc, argv);
  } else if (check_table == "ad_dsp_account_balance") {
    CheckContainerDiff<tables::data_gate::AccountBalance, kuaishou::ad::tables::AccountBalance>(argc, argv);
  } else if (check_table == "ad_dsp_account") {
    CheckContainerDiff<tables::data_gate::Account, kuaishou::ad::tables::Account>(argc, argv);
  } else if (check_table == "ad_dsp_account_support_info") {
    CheckContainerDiff<tables::data_gate::AccountSupportInfo,
      kuaishou::ad::tables::AccountSupportInfo>(argc, argv);
  } else if (check_table == "ad_dsp_app") {
    CheckContainerDiff<tables::data_gate::AdApp, kuaishou::ad::tables::AdApp>(argc, argv);
  } else if (check_table == "ad_dsp_unit_support_info") {
    CheckContainerDiff<tables::data_gate::UnitSupportInfo, kuaishou::ad::tables::UnitSupportInfo>(argc, argv);
  } else if (check_table == "ad_dsp_campaign") {
    CheckContainerDiff<tables::data_gate::Campaign, kuaishou::ad::tables::Campaign>(argc, argv);
  } else if (check_table == "ad_dsp_unit") {
    CheckContainerDiff<tables::data_gate::Unit, kuaishou::ad::tables::Unit>(argc, argv);
  } else if (check_table == "ad_dsp_creative") {
    CheckContainerDiff<tables::data_gate::Creative, kuaishou::ad::tables::Creative>(argc, argv);
  } else if (check_table == "ad_dsp_agent") {
    CheckContainerDiff<tables::data_gate::Agent, kuaishou::ad::tables::Agent>(argc, argv);
  } else if (check_table == "ad_dsp_agent_account") {
    CheckContainerDiff<tables::data_gate::AgentAccount, kuaishou::ad::tables::AgentAccount>(argc, argv);
  } else if (check_table == "ad_dsp_unit_target") {
    CheckContainerDiff<tables::data_gate::UnitTarget, kuaishou::ad::tables::UnitTarget>(argc, argv);
  } else if (check_table == "ad_dsp_target") {
    CheckContainerDiff<tables::data_gate::Target, kuaishou::ad::tables::Target>(argc, argv);
  } else if (check_table == "ad_dsp_target_paid_audience") {
    CheckContainerDiff<tables::data_gate::TargetPaidAudience,
      kuaishou::ad::tables::TargetPaidAudience>(argc, argv);
  } else if (check_table == "ad_dsp_photo") {
    CheckContainerDiff<tables::data_gate::PhotoStatus, kuaishou::ad::tables::PhotoStatus>(argc, argv);
  } else if (check_table == "ad_dsp_position") {
    CheckContainerDiff<tables::data_gate::AdPosition, kuaishou::ad::tables::AdPosition>(argc, argv);
  } else if (check_table == "ad_dsp_position_resource") {
    CheckContainerDiff<tables::data_gate::AdPositionResource,
      kuaishou::ad::tables::AdPositionResource>(argc, argv);
  } else if (check_table == "ad_dsp_creative_preview") {
    CheckContainerDiff<tables::data_gate::CreativePreview,
      kuaishou::ad::tables::AdCreativePreview>(argc, argv);
  } else if (check_table == "ad_dsp_material") {
    CheckContainerDiff<tables::data_gate::Material, kuaishou::ad::tables::Material>(argc, argv);
  } else if (check_table == "ad_dsp_industry_v3") {
    CheckContainerDiff<tables::data_gate::IndustryV3, kuaishou::ad::tables::IndustryV3>(argc, argv);
  } else if (check_table == "ad_dsp_package_bg") {
    CheckContainerDiff<tables::data_gate::PackageBg, kuaishou::ad::tables::PackageBg>(argc, argv);
  } else if (check_table == "ad_risk_creative_target") {
    CheckContainerDiff<tables::data_gate::RiskCreativeTarget,
      kuaishou::ad::tables::RiskCreativeTarget>(argc, argv);
  } else if (check_table == "ad_risk_target") {
    CheckContainerDiff<tables::data_gate::RiskTarget, kuaishou::ad::tables::RiskTarget>(argc, argv);
  } else if (check_table == "ad_risk_industry_initiative") {
    CheckContainerDiff<tables::data_gate::RiskIndustryInitiative,
      kuaishou::ad::tables::RiskIndustryInitiative>(argc, argv);
  } else if (check_table == "ad_risk_photo_initiative") {
    CheckContainerDiff<tables::data_gate::RiskPhotoInitiative,
      kuaishou::ad::tables::RiskPhotoInitiative>(argc, argv);
  } else if (check_table == "ad_risk_unit_initiative") {
    CheckContainerDiff<tables::data_gate::RiskUnitInitiative,
      kuaishou::ad::tables::RiskUnitInitiative>(argc, argv);
  } else if (check_table == "ad_risk_industry_white_account") {
    CheckContainerDiff<tables::data_gate::RiskIndustryWhiteAccount,
      kuaishou::ad::tables::RiskIndustryWhiteAccount>(argc, argv);
  } else if (check_table == "ad_dsp_trace_util") {
    CheckContainerDiff<tables::data_gate::TraceUtil, kuaishou::ad::tables::TraceUtil>(argc, argv);
  } else if (check_table == "ad_dsp_merchant_app_unit") {
    CheckContainerDiff<tables::data_gate::MerchantAppUnit, kuaishou::ad::tables::MerchantAppUnit>(argc, argv);
  } else if (check_table == "ad_dsp_card_show_data") {
    CheckContainerDiff<tables::data_gate::CardShowData, kuaishou::ad::tables::CardShowData>(argc, argv);
  } else if (check_table == "ad_dsp_trace_api_detection") {
    CheckContainerDiff<tables::data_gate::TraceApiDetection,
      kuaishou::ad::tables::TraceApiDetection>(argc, argv);
  } else if (check_table == "ad_dsp_upload_population_orientation") {
    CheckContainerDiff<tables::data_gate::UploadPopulationOrientation,
      kuaishou::ad::tables::UploadPopulationOrientation>(argc, argv);
  } else if (check_table == "ad_dsp_site_ext_info") {
    CheckContainerDiff<tables::data_gate::SiteExtInfo, kuaishou::ad::tables::SiteExtInfo>(argc, argv);
  } else if (check_table == "ad_dsp_creative_barrages") {
    CheckContainerDiff<tables::data_gate::CreativeBarrages,
      kuaishou::ad::tables::CreativeBarrages>(argc, argv);
  } else if (check_table == "ad_dsp_creative_advanced_programmed") {
    CheckContainerDiff<tables::data_gate::Creative,
      kuaishou::ad::tables::Creative>(argc, argv);
  } else if (check_table == "ad_dsp_unit_small_shop") {
    CheckContainerDiff<tables::data_gate::UnitSmallShop,
      kuaishou::ad::tables::UnitSmallShop>(argc, argv);
  }

  return 0;
}
*/

void CheckIndexFile(const std::string& index_file) {
  auto pb_reader = std::make_shared<ks::ad_base::PbReader<AdInstance>>();
  int ret = pb_reader->Open(index_file.c_str());
  if (ret != 0) {
    std::cout << "hdfs_reader.Open(" << index_file << ") failed. ret: " << ret << std::endl;
    return;
  }

  int64_t count = 0, pb_size = 0;
  while (!pb_reader->Eof()) {
    auto* ad = pb_reader->Read();
    if (!ad) {
      std::cout << "find null item" << std::endl;
      break;
    }

    count++;
    pb_size += ad->ByteSize();
    if (count < 20) {
      std::cout << "pb_size: " << ad->ByteSize() << std::endl;
    }
    if (count % 100000 == 0) {
      std::cout << "current num: " << count << std::endl;
    }
  }

  std::cout << "count: " << count << ", total pb size: " << pb_size << std::endl;
}

void ReadMessage(const std::string& index_file, int64_t id) {
  auto pb_reader = std::make_shared<ks::ad_base::PbReader<AdInstance>>();
  int ret = pb_reader->Open(index_file.c_str());
  if (ret != 0) {
    std::cout << "pb_reader.Open(" << index_file << ") failed. ret: " << ret << std::endl;
    return;
  }

  auto* desc_pool = google::protobuf::DescriptorPool::generated_pool();
  while (!pb_reader->Eof()) {
    auto* read_ad = pb_reader->Read();
    if (!read_ad) {
      std::cout << "find null item" << std::endl;
      break;
    }

    AdInstance ad;
    ad.CopyFrom(*read_ad);
    auto* ad_desc = ad.GetDescriptor();
    auto* ad_reflect = ad.GetReflection();
    int field_number = 1004;
    auto* field_desc = desc_pool->FindExtensionByNumber(ad_desc, field_number);
    auto* msg = ad_reflect->MutableMessage(&ad, field_desc);
    // TODO(caoyifan03): Add support for other types.
    auto* unit = dynamic_cast<kuaishou::ad::tables::Creative *>(msg);
    if (unit->id() == id) {
      std::cout << "pb: " << unit->ShortDebugString() << std::endl;
      break;
    }
  }
}

void ReadAttachMessage(const std::string& attach_file_name, const std::string enums_type) {
  auto type = ks::index_builder::GetAdInstanceType(enums_type);
  AdInstance ad_inst;
  ad_inst.set_type(static_cast<AdEnum::AdInstanceType>(type));
  auto* msg = ks::index_builder::GetExtensionField(&ad_inst);
  std::ifstream data_reader(attach_file_name);
  if (!data_reader) {
    std::cout << "open file: " << attach_file_name << " fail" << std::endl;
    return;
  }
  int64_t read_size = 0;
  uint32_t record_len = 0;
  uint32_t record_num = 0;
  std::vector<char> data_buf(10<<10);  // 10KB
  while (
      data_reader.read(reinterpret_cast<char*>(&record_len), sizeof(uint32_t))
          .good()) {
    int64_t record_offset = data_reader.tellg();
    if (record_offset + record_len > std::numeric_limits<uint32_t>::max()) {
      std::cout << "read data  failed " << attach_file_name
                 << " offset: " << record_offset - sizeof(uint32_t)
                 << " bad length:" << record_len << std::endl;
      return;
    }
    read_size += 4;
    if (data_buf.capacity() < record_len) {
      data_buf.reserve(record_len);
    }
    if (data_reader.read(data_buf.data(), record_len).good()) {
      read_size += record_len;

      if (!msg->ParseFromArray(data_buf.data(), record_len)) {
        std::cout << "read data  failed " << attach_file_name
                   << " offset: " << record_offset - sizeof(uint32_t)
                   << " bad length:" << record_len << std::endl;
        return;
      }
      std::cout << msg->ShortDebugString() << std::endl;
      read_size += record_len;
    } else {
      std::cout << "read data failed " << attach_file_name
                 << " offset: " << data_reader.tellg()
                 << " bad data with length:" << record_len << std::endl;
      return;
    }
    ++record_num;
  }
}

void PrintInstance(const std::string& index_file) {
  auto pb_reader = std::make_shared<ks::ad_base::PbReader<AdInstance>>();
  int ret = pb_reader->Open(index_file.c_str());
  if (ret != 0) {
    std::cout << "pb_reader.Open(" << index_file << ") failed. ret: " << ret << std::endl;
    return;
  }

  auto* desc_pool = google::protobuf::DescriptorPool::generated_pool();
  while (!pb_reader->Eof()) {
    auto* read_ad = pb_reader->Read();
    if (!read_ad) {
      std::cout << "find null item" << std::endl;
      break;
    }

    std::cout << "pb: " << read_ad->ShortDebugString() << std::endl;
  }
}

void PrintDasInstance(const std::string& hdfs_path, const std::string& pb_type) {
  LOG(INFO) << hdfs_path;
  std::shared_ptr<ks::index_builder::HdfsMessageStream> reader_ptr(
      new ks::index_builder::HdfsMessageStream(pb_type, hdfs_path));
  reader_ptr->SetBaseMessage(pb_type);
  while (reader_ptr->Valid()) {
    auto* msg = reader_ptr->NextMessage();
    LOG(INFO) << msg->ShortDebugString();
  }
}

void LoadFromBaseFromUnit(const std::string& index_file,
      std::map<int64_t, kuaishou::ad::tables::UnitSmallShopMerchantSupportInfo>* small_shop_map) {
  auto pb_reader = std::make_shared<ks::ad_base::PbReader<AdInstance>>();
  int ret = pb_reader->Open(index_file.c_str());
  if (ret != 0) {
    std::cout << "pb_reader.Open(" << index_file << ") failed. ret: " << ret
              << std::endl;
    return;
  }
  while (!pb_reader->Eof()) {
    auto* read_ad = pb_reader->Read();
    if (!read_ad) {
      std::cout << "find null item" << std::endl;
      break;
    }
    auto unit = read_ad->GetExtension(Unit::unit_old);
    // std::cout << "pb: " << target.ShortDebugString() << std::endl;
    auto unit_small_shop = unit.unit_small_shop_merchant_support_info();
    unit_small_shop.clear_merchant_item_type();
    unit_small_shop.clear_merchant_category_id();
    (*small_shop_map)[unit_small_shop.unit_id()] = unit_small_shop;
  }
}

void LoadFromBaseFromUnitSmallShop(const std::string& index_file,
      std::map<int64_t, kuaishou::ad::tables::UnitSmallShopMerchantSupportInfo>* small_shop_map) {
  auto pb_reader = std::make_shared<ks::ad_base::PbReader<AdInstance>>();
  int ret = pb_reader->Open(index_file.c_str());
  if (ret != 0) {
    std::cout << "pb_reader.Open(" << index_file << ") failed. ret: " << ret
              << std::endl;
    return;
  }
  while (!pb_reader->Eof()) {
    auto* read_ad = pb_reader->Read();
    if (!read_ad) {
      std::cout << "find null item" << std::endl;
      break;
    }
    auto unit_small_shop =
      read_ad->GetExtension(UnitSmallShopMerchantSupportInfo::unit_small_shop_merchant_support_info_old);
    unit_small_shop.clear_merchant_item_type();
    unit_small_shop.clear_merchant_category_id();
    (*small_shop_map)[unit_small_shop.unit_id()] = unit_small_shop;
  }
}

void SmallShopDiff(std::string new_unit_file, std::string new_small_shop_file) {
  std::map<int64_t, kuaishou::ad::tables::UnitSmallShopMerchantSupportInfo> new_small_shop_map;
  std::map<int64_t, kuaishou::ad::tables::UnitSmallShopMerchantSupportInfo> old_small_shop_map;
  LoadFromBaseFromUnit(new_unit_file, &new_small_shop_map);
  LoadFromBaseFromUnitSmallShop(new_small_shop_file, &old_small_shop_map);
  for (auto item : new_small_shop_map) {
    auto unit_small_shop = item.second;
    auto unit_id = item.first;
    if (old_small_shop_map.find(unit_id) == old_small_shop_map.end()) {
      std::cout << "unit_id: " << unit_id << " miss.";
      continue;
    }

    auto unit_small_shop_old = old_small_shop_map[unit_id];

    AdInstance ad_new;
    AdInstance ad_old;
    ad_new.set_type(kuaishou::ad::AdEnum::AD_DSP_UNIT_SMALL_SHOP_MERCHANT_SUPPORT_INFO);
    ad_old.set_type(kuaishou::ad::AdEnum::AD_DSP_UNIT_SMALL_SHOP_MERCHANT_SUPPORT_INFO);
    auto* new_unit_small_shop =
        dynamic_cast<kuaishou::ad::tables::UnitSmallShopMerchantSupportInfo*>(GetExtensionField(&ad_new));
    auto* old_unit_small_shop =
        dynamic_cast<kuaishou::ad::tables::UnitSmallShopMerchantSupportInfo*>(GetExtensionField(&ad_old));

    new_unit_small_shop->CopyFrom(unit_small_shop);
    old_unit_small_shop->CopyFrom(unit_small_shop_old);

    std::string report;
    if (!DiffRawAdInstances(&ad_old, &ad_new, &report)) {
      std::cout << "get a diff record: " << new_unit_small_shop->ShortDebugString()
                << " -> " << old_unit_small_shop->ShortDebugString() << std::endl;
      std::cout << "report: " << report << std::endl;
    }
  }
}


void LoadFromBase(const std::string& index_file,
                  std::map<int64_t, kuaishou::ad::tables::Target>* target_map) {
  auto pb_reader = std::make_shared<ks::ad_base::PbReader<AdInstance>>();
  int ret = pb_reader->Open(index_file.c_str());
  if (ret != 0) {
    std::cout << "pb_reader.Open(" << index_file << ") failed. ret: " << ret
              << std::endl;
    return;
  }

  auto* desc_pool = google::protobuf::DescriptorPool::generated_pool();
  while (!pb_reader->Eof()) {
    auto* read_ad = pb_reader->Read();
    if (!read_ad) {
      std::cout << "find null item" << std::endl;
      break;
    }
    auto target = read_ad->GetExtension(Target::target_old);
    // std::cout << "pb: " << target.ShortDebugString() << std::endl;
    (*target_map)[target.id()] = target;
  }
}

void LoadFromBase(const std::string& index_file,
                  std::map<int64_t, kuaishou::ad::tables::Unit>* unit_map) {
  auto pb_reader = std::make_shared<ks::ad_base::PbReader<AdInstance>>();
  int ret = pb_reader->Open(index_file.c_str());
  if (ret != 0) {
    std::cout << "pb_reader.Open(" << index_file << ") failed. ret: " << ret
              << std::endl;
    return;
  }

  auto* desc_pool = google::protobuf::DescriptorPool::generated_pool();
  while (!pb_reader->Eof()) {
    auto* read_ad = pb_reader->Read();
    if (!read_ad) {
      std::cout << "find null item" << std::endl;
      break;
    }
    auto unit = read_ad->GetExtension(Unit::unit_old);
    // std::cout << "pb: " << unit.ShortDebugString() << std::endl;
    (*unit_map)[unit.id()] = unit;
  }
}

kuaishou::ad::tables::Unit GetUnitTarget(
    int64_t unit_id,
    std::map<int64_t, kuaishou::ad::tables::Target>* target_map,
    std::map<int64_t, kuaishou::ad::tables::Unit>* unit_map) {
  kuaishou::ad::tables::Unit unit;
  if (unit_map->find(unit_id) == unit_map->end()) {
    return unit;
  }
  unit = (*unit_map)[unit_id];
  auto target_id = unit.target_id();
  unit.clear_target_id();
  if (target_map->find(target_id) != target_map->end()) {
    unit.mutable_target()->CopyFrom((*target_map)[target_id]);
    unit.mutable_target()->clear_id();
  }
  return unit;
}

void UnitTargetDiff(std::string new_target_file, std::string new_unit_file,
                    std::string old_target_file, std::string old_unit_file) {
  std::map<int64_t, kuaishou::ad::tables::Target> new_target_map;
  std::map<int64_t, kuaishou::ad::tables::Unit> new_unit_map;
  std::map<int64_t, kuaishou::ad::tables::Target> old_target_map;
  std::map<int64_t, kuaishou::ad::tables::Unit> old_unit_map;
  LoadFromBase(new_target_file, &new_target_map);
  LoadFromBase(new_unit_file, &new_unit_map);
  LoadFromBase(old_target_file, &old_target_map);
  LoadFromBase(old_unit_file, &old_unit_map);
  std::cout << " new_target_map: " << new_target_map.size()
            << " new_unit_map: " << new_unit_map.size()
            << " old_target_map: " << old_target_map.size()
            << " old_unit_map: " << old_unit_map.size() << std::endl;
  for (auto item : new_unit_map) {
    auto unit_id = item.first;
    auto unit_new = GetUnitTarget(unit_id, &new_target_map, &new_unit_map);
    auto unit_old = GetUnitTarget(unit_id, &old_target_map, &old_unit_map);
    if (unit_old.id() == 0) {
      continue;
    }
    // auto unit_new_str = unit_new.target().ShortDebugString();
    // auto unit_old_str = unit_old.target().ShortDebugString();

    AdInstance ad_new;
    AdInstance ad_old;
    ad_new.set_type(kuaishou::ad::AdEnum::UNIT);
    ad_old.set_type(kuaishou::ad::AdEnum::UNIT);
    auto* new_unit =
        dynamic_cast<kuaishou::ad::tables::Unit*>(GetExtensionField(&ad_new));
    auto* old_unit =
        dynamic_cast<kuaishou::ad::tables::Unit*>(GetExtensionField(&ad_old));

    new_unit->CopyFrom(unit_new);
    old_unit->CopyFrom(unit_old);

    std::string report;
    if (!DiffRawAdInstances(&ad_old, &ad_new, &report)) {
      std::cout << "get a diff record: " << unit_old.ShortDebugString()
                << " -> " << unit_new.ShortDebugString() << std::endl;
      std::cout << "report: " << report << std::endl;
    }
  }
}

int main(int argc, char* argv[]) {
  ks::index_builder::InitEnum2Type();
  if (std::string(argv[1]) == "check_index") {
    CheckIndexFile(argv[2]);
  } else if (std::string(argv[1]) == "read_message") {
    ReadMessage(argv[2], strtol(argv[3], nullptr, 10));
  } else if (std::string(argv[1]) == "print_instance") {
    PrintInstance(argv[2]);
  } else if (std::string(argv[1]) == "read_attach_message") {
    ReadAttachMessage(argv[2], argv[3]);
  } else if (std::string(argv[1]) == "unit-target") {
    UnitTargetDiff(argv[2], argv[3], argv[4], argv[5]);
  } else if (std::string(argv[1]) == "diff_tables") {
    std::cout << "base: " << argv[2] << " target: " << argv[3] << std::endl;
    GetDiff(argv[2], argv[3]);
  } else if (std::string(argv[1]) == "small_shop_valid") {
    SmallShopDiff(argv[2], argv[3]);
  } else {
    std::cout << "Command not supported. Please read "
                 "teams/ad/index_builder/utils/check_message.cc comment before "
                 "you proceed!"
              << std::endl;
  }
  return 0;
}
