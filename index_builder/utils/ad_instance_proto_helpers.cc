#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"
#include <memory>
#include <string>
#include <utility>
#include <vector>
#include "absl/strings/str_join.h"
#include "absl/strings/str_split.h"
#include "absl/types/span.h"
#include "teams/ad/ad_index/index/utils/ad_index_helper.h"
#include "teams/ad/ad_index/framework/pb_reader/pb_file_loader.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"

namespace ks {
namespace index_builder {

using kuaishou::ad::AdInstance;

google::protobuf::Message* GetExtensionField(kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  auto* ad_desc = ad->GetDescriptor();
  int field_number = ad_desc->extension_range(0)->start + msg_type;
  auto* desc_pool = google::protobuf::DescriptorPool::generated_pool();
  auto* field_desc = desc_pool->FindExtensionByNumber(ad_desc, field_number);
  if (!field_desc) {
    return nullptr;
  }
  auto* ref = ad->GetReflection();
  return ref->MutableMessage(ad, field_desc);
}

void ClearFields(google::protobuf::Message* msg,
                 const std::vector<std::string> &fields_to_clear) {
  auto *msg_reflect = msg->GetReflection();
  for (const std::string &field : fields_to_clear) {
    if (auto *field_desc = msg->GetDescriptor()->FindFieldByName(field);
        field_desc != nullptr) {
      msg_reflect->ClearField(msg, field_desc);
    }
  }
  VLOG(8) << "Fields " << absl::StrJoin(fields_to_clear, ",")
          << " are now cleared. msg " << msg->ShortDebugString();
}

int64_t GetPrimaryKey(kuaishou::ad::AdInstance* ad_instance) {
  auto* message = GetExtensionField(ad_instance);
  if (!message) {
    return 0;
  }
  auto& actual = *message;
  const auto* actual_ref = actual.GetReflection();
  const auto* actual_desc = actual.GetDescriptor();
  const auto *field = actual_desc->FindFieldByNumber(1);
#define CASE_READ_FIELD(CPPTYPE, func_suffix)      \
  case google::protobuf::FieldDescriptor::CPPTYPE: \
    return actual_ref->Get##func_suffix(actual, field);
  switch (field->cpp_type()) {
    CASE_READ_FIELD(CPPTYPE_INT32, Int32);
    CASE_READ_FIELD(CPPTYPE_UINT32, UInt32);
    CASE_READ_FIELD(CPPTYPE_INT64, Int64);
    CASE_READ_FIELD(CPPTYPE_UINT64, UInt64);
    default:
      return 0;
  }
#undef CASE_READ_FIELD
  return 0;
}

int64_t GetPrimaryKey(google::protobuf::Message* msg) {
  auto& actual = *msg;
  const auto* actual_ref = actual.GetReflection();
  const auto* actual_desc = actual.GetDescriptor();
  const auto* field = actual_desc->FindFieldByNumber(1);
#define CASE_READ_FIELD(CPPTYPE, func_suffix)      \
  case google::protobuf::FieldDescriptor::CPPTYPE: \
    return actual_ref->Get##func_suffix(actual, field);
  switch (field->cpp_type()) {
    CASE_READ_FIELD(CPPTYPE_INT32, Int32);
    CASE_READ_FIELD(CPPTYPE_UINT32, UInt32);
    CASE_READ_FIELD(CPPTYPE_INT64, Int64);
    CASE_READ_FIELD(CPPTYPE_UINT64, UInt64);
    default:
      return 0;
  }
#undef CASE_READ_FIELD
  return 0;
}

absl::optional<std::string> TableToProtoType(const std::string &table_name) {
  // Special namings that do not follow "ad_dsp_foo_bar" ->
  // "kuaishou.ad.tables.FooBar" or "ad_risk_foo_bar" -> "RiskFooBar".
  static absl::flat_hash_map<std::string, std::string> table_to_proto_type = {
      {"ad_lp_page_das", "AdLpPageDas"},
      {"ad_dsp_app", "AdApp"},
      {"ad_dsp_photo", "PhotoStatus"},
      {"factory_creative_info", "FactoryCreativeInfo"}};
  if (table_to_proto_type.contains(table_name)) {
    return absl::StrCat("kuaishou.ad.tables.", table_to_proto_type[table_name]);
  }

  std::vector<std::string> v = absl::StrSplit(table_name, "_");
  if (v.size() < 2) return {};

  // Convert "{ad, risk, foo, bar}" to RiskFooBar, and "{ad, dsp, foo, bar}" to
  // "FooBar".
  int start_idx = absl::StartsWith(table_name, "ad_risk") ? 1 : 2;
  std::locale default_loc;
  for (int i = start_idx; i < v.size(); i++) {
    if (v[i].empty()) return {};
    auto str_it = v[i].begin();
    *str_it = std::toupper(*str_it, default_loc);
  }
  return absl::StrCat("kuaishou.ad.tables.",
                      absl::StrJoin(absl::MakeSpan(v).subspan(2), ""));
}

bool DiffRawAdInstances(AdInstance *test_instance, AdInstance *base_instance,
                        std::string *diff_report) {
  ks::ad_server::AdIndexHelper::ConvertFieldsToExtensions(test_instance);
  ks::ad_server::AdIndexHelper::ConvertFieldsToExtensions(base_instance);

  auto* test_ad = GetExtensionField(test_instance);
  auto* base_ad = GetExtensionField(base_instance);

  google::protobuf::util::MessageDifferencer differ;
  differ.ReportDifferencesToString(diff_report);
  differ.set_message_field_comparison(
      google::protobuf::util::MessageDifferencer::EQUIVALENT);
  differ.set_float_comparison(
      google::protobuf::util::MessageDifferencer::APPROXIMATE);
  differ.set_repeated_field_comparison(
      google::protobuf::util::MessageDifferencer::AS_SET);

  return differ.Compare(*test_ad, *base_ad);
}

}  // namespace index_builder
}  // namespace ks
