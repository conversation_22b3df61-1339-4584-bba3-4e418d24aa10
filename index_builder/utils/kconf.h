#pragma once

#include <algorithm>
#include <set>
#include <string>
#include <utility>
#include <vector>

#include "absl/strings/str_cat.h"
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/index_builder/utils/builder.pb.h"
#include "teams/ad/index_builder/utils/adapter_config.pb.h"
namespace ks {
namespace index_builder {

using namespace ks::ad_base::kconf;  // NOLINT

class AdKconfUtil {
 public:
  DEFINE_PROTOBUF_NODE_KCONF(IndexBuilderConf, ad.engine, indexBuilderConf);

  // 是否开启同表多文件 stream
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableOneTbMulFile, false);
  // 僵尸创意白名单账户
  DEFINE_SET_NODE_KCONF(int64, ad.adtarget, zombieAccountWhiteList)
  DEFINE_BOOL_KCONF_NODE(ad.engine, enableGenMd5, false);

  // 是否使用统一配置
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableUnifedAdapterConfig, false);
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableTestUnifedAdapterConfig, false);


  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableParseLabelWhenMerge, false);

  // style_server 加载版本控制
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableMatrixClientInfo, false);

  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.forward_index, rehashLoadFactorThreshold);

  DEFINE_INT32_KCONF_NODE(ad.index_builder, universeInactiveCreativeExitRatio, 50);  // 联盟非活跃创意退场比例
  // 本地生活经纬度坐标网格最大数量，超过则截断
  DEFINE_INT32_KCONF_NODE(ad.index_builder, wtLivePoiPointLimit, 3000);
  // style_material 表字段清理白名单
  DEFINE_SET_NODE_KCONF(std::string, ad.index_builder, styleMaterialWhiteSet);
  DEFINE_SET_NODE_KCONF(int64, ad.index_builder, adMatrixTypeMaterialSet);
  DEFINE_SET_NODE_KCONF(int64, ad.index_builder, adMatrixStyleTypeSet);
  DEFINE_SET_NODE_KCONF(int64, ad.index_builder, adMatrixCoverTypeSet);

  // 控制样式正排数据是否 clear
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableStyleMaterialClear, false);

  // merge base 表白名单，空表示全部 merge
  DEFINE_SET_NODE_KCONF(std::string, ad.index_builder, mergeTableWhiteList);
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableMinLoadFactorCheck,
                         false)  //  dump 时是否要求 load factor 大于 0.5
  DEFINE_PROTOBUF_NODE_KCONF(DumpConfig, ad.index_builder,
                             tableConfig);  //  表名 - PB 映射
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableWinfoBlacklist, false);
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableAccountMinCostInfoParse, false);
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableAdjustPriceCaliberParse, false);
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableUnitFansSupportInfoOnlineExtParse, false);
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableWTUnitTargetExpandPopulationParse, false);
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, disablePurchaseIntentionLabelDupParser, false);
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableReadOpt, false);
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableLabel, false);
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableAdapterCreative, true);
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableVersionDetectOpt, false);
  static bool IsShardEnable(const std::string &server = "") {
    auto shard_config = getStaticIndexShardConfig();
    if (!shard_config.enable()) {
      return false;
    }

    const auto &enable_servers = shard_config.builder_enable_servers();
    if (server.empty() || enable_servers.empty()) {
      return true;
    }
    for (const auto& svr : enable_servers) {
      if (svr == server) {
        return true;
      }
    }
    return false;
  }

  static int32_t MaxShardNum() {
    auto shard_config = getStaticIndexShardConfig();
    auto max_shard_num = std::max(shard_config.shard_num(), shard_config.shard_num_bak());
    LOG_EVERY_N(INFO, 10000) << "get max_shard_num=" << max_shard_num;
    return max_shard_num;
  }

  static bool IsTableShardEnable(
      const std::string &server, const std::string &table, std::string* shard_key = nullptr) {
    auto shard_config = getStaticIndexShardConfig();
    auto ret = IsShardEnable(server);
    if (!ret) {
      return false;
    }

    auto tit = shard_config.table_shardkey().find(table);
    if (tit == shard_config.table_shardkey().end()) {
      return false;
    }
    if (shard_key != nullptr) {
      *shard_key = tit->second;
    }

    LOG_EVERY_N(INFO, 10000) << "shard enable, server=" << server << " table=" << table
                             << " shard_key=" << tit->second << " max_shard_num="
                             << std::max(shard_config.shard_num(), shard_config.shard_num_bak());
    return true;
  }

  static bool GetShardResult(const std::string &server, const std::string &table_name,
                         const ::google::protobuf::Message *msg, std::set<int32_t> *shard_ids) {
    using ::google::protobuf::FieldDescriptor;
    std::string shard_key;
    auto enable = IsTableShardEnable(server, table_name, &shard_key);
    if (!enable) {
      return false;
    }
    auto *des = msg->GetDescriptor();
    auto *ref = msg->GetReflection();
    auto field_des = des->FindFieldByName(shard_key);
    if (field_des == nullptr) {
      LOG_EVERY_N(WARNING, 1000) << "server=" << server << "table_name=" << table_name << " msg type "
                                 << msg->GetTypeName() << " has no shard_key=" << shard_key;
      return false;
    }
    int64_t id = 0;
    switch (field_des->cpp_type()) {
      case FieldDescriptor::CPPTYPE_INT32:
        id = ref->GetInt32(*msg, field_des);
        break;
      case FieldDescriptor::CPPTYPE_INT64:
        id = ref->GetInt64(*msg, field_des);
        break;
      case FieldDescriptor::CPPTYPE_UINT32:
        id = ref->GetUInt32(*msg, field_des);
        break;
      case FieldDescriptor::CPPTYPE_UINT64:
        id = ref->GetUInt64(*msg, field_des);
        break;
      default:
        LOG_EVERY_N(WARNING, 1000) << "server=" << server << "table_name=" << table_name << "msg type "
                                   << msg->GetTypeName() << " shard_key=" << shard_key
                                   << "  invalid type=" << field_des->cpp_type();
        return false;
    }
    if (shard_ids != nullptr) {
      *shard_ids = GetShardResult(id);
    }
    VLOG_EVERY_N(33, 1000) << "server=" << server << "table_name=" << table_name << "msg type "
                           << msg->GetTypeName() << " shard_key=" << shard_key << " id=" << id;
    return true;
  }

 private:
  static IndexShardConfig getStaticIndexShardConfig() {
    static auto shard_config = indexShardConfig()->data();
    return shard_config;
  }

  static std::set<int32_t> GetShardResult(int64_t id) {
    auto shard_config = getStaticIndexShardConfig();
    std::set<int32_t> shard_id;
    auto shard_num = shard_config.shard_num();
    auto shard_num_bak = shard_config.shard_num_bak();
    if (shard_num != 0) {
      shard_id.insert(id % shard_num);
    }
    if (shard_num_bak != 0) {
      shard_id.insert(id % shard_num_bak);
    }
    return shard_id;
  }
  // 分片逻辑，目前只支持正排 Consumer
  DEFINE_PROTOBUF_NODE_KCONF(IndexShardConfig, ad.engine, indexShardConfig);
};

static inline std::string GetKsnHubTypeBySuffix() {
  static const std::vector<std::pair<std::string, std::string>> kSuffixMap = {
    {"hub-default", "default"},
    {"hub-external", "external"},
    {"hub-internal", "internal"},
    {"hub-search", "search"},
    {"hub-universe", "universe"},
    {"hub-style", "style"},
    {"hub-hosting", "hosting"},
    {"hub-dpa", "dpa"},
    {"hub-cold", "cold"},
    {"hub-budget", "budget"},
    {"hub-default-preonline", "default"},
    {"hub-external-preonline", "external"},
    {"hub-internal-preonline", "internal"},
    {"hub-search-preonline", "search"},
    {"hub-universe-preonline", "universe"},
    {"hub-style-preonline", "style"},
    {"hub-hosting-preonline", "hosting"},
    {"hub-dpa-preonline", "dpa"},
    {"hub-cold-preonline", "cold"},
    {"hub-default-dryrun", "default"},
    {"hub-external-dryrun", "external"},
    {"hub-internal-dryrun", "internal"},
    {"hub-search-dryrun", "search"},
    {"hub-universe-dryrun", "universe"},
    {"hub-style-dryrun", "style"},
    {"hub-hosting-dryrun", "hosting"},
    {"hub-dpa-dryrun", "dpa"},
    {"hub-cold-dryrun", "cold"},
    {"hub-ktable", "ktable"},
    {"hub-ktable-preonline", "ktable"},
    {"hub-ktable-default", "ktable_default"},
    {"hub-ktable-offline", "ktable_offline"},
    {"hub-ktable-hosting", "ktable_hosting"},
    {"hub-staging", "staging"}
  };
  static std::string ksn(getenv("KWS_SERVICE_NAME"));
  if (ksn.empty()) { return ""; }
  for (const auto& suffix : kSuffixMap) {
    if (absl::EndsWith(ksn, suffix.first)) {
      return suffix.second;
    }
  }
  return "";
}

// 全局 kconf 配置
using UnifiedAdapterConfigType = AD_KCONF_NS::ProtoKconf<AdapterConfig>;
static AD_KCONF_NS::KconfNodeType<UnifiedAdapterConfigType>::raw_type UnifiedAdapterConfig() {
  static AD_KCONF_NS::KconfNodeType<UnifiedAdapterConfigType>::raw_type data = [] () {
    static std::string ksn_hub_key = GetKsnHubTypeBySuffix();
    auto conf_key = absl::StrCat("ad.index_builder.AdapterConfig_", ksn_hub_key);
    static AD_KCONF_NS::KconfNode<UnifiedAdapterConfigType> conf(conf_key);
    static std::string test_conf_key = "ad.index_builder.AdapterConfig_test";
    static AD_KCONF_NS::KconfNode<UnifiedAdapterConfigType> test_conf(test_conf_key);
    if (AdKconfUtil::enableTestUnifedAdapterConfig()) {
      return test_conf.Get();
    }
    return conf.Get();
  }();
  return data;
}

}  // namespace index_builder
}  // namespace ks
