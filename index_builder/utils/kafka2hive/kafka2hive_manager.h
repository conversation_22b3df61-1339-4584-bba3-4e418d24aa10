#pragma once

#include <map>
#include <memory>
#include <string>
#include <unordered_set>
#include <vector>
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/kafka2hive/kafka2hive_kconf.h"
#include "teams/ad/index_message_proxy/kafka_util/kafka_manager.h"
#include "teams/ad/index_message_proxy/kconf/ad_inc_trace.pb.h"

namespace ks {
namespace index_builder {

/*
  全量落 hive
  增量落白盒记录 - 全量
              - 分场景
  过滤原因记录
*/
using ProducerPtr = std::shared_ptr<ks::ad::index_message_proxy::KafkaProducer>;

struct Params {
  std::string cluster_id{};
  int64_t partition_id{0};
  ks::ad::index_message_proxy::FilterEnum filter_reason;
  std::string scene_name{};
};

class Recorder {
 public:
  Recorder() {}
  virtual ~Recorder() {}
  virtual void Produce(kuaishou::ad::AdInstance* ad, const Params& param) = 0;
  virtual bool IsWorking() = 0;
 protected:
  ProducerPtr producer_;
};

/*
  落全量
*/
class FullHiveRecorder : public Recorder {
 public:
  explicit FullHiveRecorder(Base2HiveConfig base2hive_config);
  void Produce(kuaishou::ad::AdInstance* ad, const Params& param) override;
  bool IsWorking() { return producer_map_.size() > 0; }

 private:
  Base2HiveConfig base2hive_config_;
  // type_str 2 producer
  std::map<std::string, ProducerPtr> producer_map_;
};

/*
  增量全场景落
*/
class FullClickHouseRecorder : public Recorder {
 public:
  explicit FullClickHouseRecorder(FullClickhouseConfig full_clickhouse_config);
  void Produce(kuaishou::ad::AdInstance* ad, const Params& param) override;
  bool IsWorking() { return producer_.get() != nullptr; }

 private:
  FullClickhouseConfig full_clickhouse_config_;
};
/*
  增量 creative 分场景
*/
class SceneClickHouseRecorder : public Recorder {
 public:
  explicit SceneClickHouseRecorder(ScenceClickhouseConfig scene_clickhouse_config);
  void Produce(kuaishou::ad::AdInstance* ad, const Params& param) override;
  bool IsWorking() { return producer_.get() != nullptr; }

 private:
  ScenceClickhouseConfig scene_clickhouse_config_;
};

/*
  过滤原因落
*/
class FilterReasonRecorder : public Recorder {
 public:
  explicit FilterReasonRecorder(FilterReasonConfig filter_reason_config);
  void Produce(kuaishou::ad::AdInstance* ad, const Params& param) override;
  bool IsWorking() { return producer_.get() != nullptr; }

 private:
  FilterReasonConfig filter_reason_config_;
};

class Kafka2HiveImp {
  using RecorderPtr = std::shared_ptr<Recorder>;

 public:
  explicit Kafka2HiveImp(Kafka2HiveConfigV2 config);
  explicit Kafka2HiveImp(const std::string& config_key);

  bool IsWorkingForNorm() { return norm_recorders_.size() > 0; }
  bool IsWorkingForFilter() { return filter_recorders_.size() > 0; }
  void ProduceForNormal(kuaishou::ad::AdInstance* ad, const Params& param);
  void ProduceForFilter(kuaishou::ad::AdInstance* ad, const Params& param);

 private:
  std::vector<RecorderPtr> norm_recorders_;
  std::vector<RecorderPtr> filter_recorders_;
};

class Kafka2HiveManager {
 public:
  static Kafka2HiveManager* GetInstance() {
    static Kafka2HiveManager instance;
    return &instance;
  }
  /*
    用于 databuild 新构建获取 kafka2hive config 配置，其他情况请勿使用
  */
  void InitWithoutProduce(const std::string& config_key);
  Kafka2HiveConfigV2 GetKafka2HiveConfigV2() { return config_v2_; }
  /*
    two init can exist at the same time
  */
  // for kv config
  void Init(const std::string& config_key);

  // for default
  void Init();

  void KafkaProduce(kuaishou::ad::AdInstance* ad,
                    std::string cluster_id = std::string(),
                    int64_t partition_id = 0);
  void KafkaFilterReason(kuaishou::ad::AdInstance* ad,
                         ks::ad::index_message_proxy::FilterEnum filter_reason);

  void KafkaProduceByMultiProducer(
      kuaishou::ad::AdInstance* ad, const std::string& scene_name,
      const std::string& cluster_id = std::string(), int64_t partition_id = 0);
  Kafka2HiveManager() = default;
  ~Kafka2HiveManager() = default;
  explicit Kafka2HiveManager(const std::string& config_key) {
    Init(config_key);
  }

 private:
  void InstanceConvertToAdInc(
      kuaishou::ad::AdInstance* ad_instance,
      ks::ad::index_message_proxy::AdIncTraceInfo& ad_inc_trace_info,  // NOLINT
      std::string cluster_id, int64_t partition_id,
      const std::string& scene_name = std::string());

 private:
  // type_str 2 producer
  std::map<std::string, ProducerPtr> producer_map_;
  // 全部数据落 hive
  ProducerPtr producer_;
  Kafka2HiveConfig config_;
  ProducerPtr scene_producer_;
  // 分场景 creative 落 hive
  ProducerPtr creative_producer_;
  // 过滤原因落表
  ProducerPtr filter_message_producer_;
  // 增量全部数据落 clickhouse 使用
  ProducerPtr ad_inc_trace_producer_;

  // 拆分
  std::shared_ptr<Kafka2HiveImp> kafka2hive_ptr_;
  // 两种功能是否在工作
  bool is_normal_working_{false};
  bool is_filter_working_{false};

  // 用于 data_build 新构建获取 kafka2hive config 配置
  Kafka2HiveConfigV2 config_v2_;
};
void InstanceConvertToAdInc(kuaishou::ad::AdInstance* ad_instance, const Params& param,
                            ks::ad::index_message_proxy::AdIncTraceInfo* ad_inc_trace_info_ptr);
}  // namespace index_builder
}  // namespace ks
