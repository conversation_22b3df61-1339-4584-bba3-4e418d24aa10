syntax = "proto3";

package ks.index_builder;

message Kafka2HiveConfig {
  map<string, string> type2kafka_topic = 1;
  // 部署类型
  string biz_type = 2;
  string es_topic = 3;
  string creative_topic = 4;
  string filter_topic = 5; // 过滤原因
  string ad_inc_topic = 6; // 增量全量数据落 clickhouse 使用
}

message Base2HiveConfig {
  map<string, string> type2kafka_topic = 1;
  string scene_name = 2;
}

message FullClickhouseConfig {
  string clickhouse_topic = 1;
  string scene_name = 2;
}

message ScenceClickhouseConfig {
  string clickhouse_topic = 1;
  string scene_name = 2;  // for test
}

message FilterReasonConfig {
  string filter_topic = 1;
  string scene_name = 2;
}


message Kafka2HiveConfigV2 {
  Base2HiveConfig base2hive_config = 1;
  FullClickhouseConfig full_clickhouse_config = 2;
  ScenceClickhouseConfig scene_clickhouse_config = 3;
  FilterReasonConfig filter_reason_config = 4;
}

message Kafka2HiveConfigMap {
  map<string, Kafka2HiveConfigV2> config_map = 1;
}