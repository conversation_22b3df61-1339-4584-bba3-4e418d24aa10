#include "teams/ad/index_builder/utils/kafka2hive/kafka2hive_manager.h"
#include <chrono>
#include <set>
#include <vector>
#include "base/encoding/base64.h"
#include "base/time/time.h"
#include "perfutil/perfutil.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_index2hive.pb.h"
#include "teams/ad/engine_base/utils/is_valid_table.h"
#include "teams/ad/index_builder/multiform_filter/ad_filter.h"
#include "teams/ad/index_builder/multiform_filter/common_tools.h"
#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"
#include "teams/ad/index_message_proxy/kconf/ad_inc_trace.pb.h"

namespace ks {
namespace index_builder {

using ks::ad::index_message_proxy::AdIncTraceInfo;
using ks::ad::index_message_proxy::AdFilterTraceInfo;
using ks::infra::PerfUtil;

int64_t GetTimestampInMs() {
  return std::chrono::duration_cast<std::chrono::microseconds>(
             std::chrono::system_clock::now().time_since_epoch())
      .count() / 1000;
}

void AdInstanceUpdateTimeSet(kuaishou::ad::AdInstance* ad) {
  auto type = kuaishou::ad::AdEnum::AdInstanceType_Name(ad->type());
  int64_t time = base::GetTimestamp() / 1000;
  auto* msg = GetExtensionField(ad);
  if (ad->type() == kuaishou::ad::AdEnum::ACCOUNT) {
    auto* account = dynamic_cast<kuaishou::ad::tables::Account*>(msg);
    if (account) {
      account->set_create_time(time);
      account->clear_phone();
    }
  } else {
    const auto* reflection = msg->GetReflection();
    const auto* descriptor = msg->GetDescriptor();
    const auto* time_field = descriptor->FindFieldByName("update_time");
    if (time_field == nullptr) {
      LOG_EVERY_N(ERROR, 10000)
          << "type" << type << " has no update_time field"
          << "Please Check Kconf  "
             "https://kconf.corp.kuaishou.com/#/ad/index_builder/"
             "kafka2HiveConfig";
      return;
    }
    if (time_field->cpp_type() !=
        google::protobuf::FieldDescriptor::CppType::CPPTYPE_INT64) {
      LOG_EVERY_N(ERROR, 10000)
          << "type" << type
          << " has update_time field is not int64 type.Please Check: "
             "https://git.corp.kuaishou.com/ks-ad/ad-new-pb-component/-/"
             "blob/master/kuaishou-ad-new-biz-proto/src/main/proto/"
             "kuaishou/ad/tables/all.proto";
      return;
    }
    reflection->SetInt64(msg, time_field, time);
  }
}

void InstanceConvertToAdInc(
    kuaishou::ad::AdInstance* ad_instance, const Params& param, AdIncTraceInfo* ad_inc_trace_info_ptr) {
  AdIncTraceInfo& ad_inc_trace_info = *ad_inc_trace_info_ptr;
  std::string cluster_id = param.cluster_id;
  int64_t partition_id = param.partition_id;
  const std::string& scene_name = param.scene_name;
  static auto gettimeinnanos = []() -> int64_t {
    struct timespec ts;
    clock_gettime(CLOCK_REALTIME, &ts);

    return ts.tv_sec * ********** + ts.tv_nsec;
  };
  int64_t primary_id = GetPrimaryKey(ad_instance);
  int64_t account_id = GetValueByFieldNameV2(ad_instance, "account_id");
  ad_inc_trace_info.set_id(primary_id);
  ad_inc_trace_info.set_account_id(account_id);
  ad_inc_trace_info.set_type(AdEnum_AdInstanceType_Name(ad_instance->type()));
  ad_inc_trace_info.set_kws_name(DeployVariable::GetKwsName());
  ad_inc_trace_info.set_pod_name(DeployVariable::GetPodName());
  ad_inc_trace_info.set_process_time(ad_instance->process_time());
  bool online_status = ks::engine_base::IsValidTable(ad_instance);
  ad_inc_trace_info.set_online_status(online_status);
  for (const auto& modify_field : ad_instance->modify_field_list()) {
    ad_inc_trace_info.add_modify_field_list(modify_field);
  }
  ad_inc_trace_info.set_admit_time(base::GetTimestamp() / 1000);
  ad_inc_trace_info.set_data_type(GetDebugDataTypeV2(ad_instance)._to_string());
  ad_inc_trace_info.set_partition_id(partition_id);
  ad_inc_trace_info.set_cluster_id(cluster_id);
  ad_inc_trace_info.set_admit_time_in_nanos(gettimeinnanos());
  ad_inc_trace_info.set_biz_type(scene_name);
}





FullHiveRecorder::FullHiveRecorder(Base2HiveConfig base2hive_config)
    : base2hive_config_(base2hive_config) {
  for (auto item : base2hive_config_.type2kafka_topic()) {
    auto type = item.first;
    auto topic = item.second;
    ProducerPtr producer_ptr;
    producer_ptr.reset(new ks::ad::index_message_proxy::KafkaProducer());
    producer_ptr->Initialize(topic);
    producer_map_.emplace(type, producer_ptr);
    LOG(INFO) << "type: " << type << " topic: " << topic << " Init";
  }
}

void FullHiveRecorder::Produce(kuaishou::ad::AdInstance* ad,
                               const Params& param) {
  auto type = kuaishou::ad::AdEnum::AdInstanceType_Name(ad->type());
  std::set<std::string> builder_product_name{
      "ad_index_adapter", "ad-index-builder", "index_builder_target"};
  static std::string product_name =
      getenv("KWS_PRODUCT_NAME") == NULL ? "" : getenv("KWS_PRODUCT_NAME");
  if (product_name != "" && builder_product_name.count(product_name) == 0) {
    return;
  }

  auto map_it = producer_map_.find(type);
  if (map_it != producer_map_.end() &&
      ad->type() == kuaishou::ad::AdEnum::CREATIVE) {
    auto* msg = GetExtensionField(ad);
    auto* creative = dynamic_cast<kuaishou::ad::tables::Creative*>(msg);
    kuaishou::ad::tables::Creative tmp_creative;
    tmp_creative.CopyFrom(*creative);
    tmp_creative.set_scene(base2hive_config_.scene_name());
    LOG_EVERY_N(INFO, 10000) << tmp_creative.ShortDebugString();
    std::string trace_log_str;
    auto size = tmp_creative.ByteSizeLong();
    trace_log_str.resize(size);
    tmp_creative.SerializeToArray(&trace_log_str.front(), size);
    ks::ad::index_message_proxy::KafkaProducer::SendMessageToKafkaWithoutSleep(
        trace_log_str, map_it->second.get());
  } else if (map_it != producer_map_.end()) {  // 各个表落 hive
    auto* msg = GetExtensionField(ad);
    AdInstanceUpdateTimeSet(ad);
    std::string msg_str;
    auto size = msg->ByteSizeLong();
    msg_str.resize(size);
    msg->SerializeToArray(&msg_str.front(), size);
    ks::ad::index_message_proxy::KafkaProducer::SendMessageToKafkaWithoutSleep(
        msg_str, map_it->second.get());
    LOG_EVERY_N(INFO, 10000)
        << "type: " << type << " debugstr: " << msg->ShortDebugString();
  }
}

FullClickHouseRecorder::FullClickHouseRecorder(
    FullClickhouseConfig full_clickhouse_config)
    : full_clickhouse_config_(full_clickhouse_config) {
  std::string topic = full_clickhouse_config_.clickhouse_topic();
  if (!topic.empty()) {
    producer_.reset(new ks::ad::index_message_proxy::KafkaProducer());
    producer_->Initialize(topic);
    LOG(INFO) << " every thing to hive topic: " << topic << " Init";
  }
}

void FullClickHouseRecorder::Produce(kuaishou::ad::AdInstance* ad,
                                     const Params& param) {
  static std::string product_name =
      getenv("KWS_PRODUCT_NAME") == NULL ? "" : getenv("KWS_PRODUCT_NAME");
  // 全量增量索引落数据
  if (producer_ && (product_name == "ad_index_message_proxy" || product_name == "")) {
    AdIncTraceInfo ad_inc_trace_info;
    Params param_tmp = param;
    param_tmp.scene_name = full_clickhouse_config_.scene_name();
    InstanceConvertToAdInc(ad, param_tmp, &ad_inc_trace_info);
    LOG_EVERY_N(INFO, 10000) << ad_inc_trace_info.ShortDebugString();
    std::string trace_log_str;
    auto size = ad_inc_trace_info.ByteSizeLong();
    trace_log_str.resize(size);
    ad_inc_trace_info.SerializeToArray(&trace_log_str.front(), size);
    ks::ad::index_message_proxy::KafkaProducer::SendMessageToKafkaWithoutSleep(
        trace_log_str, producer_.get());
  }
}

SceneClickHouseRecorder::SceneClickHouseRecorder(
    ScenceClickhouseConfig scene_clickhouse_config)
    : scene_clickhouse_config_(scene_clickhouse_config) {
  std::string topic = scene_clickhouse_config_.clickhouse_topic();
  if (!topic.empty()) {
    producer_.reset(new ks::ad::index_message_proxy::KafkaProducer());
    producer_->Initialize(topic);
    LOG(INFO) << "creative scene to hive topic: " << topic << " Init";
  }
}

void SceneClickHouseRecorder::Produce(kuaishou::ad::AdInstance* ad,
                                      const Params& param) {
  static std::string product_name =
      getenv("KWS_PRODUCT_NAME") == NULL ? "" : getenv("KWS_PRODUCT_NAME");
  // 全量增量索引落数据
  // creative 分场景落 hive
  if (producer_ && (product_name == "ad_index_message_proxy" || product_name == "") &&
      ad->type() == kuaishou::ad::AdEnum::CREATIVE) {
    Params param_tmp = param;
    param_tmp.scene_name = scene_clickhouse_config_.scene_name();
    AdIncTraceInfo ad_inc_trace_info;
    InstanceConvertToAdInc(ad, param_tmp, &ad_inc_trace_info);
    LOG_EVERY_N(INFO, 10000) << ad_inc_trace_info.ShortDebugString();
    std::string trace_log_str;
    auto size = ad_inc_trace_info.ByteSizeLong();
    trace_log_str.resize(size);
    ad_inc_trace_info.SerializeToArray(&trace_log_str.front(), size);
    ks::ad::index_message_proxy::KafkaProducer::SendMessageToKafkaWithoutSleep(
        trace_log_str, producer_.get());
  }
}

FilterReasonRecorder::FilterReasonRecorder(
    FilterReasonConfig filter_reason_config)
    : filter_reason_config_(filter_reason_config) {
  std::string topic = filter_reason_config_.filter_topic();
  if (!topic.empty()) {
    producer_.reset(new ks::ad::index_message_proxy::KafkaProducer());
    producer_->Initialize(topic);
    LOG(INFO) << " every thing to hive topic: " << topic << " Init";
  }
}

void FilterReasonRecorder::Produce(kuaishou::ad::AdInstance* ad,
                                   const Params& param) {
  static std::string product_name =
      getenv("KWS_PRODUCT_NAME") == NULL ? "" : getenv("KWS_PRODUCT_NAME");
  // 全量增量索引落数据
  // creative 分场景落 hive
  auto filter_reason = param.filter_reason;
  PerfUtil::CountLogStash(
      1, "ad.index_message_proxy", "filter_reason", GetPbTypeStr(ad->type()),
      ks::ad::index_message_proxy::FilterEnum_Name(filter_reason),
      DeployVariable::GetKwsName(), DeployVariable::GetPodName());
  if (producer_.get() == nullptr) {
    return;
  }
  if (!Kafka2HiveKconf::enableFilterReasonTrace()) {
    return;
  }
  AdFilterTraceInfo ad_filter_trace_info;
  int64_t primary_id = GetPrimaryKey(ad);
  int64_t account_id = GetValueByFieldNameV2(ad, "account_id");
  ad_filter_trace_info.set_id(primary_id);
  ad_filter_trace_info.set_account_id(account_id);
  ad_filter_trace_info.set_type(AdEnum_AdInstanceType_Name(ad->type()));
  ad_filter_trace_info.set_kws_name(DeployVariable::GetKwsName());
  ad_filter_trace_info.set_pod_name(DeployVariable::GetPodName());
  ad_filter_trace_info.set_process_time(ad->process_time());
  bool online_status = ks::engine_base::IsValidTable(ad);
  ad_filter_trace_info.set_online_status(online_status);
  ad_filter_trace_info.set_biz_type(filter_reason_config_.scene_name());
  ad_filter_trace_info.set_admit_time(GetTimestampInMs());
  ad_filter_trace_info.set_data_type(GetDebugDataTypeV2(ad)._to_string());
  ad_filter_trace_info.set_reason(filter_reason);
  LOG_EVERY_N(INFO, 10000) << ad_filter_trace_info.ShortDebugString();
  std::string trace_log_str;
  auto size = ad_filter_trace_info.ByteSizeLong();
  trace_log_str.resize(size);
  ad_filter_trace_info.SerializeToArray(&trace_log_str.front(), size);
  ks::ad::index_message_proxy::KafkaProducer::SendMessageToKafkaWithoutSleep(
      trace_log_str, producer_.get());
}

Kafka2HiveImp::Kafka2HiveImp(Kafka2HiveConfigV2 config) {
  auto hive_ptr = std::make_shared<FullHiveRecorder>(config.base2hive_config());
  if (hive_ptr->IsWorking()) {
    norm_recorders_.push_back(hive_ptr);
  }
  auto full_clickhouse_ptr =
      std::make_shared<FullClickHouseRecorder>(config.full_clickhouse_config());
  if (full_clickhouse_ptr->IsWorking()) {
    norm_recorders_.push_back(full_clickhouse_ptr);
  }
  auto scene_clickhouse_ptr = std::make_shared<SceneClickHouseRecorder>(
      config.scene_clickhouse_config());
  if (scene_clickhouse_ptr->IsWorking()) {
    norm_recorders_.push_back(scene_clickhouse_ptr);
  }
  auto fill_reason_ptr =
      std::make_shared<FilterReasonRecorder>(config.filter_reason_config());
  if (fill_reason_ptr->IsWorking()) {
    filter_recorders_.push_back(fill_reason_ptr);
  }
}

Kafka2HiveImp::Kafka2HiveImp(const std::string& config_key)
    : Kafka2HiveImp(ks::index_builder::Kafka2HiveKconf::kafka2HiveConfigMap()
                        ->data()
                        .config_map()
                        .at(config_key)) {
  LOG(INFO) << ks::index_builder::Kafka2HiveKconf::kafka2HiveConfigMap()
                   ->data()
                   .config_map()
                   .at(config_key)
                   .ShortDebugString();
}

void Kafka2HiveImp::ProduceForNormal(kuaishou::ad::AdInstance* ad,
                                     const Params& param) {
  for (auto produce : norm_recorders_) {
    produce->Produce(ad, param);
  }
}

void Kafka2HiveImp::ProduceForFilter(kuaishou::ad::AdInstance* ad,
                                     const Params& param) {
  for (auto produce : norm_recorders_) {
    produce->Produce(ad, param);
  }
}

void Kafka2HiveManager::InstanceConvertToAdInc(
    kuaishou::ad::AdInstance* ad_instance, AdIncTraceInfo& ad_inc_trace_info,
    std::string cluster_id, int64_t partition_id,
    const std::string& scene_name) {
  static auto gettimeinnanos = []() -> int64_t {
    struct timespec ts;
    clock_gettime(CLOCK_REALTIME, &ts);

    return ts.tv_sec * ********** + ts.tv_nsec;
  };
  int64_t primary_id = GetPrimaryKey(ad_instance);
  int64_t account_id = GetValueByFieldNameV2(ad_instance, "account_id");
  ad_inc_trace_info.set_id(primary_id);
  ad_inc_trace_info.set_account_id(account_id);
  ad_inc_trace_info.set_type(AdEnum_AdInstanceType_Name(ad_instance->type()));
  ad_inc_trace_info.set_kws_name(DeployVariable::GetKwsName());
  ad_inc_trace_info.set_pod_name(DeployVariable::GetPodName());
  ad_inc_trace_info.set_process_time(ad_instance->process_time());
  bool online_status = ks::engine_base::IsValidTable(ad_instance);
  ad_inc_trace_info.set_online_status(online_status);
  for (const auto& modify_field : ad_instance->modify_field_list()) {
    ad_inc_trace_info.add_modify_field_list(modify_field);
  }
  ad_inc_trace_info.set_admit_time(base::GetTimestamp() / 1000);
  ad_inc_trace_info.set_data_type(GetDebugDataTypeV2(ad_instance)._to_string());
  ad_inc_trace_info.set_partition_id(partition_id);
  ad_inc_trace_info.set_cluster_id(cluster_id);
  ad_inc_trace_info.set_admit_time_in_nanos(gettimeinnanos());
  if (scene_name.empty()) {
    ad_inc_trace_info.set_biz_type(config_.biz_type());
  } else {
    ad_inc_trace_info.set_biz_type(scene_name);
  }
}



void Kafka2HiveManager::Init(const std::string& config_key) {
  auto config_map = Kafka2HiveKconf::kafka2HiveConfigMap()->data().config_map();
  auto map_it = config_map.find(config_key);
  LOG_ASSERT(map_it != config_map.end())
      << "config not find!! key: " << config_key;
  kafka2hive_ptr_ = std::make_shared<Kafka2HiveImp>(map_it->second);
  is_normal_working_ = kafka2hive_ptr_->IsWorkingForNorm();
  is_filter_working_ = kafka2hive_ptr_->IsWorkingForFilter();
}

void Kafka2HiveManager::InitWithoutProduce(const std::string& config_key) {
  auto config_map = Kafka2HiveKconf::kafka2HiveConfigMap()->data().config_map();
  auto map_it = config_map.find(config_key);
  LOG_ASSERT(map_it != config_map.end()) << "config not find!! key: " << config_key;
  config_v2_ = map_it->second;
}

void Kafka2HiveManager::Init() {
  config_ = Kafka2HiveKconf::kafka2HiveConfig()->data();
  for (auto item : config_.type2kafka_topic()) {
    auto type = item.first;
    auto topic = item.second;
    ProducerPtr producer_ptr;
    producer_ptr.reset(new ks::ad::index_message_proxy::KafkaProducer());
    producer_ptr->Initialize(topic);
    producer_map_.emplace(type, producer_ptr);
    LOG(INFO) << "type: " << type << " topic: " << topic << " Init";
  }

  std::string es_topic = config_.es_topic();
  if (!es_topic.empty()) {
    scene_producer_.reset(new ks::ad::index_message_proxy::KafkaProducer());
    scene_producer_->Initialize(es_topic);
    LOG(INFO) << " Scene Creative to es topic: " << es_topic << " Init";
  }

  std::string creative_topic = config_.creative_topic();
  if (!creative_topic.empty()) {
    creative_producer_.reset(new ks::ad::index_message_proxy::KafkaProducer());
    creative_producer_->Initialize(creative_topic);
    LOG(INFO) << " Scene Creative to es topic: " << creative_topic << " Init";
  }

  std::string topic = Kafka2HiveKconf::everthing2hiveKafkaTopic()->data();
  if (!topic.empty()) {
    producer_.reset(new ks::ad::index_message_proxy::KafkaProducer());
    producer_->Initialize(topic);
    LOG(INFO) << " every thing to hive topic: " << topic << " Init";
  }
  std::string filter_topic = config_.filter_topic();
  if (!filter_topic.empty()) {
    filter_message_producer_.reset(
        new ks::ad::index_message_proxy::KafkaProducer());
    filter_message_producer_->Initialize(filter_topic);
    LOG(INFO) << " filter message to hive topic: " << filter_topic << " Init";
  }
  std::string ad_inc_topic = config_.ad_inc_topic();
  if (!ad_inc_topic.empty()) {
    ad_inc_trace_producer_.reset(
        new ks::ad::index_message_proxy::KafkaProducer());
    ad_inc_trace_producer_->Initialize(ad_inc_topic);
    LOG(INFO) << " ad inc message to clickhouse topic: " << ad_inc_topic
              << " Init";
  }
}

int64_t Get_default_tmp_int() {
  return 0;
}

double Get_default_tmp_double() { return 0.0; }

std::string Get_default_tmp_string() { return std::string(); }

void PerfLogFieldValue(google::protobuf::Message* msg,
                       const std::string& type) {
  auto* extension = msg;
  const google::protobuf::Reflection* extend_ref = extension->GetReflection();
  const google::protobuf::Descriptor* extend_desc = extension->GetDescriptor();
  int64_t tmp_int = 0;
  double tmp_double = 0.0;
  std::string tmp_string{};
  typedef std::vector<const google::protobuf::FieldDescriptor*> FieldList;
  FieldList field_list;
  extend_ref->ListFields(*extension, &field_list);
  for (FieldList::const_iterator it = field_list.begin();
       it != field_list.end(); ++it) {
    const google::protobuf::FieldDescriptor* from_field = *it;
    std::string field_name = from_field->name();
    if (!from_field || from_field->is_repeated()) {
      continue;
    }

    if (from_field->cpp_type() ==
        google::protobuf::FieldDescriptor::CPPTYPE_MESSAGE) {
      google::protobuf::Message* sub_message =
          extend_ref->MutableMessage(extension, from_field);
      PerfLogFieldValue(sub_message, type);
    }
#define CASE_DEAL_FIELD(CPPTYPE, func_suffix, value)                       \
  case google::protobuf::FieldDescriptor::CPPTYPE:                         \
    value = extend_ref->Get##func_suffix(*extension, from_field);          \
    if (value != Get_default_##value()) {                                  \
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "field_static", \
                              type, "not_default_value", field_name,       \
                              DeployVariable::GetKwsName(),                \
                              DeployVariable::GetPodName());               \
    } else {                                                               \
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "field_static", \
                              type, "empty_value", field_name,             \
                              DeployVariable::GetKwsName(),                \
                              DeployVariable::GetPodName());               \
    }                                                                      \
    break;
    switch (from_field->cpp_type()) {
      CASE_DEAL_FIELD(CPPTYPE_INT32, Int32, tmp_int);
      CASE_DEAL_FIELD(CPPTYPE_UINT32, UInt32, tmp_int);
      CASE_DEAL_FIELD(CPPTYPE_INT64, Int64, tmp_int);
      CASE_DEAL_FIELD(CPPTYPE_UINT64, UInt64, tmp_int);
      CASE_DEAL_FIELD(CPPTYPE_DOUBLE, Double, tmp_double);
      CASE_DEAL_FIELD(CPPTYPE_FLOAT, Float, tmp_double);
      CASE_DEAL_FIELD(CPPTYPE_BOOL, Bool, tmp_int);
      CASE_DEAL_FIELD(CPPTYPE_ENUM, EnumValue, tmp_int);
      CASE_DEAL_FIELD(CPPTYPE_STRING, String, tmp_string);
      default:
        break;
    }
  }
}

void Kafka2HiveManager::KafkaProduce(kuaishou::ad::AdInstance* ad,
                                     std::string cluster_id,
                                     int64_t partition_id) {
  if (ad == nullptr) {
    return;
  }
  if (is_normal_working_) {
    Params params;
    params.cluster_id = cluster_id;
    params.partition_id = partition_id;
    return kafka2hive_ptr_->ProduceForNormal(ad, params);
  }
  auto type = kuaishou::ad::AdEnum::AdInstanceType_Name(ad->type());
  /*
    字段级 diff打点
  */
  if (Kafka2HiveKconf::enableFieldPerf()) {
    auto type = GetPbTypeStr(ad->type());
    auto* extension = GetExtensionField(ad);
    PerfLogFieldValue(extension, type);
  }
  int64_t time = base::GetTimestamp() / 1000;
  // 应该是未使用的
  if (producer_.get() != nullptr) {
    kuaishou::ad::AdInstance2Hive instance;
    instance.set_id(GetPrimaryKey(ad));
    instance.set_type(ad->type());
    instance.set_binlog_time(ad->binlog_time());
    instance.set_process_time(time);
    instance.set_uuid(ad->uuid());
    std::string str;
    auto size = ad->ByteSizeLong();
    str.resize(size);
    ad->SerializeToArray(&str.front(), size);
    std::string code_string;
    base::Base64Encode(str, &code_string);
    instance.mutable_ad_instance()->swap(code_string);
    std::string msg_str;
    size = instance.ByteSizeLong();
    msg_str.resize(size);
    instance.SerializeToArray(&msg_str.front(), size);
    ks::ad::index_message_proxy::KafkaProducer::SendMessageToKafkaWithoutSleep(
        msg_str, producer_.get());
    LOG_EVERY_N(INFO, 10000) << "type: " << type;
    LOG_EVERY_N(INFO, 10000) << " everything2hive debugstr: "
                            << instance.ShortDebugString();
  }
  auto map_it = producer_map_.find(type);
  // 各个表落 hive
  if (map_it != producer_map_.end()) {
    auto* msg = GetExtensionField(ad);
    AdInstanceUpdateTimeSet(ad);
    std::string msg_str;
    auto size = msg->ByteSizeLong();
    msg_str.resize(size);
    msg->SerializeToArray(&msg_str.front(), size);
    ks::ad::index_message_proxy::KafkaProducer::SendMessageToKafkaWithoutSleep(
        msg_str, map_it->second.get());
    LOG_EVERY_N(INFO, 10000)
        << "type: " << type << " debugstr: " << msg->ShortDebugString();
  }
  std::string product_name =
      getenv("KWS_PRODUCT_NAME") == NULL ? "" : getenv("KWS_PRODUCT_NAME");
  // creative 分场景落 hive
  if (product_name == "ad_index_message_proxy" &&
      ad->type() == kuaishou::ad::AdEnum::CREATIVE && scene_producer_ &&
      config_.biz_type().size() > 0) {
    AdIncTraceInfo ad_inc_trace_info;
    InstanceConvertToAdInc(ad, ad_inc_trace_info, cluster_id, partition_id);
    LOG_EVERY_N(INFO, 10000) << ad_inc_trace_info.ShortDebugString();
    std::string trace_log_str;
    auto size = ad_inc_trace_info.ByteSizeLong();
    trace_log_str.resize(size);
    ad_inc_trace_info.SerializeToArray(&trace_log_str.front(), size);
    ks::ad::index_message_proxy::KafkaProducer::SendMessageToKafkaWithoutSleep(
        trace_log_str, scene_producer_.get());
  }
  std::set<std::string> builder_product_name{"ad_index_adapter",
                                             "ad-index-builder",
                                             "index_builder_target"};
  // adapter&builder 分场景落 索引
  if (builder_product_name.count(product_name) > 0 &&
      ad->type() == kuaishou::ad::AdEnum::CREATIVE && creative_producer_) {
    auto* msg = GetExtensionField(ad);
    auto* creative = dynamic_cast<kuaishou::ad::tables::Creative*>(msg);
    kuaishou::ad::tables::Creative tmp_creative;
    tmp_creative.CopyFrom(*creative);
    tmp_creative.set_scene(config_.biz_type());
    LOG_EVERY_N(INFO, 10000) << tmp_creative.ShortDebugString();

    std::string trace_log_str;
    auto size = tmp_creative.ByteSizeLong();
    trace_log_str.resize(size);
    tmp_creative.SerializeToArray(&trace_log_str.front(), size);
    ks::ad::index_message_proxy::KafkaProducer::SendMessageToKafkaWithoutSleep(
        trace_log_str, creative_producer_.get());
  }

  // 全量增量索引落数据
  if (ad_inc_trace_producer_ && product_name == "ad_index_message_proxy") {
    AdIncTraceInfo ad_inc_trace_info;
    InstanceConvertToAdInc(ad, ad_inc_trace_info, cluster_id, partition_id);
    LOG_EVERY_N(INFO, 10000) << ad_inc_trace_info.ShortDebugString();
    std::string trace_log_str;
    auto size = ad_inc_trace_info.ByteSizeLong();
    trace_log_str.resize(size);
    ad_inc_trace_info.SerializeToArray(&trace_log_str.front(), size);
    ks::ad::index_message_proxy::KafkaProducer::SendMessageToKafkaWithoutSleep(
        trace_log_str, ad_inc_trace_producer_.get());
  }
}

void Kafka2HiveManager::KafkaFilterReason(
    kuaishou::ad::AdInstance* ad,
    ks::ad::index_message_proxy::FilterEnum filter_reason) {
  if (is_filter_working_) {
    Params params;
    params.filter_reason = filter_reason;
    return kafka2hive_ptr_->ProduceForFilter(ad, params);
  }

  PerfUtil::CountLogStash(
      1, "ad.index_message_proxy", "filter_reason", GetPbTypeStr(ad->type()),
      ks::ad::index_message_proxy::FilterEnum_Name(filter_reason),
      DeployVariable::GetKwsName(), DeployVariable::GetPodName());
  if (filter_message_producer_.get() == nullptr) {
    return;
  }

  if (!Kafka2HiveKconf::enableFilterReasonTrace()) {
    return;
  }

  AdFilterTraceInfo ad_filter_trace_info;
  int64_t primary_id = GetPrimaryKey(ad);
  int64_t account_id = GetValueByFieldNameV2(ad, "account_id");
  ad_filter_trace_info.set_id(primary_id);
  ad_filter_trace_info.set_account_id(account_id);
  ad_filter_trace_info.set_type(AdEnum_AdInstanceType_Name(ad->type()));
  ad_filter_trace_info.set_kws_name(DeployVariable::GetKwsName());
  ad_filter_trace_info.set_pod_name(DeployVariable::GetPodName());
  ad_filter_trace_info.set_process_time(ad->process_time());
  bool online_status = ks::engine_base::IsValidTable(ad);
  ad_filter_trace_info.set_online_status(online_status);
  ad_filter_trace_info.set_biz_type(config_.biz_type());
  ad_filter_trace_info.set_admit_time(GetTimestampInMs());
  ad_filter_trace_info.set_data_type(GetDebugDataTypeV2(ad)._to_string());
  ad_filter_trace_info.set_reason(filter_reason);
  LOG_EVERY_N(INFO, 10000) << ad_filter_trace_info.ShortDebugString();
  std::string trace_log_str;
  auto size = ad_filter_trace_info.ByteSizeLong();
  trace_log_str.resize(size);
  ad_filter_trace_info.SerializeToArray(&trace_log_str.front(), size);
  ks::ad::index_message_proxy::KafkaProducer::SendMessageToKafkaWithoutSleep(
      trace_log_str, filter_message_producer_.get());
}

void Kafka2HiveManager::KafkaProduceByMultiProducer(
    kuaishou::ad::AdInstance* ad, const std::string& scene_name,
    const std::string& cluster_id, int64_t partition_id) {
  if (is_normal_working_) {
    Params params;
    params.cluster_id = cluster_id;
    params.partition_id = partition_id;
    return kafka2hive_ptr_->ProduceForNormal(ad, params);
  }
  static std::string product_name =
      getenv("KWS_PRODUCT_NAME") == NULL ? "" : getenv("KWS_PRODUCT_NAME");
  if (scene_name.empty()) {
    return;
  }
  // creative 分场景落 hive
  if (product_name == "ad_index_message_proxy" &&
      ad->type() == kuaishou::ad::AdEnum::CREATIVE && scene_producer_) {
    AdIncTraceInfo ad_inc_trace_info;
    InstanceConvertToAdInc(ad, ad_inc_trace_info, cluster_id, partition_id, scene_name);
    LOG_EVERY_N(INFO, 10000) << ad_inc_trace_info.ShortDebugString();
    std::string trace_log_str;
    auto size = ad_inc_trace_info.ByteSizeLong();
    trace_log_str.resize(size);
    ad_inc_trace_info.SerializeToArray(&trace_log_str.front(), size);
    ks::ad::index_message_proxy::KafkaProducer::SendMessageToKafkaWithoutSleep(
        trace_log_str, scene_producer_.get());
  }
}

}  // namespace index_builder
}  // namespace ks
