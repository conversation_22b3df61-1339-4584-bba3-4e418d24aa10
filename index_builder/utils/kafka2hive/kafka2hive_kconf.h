#pragma once
#include <string>
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/index_builder/utils/kafka2hive/kafka2hive.pb.h"
namespace ks {
namespace index_builder {

using namespace ks::ad_base::kconf;  // NOLINT

class Kafka2HiveKconf {
 public:
  DEFINE_PROTOBUF_NODE_KCONF(Kafka2HiveConfig, ad.index_builder,
                             kafka2HiveConfig);
  DEFINE_STRING_KCONF_NODE(ad.index_builder, everthing2hiveKafkaTopic)

  // 是否落过滤原因开关
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableFilterReasonTrace, false)

  // 是否落字段统计
  DEFINE_BOOL_KCONF_NODE(ad.index_builder, enableFieldPerf, false)

  DEFINE_PROTOBUF_NODE_KCONF(Kafka2HiveConfigMap, ad.index_builder,
                             kafka2HiveConfigMap)
};

}  // namespace index_builder
}  // namespace ks
