
// 用于打印索引落 hive 数据的 debug 信息
// 用法：
// 1. 打印文件的数据
//    使用：./check_hive_message read_base64 input.txt
//    要求：输入文件为按行分割，各行为 AdInstance 二进制序列化后 Base64 编码的 ASCII 字符串
#include <gflags/gflags.h>
#include <fstream>
#include <iostream>
#include "base/encoding/base64.h"
#include "base/file/file_util.h"
#include "teams/ad/index_builder/utils/ad_instance_proto_helpers.h"

void sigint_handler(int s) {
  printf("Caught signal %d , exit \n", s);
  exit(1);
}

void Base64File2Instance(std::string file) {
  std::ifstream file_stream(file);
  std::string line;
  std::string out_put;
  kuaishou::ad::AdInstance instance;
  while (std::getline(file_stream, line)) {
    base::Base64Decode(line, &out_put);
    instance.ParseFromArray(out_put.c_str(), out_put.size());
    std::cout << instance.ShortDebugString() << std::endl;
  }
}
int main(int argc, char* argv[]) {
  if (std::string(argv[1]) == "read_base64") {
    Base64File2Instance(argv[2]);
  } else {
    std::cout
        << "Command not supported. Please read "
           "teams/ad/index_builder/utils/check_hive_message.cc comment before "
           "you proceed!"
        << std::endl;
  }
}
