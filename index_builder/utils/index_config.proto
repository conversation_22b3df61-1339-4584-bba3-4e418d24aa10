syntax = "proto3";

package ks.index_builder;
import "teams/ad/index_builder/utils/common.proto";
import "teams/ad/index_builder/admit_filters/admit_filter.proto";

// 简版索引，构建、加载统一配置
message IndexConfig {
  // 构建、加载通用
  string name = 1;         // 和路径名字保持一致，加载时赋值，配置中不需要填写
  AdapterScene scene = 2;  // 场景
  AdmitFilterEnum.Type admit_param = 3;  // admit 枚举
  string field_filter_param = 4;         // 字段裁剪枚举
  ShardOptions shard_options = 5;        // 分片逻辑

  // 构建专用配置
  repeated string tables = 100;  // 索引构建表白名单，加载侧不依赖此配置

  // 加载专用配置
  message BaseIndexParam {
    string p2p_alias = 1;
    string p2p_alias_candidate = 3;
  }
  BaseIndexParam base_index_param = 200;
}