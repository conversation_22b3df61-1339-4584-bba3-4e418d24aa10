#pragma once
#include <set>
#include <cstddef>
#include <cstdint>
#include <string>
#include <vector>
#include <algorithm>
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/ad_index/index/utils/public.h"
#include "ks/util/json.h"
namespace ks {
namespace index_builder {

bool ParseSchedule(const std::string &name,
                   const std::string *string_value,
                   std::vector<int64_t> &budget_schedule_list) {  // NOLINT
  if (!string_value) {
    LOG(WARNING) << "schedule field format error, not string, name:" << name;
    return false;
  }

  budget_schedule_list.clear();
  if (string_value->empty()) {
    return true;
  }

  // budget_schedule 字段格式：[]
  base::Json json(StringToJson(*string_value));
  if (!json.IsArray()) {
    LOG_EVERY_N(ERROR, 100)
      << "budget_schedule field format error. string_value=" << *string_value << ", name:" << name;
    return false;
  }

  if (json.size() == 0) {
    return true;
  }

  if (json.size() != 7) {
    LOG_EVERY_N(ERROR, 100)
      << "budget_schedule field format error. string_value=" << *string_value << ", name:" << name;
    return false;
  }

  for (auto item : json.array()) {
    int64_t budget = 0;
    if (!item->IntValue(&budget) || budget < 0) {
      LOG_EVERY_N(ERROR, 100)
        << "budget_schedule field format error. string_value=" << *string_value << ", name:" << name;
      return false;
    }
    budget_schedule_list.push_back(budget);
  }

  // [周- ... 周日] --> [周日 周一 ... 周六]
  if (!budget_schedule_list.empty()) {
    std::rotate(
        budget_schedule_list.rbegin(), budget_schedule_list.rbegin() + 1, budget_schedule_list.rend());
  }

  return budget_schedule_list.size() == 7;
}

/* unit parser area */

class DiverseDataParser : public PbParser<kuaishou::ad::tables::Unit> {
 public:
  virtual ~DiverseDataParser() {}
  bool Parse(kuaishou::ad::tables::Unit* unit) override {
    std::string string_value = unit->diverse_data();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.objects()) {
      if (item.first == "appPackageName") {
        unit->mutable_parse_field()->set_city_app_package_name_id(convertor(item.second->StringValue()));
      }
    }
    return true;
  }
};

class UnitResourceIdsParser : public PbParser<kuaishou::ad::tables::Unit> {
 public:
  virtual ~UnitResourceIdsParser() {}
  bool Parse(kuaishou::ad::tables::Unit* unit) override {
    std::string string_value = unit->resource_ids();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t id = item->IntValue(-1);
      if (id > 0) {
        unit->add_resource_ids_vec(id);
      }
    }
    return true;
  }
};

class UnitBudgetScheduleParser : public PbParser<kuaishou::ad::tables::Unit> {
 public:
  virtual ~UnitBudgetScheduleParser() {}
  bool Parse(kuaishou::ad::tables::Unit* unit) override {
    static const std::string& kName{"unit"};
    std::vector<int64_t> budget_schedule_list;
    const std::string string_value = unit->budget_schedule();
    bool ret = ParseSchedule(kName, &string_value, budget_schedule_list);
    if (!ret) {
      return false;
    }
    auto* mutable_budget_schedule_list_vec =
        unit->mutable_budget_schedule_list_vec();
    for (const auto& value : budget_schedule_list) {
      mutable_budget_schedule_list_vec->Add(value);
    }
    return true;
  }
};

class ScheduleParser : public PbParser<kuaishou::ad::tables::Unit> {
 public:
  virtual ~ScheduleParser() {}
  bool Parse(kuaishou::ad::tables::Unit* unit) override {
    const std::string string_value = unit->schedule();
    auto* schedule_list_vec = unit->mutable_schedule_list_vec();
    Json json(StringToJson(string_value));
    if (!json.IsArray()) {
      LOG_EVERY_N(ERROR, 10000)
          << "schedule field format error, not valid json string. string_value="
          << string_value;
      return false;
    }
    if (json.size() == 0) {
      return true;
    }
    if (json.size() != 7) {
      LOG_EVERY_N(ERROR, 10000)
          << "schedule field format error, not valid json string. string_value="
          << string_value;
      return false;
    }
    uint32 day = 0;
    for (auto iter = json.array_begin(); iter != json.array_end();
         ++iter, ++day) {
      auto inner_json = *iter;
      if (!inner_json->IsArray()) {
        LOG_EVERY_N(ERROR, 10000) << "schedule field format error, not valid json string. string_value="
                   << string_value;
        return false;
      }
      for (auto inner_iter = inner_json->array_begin(); inner_iter != inner_json->array_end(); ++inner_iter) {
        int64 pos = 0;
        if ((*inner_iter)->IntValue(&pos)) {
          if (pos < 0 || pos > 23) {
            LOG_EVERY_N(ERROR, 100) << "schedule field format error, not valid json string. string_value="
                       << string_value;
            return false;
          }

          auto position = day * 24 + pos;
          schedule_list_vec->Add(static_cast<int16_t>(position));
          if (position > 65536) {
            LOG(FATAL) << "unexpect schedule " << *(schedule_list_vec->rbegin());
          }
        } else {
          LOG_EVERY_N(ERROR, 100) << "schedule field format error, not valid json string. string_value="
                     << string_value;
          return false;
        }
      }
    }
    std::sort(schedule_list_vec->begin(), schedule_list_vec->end());
    return true;
  }
};

/*
  default 从
*/
class UnitDefaultParser : public PbParser<kuaishou::ad::tables::Unit> {
 public:
  virtual ~UnitDefaultParser() {}
  static void ParseStringField(kuaishou::ad::tables::Unit* unit) {
    if (!unit) {
      return;
    }
    bool has_invalid_schedule = false;
    do {
      base::Json json(base::StringToJson(unit->schedule()));
      if (!json.IsArray() || json.size() != 7) {
        break;
      }
      std::vector<int32_t> week_hour_tmp;
      for (auto iter = json.array_begin(); iter != json.array_end(); ++iter) {
        auto inner_json = *iter;
        if (!inner_json->IsArray()) {
          has_invalid_schedule = true;
          break;
        }
        int32_t hour_bit = 0x00;
        for (auto inner_iter = inner_json->array_begin();
             inner_iter != inner_json->array_end(); ++inner_iter) {
          int64 pos = 0;
          if ((*inner_iter)->IntValue(&pos)) {
            if (pos < 0 || pos > 23) {
              has_invalid_schedule = true;
              break;
            }
            int32_t tmp = 0x01;
            tmp <<= pos;
            hour_bit = hour_bit | tmp;
          } else {
            has_invalid_schedule = true;
            break;
          }
        }
        if (has_invalid_schedule) {
          break;
        }
        week_hour_tmp.push_back(hour_bit);
      }
      if (has_invalid_schedule) {
        break;
      }
      std::rotate(week_hour_tmp.rbegin(), week_hour_tmp.rbegin() + 1,
                  week_hour_tmp.rend());
      for (int i = 0; i < week_hour_tmp.size(); ++i) {
        unit->mutable_parse_field()->add_hours(week_hour_tmp[i]);
      }
    } while (false);
    if (has_invalid_schedule ||
        unit->mutable_parse_field()->hours_size() != 7) {
      unit->mutable_parse_field()->clear_hours();
    } else {
      // to do(liuwenlong03): 留黑白名单
      // unit->clear_schedule();
    }
    ParseResourceIds(unit);
    ParseGiftData(unit);
    ParseUri(unit);
  }
  static void ParseResourceIds(kuaishou::ad::tables::Unit* unit) {
    if (nullptr == unit) {
      return;
    }
    base::Json json(base::StringToJson(unit->resource_ids()));
    if (!json.IsArray()) {
      return;
    }
    for (auto iter = json.array_begin(); iter != json.array_end(); ++iter) {
      int64_t id = 0;
      if ((*iter)->IntValue(&id) && id > 0) {
        unit->mutable_parse_field()->add_resource_ids(id);
      }
    }
  }
  static void ParseGiftData(kuaishou::ad::tables::Unit* unit) {
    if (nullptr == unit) {
      return;
    }
    base::Json json(base::StringToJson(unit->gift_data()));
    int64 target_action_type = json.GetInt("targetActionType", 0);
    unit->mutable_parse_field()->set_target_action_type(target_action_type);
  }
  static void ParseUri(kuaishou::ad::tables::Unit* unit) {
    if (nullptr == unit) {
      return;
    }
    auto uri = unit->uri();
    static std::set<std::string> host_set = {"https://moli.kuaishou.com", "https://chenzhongkj.com"};
    bool match = false;
    for (const auto& host : host_set) {
      if (uri.find(host) != uri.npos) {
        match = true;
        break;
      }
    }
    if (match) {
      int64_t page_id = 0;
      if (GetPageIdFromURI(uri, &page_id) && page_id > 0) {
        unit->set_is_site(true);
        if (unit->site_id() == 0) {
          unit->set_site_id(page_id);
        }
      }
    }
    return;
  }

  bool Parse(kuaishou::ad::tables::Unit* unit) override {
    ParseStringField(unit);
    // 业务端字段冲突处理
    unit->mutable_unit_small_shop_merchant_support_info()->set_item_type(
        unit->unit_small_shop_merchant_support_info().merchant_item_type());
    unit->mutable_unit_small_shop_merchant_support_info()->set_category_id(
        unit->unit_small_shop_merchant_support_info().merchant_category_id());
    /*
      横级联
      全量无问题
      增量成功则 merchant_unit_id 有值
    */
    if (unit->unit_small_shop_merchant_support_info().merchant_unit_id() > 0) {
      unit->mutable_unit_small_shop_merchant_support_info()->set_unit_id(
          unit->id());
    }
#define ParserExecution(PARSER) \
  PARSER PARSER##_TEMP;         \
  PARSER##_TEMP.Parse(unit);
  ParserExecution(UnitResourceIdsParser)
  ParserExecution(UnitBudgetScheduleParser)
  ParserExecution(ScheduleParser)
  ParserExecution(DiverseDataParser)
#undef ParserExecution
    return true;
  }
};
PARSER_REGISTER_WITH_PRIORITY(UnitDefaultParser, google::protobuf::Message,
                              kuaishou::ad::tables::Unit, 101)
}  // namespace index_builder
}  // namespace ks
