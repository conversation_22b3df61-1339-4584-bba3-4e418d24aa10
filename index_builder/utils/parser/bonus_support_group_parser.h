#pragma once
#include <string>
#include <vector>
#include "serving_base/jansson/json.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "base/hash_function/city.h"

namespace ks {
namespace index_builder {

class BonusSupportGroupParser : public PbParser<kuaishou::ad::tables::BonusSupportGroup> {
 public:
  virtual ~BonusSupportGroupParser() {}
  bool Parse(kuaishou::ad::tables::BonusSupportGroup* bonus_support_group) override {
    LOG_FIRST_N(INFO, 100) << "BonusSupportGroup ShortDebugString before parse: "
        << bonus_support_group->ShortDebugString();
    GetGroupBonusAccounts(bonus_support_group);
    GetProductIdList(bonus_support_group);
    GetOcpxActionTypeList(bonus_support_group);
    GetCrowdPackIdList(bonus_support_group);
    GetPhotoIdList(bonus_support_group);
    LOG_FIRST_N(INFO, 100) << "BonusSupportGroup ShortDebugString after parse: "
        << bonus_support_group->ShortDebugString();
    return true;
  }

  bool GetGroupBonusAccounts(kuaishou::ad::tables::BonusSupportGroup* bonus_support_group) {
    auto* extend_fields = bonus_support_group->mutable_extend_fields();
    const std::string& string_value = bonus_support_group->group_bonus_accounts();
    if (string_value.empty()) return true;
    base::Json json(base::StringToJson(string_value));
    if (!json.IsArray()) {
      LOG_EVERY_N(WARNING, 1000) << "Parse Error :" << string_value;
      return true;
    }
    for (auto* account : json.array()) {
      int64_t acc_id = 0;
      account->IntValue(&acc_id);
      extend_fields->add_group_bonus_accounts(acc_id);
    }
    bonus_support_group->clear_group_bonus_accounts();
    return true;
  }

  bool GetProductIdList(kuaishou::ad::tables::BonusSupportGroup* bonus_support_group) {
    auto* extend_fields = bonus_support_group->mutable_extend_fields();
    const std::string& string_value = bonus_support_group->product_name_list();
    if (string_value.empty()) return true;
    base::Json json(base::StringToJson(string_value));
    if (!json.IsArray()) {
      LOG_EVERY_N(WARNING, 1000) << "Parse Error :" << string_value;
      return true;
    }
    for (auto* val : json.array()) {
      std::string name = "";
      val->StringValue(&name);
      int64_t id = base::CityHash64(name.data(), name.size());
      extend_fields->add_product_id_list(id);
    }
    bonus_support_group->clear_product_name_list();
    return true;
  }

  bool GetOcpxActionTypeList(kuaishou::ad::tables::BonusSupportGroup* bonus_support_group) {
    auto* extend_fields = bonus_support_group->mutable_extend_fields();
    const std::string& string_value = bonus_support_group->ocpx_action_type_list();
    if (string_value.empty()) return true;
    base::Json json(base::StringToJson(string_value));
    if (!json.IsArray()) {
      LOG_EVERY_N(WARNING, 1000) << "Parse Error :" << string_value;
      return true;
    }
    for (auto* val : json.array()) {
      int64_t id = 0;
      val->IntValue(&id);
      extend_fields->add_ocpx_action_type_list(id);
    }
    bonus_support_group->clear_ocpx_action_type_list();
    return true;
  }

  bool GetCrowdPackIdList(kuaishou::ad::tables::BonusSupportGroup* bonus_support_group) {
    auto* extend_fields = bonus_support_group->mutable_extend_fields();
    const std::string& string_value = bonus_support_group->crowd_pack_id_list();
    if (string_value.empty()) return true;
    base::Json json(base::StringToJson(string_value));
    if (!json.IsArray()) {
      LOG_EVERY_N(WARNING, 1000) << "Parse Error :" << string_value;
      return true;
    }
    for (auto* val : json.array()) {
      int64_t id = 0;
      val->IntValue(&id);
      extend_fields->add_crowd_pack_id_list(id);
    }
    bonus_support_group->clear_crowd_pack_id_list();
    return true;
  }

  bool GetPhotoIdList(kuaishou::ad::tables::BonusSupportGroup* bonus_support_group) {
    auto* extend_fields = bonus_support_group->mutable_extend_fields();
    const std::string& string_value = bonus_support_group->photo_id_list();
    if (string_value.empty()) return true;
    base::Json json(base::StringToJson(string_value));
    if (!json.IsArray()) {
      LOG_EVERY_N(WARNING, 1000) << "Parse Error :" << string_value;
      return true;
    }
    for (auto* val : json.array()) {
      int64_t id = 0;
      val->IntValue(&id);
      extend_fields->add_photo_id_list(id);
    }
    bonus_support_group->clear_photo_id_list();
    return true;
  }
};

PARSER_REGISTER(BonusSupportGroupParser, google::protobuf::Message,
                kuaishou::ad::tables::BonusSupportGroup)
}  // namespace index_builder
}  // namespace ks
