#pragma once

#include <algorithm>
#include <string>
#include <utility>
#include <vector>
#include "base/hash_function/city.h"
#include "ks/util/json.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
namespace ks {
namespace index_builder {

class AdMagicSitePageDasParser : public PbParser<kuaishou::ad::tables::AdMagicSitePageDas> {
 public:
  virtual ~AdMagicSitePageDasParser() {}
  bool Parse(kuaishou::ad::tables::AdMagicSitePageDas* magic_site) override {
    base::Json page_component_js(
        base::StringToJson(magic_site->page_component()));
    static const std::pair<int64_t, int64_t> small_game_old{3, 1};
    static const std::pair<int64_t, int64_t> small_game_new{34, 0};
    static const std::pair<int64_t, int64_t> small_game_oppo{3, 2};
    static const std::pair<int64_t, int64_t> small_game_qq{3, 3};
    static const std::pair<int64_t, int64_t> image_quick_app{0, 3};
    static const std::pair<int64_t, int64_t> download_quick_app{7, 1};
    static const std::pair<int64_t, int64_t> drag_image_quick_app{10, 3};
    uint64_t landing_page_component{0};
    if (page_component_js.IsArray()) {
      for (auto iter = page_component_js.array_begin();
           iter != page_component_js.array_end(); ++iter) {
        int type = (*iter)->GetInt("type", -1);
        int sub_type = (*iter)->GetInt("subType", -1);
        if (type == -1 || sub_type == -1) {
          continue;
        }
        magic_site->mutable_extend_fields()->add_type_vec(type);
        magic_site->mutable_extend_fields()->add_sub_type_vec(sub_type);
        if (std::pair<int64_t, int64_t>(type, sub_type) == small_game_old) {
          landing_page_component |= kuaishou::ad::AdEnum::SMALL_GAME_OLD;
        } else if (std::pair<int64_t, int64_t>(type, sub_type) ==
                   small_game_new) {
          landing_page_component |= kuaishou::ad::AdEnum::SMALL_GAME_NEW;
        } else if (std::pair<int64_t, int64_t>(type, sub_type) ==
                   small_game_oppo) {
          landing_page_component |= kuaishou::ad::AdEnum::SMALL_GAME_OPPO_REAL;
        } else if (std::pair<int64_t, int64_t>(type, sub_type) == small_game_qq) {
          landing_page_component |= kuaishou::ad::AdEnum::SMALL_GAME_QQ;
        } else if (std::pair<int64_t, int64_t>(type, sub_type) == image_quick_app) {
          landing_page_component |= kuaishou::ad::AdEnum::IMAGE_QUICK_APP;
        } else if (std::pair<int64_t, int64_t>(type, sub_type) == download_quick_app) {
          landing_page_component |= kuaishou::ad::AdEnum::DOWNLOAD_QUICK_APP;
        } else if (std::pair<int64_t, int64_t>(type, sub_type) == drag_image_quick_app) {
          landing_page_component |= kuaishou::ad::AdEnum::DRAG_IMAGE_QUICK_APP;
        }
      }
    }
    magic_site->mutable_extend_fields()->set_landing_page_component(landing_page_component);
    ParseAdlpDataReportInfo(magic_site);
    return true;
  }

  void ParseAdlpDataReportInfo(kuaishou::ad::tables::AdMagicSitePageDas* magic_site) {
    base::Json adlp_data_report_info_json(base::StringToJson(magic_site->adlp_data_report_info()));
    if (adlp_data_report_info_json.IsArray()) {
      for (auto item : adlp_data_report_info_json.array()) {
        int64_t val = 0;
        if (!item->IntValue(&val)) {
          continue;
        }
        magic_site->mutable_extend_fields()->add_adlp_data_report_info_vec(val);
      }
    } else {
      LOG_EVERY_N(WARNING, 10000) << "adlp_data_report_info is not array json!";
    }
  }
};
PARSER_REGISTER(AdMagicSitePageDasParser, google::protobuf::Message,
                kuaishou::ad::tables::AdMagicSitePageDas)
}  // namespace index_builder
}  // namespace ks
