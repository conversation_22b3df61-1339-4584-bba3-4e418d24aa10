#pragma once
#include <string>
#include <vector>
#include <algorithm>

#include "absl/base/call_once.h"
#include "base/hash_function/city.h"
#include "base/strings/string_split.h"
#include "perfutil/perfutil.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/kconf.h"
namespace ks {
namespace index_builder {
using ks::infra::PerfUtil;

static bool enableMatrixClientInfo = false;
absl::once_flag rule_parser_init_once;

// used for creative config init
void RuleParserInitOnce() {
  enableMatrixClientInfo = AdKconfUtil::enableMatrixClientInfo();
}

class MatrixStyleMaterialRuleParser
    : public PbParser<kuaishou::ad::tables::AdMatrixStyleMaterialSceneRule> {
 public:
  virtual ~MatrixStyleMaterialRuleParser() {}
  bool Parse(kuaishou::ad::tables::AdMatrixStyleMaterialSceneRule* style) override {
    if (!style) {
      return false;
    }
    absl::call_once(rule_parser_init_once, RuleParserInitOnce);
    base::Json white_list(base::StringToJson(style->white_list()));
    auto* block_rules = style->mutable_parse_field()->mutable_block_rules();
    do {
      if (!white_list.IsArray() || white_list.size() == 0) {
        break;
      }

      for (auto* item : white_list.array()) {
        if (!item->IsObject()) {
          continue;
        }

        auto* one_data = block_rules->add_datas();
        one_data->set_bind_type(item->GetInt("bindType", 0));
        auto* white_items = item->Get("whiteList");
        if (white_items && white_items->IsArray()) {
          for (auto* white_item : white_items->array()) {
            std::string s;
            if (white_item->StringValue(&s)) {
              one_data->add_white_list(s);
            }
          }
        }
        auto* black_items = item->Get("blackList");
        if (black_items && black_items->IsArray()) {
          for (auto* black_item : black_items->array()) {
            std::string s;
            if (black_item->StringValue(&s)) {
              one_data->add_black_list(s);
            }
          }
        }
      }
    } while (false);

    base::Json client_rules(base::StringToJson(style->client_rules()));
    auto* client_rules_pb = style->mutable_parse_field()->mutable_client_rules();
    do {
      if (!client_rules.IsArray() || client_rules.size() == 0) {
        break;
      }
      for (auto* item : client_rules.array()) {
        if (!item->IsObject()) {
          continue;
        }

        int32_t product = item->GetInt("product", 0);
        if (product == 28) {
          client_rules_pb->set_ad_client(item->GetString("version", ""));
        } else if (product == 29) {
          client_rules_pb->set_content_client(item->GetString("version", ""));
        }
        if (enableMatrixClientInfo) {
          auto* one_client_info = client_rules_pb->add_client_infos();
          one_client_info->set_photo_page(item->GetString("photoPage", ""));
          one_client_info->set_product(product);
          one_client_info->set_browse_type(item->GetInt("browseType", 0));
          one_client_info->set_platform(item->GetString("platform", ""));
          one_client_info->set_app_ver(item->GetString("appVer", ""));
          auto source_types = item->Get("adSourceType");
          auto campaign_types = item->Get("campaignType");
          if (source_types && source_types->IsArray()) {
            for (auto it = source_types->array_begin(); it != source_types->array_end(); ++it) {
              int64_t ad_source_type = 0;
              if ((*it)->IntValue(&ad_source_type)) {
                one_client_info->add_ad_source_type(ad_source_type);
              }
            }
          }
          if (campaign_types && campaign_types->IsArray()) {
            for (auto it = campaign_types->array_begin(); it != campaign_types->array_end(); ++it) {
              int64_t campaign_type = 0;
              if ((*it)->IntValue(&campaign_type)) {
                one_client_info->add_campaign_type(campaign_type);
              }
            }
          }
        }
      }
    } while (false);

    auto* componentized_style_rules = style->mutable_parse_field()->mutable_componentized_style_rules();
    if (!style->universe().empty()) {
      auto status = ::google::protobuf::util::JsonStringToMessage(style->universe(),
                                                                  componentized_style_rules);
      if (status.ok()) {
         PerfUtil::CountLogStash(1, "ad.index_builder", "parse_componentized_style_rule_info", "success");
      } else {
         PerfUtil::CountLogStash(1, "ad.index_builder", "parse_componentized_style_rule_info", "fail",
                                 absl::StrCat(status.error_code()));
      }
    }

    LOG_EVERY_N(INFO, 1000) << "parse_field:" << style->parse_field().ShortDebugString();
    return true;
  }
};

PARSER_REGISTER(MatrixStyleMaterialRuleParser, google::protobuf::Message,
                kuaishou::ad::tables::AdMatrixStyleMaterialSceneRule)
}  // namespace index_builder
}  // namespace ks
