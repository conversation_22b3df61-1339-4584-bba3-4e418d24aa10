#pragma once
#include <set>
#include <string>
#include <vector>
#include <map>
#include <algorithm>
#include <unordered_map>

#include "base/encoding/base64.h"
#include "base/hash_function/city.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"
#include "teams/ad/index_builder/utils/parser/photo_parser.h"

namespace ks {
namespace index_builder {

static void ParseExtendFields(kuaishou::ad::tables::WTPhoto* wt_photo) {
  if (wt_photo == nullptr) { return; }
}

static void ParseStringFields(kuaishou::ad::tables::WTPhoto* wt_photo) {
  if (wt_photo == nullptr) { return; }

  auto* parse_fields =  wt_photo->mutable_parse_fields();

  // 解析 swing_photo_lst;
  SIMPLE_PARSE_INT64_USE_SPLLIT(wt_photo, swing_photo_lst, ",");
  // 解析 ad_spu_ids
  SIMPLE_PARSE_INT64_USE_SPLLIT(wt_photo, ad_spu_ids, ",");
  // 解析 ad_face_ids
  SIMPLE_PARSE_INT64_USE_SPLLIT(wt_photo, ad_face_ids, ",");
  // 解析 ad_items
  SIMPLE_PARSE_INT64_USE_SPLLIT(wt_photo, ad_items, ",");
  // 解析 similar_photo_list
  SIMPLE_PARSE_INT64_USE_SPLLIT(wt_photo, similar_photo_list, ",");
  // 解析 exp1_sim_photo_list_str
  SIMPLE_PARSE_INT64_USE_SPLLIT(wt_photo, exp1_sim_photo_list_str, ",");
  // 解析 exp2_sim_photo_list
  SIMPLE_PARSE_INT64_USE_SPLLIT(wt_photo, exp2_sim_photo_list, ",");
  // 解析 dup_photo_list
  SIMPLE_PARSE_INT64_USE_SPLLIT(wt_photo, dup_photo_list, ",");

  // 解析 leverage_rl
  SIMPLE_PARSE_FLOAT_USE_SPLLIT(wt_photo, leverage_rl, ",");
  // 解析 replace_photo_list
  SIMPLE_PARSE_INT64_USE_SPLLIT(wt_photo, replace_photo_list, ",");

  // 解析 similar_photo
  auto parse_similar_photo = [&]() {
    const auto& similar_photo = wt_photo->similar_photos();
    if (similar_photo.empty()) { return; }
    std::vector<absl::string_view> tokens = absl::StrSplit(similar_photo, ",");
    if (tokens.size() != 2) {
      LOG(ERROR) << "split parse similar_photo err:" << similar_photo
                 << ", wt_photo_id:" << wt_photo->id();
      return;
    }
    int64 photo_id = 0;
    if (!absl::SimpleAtoi(tokens[0], &photo_id) || photo_id == 0) {
      LOG_EVERY_N(ERROR, 10000) << "SimpleAtoi parse photo_id err:" << tokens[0]
                               << ", src:" << similar_photo
                               << ", wt_photo_id:" << wt_photo->id();
      return;
    }
    int64 score = 0;
    if (!absl::SimpleAtoi(tokens[1], &score)) {
      LOG_EVERY_N(ERROR, 10000) << "SimpleAtoi parse score err:" << tokens[1]
                               << ", src:" << similar_photo
                               << ", wt_photo_id:" << wt_photo->id();
      return;
    }
    parse_fields->mutable_similar_photo()->set_similar_photo(photo_id);
    parse_fields->mutable_similar_photo()->set_similar_score(score);
    LOG_FIRST_N(INFO, 100) << parse_fields->ShortDebugString();
  };
  parse_similar_photo();
  wt_photo->clear_similar_photos();


  SIMPLE_PARSE_RF_LABEL_DATA(wt_photo, photo_quality_score);
  SIMPLE_PARSE_RF_LABEL_DATA(wt_photo, photo_quality_score_exp);
  SIMPLE_PARSE_RF_LABEL_DATA(wt_photo, mmu_posterior_score);
  SIMPLE_PARSE_RF_LABEL_DATA(wt_photo, bad_photo_tax_coef);

  const auto& ad_photo_hetu_category = wt_photo->ad_photo_hetu_category();
  if (!ad_photo_hetu_category.empty()) {
    kuaishou::ad::tables::LabelData label_data;
    std::string decode_string;
    if (base::Base64Decode(ad_photo_hetu_category, &decode_string)) {
      label_data.ParseFromString(decode_string);
      if (label_data.r_i64_size() == 3) {
        parse_fields->mutable_hetu_category_info()->set_first_level_category_id(label_data.r_i64(0));
        parse_fields->mutable_hetu_category_info()->set_second_level_category_id(label_data.r_i64(1));
        parse_fields->mutable_hetu_category_info()->set_third_level_category_id(label_data.r_i64(2));
      }
    }
    wt_photo->clear_ad_photo_hetu_category();
    LOG_FIRST_N(INFO, 100) << parse_fields->ShortDebugString();
  }

  const auto& coar_list_info = wt_photo->coar_list_info();
  if (!coar_list_info.empty()) {
    base::Json json(base::StringToJson(coar_list_info));
    if (!json.IsArray() || json.size() != 2) {
      LOG(ERROR) << "coar_list_info err:" << coar_list_info;
      return;
    }
    parse_fields->set_product_cluster_id_v1(json.GetInt(0, 0));
    parse_fields->set_product_cluster_id_v2(json.GetInt(1, 0));
    wt_photo->clear_coar_list_info();
  }
  // 本地生活
  // 解析 local_life_goods_list
  SIMPLE_PARSE_INT64_USE_SPLLIT(wt_photo, local_life_goods_list, ",");
  // 解析 local_life_poi_list
  SIMPLE_PARSE_INT64_USE_SPLLIT(wt_photo, local_life_poi_list, ",");
  // 解析直播间本地经纬度
  const std::string& dis_value = wt_photo->poi_point_list();
  if (!dis_value.empty()) {
    Json json(StringToJson(dis_value));
    // distance 格式："[{\"lat\":26.038841,\"lnt\":119.27562,\"dis\":6}]"
    int32_t count = 0;
    std::set<int64_t> distrinct_term;
    for (auto item : json.array()) {
      double lon = item->GetFloat("lnt", -1000);
      double lat = item->GetFloat("lat", -1000);
      // 最高 15km
      double dis = std::min(15000.0, item->GetFloat("dis", 0.0));
      std::vector<std::string> terms;
      if (dis <= 0 || !ks::ad_server::GetFastCoveringTerms(lat, lon, dis, &terms)) {
        LOG_EVERY_N(WARNING, 10000) << "Invalid poi_point_list: " << dis_value;
        continue;
      }
      for (const std::string& term : terms) {
        if (count++ > AdKconfUtil::wtLivePoiPointLimit()) {
          break;
        }
        uint64_t uint64_sign = base::CityHash64(term.data(), term.size());
        int64_t tmp = *(reinterpret_cast<int64_t*>(&uint64_sign));
        if (distrinct_term.count(tmp) > 0) {
          continue;
        }
        distrinct_term.insert(tmp);
        parse_fields->add_distance_s2_terms(tmp);
      }
      parse_fields->add_lat(lat);
      parse_fields->add_lon(lon);
      parse_fields->add_dis(dis);
    }
  }
  wt_photo->clear_local_life_goods_list();
  wt_photo->clear_local_life_poi_list();
  wt_photo->clear_poi_point_list();
}

static void ParsePhotoStatusProto(kuaishou::ad::tables::PhotoStatus* proto) {
  if (proto == nullptr) { return; }
  static AdDspPhotoParser photo_parser;
  photo_parser.Parse(proto);
  return;
}

class WTPhotoParser : public PbParser<kuaishou::ad::tables::WTPhoto> {
 public:
  virtual ~WTPhotoParser() {}
  bool Parse(kuaishou::ad::tables::WTPhoto* wt_photo) override {
    PARSE_BINARY_STRING_TO_MESSAGE(wt_photo, photo_status);
    ParsePhotoStatusProto(wt_photo->mutable_photo_status());
    ParseExtendFields(wt_photo);
    ParseStringFields(wt_photo);
    wt_photo->set_final_status(wt_photo->photo_status().final_status());
    return true;
  }
};

PARSER_REGISTER(WTPhotoParser, google::protobuf::Message, kuaishou::ad::tables::WTPhoto);

}  // namespace index_builder
}  // namespace ks
