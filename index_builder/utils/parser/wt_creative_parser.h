#pragma once
#include <string>
#include <vector>
#include <map>
#include <algorithm>
#include <unordered_map>

#include "absl/strings/str_split.h"
#include "base/encoding/base64.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"
#include "teams/ad/index_builder/utils/parser/creative_parser.h"

namespace ks {
namespace index_builder {

static void ParseExtendFields(kuaishou::ad::tables::WTCreative* proto) {
  if (proto == nullptr) { return; }
}
static void ParseStringFields(kuaishou::ad::tables::WTCreative* proto) {
  if (proto == nullptr) { return; }
  auto* parse_fields = proto->mutable_parse_fields();

  SIMPLE_PARSE_RI_LABEL_DATA(proto, dup_creative_id);

  SIMPLE_PARSE_RI_LABEL_DATA(proto, pla_creative_id);

  const auto& ecom_statistics_cost = proto->ecom_statistics_cost();
  if (!ecom_statistics_cost.empty()) {
    kuaishou::ad::tables::LabelData label_data;
    std::string decode_string;
    if (base::Base64Decode(ecom_statistics_cost, &decode_string)) {
      label_data.ParseFromString(decode_string);
      if (label_data.r_i64_size() > 0) {
        parse_fields->set_statistics_merchant_order_num(label_data.r_i64(0));
      }
      if (label_data.r_f_size() > 0) {
        parse_fields->set_ecom_statistics_cost(label_data.r_f(0));
      }
    }
    proto->clear_ecom_statistics_cost();
    LOG_FIRST_N(INFO, 100) << parse_fields->ShortDebugString();
  }
  do {
    const auto& new_creative_tag_key = proto->new_creative_tag_key();
    const auto& new_creative_tag_value = proto->new_creative_tag_value();
    if (new_creative_tag_key.empty() || new_creative_tag_value.empty()) break;
    base::Json key_json(StringToJson(new_creative_tag_key));
    base::Json value_json(StringToJson(new_creative_tag_value));
    if (!key_json.IsArray() || !value_json.IsArray()) break;
    if (key_json.size() != value_json.size()) break;
    int size = key_json.size();
    for (int i = 0; i < size; i++) {
      int key = key_json.GetInt(i, 0);
      int value = value_json.GetInt(i, 0);
      if (key == 0 || value == 0) continue;
      parse_fields->add_new_creative_tag_key(key);
      parse_fields->add_new_creative_tag_value(value);
    }
    proto->clear_new_creative_tag_key();
    proto->clear_new_creative_tag_value();
    LOG_FIRST_N(INFO, 100) << parse_fields->ShortDebugString();
  } while (0);
}

static void ParseCreativeProto(kuaishou::ad::tables::Creative* proto) {
  if (proto == nullptr) { return; }
  static CreativeDefaultParser proto_parser;
  proto_parser.Parse(proto);
  return;
}

class WTCreativeParser : public PbParser<kuaishou::ad::tables::WTCreative> {
 public:
  virtual ~WTCreativeParser() {}
  bool Parse(kuaishou::ad::tables::WTCreative* proto) override {
    // PARSE_BINARY_STRING_TO_MESSAGE(proto, creative);
    // ParseCreativeProto(proto->mutable_creative());
    if (proto) {
      proto->clear_creative_binary();
    }
    ParseExtendFields(proto);
    ParseStringFields(proto);
    return true;
  }
};

PARSER_REGISTER(WTCreativeParser, google::protobuf::Message, kuaishou::ad::tables::WTCreative);

}  // namespace index_builder
}  // namespace ks
