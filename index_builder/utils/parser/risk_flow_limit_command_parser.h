#pragma once
#include <cstdint>
#include <set>
#include <string>
#include <vector>
#include <unordered_set>
#include "perfutil/perfutil.h"
#include "serving_base/jansson/json.h"
#include "absl/strings/numbers.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_index/index/utils/public.h"
#include "serving_base/region/region_dict.h"

namespace ks {
namespace index_builder {

class AdRiskFlowLimitCommandParser : public PbParser<kuaishou::ad::tables::AdRiskFlowLimitCommand> {
 public:
  ~AdRiskFlowLimitCommandParser() override {}
  void ParseFlowLimitField(kuaishou::ad::tables::AdRiskFlowLimitCommand *risk_limit) {
    auto array_json2_field = [](const std::string &str, google::protobuf::RepeatedField<int32_t> *field) {
      Json json(StringToJson(str));
      if (!json.IsArray()) {
        LOG(ERROR) << "format error, not valid json string. string_value=" << str;
        return;
      }
      if (json.size() == 0) {
        return;
      }
      for (auto iter = json.array_begin(); iter != json.array_end(); ++iter) {
        auto &inner_json = *iter;
        if (!inner_json->IsInteger()) {
          LOG(ERROR) << "format error, not valid json string. string_value=" << str;
          return;
        }
        int64 value = 0;
        if (!inner_json->IntValue(&value)) {
          LOG(ERROR) << "format error, not valid json string. string_value=" << str;
          return;
        }
        field->Add(value);
      }
    };
    array_json2_field(risk_limit->media_source(),
                      risk_limit->mutable_extend_fields()->mutable_media_source());
    array_json2_field(risk_limit->ad_type(), risk_limit->mutable_extend_fields()->mutable_ad_type());
    array_json2_field(risk_limit->flow_source(), risk_limit->mutable_extend_fields()->mutable_flow_source());
    array_json2_field(risk_limit->account_types(),
                      risk_limit->mutable_extend_fields()->mutable_account_types());
    ParseAge(risk_limit);
    ParseRate(risk_limit);
    ParseSchedule(risk_limit);
    ParseRegion(risk_limit);
    return;
  }
  void ParseRate(kuaishou::ad::tables::AdRiskFlowLimitCommand *risk_limit) {
    auto rate = risk_limit->rate_limiter_percent();

    const int32_t kStep = 10;
    std::vector<int64_t> temp{0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    if (rate != 0) {
      int32_t count = rate / kStep;
      if (rate > 0 && rate < kStep) {
        count = 1;
      }
      ad_base::AdRandomShuffle::Shuffle(temp.begin(), temp.end());
      temp.resize(count);
      for (auto &item : temp) {
        risk_limit->mutable_extend_fields()->mutable_rate_limiter_percent()->Add(item);
      }
    }
    return;
  }
  void ParseRegion(kuaishou::ad::tables::AdRiskFlowLimitCommand *risk_limit) {
    // maybe dup
    std::unordered_set<int64_t> forbidden_region_set;
    // region 格式：["中国-北京","中国-上海"]
    Json json(StringToJson(risk_limit->forbidden_region()));
    auto p_region_dict = ::base::RegionDict::GetDict();
    for (auto item : json.array()) {
      int64_t region_id = ks::ad_server::GetRegionIdWithCache(item->StringValue());
      if (region_id > 0) {
        forbidden_region_set.insert(region_id);
      } else {
        LOG(ERROR) << "perhaps region format error, str: " << risk_limit->ShortDebugString();
      }
    }
    for (auto &region : forbidden_region_set) {
      risk_limit->mutable_extend_fields()->mutable_forbidden_region()->Add(region);
    }
    if (forbidden_region_set.size() > 100) {
      risk_limit->mutable_extend_fields()->clear_forbidden_region();
      risk_limit->mutable_extend_fields()->add_forbidden_region(469025);
      ks::infra::PerfUtil::CountLogStash(1, "ad.risk", "fordbiden_region_over");
    }
    return;
  }
  void ParseSchedule(kuaishou::ad::tables::AdRiskFlowLimitCommand *risk_limit) {
    Json json(StringToJson(risk_limit->schedule()));
    if (!json.IsArray()) {
      LOG(ERROR) << "schedule field format error, not valid json string. string_value="
                 << risk_limit->schedule();
      return;
    }
    if (json.size() == 0) {
      return;
    }
    for (auto iter = json.array_begin(); iter != json.array_end(); ++iter) {
      auto &inner_json = *iter;
      if (!inner_json->IsInteger()) {
        LOG(ERROR) << "schedule field format error, not valid json string. string_value="
                   << risk_limit->schedule();
        return;
      }
      int64 pos = 0;
      if (!inner_json->IntValue(&pos) || pos < 0 || pos > 23) {
        LOG(ERROR) << "schedule field format error, not valid json string. string_value="
                   << risk_limit->schedule();
        return;
      }
      risk_limit->mutable_extend_fields()->mutable_schedule()->Add(pos);
    }
    return;
  }
  void ParseAge(kuaishou::ad::tables::AdRiskFlowLimitCommand *risk_limit) {
    Json json(StringToJson(risk_limit->age()));
    if (!json.IsArray()) {
      LOG(ERROR) << "age field format error, not valid json string. string_value=" << risk_limit->age();
      return;
    }
    if (json.size() == 0) {
      return;
    }
    for (auto iter = json.array_begin(); iter != json.array_end(); ++iter) {
      auto &inner_json = *iter;
      if (!inner_json->IsArray()) {
        LOG(ERROR) << "age field format error, not valid json string. string_value=" << risk_limit->age();
        return;
      }
      if (inner_json->size() != 2) {
        LOG(ERROR) << "age field format error, not valid json string. string_value=" << risk_limit->age();
        return;
      }
      int64_t left = 0, right = 0;
      if (!inner_json->array().at(0)->IntValue(&left)) {
        LOG(ERROR) << "age field format error, not valid json string. string_value=" << risk_limit->age();
        return;
      }
      if (!inner_json->array().at(1)->IntValue(&right)) {
        LOG(ERROR) << "age field format error, not valid json string. string_value=" << risk_limit->age();
        return;
      }
      for (int64_t age = left; age <= right; ++age) {
        risk_limit->mutable_extend_fields()->mutable_age()->Add(age);
      }
    }
    if (risk_limit->extend_fields().age_size() > 40) {
      risk_limit->mutable_extend_fields()->clear_age();
    }
    return;
  }
  bool Parse(kuaishou::ad::tables::AdRiskFlowLimitCommand *risk_limit) override {
    ParseFlowLimitField(risk_limit);
    ks::ad_server::RiskLimitCondition conditions;
    conditions.media_source.assign(risk_limit->extend_fields().media_source().begin(),
                                   risk_limit->extend_fields().media_source().end());
    conditions.ad_type.assign(risk_limit->extend_fields().ad_type().begin(),
                              risk_limit->extend_fields().ad_type().end());
    conditions.flow_source.assign(risk_limit->extend_fields().flow_source().begin(),
                                  risk_limit->extend_fields().flow_source().end());
    conditions.region.assign(risk_limit->extend_fields().forbidden_region().begin(),
                             risk_limit->extend_fields().forbidden_region().end());
    conditions.schedule.assign(risk_limit->extend_fields().schedule().begin(),
                               risk_limit->extend_fields().schedule().end());
    conditions.age.assign(risk_limit->extend_fields().age().begin(), risk_limit->extend_fields().age().end());
    conditions.rate.assign(risk_limit->extend_fields().rate_limiter_percent().begin(),
                           risk_limit->extend_fields().rate_limiter_percent().end());
    if (conditions.rate.empty()) {
      return true;
    }
    auto hashs = ks::ad_server::BuildAllConditionHashs(&conditions);
    for (auto &hash : hashs) {
      risk_limit->mutable_extend_fields()->mutable_city_risk_hash()->Add(hash);
    }
    return true;
  }
};
PARSER_REGISTER(AdRiskFlowLimitCommandParser, google::protobuf::Message,
                kuaishou::ad::tables::AdRiskFlowLimitCommand)
}  // namespace index_builder
}  // namespace ks
