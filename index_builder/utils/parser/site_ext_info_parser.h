#pragma once
#include <algorithm>
#include <string>
#include <vector>

#include "teams/ad/ad_index/index/utils/public.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"
#include "teams/ad/ad_base/src/common/os_version.h"

namespace ks {
namespace index_builder {

class SiteExtInfoParser : public PbParser<kuaishou::ad::tables::SiteExtInfo> {
 public:
  virtual ~SiteExtInfoParser() {}
  bool Parse(kuaishou::ad::tables::SiteExtInfo* table) override {
    const auto& version = table->version();
    // version 字段格式：{"androidApp":"2.6","iosApp":"2.7","iosNebula":"1.5","androidNebula":"2.0"}
    J<PERSON> json(StringToJson(version));
    for (auto iter = json.object_begin(); iter != json.object_end(); ++iter) {
      if (iter->first == "androidApp") {
        std::string android_version = iter->second->StringValue();
        table->mutable_extend_fields()->set_android_version(ks::ad_base::OsVersionTrans(android_version));
      } else if (iter->first == "iosApp") {
        std::string ios_version = iter->second->StringValue();
        table->mutable_extend_fields()->set_ios_version(ks::ad_base::OsVersionTrans(ios_version));
      } else if (iter->first == "iosNebula") {
        std::string nebula_ios_version = iter->second->StringValue();
        table->mutable_extend_fields()->set_nebula_ios_version(
            ks::ad_base::OsVersionTrans(nebula_ios_version));
      } else if (iter->first == "androidNebula") {
        std::string nebula_android_version = iter->second->StringValue();
        table->mutable_extend_fields()->set_nebula_android_version(
            ks::ad_base::OsVersionTrans(nebula_android_version));
      }
    }
    return true;
  }
};

PARSER_REGISTER_WITH_PRIORITY(SiteExtInfoParser, google::protobuf::Message,
                              kuaishou::ad::tables::SiteExtInfo, 101)

}  // namespace index_builder
}  // namespace ks
