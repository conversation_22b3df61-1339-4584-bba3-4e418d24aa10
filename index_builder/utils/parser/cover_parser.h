#pragma once
#include <string>
#include <vector>
#include <algorithm>
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
namespace ks {
namespace index_builder {
class AdDspCoverParser
    : public PbParser<kuaishou::ad::tables::AdDspCover> {
 public:
  virtual ~AdDspCoverParser() {}
  bool Parse(kuaishou::ad::tables::AdDspCover* cover) override {
    const std::string& cover_md5 = cover->md5();
    if (!cover_md5.empty()) {
      auto* parse_field = cover->mutable_parse_field();
      if (cover_md5.length() > 16) {
        std::string&& md5_str = cover_md5.substr(cover_md5.length() - 16);
        parse_field->set_md5_uint(strtoul(md5_str.c_str(), nullptr, 16));
      } else {
        parse_field->set_md5_uint(strtoul(cover_md5.c_str(), nullptr, 16));
      }
      // to do(liuwenlong03): 留黑白名单
      // cover->clear_md5();
    }
    return true;
  }
};
PARSER_REGISTER(AdDspCoverParser, google::protobuf::Message,
                kuaishou::ad::tables::AdDspCover)
}  // namespace index_builder
}  // namespace ks
