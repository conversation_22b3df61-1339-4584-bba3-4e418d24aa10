#pragma once
#include <algorithm>
#include <string>
#include <vector>
#include <unordered_set>

#include "serving_base/region/region_dict.h"
#include "teams/ad/ad_base/src/common/os_version.h"
#include "teams/ad/ad_base/src/math/random/random_shuffle.h"
#include "teams/ad/ad_index/index/utils/public.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

class RiskIndustryInitiativeParser : public PbParser<kuaishou::ad::tables::RiskIndustryInitiative> {
 public:
  virtual ~RiskIndustryInitiativeParser() {}
  bool RiskScheduleParse(kuaishou::ad::tables::RiskIndustryInitiative* table) {
    const auto& string_value = table->forbidden_schedule();
    if (string_value.empty()) {
      return true;
    }
    Json json(StringToJson(string_value));
    if (!json.IsArray()) {
      LOG(ERROR) << "schedule field format error, not valid json string. string_value=" << string_value;
      return false;
    }
    if (json.size() == 0) {
      return true;
    }
    if (json.size() != 7) {
      LOG(ERROR) << "schedule field format error, not valid json string. string_value=" << string_value;
      return false;
    }
    auto& schedule_list = *table->mutable_extend_fields()->mutable_schedule_list();
    uint32 day = 0;
    for (auto iter = json.array_begin(); iter != json.array_end(); ++iter, ++day) {
      auto inner_json = *iter;
      if (!inner_json->IsArray()) {
        LOG(ERROR) << "schedule field format error, not valid json string. string_value=" << string_value;
        return false;
      }
      for (auto inner_iter = inner_json->array_begin(); inner_iter != inner_json->array_end(); ++inner_iter) {
        int64 pos = 0;
        if ((*inner_iter)->IntValue(&pos)) {
          if (pos < 0 || pos > 23) {
            LOG(ERROR) << "schedule field format error, not valid json string. string_value=" << string_value;
            return false;
          }

          schedule_list.Add(day * 24 + pos);
        } else {
          LOG(ERROR) << "schedule field format error, not valid json string. string_value=" << string_value;
          return false;
        }
      }
    }
    return true;
  }
  bool RiskRateLimiterParse(kuaishou::ad::tables::RiskIndustryInitiative* table) {
    auto func = [](double rate) {
      std::vector<int64_t> ret;

      const double kStep = 5.0;
      std::vector<int64_t> temp{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19};
      if (rate != 0.0) {
        int32_t count = rate / kStep;
        if (rate > 0.0 && rate < 5.0) {
          count = 1;
        }
        ad_base::AdRandomShuffle::Shuffle(temp.begin(), temp.end());
        temp.resize(count);
        ret.swap(temp);
      }
      return ret;
    };
    auto rate_limiter_list = func(table->rate_limiter_percent());
    RepeatedAssign(table->mutable_extend_fields()->mutable_rate_limiter_list(), rate_limiter_list);
    return true;
  }
  bool RiskIndustryInitiativeAgeParse(kuaishou::ad::tables::RiskIndustryInitiative* table) {
    const auto& string_value = table->age();
    if (string_value.empty()) {
      return false;
    }
    auto& forbidden_age_list = *table->mutable_extend_fields()->mutable_forbidden_age_list();
    std::unordered_set<int64_t> forbidden_age_set;
    forbidden_age_list.Clear();

    // age 字符串格式： [[18,23],[24,30]]
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      if (!item->IsArray() || item->array().size() != 2) {
        LOG(ERROR) << "bad age format:" << item->ToString();
        continue;
      }
      int min_age = item->GetInt(0, -1);
      int max_age = item->GetInt(1, -1);
      if (min_age >= 0 && min_age <= max_age) {
        for (int age = min_age; age <= max_age; ++age) { forbidden_age_set.insert(age); }
      }
    }
        RepeatedAssign(&forbidden_age_list, forbidden_age_set);
    return true;
  }
  bool RiskIndustryInitiativeRegionParse(kuaishou::ad::tables::RiskIndustryInitiative* table) {
    const auto& string_value = table->forbidden_region_data();
    if (string_value.empty()) {
      return false;
    }
    auto& forbidden_region_list = *table->mutable_extend_fields()->mutable_forbidden_region_list();
    // maybe dup
    std::unordered_set<int64_t> forbidden_region_set;
    // region 格式：["中国-北京","中国-上海"]
    Json json(StringToJson(string_value));
    auto p_region_dict = ::base::RegionDict::GetDict();
    std::vector<uint32_t> region_ids;
    for (auto item : json.array()) {
      int64_t region_id = ad_server::GetRegionIdWithCache(item->StringValue());
      if (region_id > 0) {
        forbidden_region_set.insert(region_id);
      } else {
        LOG(ERROR) << "perhaps region format error, target id: " << table->id()
                   << ", region: " << string_value;
      }
    }

    forbidden_region_list.Clear();
    RepeatedAssign(&forbidden_region_list, forbidden_region_set);
    return true;
  }
  bool Parse(kuaishou::ad::tables::RiskIndustryInitiative* table) override {
    RiskScheduleParse(table);
    RiskRateLimiterParse(table);
    RiskIndustryInitiativeAgeParse(table);
    RiskIndustryInitiativeRegionParse(table);
    return true;
  }
};

PARSER_REGISTER_WITH_PRIORITY(RiskIndustryInitiativeParser, google::protobuf::Message,
                              kuaishou::ad::tables::RiskIndustryInitiative, 101)

}  // namespace index_builder
}  // namespace ks
