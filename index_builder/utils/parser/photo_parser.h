#pragma once
#include <algorithm>
#include <functional>
#include <map>
#include <string>
#include <vector>
#include <unordered_set>
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "ks/base/abtest/metrics/abtest_metric.h"
namespace ks {
namespace index_builder {

using LabelFunction =
    std::function<void(kuaishou::ad::tables::PhotoStatus* photo_status,
                       const kuaishou::ad::tables::OneChannel& one_channel)>;

static void LabelParser(kuaishou::ad::tables::PhotoStatus* photo_status) {
  LabelFunction photo_quality_score =
      [](kuaishou::ad::tables::PhotoStatus* photo_status,
         const kuaishou::ad::tables::OneChannel& one_channel) {
        if (one_channel.label_data().label_data().r_f_size() > 0) {
          photo_status->set_photo_quality_score(
              one_channel.label_data().label_data().r_f(0));
        }
        if (photo_status->photo_quality_score() != 0) {
          LOG_FIRST_N(INFO, 100) << photo_status->ShortDebugString();
        }
      };
  LabelFunction hetu_category_info = [](kuaishou::ad::tables::PhotoStatus*
                                            photo_status,
                                        const kuaishou::ad::tables::OneChannel&
                                            one_channel) {
    if (one_channel.label_data().label_data().r_i64_size() == 3) {
      auto* extend = photo_status->mutable_extend_fields();
      extend->mutable_hetu_category_info()->set_first_level_category_id(
          one_channel.label_data().label_data().r_i64(0));
      extend->mutable_hetu_category_info()->set_second_level_category_id(
          one_channel.label_data().label_data().r_i64(1));
      extend->mutable_hetu_category_info()->set_third_level_category_id(
          one_channel.label_data().label_data().r_i64(2));
    }
    if (photo_status->extend_fields().has_hetu_category_info()) {
      LOG_FIRST_N(INFO, 100) << photo_status->ShortDebugString();
    }
  };
  LabelFunction photo_quality_score_exp =
      [](kuaishou::ad::tables::PhotoStatus* photo_status,
         const kuaishou::ad::tables::OneChannel& one_channel) {
        if (one_channel.label_data().label_data().r_f_size() > 0) {
          photo_status->set_photo_quality_score_exp(
              one_channel.label_data().label_data().r_f(0));
        }
        if (photo_status->photo_quality_score_exp() != 0) {
          LOG_FIRST_N(INFO, 100) << photo_status->ShortDebugString();
        }
      };
  LabelFunction mmu_posterior_score =
      [](kuaishou::ad::tables::PhotoStatus* photo_status,
         const kuaishou::ad::tables::OneChannel& one_channel) {
        if (one_channel.label_data().label_data().r_f_size() > 0) {
          photo_status->mutable_extend_fields()->set_mmu_posterior_score(
              one_channel.label_data().label_data().r_f(0));
        }
        if (photo_status->extend_fields().mmu_posterior_score() != 0) {
          LOG_FIRST_N(INFO, 100) << photo_status->ShortDebugString();
        }
      };
  LabelFunction bad_photo_tax_coef =
      [](kuaishou::ad::tables::PhotoStatus* photo_status,
        const kuaishou::ad::tables::OneChannel& one_channel) {
        if (one_channel.label_data().label_data().r_f_size() > 0) {
          photo_status->mutable_extend_fields()->set_bad_photo_tax_coef(
              one_channel.label_data().label_data().r_f(0));
        }
        if (photo_status->extend_fields().bad_photo_tax_coef() != 0) {
          LOG_FIRST_N(INFO, 100) << photo_status->ShortDebugString();
        }
      };

  static std::map<std::string, LabelFunction> funtion_map{
      {"adphoto_quality_score", photo_quality_score},
      {"adphoto_quality_score_exp", photo_quality_score_exp},
      {"mmu_posterior_score", mmu_posterior_score},
      {"ad_photo_hetu_category", hetu_category_info},
      {"bad_photo_tax_coef", bad_photo_tax_coef}};
  for (const auto& one_channel :
       photo_status->extend_fields().level_label_data().channel_data()) {
    auto channel_name = one_channel.name();
    auto map_it = funtion_map.find(channel_name);
    if (map_it != funtion_map.end()) {
      auto func = map_it->second;
      func(photo_status, one_channel);
    }
  }
  // 已解析则去掉
  photo_status->mutable_extend_fields()->clear_level_label_data();
}

/* unit parser area */
// DiverseDataParser 针对 optional 结构 略过
class AdDspPhotoParser : public PbParser<kuaishou::ad::tables::PhotoStatus> {
 public:
  virtual ~AdDspPhotoParser() {}
  bool Parse(kuaishou::ad::tables::PhotoStatus* photo_status) override {
    base::Json json(base::StringToJson(photo_status->top5_color()));
    for (auto* item : json.array()) {
      if (item->size() != 4) {
        continue;
      }
      int64_t r = 0, g = 0, b = 0;
      double ratio = 0.0;
      auto& color_array = item->array();
      color_array[0]->IntValue(&r);
      color_array[1]->IntValue(&g);
      color_array[2]->IntValue(&b);
      color_array[3]->FloatValue(&ratio);
      auto* color_item = photo_status->mutable_parse_field()->add_top5_color();
      color_item->set_r(r);
      color_item->set_g(g);
      color_item->set_b(b);
      color_item->set_ratio(ratio);
    }

    base::Json photo_promotable_status_json(base::StringToJson(photo_status->tag()));
    const std::string& photo_promotable_status_str = photo_status->tag();
    if (!photo_promotable_status_str.empty()) {
      for (const auto item : photo_promotable_status_json.array()) {
        int64_t value = item->IntValue(-1);
        if (value >= 0) {
          photo_status->mutable_extend_fields()->add_photo_promotable_status(value);
          falcon::Inc("index_builder.photo_promotable_status_add_success");
        }
      }
    }

    base::Json plc_biz_type_json(base::StringToJson(photo_status->plc_biz_type()));
    const std::string& plc_biz_type_str = photo_status->plc_biz_type();
    if (!plc_biz_type_str.empty() && plc_biz_type_json.IsArray()) {
      for (auto item : plc_biz_type_json.array()) {
        int64_t value = item->IntValue(-1);
        if (value >= 0) {
          photo_status->mutable_extend_fields()->add_plc_biz_type(value);
          falcon::Inc("index_builder.photo_status_plc_biz_type_add_success");
        }
      }
    }

    // to do(liuwenlong03): 留黑白名单
    // photo_status->clear_top5_color();
    const std::string& photo_md5 = photo_status->md5();
    if (!photo_md5.empty()) {
      auto* parse_field = photo_status->mutable_parse_field();
      if (photo_md5.length() > 16) {
        std::string&& md5_str = photo_md5.substr(photo_md5.length() - 16);
        parse_field->set_md5_uint(strtoul(md5_str.c_str(), nullptr, 16));
      } else {
        parse_field->set_md5_uint(strtoul(photo_md5.c_str(), nullptr, 16));
      }
      // to do(liuwenlong03): 留黑白名单
      // photo_status->clear_md5();
    }
    LabelParser(photo_status);
    // 最终状态收敛 true 可见
    {
      bool final_status =
          (photo_status->visible()) && (!photo_status->retired_status());
      bool dsp_retired_status =
          photo_status->recently_used_time() >= photo_status->expire_time();
      final_status &= dsp_retired_status;
      photo_status->set_final_status(final_status);
    }
    {
      static const std::unordered_set<std::string> kWorldList = {"w_n_kuaishou_apps_sid_19"};
      ExperimentInfo exp_info =
          abtest::GetExperimentInfo(photo_status->photo_id(), std::to_string(photo_status->photo_id()),
                                    std::to_string(photo_status->photo_id()), kWorldList);
      for (const auto& kv : exp_info) {
        if (kv.second.experimentId.empty() || kv.second.groupId.empty()) continue;
        auto* hit_info = photo_status->mutable_extend_fields()->add_photo_id_abtest_hit();
        hit_info->set_experiment_id(kv.second.experimentId);
        hit_info->set_group_id(kv.second.groupId);
      }
      LOG_EVERY_N(INFO, 100000) << "photo_id hit exp: " << photo_status->ShortDebugString();
    }
    // 解析 UePhotoLimitinfo
    base::Json ue_photo_json(base::StringToJson(photo_status->ue_photo_limit_info().limit_info()));
    for (auto* item : ue_photo_json.array()) {
      if (!item->IsObject()) {
        continue;
      }
      auto* photo_limit_info_tags = photo_status->mutable_ue_photo_limit_info()->add_photo_limit_info_tags();
      int32_t tag = item->GetInt("tag", 0);
      int32_t ratio = item->GetInt("rate", 0);
      LOG_EVERY_N(INFO, 10000) << "vanke, tag=" << tag << ", ratio=" << ratio;
      if (tag == 1101) {
        photo_status->set_cpm_tag_1101(ratio);
        LOG_EVERY_N(INFO, 10000) << "vanke, cpm_tag_1101=" << ratio;
      } else if (tag == 1102) {
        photo_status->set_cpm_tag_1102(ratio);
        LOG_EVERY_N(INFO, 10000) << "vanke, cpm_tag_1102=" << ratio;
      }
      photo_limit_info_tags->set_tag(tag);
      photo_limit_info_tags->set_ratio(ratio);
    }
    photo_status->mutable_ue_photo_limit_info()->clear_limit_info();
    // 解析 photo_tag
    base::Json photo_tag_json(base::StringToJson(photo_status->photo_tag()));
    if (!photo_tag_json.IsArray()) {
      LOG_EVERY_N(ERROR, 10000)
          << "photo_tag format error, not valid json string. string_value="
          << photo_status->photo_tag();
    }
    for (auto item : photo_tag_json.array()) {
      int64_t photo_tag = 0;
      if (!item->IntValue(&photo_tag)) {
        continue;
      }
      photo_status->mutable_extend_fields()->add_photo_tag(photo_tag);
    }
    photo_status->clear_photo_tag();
    // 解析 photo_author_tag
    base::Json photo_author_tag_json(base::StringToJson(photo_status->photo_author_tag()));
    if (!photo_author_tag_json.IsArray()) {
      LOG_EVERY_N(ERROR, 10000)
          << "photo_author_tag format error, not valid json string. string_value="
          << photo_status->photo_author_tag();
    }
    for (auto item : photo_author_tag_json.array()) {
      int64_t photo_author_tag = 0;
      if (!item->IntValue(&photo_author_tag)) {
        continue;
      }
      photo_status->mutable_extend_fields()->add_photo_author_tag(photo_author_tag);
    }
    photo_status->clear_photo_author_tag();
    return true;
  }
};
PARSER_REGISTER(AdDspPhotoParser, google::protobuf::Message,
                kuaishou::ad::tables::PhotoStatus)
}  // namespace index_builder
}  // namespace ks
