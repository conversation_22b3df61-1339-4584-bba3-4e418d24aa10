#pragma once
#include <string>
#include <vector>
#include <map>
#include <algorithm>
#include <unordered_map>

#include "serving_base/jansson/json.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"

namespace ks {
namespace index_builder {

static void ParseStringFields(kuaishou::ad::tables::FansTopUserStatus* proto) {
  if (proto == nullptr) { return; }
  // 解析 tag
  base::Json tag_json(base::StringToJson(proto->tag()));
  if (!tag_json.IsArray()) {
    LOG_EVERY_N(ERROR, 10000)
        << "tag format error, not valid json string. string_value=" << proto->tag();
  }
  for (auto item : tag_json.array()) {
    int64_t tag = 0;
    if (!item->IntValue(&tag)) {
      continue;
    }
    proto->mutable_parse_fields()->add_tag(tag);
  }
  proto->clear_tag();
}

class FansTopUserStatusParser : public PbParser<kuaishou::ad::tables::FansTopUserStatus> {
 public:
  virtual ~FansTopUserStatusParser() {}
  bool Parse(kuaishou::ad::tables::FansTopUserStatus* proto) override {
    ParseStringFields(proto);
    return true;
  }
};

PARSER_REGISTER(FansTopUserStatusParser, google::protobuf::Message, kuaishou::ad::tables::FansTopUserStatus);

}  // namespace index_builder
}  // namespace ks
