#pragma once
#include <algorithm>
#include <string>
#include <vector>

#include "teams/ad/ad_index/index/utils/public.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

class AdDspNegativeWordParser : public PbParser<kuaishou::ad::tables::AdDspNegativeWord> {
 public:
  virtual ~AdDspNegativeWordParser() {}
  bool Parse(kuaishou::ad::tables::AdDspNegativeWord* table) override {
    auto phrase_word_ids = JsonStrToRepeatedInt(table->phrase_word_ids());
    RepeatedAssign(table->mutable_extend_fields()->mutable_phrase_word_ids(), phrase_word_ids);
    auto exact_word_ids = JsonStrToRepeatedInt(table->exact_word_ids());
    RepeatedAssign(table->mutable_extend_fields()->mutable_exact_word_ids(), exact_word_ids);
    auto phrase_word = JsonStrToRepeatedStr(table->phrase_word());
    RepeatedAssign(table->mutable_extend_fields()->mutable_phrase_word_strs(), phrase_word);
    auto exact_word = JsonStrToRepeatedStr(table->exact_word());
    RepeatedAssign(table->mutable_extend_fields()->mutable_exact_word_strs(), exact_word);
    return true;
  }
};

PARSER_REGISTER_WITH_PRIORITY(AdDspNegativeWordParser, google::protobuf::Message,
                              kuaishou::ad::tables::AdDspNegativeWord, 101)

}  // namespace index_builder
}  // namespace ks
