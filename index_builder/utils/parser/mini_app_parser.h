#pragma once
#include <algorithm>
#include <string>
#include <vector>

#include "teams/ad/ad_base/src/common/os_version.h"
#include "teams/ad/ad_index/index/utils/public.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

class AdDspMiniAppParser : public PbParser<kuaishou::ad::tables::AdDspMiniApp> {
 public:
  virtual ~AdDspMiniAppParser() {}
  bool Parse(kuaishou::ad::tables::AdDspMiniApp* table) override {
    table->mutable_extend_fields()->set_main_ios_mini_version(
        ks::ad_base::OsVersionTrans(table->main_ios_mini_version()));
    table->mutable_extend_fields()->set_main_android_mini_version(
        ks::ad_base::OsVersionTrans(table->main_android_mini_version()));
    table->mutable_extend_fields()->set_nebula_ios_mini_version(
        ks::ad_base::OsVersionTrans(table->nebula_ios_mini_version()));
    table->mutable_extend_fields()->set_nebula_android_mini_version(
        ks::ad_base::OsVersionTrans(table->nebula_android_mini_version()));
    table->mutable_extend_fields()->set_mini_ios_system_version(
        ks::ad_base::OsVersionTrans(table->mini_ios_system_version()));
    table->mutable_extend_fields()->set_mini_android_system_version(
        ks::ad_base::OsVersionTrans(table->mini_android_system_version()));
    auto main_android_block_versions = JsonStrToRepeatedStr(table->main_android_block_versions());
    auto main_ios_block_versions = JsonStrToRepeatedStr(table->main_ios_block_versions());
    auto nebula_android_block_versions = JsonStrToRepeatedStr(table->nebula_android_block_versions());
    auto nebula_ios_block_versions = JsonStrToRepeatedStr(table->nebula_ios_block_versions());
    uint64_t uint64_sign = base::CityHash64(table->mini_app_id_platform().data(),
                                            table->mini_app_id_platform().size());
    int64_t mini_app_id_platform_id = *(reinterpret_cast<int64_t*>(&uint64_sign));
    table->mutable_extend_fields()->set_mini_app_id_platform_hash(mini_app_id_platform_id);
    std::for_each(
        main_android_block_versions.begin(), main_android_block_versions.end(), [&](const std::string& str) {
          table->mutable_extend_fields()->add_main_android_block_versions(ks::ad_base::OsVersionTrans(str));
        });
    std::for_each(
        main_ios_block_versions.begin(), main_ios_block_versions.end(), [&](const std::string& str) {
          table->mutable_extend_fields()->add_main_ios_block_versions(ks::ad_base::OsVersionTrans(str));
        });
    std::for_each(
        nebula_android_block_versions.begin(), nebula_android_block_versions.end(),
        [&](const std::string& str) {
          table->mutable_extend_fields()->add_nebula_android_block_versions(ks::ad_base::OsVersionTrans(str));
        });
    std::for_each(
        nebula_ios_block_versions.begin(), nebula_ios_block_versions.end(), [&](const std::string& str) {
          table->mutable_extend_fields()->add_nebula_ios_block_versions(ks::ad_base::OsVersionTrans(str));
        });
    return true;
  }
};

PARSER_REGISTER_WITH_PRIORITY(AdDspMiniAppParser, google::protobuf::Message,
                              kuaishou::ad::tables::AdDspMiniApp, 101)

}  // namespace index_builder
}  // namespace ks
