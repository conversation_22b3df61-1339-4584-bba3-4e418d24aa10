#pragma once
#include <string>
#include <vector>
#include "serving_base/jansson/json.h"
#include "base/strings/string_number_conversions.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"

namespace ks {
namespace index_builder {


class UnitSupportInfoParser : public PbParser<kuaishou::ad::tables::UnitSupportInfo> {
 public:
  virtual ~UnitSupportInfoParser() {}
  bool Parse(kuaishou::ad::tables::UnitSupportInfo* unit_support_info) override {
    auto* extend_fields = unit_support_info->mutable_extend_fields();
    auto pos_premium_config_parser = [&]() -> bool {
      std::string string_value =
          unit_support_info->pos_premium_config();
      if (string_value.empty()) return true;
      base::Json json(base::StringToJson(string_value));
      int64 value = 0;
      if (json.GetInt("enablePosPremium", &value))
        extend_fields->set_pos_premium_flag(value);
      std::string default_value = "";
      double thanosPremiumCoefficient{0.0};
      double feedPremiumCoefficient{0.0};
      if (json.GetString("thanosPremiumCoefficient", &default_value)) {
        base::StringToDouble(default_value, &thanosPremiumCoefficient);
        extend_fields->set_thanos_premium_coefficient(thanosPremiumCoefficient);
      }
      if (json.GetString("feedPremiumCoefficient", &default_value)) {
        base::StringToDouble(default_value, &feedPremiumCoefficient);
        extend_fields->set_feed_premium_coefficient(feedPremiumCoefficient);
      }
      return true;
    };
    auto smart_asset_parser = [&]() -> bool {
      // 解析 smart_asset
      std::string smart_asset_str = unit_support_info->smart_asset();
      if (smart_asset_str.empty()) {
        LOG_FIRST_N(INFO, 10) << "smart_asset is empty";
        return true;
      }
      base::Json json(base::StringToJson(smart_asset_str));
      bool asset_mining = false;
      if (json.GetBoolean("assetMining", &asset_mining)) {
        extend_fields->set_asset_mining(asset_mining);
      }
      return true;
    };

    auto series_pay_template_id_multi_parser = [&]() -> bool {
      // 解析 series_pay_template_id_multi
      std::string string_value = unit_support_info->series_pay_template_id_multi();
      if (string_value.empty() || string_value == "[]") {
        LOG_FIRST_N(INFO, 10) << "series_pay_template_id_multi is empty";
        return true;
      }
      auto template_ids = JsonStrToRepeatedInt(string_value);
      RepeatedAssign(unit_support_info->mutable_parse_fields()->mutable_series_pay_template_id_multi(),
                    template_ids);
      return true;
    };

    auto poi_ids_real_parser = [&]() -> bool {
      std::string string_value =
          unit_support_info->poi_ids_real();
      if (string_value.empty()) return true;
      base::Json json(base::StringToJson(string_value));
      for (auto item : json.array()) {
        int64_t label;
        if (item->IntValue(&label)) {
          unit_support_info->mutable_parse_fields()->add_poi_ids_real(label);
        }
      }
      return true;
    };

    pos_premium_config_parser();
    smart_asset_parser();
    series_pay_template_id_multi_parser();
    poi_ids_real_parser();
    return true;
  }
};


PARSER_REGISTER(UnitSupportInfoParser, google::protobuf::Message,
                kuaishou::ad::tables::UnitSupportInfo)
}  // namespace index_builder
}  // namespace ks
