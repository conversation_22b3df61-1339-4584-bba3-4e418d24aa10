#pragma once

#include <algorithm>
#include <string>
#include <vector>
#include <string_view>

#include "teams/ad/ad_index/index/utils/public.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

class NaBookPanelContentParser : public PbParser<kuaishou::ad::tables::NaBookPanel> {
 public:
  virtual ~NaBookPanelContentParser() {}

  bool Parse(kuaishou::ad::tables::NaBookPanel* item) override {
    google::protobuf::util::JsonParseOptions options;
    options.ignore_unknown_fields = true;
    auto status = google::protobuf::util::JsonStringToMessage(item->content(),
                                                                item->mutable_panel_content(), options);
    if (!status.ok()) {
    ks::infra::PerfUtil::CountLogStash(
        1, "ad.index_builder", "parse_extend_fields_failed", "na_book_panel");
    item->clear_panel_content();
    }
    return true;
  }
};

PARSER_REGISTER_WITH_PRIORITY(NaBookPanelContentParser, google::protobuf::Message,
                              kuaishou::ad::tables::NaBookPanel, 101)

}  // namespace index_builder
}  // namespace ks
