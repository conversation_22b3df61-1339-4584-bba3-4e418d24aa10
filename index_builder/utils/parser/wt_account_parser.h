#pragma once
#include <string>
#include <vector>
#include <map>
#include <algorithm>
#include <unordered_map>

#include "absl/strings/str_split.h"
#include "base/encoding/base64.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/table_extend_fields.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"
#include "teams/ad/index_builder/utils/parser/account_parser.h"

namespace ks {
namespace index_builder {

static void ParseExtendFields(kuaishou::ad::tables::WTAccount* proto) {
  if (proto == nullptr) { return; }
}
static void ParseStringFields(kuaishou::ad::tables::WTAccount* proto) {
  if (proto == nullptr) { return; }
  auto* parse_fields = proto->mutable_parse_fields();

  const auto& ecom_is_operation_author = proto->ecom_is_operation_author();
  if (!ecom_is_operation_author.empty()) {
    kuaishou::ad::tables::LabelData label_data;
    std::string decode_string;
    if (base::Base64Decode(ecom_is_operation_author, &decode_string)) {
      label_data.ParseFromString(decode_string);
      if (label_data.r_f_size() > 0) {
        if (label_data.r_f(0)) {
          parse_fields->set_ecom_is_operation_author(1);
        } else {
          parse_fields->set_ecom_is_operation_author(0);
        }
      }
    }
    proto->clear_ecom_is_operation_author();
    LOG_FIRST_N(INFO, 100) << parse_fields->ShortDebugString();
  }

  const auto& ecom_is_new_account = proto->ecom_is_new_account();
  if (!ecom_is_new_account.empty()) {
    kuaishou::ad::tables::LabelData label_data;
    std::string decode_string;
    if (base::Base64Decode(ecom_is_new_account, &decode_string)) {
      label_data.ParseFromString(decode_string);
      if (label_data.r_f_size() > 0) {
        if (label_data.r_f(0)) {
          parse_fields->set_ecom_is_new_account(true);
        } else {
          parse_fields->set_ecom_is_new_account(false);
        }
      }
    }
    proto->clear_ecom_is_new_account();
    LOG_FIRST_N(INFO, 100) << parse_fields->ShortDebugString();
  }
  // 解析 ue_account_strategy_tag
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, ue_account_strategy_tag, ",");
  // 解析 ue_account_strategy_score
  SIMPLE_PARSE_FLOAT_USE_SPLLIT(proto, ue_account_strategy_score, ",");
}

static void ParseAccountProto(kuaishou::ad::tables::Account* proto) {
  if (proto == nullptr) { return; }
  static AdDspAccountParser proto_parser;
  proto_parser.Parse(proto);
  return;
}

class WTAccountParser : public PbParser<kuaishou::ad::tables::WTAccount> {
 public:
  virtual ~WTAccountParser() {}
  bool Parse(kuaishou::ad::tables::WTAccount* proto) override {
    // PARSE_BINARY_STRING_TO_MESSAGE(proto, account);
    // ParseAccountProto(proto->mutable_account());
    ParseExtendFields(proto);
    ParseStringFields(proto);
    return true;
  }
};

PARSER_REGISTER(WTAccountParser, google::protobuf::Message, kuaishou::ad::tables::WTAccount);

}  // namespace index_builder
}  // namespace ks
