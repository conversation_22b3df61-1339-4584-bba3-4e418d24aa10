#pragma once
#include <set>
#include <string>
#include <vector>
#include <algorithm>
#include "base/hash_function/city.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "base/strings/string_split.h"
#include "teams/ad/index_builder/utils/kconf.h"
namespace ks {
namespace index_builder {
class StyleMaterialParser
    : public PbParser<kuaishou::ad::tables::AdStyleMaterial> {
 public:
  virtual ~StyleMaterialParser() {}
  bool Parse(kuaishou::ad::tables::AdStyleMaterial* style) override {
    base::Json style_content(base::StringToJson(style->style_content()));
    if (!style_content.IsObject()) {
      falcon::Inc("index_builder.ad_style.style_content_error");
      return false;
    }
    do {
      auto* parse_field = style->mutable_parse_field();
      auto material_type = kuaishou::ad::AdEnum_CreativeMaterialType(
          style_content.GetInt("materialType", 0));
      if (parse_field) {
        // 共用字段
        parse_field->set_material_type(material_type);
        parse_field->set_rule_id(style_content.GetInt("ruleId", 0));
        parse_field->set_template_id(style_content.GetInt("templateId", 0));
        parse_field->set_cover_height(style_content.GetInt("coverHeight", 0));
        parse_field->set_cover_width(style_content.GetInt("coverWidth", 0));
        parse_field->set_exp_tag(style_content.GetInt("materialExpTag", 0));
        auto* tags = style_content.Get("tags");
        if (tags && tags->IsArray()) {
          for (auto* tag : tags->array()) {
            int32 value = tag->IntValue(-1);
            if (value >= 0) {
              parse_field->add_tags(value);
            }
          }
        }
        // 视频素材
        if (material_type == kuaishou::ad::AdEnum::VERTICAL_SCREEN ||
            material_type == kuaishou::ad::AdEnum::HORIZONTAL_SCREEN) {
          parse_field->set_duration(style_content.GetInt("duration", 0));
          parse_field->set_photo_id(style_content.GetInt("adapterPhotoId", 0));
          parse_field->set_video_url(style_content.GetString("videoUrl", ""));
          parse_field->set_cover_url(style_content.GetString("coverUrl", ""));
          parse_field->set_video_bytes(style_content.GetInt("videoBytes", 0));
        }
        // 图片素材
        if (material_type == kuaishou::ad::AdEnum::VERTICAL_IMAGE ||
            material_type == kuaishou::ad::AdEnum::HORIZONTAL_IMAGE ||
            material_type == kuaishou::ad::AdEnum::BANNER_IMAGE) {
          parse_field->set_material_url(
              style_content.GetString("materialUrl", ""));
          parse_field->set_compress_material_url(
              style_content.GetString("compressMaterialUrl", ""));
          parse_field->set_phone_specific_url(
              style_content.GetString("phoneSpecificUrl", ""));
        }
        // 竖版图片标准宽高素材
        if (material_type == kuaishou::ad::AdEnum::VERTICAL_IMAGE) {
          parse_field->set_cover_standard(
              style_content.GetString("coverStandard", ""));
          parse_field->set_compress_cover_standard(
              style_content.GetString("compressCoverStandard", ""));
          parse_field->mutable_ext_material_info()->set_ext_cover_url(
              style_content.GetString("extCoverUrl", ""));
          parse_field->mutable_ext_material_info()->set_ext_cover_height(
              style_content.GetInt("extCoverHeight", 0));
          parse_field->mutable_ext_material_info()->set_ext_cover_width(
              style_content.GetInt("extCoverWidth", 0));
        }
        // 多图素材
        if (material_type == kuaishou::ad::AdEnum::HORIZONTAL_SMALL_IMAGE_GROUP) {
          falcon::Inc("index_builder.ad_style.horizontal_small_image_group");
          auto pics = style_content.Get("groupImage");
          if (!pics || !pics->IsArray()) {
            break;
          }
          for (auto* item : pics->array()) {
            if (!item || !item->IsObject()) {
              continue;
            }
            auto* pic_pb = parse_field->add_picture_group();
            pic_pb->set_material_url(item->GetString("materialUrl", ""));
            pic_pb->set_compress_material_url(item->GetString("compressMaterialUrl", ""));
          }
        }
        // 素材生成时间
        parse_field->set_create_timestamp(
            style_content.GetInt("currentTimeMills", 0));
      }
    } while (false);

    auto uri = style_content.GetString("url", "");
    static std::set<std::string> host_set = {"https://moli.kuaishou.com", "https://chenzhongkj.com"};
    bool match = false;
    for (const auto& host : host_set) {
      if (uri.find(host) != uri.npos) {
        match = true;
        break;
      }
    }
    if (match) {
      int64_t page_id = 0;
      if (GetPageIdFromURI(uri, &page_id) && page_id > 0) {
        style->set_is_site(true);
        style->set_site_id(page_id);
      }
    }

    // material_target 解析到 parse_field 不会被过滤
    base::Json material_target(base::StringToJson(style->material_target()));
    if (!material_target.IsObject()) {
      falcon::Inc("index_builder.ad_style.material_target_error");
      return true;
    }
    auto *parse_field = style->mutable_parse_field();
    std::string adcode_str = material_target.GetString("region", "");
    if (adcode_str.find(",") == std::string::npos) {
      int32_t city_id{0};
      if (adcode_str.empty()) {
      } else if (adcode_str.size() <= 4) {
        auto ok = absl::SimpleAtoi(adcode_str, &city_id);
        parse_field->mutable_material_target()->set_city_id(city_id);
      } else {
        auto ok = absl::SimpleAtoi(adcode_str.substr(0, 4), &city_id);
        parse_field->mutable_material_target()->set_city_id(city_id);
      }
    } else {
      std::vector<std::string> city_vec;
      base::SplitString(adcode_str, ",", &city_vec);
      for (const auto &code : city_vec) {
        std::string city;
        int32_t city_id{0};
        if (code.size() <= 2) {
          city = code;
        } else {
          city = code.substr(0, 2);
        }
        if (absl::SimpleAtoi(city, &city_id)) {
          parse_field->mutable_material_target()->add_city_ids(city_id);
        }
      }
    }
    auto gender_str = material_target.GetString("gender", "");
    int32_t gender{0};
    if (absl::SimpleAtoi(gender_str, &gender)) {
      parse_field->mutable_material_target()->set_gender(gender);
    }
    std::vector<std::string> age_vec;
    base::SplitString(material_target.GetString("age", ""), std::string(","), &age_vec);
    for (const auto &age : age_vec) {
      int32_t age_id{0};
      if (absl::SimpleAtoi(age, &age_id)) {
        parse_field->mutable_material_target()->add_age(age_id);
      }
    }
    return true;
  }
};
PARSER_REGISTER(StyleMaterialParser, google::protobuf::Message,
                kuaishou::ad::tables::AdStyleMaterial)
}  // namespace index_builder
}  // namespace ks
