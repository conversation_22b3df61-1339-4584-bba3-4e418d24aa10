
#pragma once

#include <algorithm>
#include <string>
#include <vector>

#include "teams/ad/ad_index/index/utils/public.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

class BsLiveSkuToDasParser : public PbParser<kuaishou::ad::tables::BsLiveSkuToDas> {
 public:
  virtual ~BsLiveSkuToDasParser() {}

  bool Parse(kuaishou::ad::tables::BsLiveSkuToDas* item) override {
    auto parse_fields = item->mutable_parse_fields();
    SIMPLE_PARSE_INT64_USE_SPLLIT(item, sku_ids, ",");
    return true;
  }
};

PARSER_REGISTER_WITH_PRIORITY(BsLiveSkuToDasParser, google::protobuf::Message,
                              kuaishou::ad::tables::BsLiveSkuToDas, 101)

}  // namespace index_builder
}  // namespace ks
