#pragma once
#include <string>
#include <vector>
#include <algorithm>
#include "teams/ad/index_builder/utils/parser/extra_parser.h"

namespace ks {
namespace index_builder {



/* unit parser area */

// DiverseDataParser 针对 optional 结构 略过

class AdDspPhotoNegativeParser : public PbParser<kuaishou::ad::tables::AdDspPhotoNegative> {
 public:
  virtual ~AdDspPhotoNegativeParser() {}
  bool Parse(kuaishou::ad::tables::AdDspPhotoNegative* photo_negative) override {
    std::string string_value = photo_negative->tag();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t id = item->IntValue(-1);
      if (id > 0) {
        photo_negative->mutable_extend_fields()->add_tag_vec(id);
      }
    }
    return true;
  }
};

PARSER_REGISTER(AdDspPhotoNegativeParser, google::protobuf::Message,
                kuaishou::ad::tables::AdDspPhotoNegative)
}  // namespace index_builder
}  // namespace ks
