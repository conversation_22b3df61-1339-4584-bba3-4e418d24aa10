#pragma once
#include <cstddef>
#include <string>
#include <vector>
#include <map>
#include <algorithm>
#include <unordered_map>

#include "absl/strings/str_split.h"
#include "base/encoding/base64.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

static void ParseStringFields(kuaishou::ad::tables::WTSearchLiveItem* proto) {
  if (proto == nullptr) { return; }
  auto* parse_fields =  proto->mutable_parse_fields();
  // 解析 exclude_regions;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, item_activity_ids, ",");
  LOG_FIRST_N(INFO, 100) << parse_fields->ShortDebugString();
}

class WTSearchLiveItemParser : public PbParser<kuaishou::ad::tables::WTSearchLiveItem> {
 public:
  virtual ~WTSearchLiveItemParser() {}
  bool Parse(kuaishou::ad::tables::WTSearchLiveItem* proto) override {
    ParseStringFields(proto);
    return true;
  }
};

PARSER_REGISTER(WTSearchLiveItemParser, google::protobuf::Message, kuaishou::ad::tables::WTSearchLiveItem);

}  // namespace index_builder
}  // namespace ks
