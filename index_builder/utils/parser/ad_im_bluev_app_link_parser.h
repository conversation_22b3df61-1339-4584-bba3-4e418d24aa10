#pragma once

#include <algorithm>
#include <string>
#include <vector>
#include <string_view>

#include "teams/ad/ad_index/index/utils/public.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

class AdImBluevAppLinkParser : public PbParser<kuaishou::ad::tables::AdImBluevAppLink> {
 public:
  virtual ~AdImBluevAppLinkParser() {}

  bool Parse(kuaishou::ad::tables::AdImBluevAppLink* item) override {
    auto identifier_report_info = JsonStrToRepeatedInt(item->identifier_report_info());
    RepeatedAssign(item->mutable_parse_fields()->mutable_identifier_report_info_ids(),
                  identifier_report_info);
    return true;
  }
};

PARSER_REGISTER_WITH_PRIORITY(AdImBluevAppLinkParser, google::protobuf::Message,
                              kuaishou::ad::tables::AdImBluevAppLink, 101)

}  // namespace index_builder
}  // namespace ks
