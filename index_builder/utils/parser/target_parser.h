// Target 许多字段都是 json string 格式。Target Server 需要把他们变成展开变成 repeated 的形式。
#pragma once

#include <algorithm>
#include <set>
#include <string>
#include <vector>
#include <functional>
#include "ks/util/json.h"
#include "teams/ad/ad_index/index/common.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/ad_index/index/utils/s2utils.h"
#include "teams/ad/ad_base/src/better_enums/better_enums.h"

namespace ks {
namespace index_builder {
BETTER_ENUM(TargetGender, uint8_t, Male = 1, Female, Both)

using RangeItem = kuaishou::ad::tables::Target_RangeItem;
std::function<bool(const RangeItem&, const RangeItem&)> range_item_compare =
    [](const RangeItem& a, const RangeItem& b) {
      return (a.min() < b.min()) || (a.min() == b.min() && a.max() < b.max());
    };

class TargetInterestVideoParser
    : public PbParser<kuaishou::ad::tables::Target> {
 public:
  virtual ~TargetInterestVideoParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->interest_video();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      target->add_interest_video_vec(convertor(item->StringValue()));
    }
    auto interest_video_vec = target->mutable_interest_video_vec();
    std::sort(interest_video_vec->begin(), interest_video_vec->end());
    return true;
  }
};

// 这个 set 需要自己写自己补充下
class TargetPlatformParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetPlatformParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->platform();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto iter = json.object_begin(); iter != json.object_end(); ++iter) {
      target->add_platform_ids_vec(convertor(iter->first));
      target->set_need_check_os_version(true);
      target->set_min_os_version_raw(iter->second->GetFloat("min", 0.0));
      target->set_max_os_version_raw(iter->second->GetFloat("max", 0.0));
    }
    return true;
  }
};

class TargetInterestParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetInterestParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->interest();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      target->add_interest_vec(convertor(item->StringValue()));
    }
    auto interest_vec = target->mutable_interest_vec();
    std::sort(interest_vec->begin(), interest_vec->end());
    return true;
  }
};

class TargetBusinessInterestParser
    : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetBusinessInterestParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->business_interest();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      const std::string& biz_interest = item->StringValue();
      if (!biz_interest.empty()) {
        target->add_business_interest_vec(convertor(item->StringValue()));
      } else {
        LOG(ERROR) << "invalid target setting: " << string_value;
        return false;
      }
    }
    auto business_interest_vec = target->mutable_business_interest_vec();
    std::sort(business_interest_vec->begin(), business_interest_vec->end());
    return true;
  }
};


// start of part2
class TargetAgeParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  virtual ~TargetAgeParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->age();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int min_age = item->GetInt("min", -1);
      int max_age = item->GetInt("max", -1);
      if (min_age >= 0 && min_age <= max_age) {
        // addRange adds [min, max]
        auto range_item = target->add_age_vec();
        range_item->set_min(min_age);
        range_item->set_max(max_age);
      }
    }
    auto age_vec = target->mutable_age_vec();
    std::sort(age_vec->begin(), age_vec->end(), range_item_compare);
    return true;
  }
};

class TargetLanguageParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetLanguageParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->language();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      target->add_language_vec(convertor(item->StringValue()));
    }
    auto language_vec = target->mutable_language_vec();
    std::sort(language_vec->begin(), language_vec->end());
    return true;
  }
};

class TargetNetworkParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetNetworkParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->network();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      target->add_network_vec(convertor(item->StringValue()));
    }
    auto network_vec = target->mutable_network_vec();
    std::sort(network_vec->begin(), network_vec->end());
    return true;
  }
};

class TargetPageParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetPageParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->page();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      target->add_page_vec(item->IntValue(kuaishou::ad::AdEnum::HOT));
    }
    auto page_vec = target->mutable_page_vec();
    std::sort(page_vec->begin(), page_vec->end());
    return true;
  }
};

// TargetRegionParser 依赖 GetRegionIdWithCache 暂不转移

class TargetRegionIdsParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetRegionIdsParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->region_ids();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t region;
      if (item->IntValue(&region) &&
          region < ((int64_t)ks::ad_server::TargetUserType::Frequent << 48)) {
        target->add_region_ids_vec(region);
      } else {
        LOG(ERROR) << "perhaps region_ids format error, target id: "
                   << target->id() << ", region: " << region;
      }
    }
    auto region_ids_vec = target->mutable_region_ids_vec();
    std::sort(region_ids_vec->begin(), region_ids_vec->end());
    return true;
  }
};

class TargetDistrictIdsParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetDistrictIdsParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->district_ids();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t district_id;
      if (item->IntValue(&district_id) &&
          district_id <
              ((int64_t)ks::ad_server::TargetUserType::Frequent << 48)) {
        target->add_district_ids_vec(district_id);
      } else {
        LOG(ERROR) << "perhaps region format error, target id: " << target->id()
                   << ", district_id: " << district_id;
      }
    }
    auto district_ids_vec = target->mutable_district_ids_vec();
    std::sort(district_ids_vec->begin(), district_ids_vec->end());
    return true;
  }
};

class TargetAudienceParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetAudienceParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->audience();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t audience = item->IntValue(-1);
      if (audience > 0) {
        target->add_audience_vec(audience);
      }
    }
    auto audience_vec = target->mutable_audience_vec();
    std::sort(audience_vec->begin(), audience_vec->end());
    return true;
  }
};

class TargetPaidAudienceParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetPaidAudienceParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->paid_audience();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t paid_audience = item->IntValue(-1);
      if (paid_audience > 0) {
        target->add_paid_audience_vec(paid_audience);
      }
    }
    auto paid_audience_vec = target->mutable_paid_audience_vec();
    std::sort(paid_audience_vec->begin(), paid_audience_vec->end());
    return true;
  }
};

class TargetPopulationParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetPopulationParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->population();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t population = item->IntValue(-1);
      if (population > 0) {
        target->add_population_vec(population);
      }
    }
    auto population_vec = target->mutable_population_vec();
    std::sort(population_vec->begin(), population_vec->end());
    return true;
  }
};

class TargetKeywordPopulationParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetKeywordPopulationParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->keyword_population();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t keyword_population = item->IntValue(-1);
      if (keyword_population > 0) {
        target->mutable_extend_fields()->add_keyword_population(keyword_population);
      }
    }
    auto vec = target->mutable_extend_fields()->mutable_keyword_population();
    std::sort(vec->begin(), vec->end());
    return true;
  }
};

class TargetEnginePopulationHardParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetEnginePopulationHardParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->engine_population_hard();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t population = item->IntValue(-1);
      if (population > 0) {
        target->mutable_extend_fields()->add_engine_population_hard(population);
      }
    }
    return true;
  }
};

class TargetFansLevelPopulationParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetFansLevelPopulationParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->fans_level_population();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t population = item->IntValue(-1);
      if (population > 0) {
        target->mutable_extend_fields()->add_fans_level_population(population);
        target->mutable_extend_fields()->add_population(population);
      }
    }
    return true;
  }
};

class TargetFilterConvertedWechatIdParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetFilterConvertedWechatIdParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->filter_converted_wechat_id();
    if (string_value.empty())
      return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      std::string str = item->StringValue();
      uint64_t uint64_sign = base::CityHash64(str.data(), str.size());
      int64_t hash_id = *(reinterpret_cast<int64_t*>(&uint64_sign));
      target->mutable_extend_fields()->add_filter_converted_wechat_id(hash_id);
    }
    return true;
  }
};

class TargetOperatorsParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetOperatorsParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->operators();
    if (string_value.empty())
      return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t op = item->IntValue(-1);
      if (op > 0) {
        target->mutable_extend_fields()->add_operators(op);
      }
    }
    return true;
  }
};

class TargetBehaviorInterestKeyword
    : public PbParser<kuaishou::ad::tables::Target> {
 public:
  virtual ~TargetBehaviorInterestKeyword() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->behavior_interest_keyword();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t id = item->IntValue(-1);
      if (id > 0) {
        target->add_behavior_interest_keyword_vec(id);
      }
    }
    auto behavior_interest_keyword_vec =
        target->mutable_behavior_interest_keyword_vec();
    std::sort(behavior_interest_keyword_vec->begin(),
              behavior_interest_keyword_vec->end());
    return true;
  }
};

class TargetExcludePopulationParser
    : public PbParser<kuaishou::ad::tables::Target> {
 public:
  virtual ~TargetExcludePopulationParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->exclude_population();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t id = item->IntValue(-1);
      if (id > 0) {
        target->add_exclude_population_vec(id);
      }
    }
    auto exclude_population_vec = target->mutable_exclude_population_vec();
    std::sort(exclude_population_vec->begin(), exclude_population_vec->end());
    return true;
  }
};

class TargetPurchaseIntentParser
    : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetPurchaseIntentParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->purchase_intention_label();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t id = item->IntValue(-1);
      if (id > 0) {
        target->mutable_extend_fields()->add_purchase_intention_label(id);
      }
    }
    auto purchase_intention_label =
        target->mutable_extend_fields()->mutable_purchase_intention_label();
    std::sort(purchase_intention_label->begin(), purchase_intention_label->end());
    return true;
  }
};

class TargetCelebrityParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetCelebrityParser() override = default;
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->celebrity_label();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t id = item->IntValue(-1);
      if (id > 0) {
        target->mutable_extend_fields()->add_celebrity_label(id);
      }
    }
    auto celebrity_label =
        target->mutable_extend_fields()->mutable_celebrity_label();
    std::sort(celebrity_label->begin(), celebrity_label->end());
    return true;
  }
};

class TargetCorePopulationParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetCorePopulationParser() override = default;
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->core_population_label();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t id = item->IntValue(-1);
      if (id > 0) {
        target->mutable_extend_fields()->add_core_population_label(id);
      }
    }
    auto core_population_label =
        target->mutable_extend_fields()->mutable_core_population_label();
    std::sort(core_population_label->begin(), core_population_label->end());
    return true;
  }
};

class TargetEnginePopulationParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetEnginePopulationParser() override = default;
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->engine_population();
    if (!string_value.empty()) {
      Json json(StringToJson(string_value));
      for (auto item : json.array()) {
        int64_t id = item->IntValue(-1);
        if (id > 0) {
          target->mutable_extend_fields()->add_engine_population(id);
        }
      }
    }

    string_value = target->exclude_engine_population();
    if (!string_value.empty()) {
      Json json(StringToJson(string_value));
      for (auto item : json.array()) {
        int64_t id = item->IntValue(-1);
        if (id > 0) {
          target->mutable_extend_fields()->add_exclude_engine_population(id);
        }
      }
    }
    return true;
  }
};

class TargetDevicePriceParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  virtual ~TargetDevicePriceParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->device_price();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int min_age = floor(item->GetInt("min", -1) / 100.0);
      int max_age = floor(item->GetInt("max", -1) / 100.0);
      if (min_age >= 0 && min_age <= max_age) {
        // addRange adds [min, max]
        auto range_item = target->add_device_price_vec();
        range_item->set_min(min_age);
        range_item->set_max(max_age);
      }
    }
    auto device_price_vec = target->mutable_device_price_vec();
    std::sort(device_price_vec->begin(), device_price_vec->end(),
              range_item_compare);
    return true;
  }
};

class TargetDeviceBrandParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  virtual ~TargetDeviceBrandParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->device_brand();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      std::string brand_name = item->StringValue("");
      if (brand_name != "") {
        target->add_device_brand_vec(convertor(brand_name));
      }
    }
    auto device_brand_vec = target->mutable_device_brand_vec();
    std::sort(device_brand_vec->begin(), device_brand_vec->end());
    return true;
  }
};

class TargetFansStarParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  virtual ~TargetFansStarParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->fans_star();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      target->add_fans_star_vec(convertor(item->StringValue()));
    }
    auto fans_star_vec = target->mutable_fans_star_vec();
    std::sort(fans_star_vec->begin(), fans_star_vec->end());
    return true;
  }
};

class TargetPackageNameParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  virtual ~TargetPackageNameParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->package_name();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      target->add_package_name_vec(convertor(item->StringValue()));
    }
    if (target->package_name_vec_size() > 0) {
      target->set_package_name_size(1);
    } else {
      target->set_package_name_size(0);
    }
    auto package_name_vec = target->mutable_package_name_vec();
    std::sort(package_name_vec->begin(), package_name_vec->end());
    return true;
  }
};

class TargetSocialStarLabelParser
    : public PbParser<kuaishou::ad::tables::Target> {
 public:
  virtual ~TargetSocialStarLabelParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->social_star_label();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      target->add_social_star_label_vec(convertor(item->StringValue()));
    }
    auto social_star_label_vec = target->mutable_social_star_label_vec();
    std::sort(social_star_label_vec->begin(), social_star_label_vec->end());
    return true;
  }
};

class TargetAppInterestIdsParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetAppInterestIdsParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->app_interest_ids();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t id;
      if (item->IntValue(&id)) {
        target->mutable_extend_fields()->add_app_interest_ids(id);
      } else {
        LOG(ERROR) << "perhaps app_interest_ids format error, target id: "
                   << target->id() << ", id: " << id;
      }
    }
    auto app_interest_ids =
        target->mutable_extend_fields()->mutable_app_interest_ids();
    std::sort(app_interest_ids->begin(), app_interest_ids->end());
    return true;
  }
};
// end of part2

// part 3
class TargetPurchaseIntentionLabelParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  bool Parse(kuaishou::ad::tables::Target* target) override {
    if (AdKconfUtil::disablePurchaseIntentionLabelDupParser()) {
      return true;
    }
    std::string string_value = target->purchase_intention_label();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t label;
      if (item->IntValue(&label)) {
        target->mutable_extend_fields()->add_purchase_intention_label(label);
      } else {
        LOG(ERROR) << "perhaps purchase_intention_label format error, target id: "
                   << target->id() << ", label: " << label;
      }
    }
    auto purchase_intention_label =
        target->mutable_extend_fields()->mutable_purchase_intention_label();
    std::sort(purchase_intention_label->begin(), purchase_intention_label->end());
    return true;
  }
};

class TargetGenderParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string gender_str = target->gender();
    if (gender_str.empty()) return true;
    if (gender_str == "M") {
      target->mutable_extend_fields()->set_gender(kuaishou::ad::AdEnum::GENDER_MALE);
    } else if (gender_str == "F") {
      target->mutable_extend_fields()->set_gender(kuaishou::ad::AdEnum::GENDER_FEMALE);
    }
    return true;
  }
};

class TargetExcludeMediaParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->exclude_media();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t label;
      if (item->IntValue(&label)) {
        target->mutable_extend_fields()->add_exclude_media(label);
      } else {
        LOG(ERROR) << "perhaps exclude_media format error, target id: "
                   << target->id() << ", label: " << label;
      }
    }
    auto exclude_media =
        target->mutable_extend_fields()->mutable_exclude_media();
    std::sort(exclude_media->begin(), exclude_media->end());
    return true;
  }
};

class TargetMediaParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->media();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t label;
      if (item->IntValue(&label)) {
        target->mutable_extend_fields()->add_media(label);
      } else {
        LOG(ERROR) << "perhaps media format error, target id: "
                   << target->id() << ", label: " << label;
      }
    }
    auto media =
        target->mutable_extend_fields()->mutable_media();
    std::sort(media->begin(), media->end());
    return true;
  }
};

class TargetDistanceParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->distance();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    // distance 格式："[{\"lat\":26.038841,\"lng\":119.27562,\"dis\":6}]"
    for (auto item : json.array()) {
      double lon = item->GetFloat("lng", -1000);
      double lat = item->GetFloat("lat", -1000);
      // 最高 15km
      int64 dis = std::min(15000l, item->GetInt("dis", 0));

      std::vector<std::string> terms;
      if (dis <= 0 || !ks::ad_server::GetFastCoveringTerms(lat, lon, dis, &terms)) {
        LOG(WARNING) << "Invalid targeting: " << string_value;
        continue;
      }
      for (const std::string& term : terms) {
        target->mutable_extend_fields()->add_distance_s2_terms(term);
      }
      auto * new_distance_pb = target->mutable_extend_fields()->add_distance();
      new_distance_pb->set_lat(lat);
      new_distance_pb->set_lon(lon);
      new_distance_pb->set_dis(dis);
    }
    // to do: 比较函数 这个字段目前只在 粉条使用
    // auto distance = target->mutable_extend_fields()->mutable_distance();
    // std::sort(distance->begin(), distance->end());
    return true;
  }
};

class TargetIntelliExtendParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->intelli_extend();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));

    auto* intelli_extend_pb = target->mutable_extend_fields()->mutable_intelli_extend();
    if (!json.GetBoolean("isOpen", false)) {
      // Skip if intelli extend is not open.
      intelli_extend_pb->set_is_open(false);
      intelli_extend_pb->set_no_age_break(false);
      intelli_extend_pb->set_no_gender_break(false);
      intelli_extend_pb->set_no_area_break(false);
      intelli_extend_pb->set_auto_target(false);
      intelli_extend_pb->set_extend_times(0);
      return true;
    }
    intelli_extend_pb->set_is_open(true);
    intelli_extend_pb->set_extend_times(json.GetInt("extendTimes", 1));
    if (json.GetBoolean("noAgeBreak", true)) {
      intelli_extend_pb->set_no_age_break(true);
    }
    if (json.GetBoolean("noGenderBreak", true)) {
      intelli_extend_pb->set_no_gender_break(true);
    }
    if (json.GetBoolean("noAreaBreak", true)) {
      intelli_extend_pb->set_no_area_break(true);
    }

    if (json.GetBoolean("autoTarget", false)) {
      intelli_extend_pb->set_auto_target(true);
    }
    return true;
  }
};

class TargetBrandIdsParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargetBrandIdsParser() {}
  bool Parse(kuaishou::ad::tables::Target* target) override {
    std::string string_value = target->device_brand_ids();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t id;
      if (item->IntValue(&id)) {
        target->mutable_extend_fields()->add_device_brand_ids(id);
      } else {
        LOG(ERROR) << "perhaps device_brand_ids format error, target id: "
                   << target->id() << ", id: " << id;
      }
    }
    auto device_brand_ids = target->mutable_extend_fields()->mutable_device_brand_ids();
    std::sort(device_brand_ids->begin(), device_brand_ids->end());
    return true;
  }
};
// end of part 3


class TargeDefaultParser : public PbParser<kuaishou::ad::tables::Target> {
 public:
  ~TargeDefaultParser() {}
  static void ParseExtendFields(kuaishou::ad::tables::Target* target) {
    if (!target) {
      return;
    }
      // to do(liuwenlong03): 迁移完加上字段清理（依赖索引层白名单可能并不需要）
#define SET_JSON_ITEM(field, type)                             \
  if (!target->field().empty()) {                              \
    base::Json json_item(base::StringToJson(target->field())); \
    if (json_item.Is##type()) {                                \
      json_obj.set(#field, json_item);                         \
    }                                                          \
  }
    base::JsonObject json_obj;
    // age，platform。intelli_extend 这几个字段算法侧离线依赖，暂不清理
    SET_JSON_ITEM(age, Array);
    SET_JSON_ITEM(interest, Array);
    SET_JSON_ITEM(network, Array);
    SET_JSON_ITEM(platform, Object);
    SET_JSON_ITEM(region, Array);
    SET_JSON_ITEM(audience, Array);
    SET_JSON_ITEM(paid_audience, Array);
    SET_JSON_ITEM(population, Array);
    SET_JSON_ITEM(exclude_population, Array);
    SET_JSON_ITEM(device_price, Array);
    SET_JSON_ITEM(device_brand, Array);
    SET_JSON_ITEM(interest_video, Array);
    SET_JSON_ITEM(package_name, Array);
    SET_JSON_ITEM(fans_star, Array);
    SET_JSON_ITEM(business_interest, Array);
    SET_JSON_ITEM(intelli_extend, Object);
    SET_JSON_ITEM(social_star_label, Array);
    SET_JSON_ITEM(behavior_interest_keyword, Array);
    SET_JSON_ITEM(region_ids, Array);
    SET_JSON_ITEM(district_ids, Array);
    SET_JSON_ITEM(language, Array);
    SET_JSON_ITEM(page, Array);
    SET_JSON_ITEM(seed_population, Array);
    SET_JSON_ITEM(exclude_new_customer_population, Array)
    ParseStringField(target, &json_obj);
    const std::string& json_str = json_obj.ToString();
    if (!json_str.empty()) {
      google::protobuf::util::JsonParseOptions options;
      options.ignore_unknown_fields = true;
      auto status = google::protobuf::util::JsonStringToMessage(
          json_str, target->mutable_extend_fields(), options);
      if (!status.ok()) {
        ks::infra::PerfUtil::CountLogStash(
            1, "ad.index_builder", "parse_extend_fields_failed", "target");
        LOG(ERROR) << "JsonStringToMessage failed. error: "
                   << status.error_message() << ", json_str: " << json_str;
        target->clear_extend_fields();
      }
    }
  }
  static void ParseStringField(kuaishou::ad::tables::Target* target,
                               base::JsonObject* extend_json) {
    if (target == nullptr || extend_json == nullptr) {
      return;
    }
    // 解析 gender
    if (!target->gender().empty()) {
      std::vector<int64_t> vec;
      if (target->gender() == "M") {
        vec.push_back(+TargetGender::Male);
      } else if (target->gender() == "F") {
        vec.push_back(+TargetGender::Female);
      }
      if (vec.size() == 1 && vec[0] == +TargetGender::Male) {
        extend_json->set("gender", +TargetGender::Male);
      } else if (vec.size() == 1 && vec[0] == +TargetGender::Female) {
        extend_json->set("gender", +TargetGender::Female);
      } else {
        extend_json->set("gender", +TargetGender::Both);
      }
    }
    return;
  }
  bool Parse(kuaishou::ad::tables::Target* target) override {
    ParseExtendFields(target);
  #define ParserExecution(PARSER) \
    PARSER PARSER##_TEMP;\
    PARSER##_TEMP.Parse(target);
    ParserExecution(TargetInterestVideoParser);
    ParserExecution(TargetPlatformParser);
    ParserExecution(TargetInterestParser);
    ParserExecution(TargetAppInterestIdsParser);
    ParserExecution(TargetBusinessInterestParser);
    ParserExecution(TargetAgeParser);
    ParserExecution(TargetLanguageParser);
    ParserExecution(TargetNetworkParser);
    ParserExecution(TargetPageParser);
    ParserExecution(TargetRegionIdsParser);
    ParserExecution(TargetDistrictIdsParser);
    ParserExecution(TargetAudienceParser);
    ParserExecution(TargetPaidAudienceParser);
    ParserExecution(TargetPopulationParser);
    ParserExecution(TargetBehaviorInterestKeyword);
    ParserExecution(TargetExcludePopulationParser);
    ParserExecution(TargetDevicePriceParser);
    ParserExecution(TargetDeviceBrandParser);
    ParserExecution(TargetFansStarParser);
    ParserExecution(TargetPackageNameParser);
    ParserExecution(TargetSocialStarLabelParser);
    ParserExecution(TargetPurchaseIntentParser);
    ParserExecution(TargetCelebrityParser);
    ParserExecution(TargetPurchaseIntentionLabelParser);
    ParserExecution(TargetGenderParser);
    ParserExecution(TargetExcludeMediaParser);
    ParserExecution(TargetMediaParser);
    ParserExecution(TargetDistanceParser);
    ParserExecution(TargetIntelliExtendParser);
    ParserExecution(TargetBrandIdsParser);
    ParserExecution(TargetCorePopulationParser);
    ParserExecution(TargetEnginePopulationParser);
    ParserExecution(TargetKeywordPopulationParser);
    ParserExecution(TargetEnginePopulationHardParser);
    ParserExecution(TargetFansLevelPopulationParser);
    ParserExecution(TargetFilterConvertedWechatIdParser);
    ParserExecution(TargetOperatorsParser);
#undef ParserExecution
    return true;
  }
};

// default 优先级最高
PARSER_REGISTER_WITH_PRIORITY(TargeDefaultParser, google::protobuf::Message,
                              kuaishou::ad::tables::Target, 101)
}  // namespace index_builder
}  // namespace ks
