#pragma once
#include <cstddef>
#include <string>
#include <vector>
#include <map>
#include <algorithm>
#include <unordered_map>

#include "absl/strings/str_split.h"
#include "base/encoding/base64.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

static void ParseOcpxActionTypeConstraint(kuaishou::ad::tables::AdAuaxSimpleProject* proto) {
  if (proto == nullptr) {
    return;
  }

  base::Json json(base::StringToJson(proto->ocpx_action_type_constraint()));
  if (!json.IsArray()) {
    LOG_EVERY_N(ERROR, 100000)
        << "format error, AdAuaxSimpleProject not valid json array string. string_value="
        << proto->ocpx_action_type_constraint();
    return;
  }

  auto* parse_fields = proto->mutable_parse_fields();
  parse_fields->clear_ocpx_action_type_constraint_type();
  parse_fields->clear_ocpx_action_type_constraint_value();

  for (const auto& item : json.array()) {
    parse_fields->add_ocpx_action_type_constraint_type(item->GetInt("ocpxActionType", 0));
    parse_fields->add_ocpx_action_type_constraint_value(item->GetNumber("value", 0.0));
  }
  LOG_FIRST_N(INFO, 100) << "AdAuaxSimpleProject ParseOcpxActionTypeConstraint "
      << proto->ShortDebugString();
  proto->clear_ocpx_action_type_constraint();
}

class AdAuaxSimpleProjectParser : public PbParser<kuaishou::ad::tables::AdAuaxSimpleProject> {
 public:
  virtual ~AdAuaxSimpleProjectParser() {}
  bool Parse(kuaishou::ad::tables::AdAuaxSimpleProject* proto) override {
    ParseOcpxActionTypeConstraint(proto);
    return true;
  }
};

PARSER_REGISTER(AdAuaxSimpleProjectParser,
    google::protobuf::Message,
    kuaishou::ad::tables::AdAuaxSimpleProject);

}  // namespace index_builder
}  // namespace ks

