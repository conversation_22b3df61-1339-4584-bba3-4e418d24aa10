#pragma once
#include <set>
#include <string>
#include <vector>
#include "serving_base/jansson/json.h"
#include "absl/strings/numbers.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"

namespace ks {
namespace index_builder {

class RiskLimitInitiativeParser : public PbParser<kuaishou::ad::tables::RiskLimitInitiative> {
 public:
  ~RiskLimitInitiativeParser() override {}

  bool Parse(kuaishou::ad::tables::RiskLimitInitiative *risk_limit) override {
    int64_t value = 0;
    static std::set<kuaishou::ad::AdEnum_LimitBizType> int_type_set{
        kuaishou::ad::AdEnum_LimitBizType_LIMIT_TYPE_USER_ID,
        kuaishou::ad::AdEnum_LimitBizType_LIMIT_TYPE_LIVE_ID};
    if (int_type_set.count(risk_limit->biz_type()) != 0) {
      if (!absl::SimpleAtoi(risk_limit->biz_id(), &value)) {
        LOG(INFO) << " RiskLimitInitiativeParser Parse userid error" << risk_limit->ShortDebugString();
        return true;
      }
    }
    risk_limit->set_biz_id_real(value);
    return true;
  }
};
PARSER_REGISTER(RiskLimitInitiativeParser, google::protobuf::Message,
                kuaishou::ad::tables::RiskLimitInitiative)
}  // namespace index_builder
}  // namespace ks
