#pragma once
#include <algorithm>
#include <string>
#include <vector>
#include "ks/util/json.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/target_parser.h"
namespace ks {
namespace index_builder {

class PhotosInfosParser
    : public PbParser<kuaishou::ad::tables::AdDspHostingProjectFiction> {
 public:
  virtual ~PhotosInfosParser() {}
  bool Parse(kuaishou::ad::tables::AdDspHostingProjectFiction* project_fiction)
      override {
    std::string string_value = project_fiction->photo_infos();
    if (string_value.empty()) return true;

    auto* extend_fields = project_fiction->mutable_extend_fields();
    Json json(StringToJson(string_value));
    google::protobuf::util::JsonParseOptions options;
    options.ignore_unknown_fields = true;
    extend_fields->clear_photo_infos();
    for (auto item : json.array()) {
      auto photo_info = extend_fields->add_photo_infos();
      std::string json_str = base::JsonToString(item->get());
      auto status = google::protobuf::util::JsonStringToMessage(
          json_str, photo_info, options);
      if (!status.ok()) {
        LOG_EVERY_N(WARNING, 1000) << json_str;
      }
    }
    return true;
  }
};

// DiverseDataParser 针对 optional 结构 略过
class CaptionsParser
    : public PbParser<kuaishou::ad::tables::AdDspHostingProjectFiction> {
 public:
  virtual ~CaptionsParser() {}
  bool Parse(kuaishou::ad::tables::AdDspHostingProjectFiction* project_fiction)
      override {
    std::string string_value = project_fiction->captions();
    if (string_value.empty()) return true;
    auto* extend_fields = project_fiction->mutable_extend_fields();
    Json json(StringToJson(string_value));
    extend_fields->clear_captions();
    for (auto item : json.array()) {
      std::string value = item->StringValue();
      extend_fields->add_captions(item->StringValue());
    }
    return true;
  }
};

PARSER_REGISTER(PhotosInfosParser, google::protobuf::Message,
                kuaishou::ad::tables::AdDspHostingProjectFiction)
PARSER_REGISTER(CaptionsParser, google::protobuf::Message,
                kuaishou::ad::tables::AdDspHostingProjectFiction)
}  // namespace index_builder
}  // namespace ks
