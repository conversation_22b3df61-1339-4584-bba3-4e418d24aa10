#pragma once
#include <algorithm>
#include <map>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "absl/strings/str_split.h"
#include "base/encoding/base64.h"
#include "ks/base/abtest/metrics/abtest_metric.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

static void ParseExtendFields(kuaishou::ad::tables::WTAuthor* proto) {
  if (proto == nullptr) { return; }
  // 命中 author 分流实验打标
  {
    static const std::unordered_set<std::string> kWorldList = {"w_n_kuaishou_apps_sid_18"};
    ExperimentInfo exp_info = abtest::GetExperimentInfo(
        proto->id(), std::to_string(proto->id()), std::to_string(proto->id()), kWorldList);
    for (const auto& kv : exp_info) {
      if (kv.second.experimentId.empty() || kv.second.groupId.empty())
        continue;
      auto* hit_info = proto->mutable_extend_fields()->add_author_id_abtest_hit();
      hit_info->set_experiment_id(kv.second.experimentId);
      hit_info->set_group_id(kv.second.groupId);
    }
    LOG_EVERY_N(INFO, 100000) << "photo_id hit exp: " << proto->ShortDebugString();
  }
  {
    Json json{StringToJson("[]")};
    if ((proto->shop_dist_type() == 1 && proto->shop_score_v3() >= 4.0) ||
        (proto->shop_dist_type() == 2 && proto->master_score() >= 4.0)) {
      json.append(1);
    } else {
      json.append(0);
    }
    json.append(0);
    json.append(0);
    json.append(0);
    if (proto->shop_score() >= 4.04 || proto->shop_quality_score() >= 4.0) {
      json.append(1);
    } else {
      json.append(0);
    }
    proto->set_author_admit(json.ToString());
  }
  {
    auto string_value = proto->author_admit();
    if (!string_value.empty()) {
      Json json(StringToJson(string_value));
      if (json.IsArray()) {
        int i = 1;
        for (auto item : json.array()) {
          int64_t v = item->IntValue(-1);
          if (v == 1) {
            proto->mutable_extend_fields()->add_author_admit(i);
          }
          i++;
        }
      }
    }
  }
  {
    int64_t adp_last_active_time = proto->adp_last_active_time();
    bool status = (base::GetTimestamp() / 1000 - adp_last_active_time) > 7*86400*1000;
    if (status) {
      proto->set_adp_exeunt_status(1);
    } else {
      proto->set_adp_exeunt_status(0);
    }
  }
}
static void ParseStringFields(kuaishou::ad::tables::WTAuthor* proto) {
  if (proto == nullptr) { return; }
  auto* parse_fields = proto->mutable_parse_fields();
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, shop_ecology_score, ",");
  SIMPLE_PARSE_FLOAT_USE_SPLLIT(proto, pre_target_cost_bucket_ratio, ",");
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, index_bucket_string, ",");
}

class WTAuthorParser : public PbParser<kuaishou::ad::tables::WTAuthor> {
 public:
  virtual ~WTAuthorParser() {}
  bool Parse(kuaishou::ad::tables::WTAuthor* proto) override {
    ParseExtendFields(proto);
    ParseStringFields(proto);
    return true;
  }
};

PARSER_REGISTER(WTAuthorParser, google::protobuf::Message, kuaishou::ad::tables::WTAuthor);

}  // namespace index_builder
}  // namespace ks
