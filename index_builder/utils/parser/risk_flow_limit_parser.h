#pragma once
#include <set>
#include <string>
#include <vector>
#include "serving_base/jansson/json.h"
#include "absl/strings/numbers.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"

namespace ks {
namespace index_builder {

class AdRiskFlowLimitParser : public PbParser<kuaishou::ad::tables::AdRiskFlowLimit> {
 public:
  ~AdRiskFlowLimitParser() override {}
  static int64_t GetCityProductId(const std::string &product_name) {
    uint64_t uint64_sign = base::CityHash64(product_name.data(), product_name.size());
    return *(reinterpret_cast<int64_t *>(&uint64_sign));
  }
  bool Parse(kuaishou::ad::tables::AdRiskFlowLimit *risk_limit) override {
    int64_t value = 0;
    static std::set<kuaishou::ad::AdEnum_LimitBizType> int_type_set{
        kuaishou::ad::AdEnum::LIMIT_TYPE_USER_ID,
        kuaishou::ad::AdEnum::LIMIT_TYPE_LIVE_ID,
        kuaishou::ad::AdEnum::LIMIT_TYPE_INDUSTRY_ID,
        kuaishou::ad::AdEnum::LIMIT_TYPE_ACCOUNT_ID,
        kuaishou::ad::AdEnum::LIMIT_TYPE_UNIT_ID,
        kuaishou::ad::AdEnum::LIMIT_TYPE_PHOTO_ID,
        kuaishou::ad::AdEnum::LIMIT_TYPE_CREATIVE_ID,
        kuaishou::ad::AdEnum::LIMIT_TYPE_COVER_ID,
        kuaishou::ad::AdEnum::LIMIT_TYPE_APP_ID,
        kuaishou::ad::AdEnum::LIMIT_TYPE_LIVE_USER_ID,
        kuaishou::ad::AdEnum::LIMIT_TYPE_GLOBAL_APP_ID,
        kuaishou::ad::AdEnum::LIMIT_TYPE_APP_PACKAGE_ID,
        kuaishou::ad::AdEnum::LIMIT_TYPE_RISK_LABEL,
        kuaishou::ad::AdEnum::LIMIT_TYPE_ITEM_ID,
        kuaishou::ad::AdEnum::LIMIT_TYPE_FANS_USER_ID};
    static std::set<kuaishou::ad::AdEnum_LimitBizType> string_type_set{
        kuaishou::ad::AdEnum::LIMIT_TYPE_PRODUCT_NAME,
        kuaishou::ad::AdEnum::LIMIT_TYPE_LICENCE_NUM,
        kuaishou::ad::AdEnum::LIMIT_TYPE_MINI_APP_ID,
    };
    if (int_type_set.count(risk_limit->biz_type()) != 0) {
      if (!absl::SimpleAtoi(risk_limit->biz_id(), &value)) {
        LOG(INFO) << " AdRiskFlowLimitParser Parse  "
                  << kuaishou::ad::AdEnum_LimitBizType_Name(risk_limit->biz_type()) << "  error"
                  << risk_limit->ShortDebugString();
        return true;
      }
    }
    if (string_type_set.count(risk_limit->biz_type()) != 0) {
      value = convertor(risk_limit->biz_id());
      LOG(INFO) << " AdRiskFlowLimitParser Parse  "
                << kuaishou::ad::AdEnum_LimitBizType_Name(risk_limit->biz_type()) << "  error"
                << risk_limit->ShortDebugString();
    }
    risk_limit->mutable_extend_fields()->set_biz_id(value);

    auto array_json2_field = [](const std::string &str, google::protobuf::RepeatedField<int64_t> *field) {
      Json json(StringToJson(str));
      if (!json.IsArray()) {
        LOG(ERROR) << "format error, not valid json string. string_value=" << str;
        return;
      }
      if (json.size() == 0) {
        return;
      }
      for (auto iter = json.array_begin(); iter != json.array_end(); ++iter) {
        auto &inner_json = *iter;
        if (!inner_json->IsInteger()) {
          LOG(ERROR) << "format error, not valid json string. string_value=" << str;
          return;
        }
        int64 num = 0;
        if (!inner_json->IntValue(&num)) {
          LOG(ERROR) << "format error, not valid json string. string_value=" << str;
          return;
        }
        field->Add(num);
      }
    };
    array_json2_field(risk_limit->limit_command_id(),
                      risk_limit->mutable_extend_fields()->mutable_limit_command_id());
    return true;
  }
};
PARSER_REGISTER(AdRiskFlowLimitParser, google::protobuf::Message,
                kuaishou::ad::tables::AdRiskFlowLimit)
}  // namespace index_builder
}  // namespace ks
