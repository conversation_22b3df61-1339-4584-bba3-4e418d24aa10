#pragma once
#include <string>
#include <vector>
#include <algorithm>
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/ad_index/index/utils/public.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

class AdAppReleaseParser : public PbParser<kuaishou::ad::tables::AdAppRelease> {
 public:
  virtual ~AdAppReleaseParser() {}
  bool Parse(kuaishou::ad::tables::AdAppRelease* table) override {
    table->mutable_extend_fields()->set_app_privacy_hash(hash_convertor(table->app_privacy_url()));
    table->mutable_extend_fields()->set_real_app_version_hash(hash_convertor(table->real_app_version()));
    table->mutable_extend_fields()->set_package_name_id(convertor(table->package_name()));
    table->mutable_extend_fields()->set_sys_package_name_id(convertor(table->sys_package_name()));
    auto permission_information = JsonStrToRepeatedInt(table->permission_information());
    table->mutable_extend_fields()->set_has_permission_information(permission_information.size() != 0);
    RepeatedAssign(table->mutable_extend_fields()->mutable_permission_information(), permission_information);
    return true;
  }
};

PARSER_REGISTER_WITH_PRIORITY(AdAppReleaseParser, google::protobuf::Message,
                              kuaishou::ad::tables::AdAppRelease, 101)

}  // namespace index_builder
}  // namespace ks
