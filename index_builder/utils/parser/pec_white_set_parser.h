#pragma once
#include <string>
#include <vector>
#include <map>
#include <algorithm>

#include "base/common/basic_types.h"
#include "base/hash_function/city.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "absl/strings/str_split.h"
#include "absl/strings/numbers.h"
namespace ks {
namespace index_builder {

class PecWhiteSetParser : public PbParser<kuaishou::ad::tables::PecWhiteSet> {
 public:
  virtual ~PecWhiteSetParser() {}
  static int64_t GetCityProductId(const std::string &product_name) {
    uint64_t uint64_sign = base::CityHash64(product_name.data(), product_name.size());
    return *(reinterpret_cast<int64_t *>(&uint64_sign));
  }
  static void ParseExtendFields(kuaishou::ad::tables::PecWhiteSet *pec_white_set) {
    auto *extend_fields = pec_white_set->mutable_extend_fields();
    if (!pec_white_set->account_ids().empty()) {
      extend_fields->clear_account_ids();
      auto elems = absl::StrSplit(pec_white_set->account_ids(), ",", absl::SkipEmpty());
      for (const auto &elem : elems) {
        int64_t id = 0;
        if (absl::SimpleAtoi(elem, &id)) {
          extend_fields->add_account_ids(id);
        }
      }
      pec_white_set->clear_account_ids();
    }
    if (!pec_white_set->campaign_ids().empty()) {
      extend_fields->clear_campaign_ids();
      auto elems = absl::StrSplit(pec_white_set->campaign_ids(), ",", absl::SkipEmpty());
      for (const auto &elem : elems) {
        int64_t id = 0;
        if (absl::SimpleAtoi(elem, &id)) {
          extend_fields->add_campaign_ids(id);
        }
      }
      pec_white_set->clear_campaign_ids();
    }
    if (!pec_white_set->scene().empty()) {
      extend_fields->clear_scene_ids();
      auto elems = absl::StrSplit(pec_white_set->scene(), ",", absl::SkipEmpty());
      for (const auto &elem : elems) {
        int32_t id = 0;
        if (absl::SimpleAtoi(elem, &id)) {
          extend_fields->add_scene_ids(id);
        }
      }
      pec_white_set->clear_scene();
    }
    if (!pec_white_set->product_names().empty()) {
      extend_fields->clear_product_ids();
      auto elems = absl::StrSplit(pec_white_set->product_names(), ",", absl::SkipEmpty());
      for (const auto& elem : elems) {
        int32_t id = 0;
        if (!elem.empty()) {
          extend_fields->add_product_ids(GetCityProductId(std::string(elem)));
        }
      }
    }
    if (!pec_white_set->campaign_types().empty()) {
      extend_fields->clear_allowed_campaign_types();
      auto elems = absl::StrSplit(pec_white_set->campaign_types(), ",", absl::SkipEmpty());
      for (const auto& elem : elems) {
        int32_t id = 0;
        if (absl::SimpleAtoi(elem, &id)) {
          extend_fields->add_allowed_campaign_types(id);
        }
      }
      pec_white_set->clear_campaign_types();
    }
    return;
  }
  bool Parse(kuaishou::ad::tables::PecWhiteSet *pec_white_set) override {
    if (!pec_white_set->product_name().empty()) {
      pec_white_set->mutable_extend_fields()->set_product_id(GetCityProductId(pec_white_set->product_name()));
    }

    ParseExtendFields(pec_white_set);
    return true;
  }
};
PARSER_REGISTER(PecWhiteSetParser, google::protobuf::Message, kuaishou::ad::tables::PecWhiteSet)
}  // namespace index_builder
}  // namespace ks
