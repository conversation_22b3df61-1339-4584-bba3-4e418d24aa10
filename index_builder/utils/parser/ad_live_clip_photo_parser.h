#pragma once
#include <string>
#include <vector>
#include <map>
#include <algorithm>
#include <unordered_map>

#include "absl/strings/str_split.h"
#include "base/encoding/base64.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

static void ParseStringFields(kuaishou::ad::tables::AdLiveClipPhotoInfo* proto) {
  if (proto == nullptr) { return; }
  auto* parse_fields = proto->mutable_parse_fields();

  const auto& clip_tags = proto->clip_tags();
  if (!clip_tags.empty()) {
    std::vector<absl::string_view> tokens = absl::StrSplit(clip_tags, ",", absl::SkipEmpty());
    if (tokens.empty()) {
      return;
    }
    for (const auto& token : tokens) {
      int32_t clip_tag = 0;
      if (!absl::SimpleAtoi(token, &clip_tag)) {
        continue;
      }
      parse_fields->add_clip_tags(clip_tag);
    }
    proto->clear_clip_tags();
    LOG_FIRST_N(INFO, 100) << parse_fields->ShortDebugString();
  }
}

static void ParseStringFields(ad::build_service::TableRow* row) {
  if (row == nullptr) { return; }
  const auto& clip_tags = row->GetValue<std::string>("clip_tags");
  if (clip_tags.empty()) { return; }
  std::vector<absl::string_view> tokens = absl::StrSplit(clip_tags, ",", absl::SkipEmpty());
  if (tokens.empty()) { return; }
  std::vector<int32_t> clip_tag_v;
  for (const auto& token : tokens) {
    int32_t clip_tag = 0;
    if (!absl::SimpleAtoi(token, &clip_tag)) { continue; }
    clip_tag_v.emplace_back(clip_tag);
  }
  if (clip_tag_v.empty()) { return; }
  row->SetValue("parse_fields__clip_tags", clip_tag_v);
  LOG_FIRST_N(INFO, 100) << row->ShortDebugString();
}

class AdLiveClipPhotoParser : public PbParser<kuaishou::ad::tables::AdLiveClipPhotoInfo> {
 public:
  virtual ~AdLiveClipPhotoParser() {}
  bool Parse(kuaishou::ad::tables::AdLiveClipPhotoInfo* proto) override {
    ParseStringFields(proto);
    return true;
  }
  bool ParseTableRow(ad::build_service::TableRow* row) override {
    ParseStringFields(row);
    return true;
  }
};

PARSER_REGISTER(AdLiveClipPhotoParser, google::protobuf::Message, kuaishou::ad::tables::AdLiveClipPhotoInfo);

}  // namespace index_builder
}  // namespace ks
