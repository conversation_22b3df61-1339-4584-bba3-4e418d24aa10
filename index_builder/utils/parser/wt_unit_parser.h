#pragma once
#include <string>
#include <vector>
#include <map>
#include <algorithm>
#include <unordered_map>

#include "absl/strings/str_split.h"
#include "base/encoding/base64.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"
#include "teams/ad/index_builder/utils/parser/unit_parser.h"

namespace ks {
namespace index_builder {

static void ParseExtendFields(kuaishou::ad::tables::WTUnit* proto) {
  if (proto == nullptr) { return; }
}
static void ParseStringFields(kuaishou::ad::tables::WTUnit* proto) {
  if (proto == nullptr) { return; }
  if (AdKconfUtil::enableWTUnitTargetExpandPopulationParse()) {
    auto* parse_fields = proto->mutable_parse_fields();
    SIMPLE_PARSE_INT64_USE_SPLLIT(proto, target_expand_population, ",");
  }
}

static void ParseUnitProto(kuaishou::ad::tables::Unit* proto) {
  if (proto == nullptr) { return; }
  static UnitDefaultParser proto_parser;
  proto_parser.Parse(proto);
  return;
}

class WTUnitParser : public PbParser<kuaishou::ad::tables::WTUnit> {
 public:
  virtual ~WTUnitParser() {}
  bool Parse(kuaishou::ad::tables::WTUnit* proto) override {
    PARSE_BINARY_STRING_TO_MESSAGE(proto, unit);
    // ParseUnitProto(proto->mutable_unit());
    ParseExtendFields(proto);
    ParseStringFields(proto);
    return true;
  }
};

PARSER_REGISTER(WTUnitParser, google::protobuf::Message, kuaishou::ad::tables::WTUnit);

}  // namespace index_builder
}  // namespace ks
