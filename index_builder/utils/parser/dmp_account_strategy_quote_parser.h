#pragma once
#include <cstddef>
#include <set>
#include <string>
#include <vector>
#include <map>
#include <algorithm>
#include <unordered_map>

#include "absl/strings/str_split.h"
#include "base/encoding/base64.h"
#include "base/hash_function/city.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

static void ParseStringFields(kuaishou::ad::tables::AdDmpAccountStrategyQuote* proto) {
  if (proto == nullptr) { return; }
  auto* parse_field =  proto->mutable_parse_field();
  // 解析 quote_id_list
  const std::string& quote_id_list = proto->quote_id_list();
  if (quote_id_list.empty()) {
    return;
  }
  <PERSON><PERSON> json(StringToJson(quote_id_list));
  // 数据格式："[1,2,3,4]"
  int64_t quote_id = 0;
  for (auto item : json.array()) {
    if (item->IntValue(&quote_id)) {
      parse_field->add_quote_id_list(quote_id);
    }
  }
  proto->clear_quote_id_list();
  LOG_FIRST_N(INFO, 100) << parse_field->ShortDebugString();
}


class AdDmpAccountStrategyQuoteParser : public PbParser<kuaishou::ad::tables::AdDmpAccountStrategyQuote> {
 public:
  virtual ~AdDmpAccountStrategyQuoteParser() {}
  bool Parse(kuaishou::ad::tables::AdDmpAccountStrategyQuote* proto) override {
    ParseStringFields(proto);
    return true;
  }
};

PARSER_REGISTER(AdDmpAccountStrategyQuoteParser, google::protobuf::Message,
                kuaishou::ad::tables::AdDmpAccountStrategyQuote);

}  // namespace index_builder
}  // namespace ks
