#pragma once

#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_index/framework/core/data_parser.h"

namespace ks {
namespace index_builder {
template <typename TData, kuaishou::ad::AdEnum::AdInstanceType type>
void AdInstanceParse(const kuaishou::ad::AdInstance& ad, TData& data) {  // NOLINT
  static ks::ad_base::PbDataParser<kuaishou::ad::AdInstance,
                                   kuaishou::ad::AdEnum::AdInstanceType, TData>
      inner_paser(type);
  inner_paser.Parse(ad, data);
}
}  // namespace index_builder
}  // namespace ks
