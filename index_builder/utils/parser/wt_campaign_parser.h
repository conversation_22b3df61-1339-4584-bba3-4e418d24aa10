#pragma once
#include <string>
#include <vector>
#include <map>
#include <algorithm>
#include <unordered_map>

#include "absl/strings/str_split.h"
#include "base/encoding/base64.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

static void ParseExtendFields(kuaishou::ad::tables::WTCampaign* proto) {
  if (proto == nullptr) { return; }
}
static void ParseStringFields(kuaishou::ad::tables::WTCampaign* proto) {
  if (proto == nullptr) { return; }
}

class WTCampaignParser : public PbParser<kuaishou::ad::tables::WTCampaign> {
 public:
  virtual ~WTCampaignParser() {}
  bool Parse(kuaishou::ad::tables::WTCampaign* proto) override {
    ParseExtendFields(proto);
    ParseStringFields(proto);
    return true;
  }
};

PARSER_REGISTER(WTCampaignParser, google::protobuf::Message, kuaishou::ad::tables::WTCampaign);

}  // namespace index_builder
}  // namespace ks
