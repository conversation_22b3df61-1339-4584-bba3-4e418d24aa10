#pragma once

#include <string>
#include <vector>
#include <algorithm>
#include <unordered_set>

#include "absl/strings/numbers.h"
#include "perfutil/perfutil.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/index_builder/utils/kconf.h"

namespace ks {
namespace index_builder {

static const std::unordered_set<int32_t> AD_COMPONENT_STYLE_SET{100, 103};
using ks::infra::PerfUtil;

class MatrixStyleMaterialParser
  : public PbParser<kuaishou::ad::tables::AdMatrixStyleMaterial> {
 public:
  virtual ~MatrixStyleMaterialParser() {}
  bool Parse(kuaishou::ad::tables::AdMatrixStyleMaterial* style) override {
    ParseAdMaterial(style);
    ParseAdStyle(style);
    ParseAdComponentStyle(style);
    ParseAdCover(style);
    return true;
  }

  bool ParseAdStyle(kuaishou::ad::tables::AdMatrixStyleMaterial* style) {
    if (!style || ks::index_builder::AdKconfUtil::adMatrixStyleTypeSet()->count(style->type()) == 0) {
      return false;
    }
    base::Json style_content(base::StringToJson(style->content()));
    if (!style_content.IsObject()) {
      PerfUtil::CountLogStash(1, "ad.index_builder", "ad_style.matrix_style",
                        "content_error", std::to_string(style->type()));
      return false;
    }
    auto* parse_field = style->mutable_parse_field();
    // 样式组件化相关字段
    auto component_ids = style_content.Get("componentIds");
    if (!component_ids) {
      component_ids = style_content.Get("sortComponentIds");
    }
    if (component_ids && component_ids->IsArray()) {
      for (auto* component_id : component_ids->array()) {
        int64 value = component_id->IntValue(-1);
        if (value >= 0) {
          parse_field->add_component_ids(value);
        }
      }
    }
    auto* multi_sort_component_ids = style_content.Get("multiSortComponentIds");
    if (multi_sort_component_ids && multi_sort_component_ids->IsArray()) {
      for (auto* multi_component_ids : multi_sort_component_ids->array()) {
        if (multi_component_ids && multi_component_ids->IsArray()) {
          auto* component_id_list = parse_field->add_multi_sort_component_ids();
          for (auto* component_id : multi_component_ids->array()) {
            int64 value = component_id->IntValue(-1);
            if (value >= 0) {
              component_id_list->add_value(value);
            }
          }
        }
      }
    }
    return true;
  }

  bool ParseAdMaterial(kuaishou::ad::tables::AdMatrixStyleMaterial* style) {
    if (!style || ks::index_builder::AdKconfUtil::adMatrixTypeMaterialSet()->count(style->type()) == 0) {
      return false;
    }
    base::Json style_content(base::StringToJson(style->content()));
    if (!style_content.IsObject()) {
      PerfUtil::CountLogStash(1, "ad.index_builder", "ad_style.matrix_style",
                        "content_error", std::to_string(style->type()));
      return false;
    }
    do {
      auto* parse_field = style->mutable_parse_field();
      auto material_type = kuaishou::ad::AdEnum_CreativeMaterialType(
          style_content.GetInt("materialType", 0));
      if (parse_field) {
        // 共用字段
        parse_field->set_material_type(material_type);
        parse_field->set_rule_id(style_content.GetInt("ruleId", 0));
        parse_field->set_template_id(style_content.GetInt("templateId", 0));
        parse_field->set_cover_height(style_content.GetInt("coverHeight", 0));
        parse_field->set_cover_width(style_content.GetInt("coverWidth", 0));
        parse_field->set_exp_tag(style_content.GetInt("materialExpTag", 0));
        parse_field->set_conversion_info_json(style_content.GetString("conversionInfoJson", ""));
        parse_field->set_site_id(style_content.GetInt("siteId", 0));
        auto* tags = style_content.Get("tags");
        if (tags && tags->IsArray()) {
          for (auto* tag : tags->array()) {
            int32 value = tag->IntValue(-1);
            if (value >= 0) {
              parse_field->add_tags(value);
            }
          }
        }
        // 视频素材
        if (material_type == kuaishou::ad::AdEnum::VERTICAL_SCREEN ||
            material_type == kuaishou::ad::AdEnum::HORIZONTAL_SCREEN) {
          parse_field->set_duration(style_content.GetInt("duration", 0));
          parse_field->set_photo_id(style_content.GetInt("adapterPhotoId", 0));
          parse_field->set_video_url(style_content.GetString("videoUrl", ""));
          parse_field->set_cover_url(style_content.GetString("coverUrl", ""));
          parse_field->set_video_bytes(style_content.GetInt("videoBytes", 0));
        }
        // 图片素材
        if (material_type == kuaishou::ad::AdEnum::VERTICAL_IMAGE ||
            material_type == kuaishou::ad::AdEnum::HORIZONTAL_IMAGE ||
            material_type == kuaishou::ad::AdEnum::BANNER_IMAGE ||
            material_type == kuaishou::ad::AdEnum::HORIZONTAL_SMALL_IMAGE) {
          parse_field->set_material_url(
              style_content.GetString("materialUrl", ""));
          parse_field->set_compress_material_url(
              style_content.GetString("compressMaterialUrl", ""));
          parse_field->set_phone_specific_url(
              style_content.GetString("phoneSpecificUrl", ""));
        }
        // 竖版图片标准宽高素材
        if (material_type == kuaishou::ad::AdEnum::VERTICAL_IMAGE) {
          parse_field->set_cover_standard(
              style_content.GetString("coverStandard", ""));
          parse_field->set_compress_cover_standard(
              style_content.GetString("compressCoverStandard", ""));
          parse_field->mutable_ext_material_info()->set_ext_cover_url(
              style_content.GetString("extCoverUrl", ""));
          parse_field->mutable_ext_material_info()->set_ext_cover_height(
              style_content.GetInt("extCoverHeight", 0));
          parse_field->mutable_ext_material_info()->set_ext_cover_width(
              style_content.GetInt("extCoverWidth", 0));
        }
        // 多图素材
        if (material_type == kuaishou::ad::AdEnum::HORIZONTAL_SMALL_IMAGE_GROUP) {
          auto pics = style_content.Get("groupImage");
          if (!pics || !pics->IsArray()) {
            break;
          }
          for (auto* item : pics->array()) {
            if (!item || !item->IsObject()) {
              continue;
            }
            auto* pic_pb = parse_field->add_picture_group();
            pic_pb->set_material_url(item->GetString("materialUrl", ""));
            pic_pb->set_compress_material_url(item->GetString("compressMaterialUrl", ""));
          }
        }
        // DPA 特殊逻辑
        if (style->type() == 16) {
          parse_field->set_dpa_photo_id(style_content.GetInt("photoId", 0));
        }
        if (style->type() == 2) {
          parse_field->set_card_type(style_content.GetInt("playCardType", 0));
        }
      }
    } while (false);

    return true;
  }

  bool ParseAdComponentStyle(kuaishou::ad::tables::AdMatrixStyleMaterial* style) {
    if (!style || AD_COMPONENT_STYLE_SET.count(style->type()) == 0) {
      return false;
    }
    auto* component_style = style->mutable_parse_field()->mutable_component_style();
    if (!component_style) { return false; }
    auto* forward_label = component_style->mutable_forward_label();
    auto* reverse_label = component_style->mutable_reverse_label();
    if (!forward_label || !reverse_label) { return false; }

    base::Json raw_forward_label(base::StringToJson(style->forward_label()));
    base::Json raw_reverse_label(base::StringToJson(style->reverse_label()));

    auto parse_label = [&](auto& label_json, auto* label_pb) -> bool {
      if (!label_json.IsObject() || !label_pb) {
        return false;
      }
      auto* first_industry = label_json.Get("industries");
      if (first_industry && first_industry->IsArray()) {
        for (auto* item : first_industry->array()) {
          std::string item_str;
          int64_t item_int;
          if (item->StringValue(&item_str) &&
              absl::SimpleAtoi(item_str, &item_int)) {
            label_pb->add_first_industry(item_int);
          }
        }
      }

      auto* ocpc_action_type = label_json.Get("optimizes");
      if (ocpc_action_type && ocpc_action_type->IsArray()) {
        for (auto* item : ocpc_action_type->array()) {
          std::string item_str;
          kuaishou::ad::AdActionType item_int;
          if (item->StringValue(&item_str) &&
              kuaishou::ad::AdActionType_Parse(item_str, &item_int)) {
            label_pb->add_ocpc_action_type(static_cast<int64_t>(item_int));
          }
        }
      }

      label_pb->set_sdk_version(label_json.GetString("sdkVersions", ""));
      label_pb->set_color_theme(label_json.GetString("theme", ""));
      return true;
    };

    parse_label(raw_forward_label, forward_label);
    parse_label(raw_reverse_label, reverse_label);

    return true;
  }

  bool ParseAdCover(kuaishou::ad::tables::AdMatrixStyleMaterial* style) {
    if (!style || ks::index_builder::AdKconfUtil::adMatrixCoverTypeSet()->count(style->type()) == 0) {
      return false;
    }
    base::Json style_content(base::StringToJson(style->content()));
    if (!style_content.IsObject()) {
      PerfUtil::CountLogStash(1, "ad.index_builder", "ad_style.matrix_style",
                        "content_error", std::to_string(style->type()));
      return false;
    }
    auto* images = style_content.Get("images");
    if (!images || !images->IsArray()) {
      PerfUtil::CountLogStash(1, "ad.index_builder", "ad_style.matrix_style",
                        "images_error", std::to_string(style->type()));
      return false;
    }
    auto* cover_list = style->mutable_parse_field()->mutable_cover_list();
    if (cover_list) {
      cover_list->set_photo_id(style_content.GetInt("photoId", 0));
      cover_list->set_item_id(style_content.GetInt("itemId", 0));
      for (auto* image : images->array()) {
        if (!image || !image->IsObject()) {
          continue;
        }
        auto* cover = cover_list->add_images();
        if (cover) {
          cover->set_cover_id(image->GetInt("coverId", 0));
          cover->set_path(image->GetString("path", ""));
          cover->set_type(image->GetString("type", ""));
        }
      }
    }
    PerfUtil::CountLogStash(1, "ad.index_builder", "ad_style.matrix_style",
                        "cover_success", std::to_string(style->type()));
    return true;
  }
};
PARSER_REGISTER(MatrixStyleMaterialParser, google::protobuf::Message,
                kuaishou::ad::tables::AdMatrixStyleMaterial)
}  // namespace index_builder
}  // namespace ks
