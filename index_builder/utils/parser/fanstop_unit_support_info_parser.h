#pragma once

#include <cstddef>
#include <string>
#include <vector>
#include <algorithm>

#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/kconf.h"
namespace ks {
namespace index_builder {

using kuaishou::ad::tables::UnitFanstopSupportInfo;


class UnitFansTopSupportInfoParser : public PbParser<UnitFanstopSupportInfo> {
 public:
  virtual ~UnitFansTopSupportInfoParser() {}
  static void ParseOnlineExtAttr(UnitFanstopSupportInfo* unit) {
    if (AdKconfUtil::enableUnitFansSupportInfoOnlineExtParse()) {
      if (unit == nullptr) return;
      std::string online_ext_attr = unit->online_ext_attr();
      if (online_ext_attr.empty()) return;
      base::Json json(base::StringToJson(online_ext_attr));
      if (!json.IsObject()) return;
      int32_t user_intention_type = json.GetInt("userIntentionType", 0);
      unit->set_user_intention_type(user_intention_type);
      ks::infra::PerfUtil::CountLogStash(1, "ad.index_builder", "parse_online_ext_attr");
    }
  }
  bool Parse(UnitFanstopSupportInfo* unit) override {
    if (unit == nullptr) {
      return false;
    }
    ParseOnlineExtAttr(unit);
    std::string json_str = unit->offline_ext_attr();
    if (json_str.empty()) {
      return true;
    }
    Json offline_ex_attr(StringToJson(json_str));
    int64_t enable_dsp_delivery = offline_ex_attr.GetInt("enableDspDelivery", 0);
    unit->set_enable_dsp_delivery(enable_dsp_delivery);
    return true;
  }
};

PARSER_REGISTER(UnitFansTopSupportInfoParser, google::protobuf::Message,
                kuaishou::ad::tables::UnitFanstopSupportInfo)

}  // namespace index_builder
}  // namespace ks

