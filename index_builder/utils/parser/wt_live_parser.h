#pragma once
#include <cstddef>
#include <set>
#include <string>
#include <vector>
#include <map>
#include <algorithm>
#include <unordered_map>

#include "absl/strings/str_split.h"
#include "base/encoding/base64.h"
#include "base/hash_function/city.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

static void ParseExtendFields(kuaishou::ad::tables::WTLive* proto) {
  if (proto == nullptr) { return; }
}
static void ParseStringFields(kuaishou::ad::tables::WTLive* proto) {
  if (proto == nullptr) { return; }
  auto* parse_fields =  proto->mutable_parse_fields();
  // 解析 exclude_regions;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, exclude_regions, ",");
  // 解析 ad_spu_ids;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, ad_spu_ids, ";");
  // 解析 ad_face_ids;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, ad_face_ids, ";");
  // 解析 explain_spus;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, explain_spus, ";");
  // 解析 chart_spus;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, chart_spus, ";");
  // 解析 future_spus;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, future_spus, ";");
  // 解析 explain_swing_spus;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, explain_swing_spus, ";");
  // 解析 chart_swing_spus;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, chart_swing_spus, ";");
  // 解析 future_swing_spus;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, future_swing_spus, ";");
  // 解析 explain_items;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, explain_items, ";");
  // 解析 future_items;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, future_items, ";");
  // 解析 chart_items;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, chart_items, ";");
  // 解析 ecom_spu_ids;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, ecom_spu_ids, ",");
  // 解析 ecom_item_ids;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, ecom_item_ids, ",");
  // 解析 mmu_category_id_a_list
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, mmu_category_id_a_list, ",");
  // 解析 mmu_category_id_b_list;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, mmu_category_id_b_list, ",");
  // 解析 min_kg_tag_ids;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, min_kg_tag_ids, ",");
  // 解析 max_kg_tag_ids;
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, max_kg_tag_ids, ",");
  // 解析 recruit_jobs;
  auto parse_recruit_jobs = [&](const std::string& str) {
    if (str.empty()) { return; }
    std::vector<absl::string_view> tokens = absl::StrSplit(str, ";", absl::SkipEmpty());
    if (tokens.empty()) { return; }
    for (const auto& token : tokens) {
      std::vector<absl::string_view> data = absl::StrSplit(token, ",", absl::SkipEmpty());
      if (data.size() != 3) { continue; }
      int32_t gender = 0;
      if (!absl::SimpleAtoi(data[0], &gender)) { continue; }
      int32_t age_range = 0;
      if (!absl::SimpleAtoi(data[1], &age_range)) { continue; }
      auto* recruit_job = parse_fields->add_recruit_jobs();
      recruit_job->set_gender(gender);
      // age 范围为拼接存储，高三位数字为年龄起点，低三位数字为年龄终点
      recruit_job->set_age_start(age_range / 1000);
      recruit_job->set_age_end(age_range % 1000);
    }
  };
  // 解析 exclude_regions_v1
  auto parse_exclude_regions = [&](const std::string& str) {
    if (str.empty()) {
      return;
    }
    std::vector<absl::string_view> tokens = absl::StrSplit(str, ";", absl::SkipEmpty());
    if (tokens.empty()) {
      return;
    }
    auto* exclude_regions_v1 = parse_fields->mutable_exclude_regions_v1();
    for (const auto& token : tokens) {
      std::vector<absl::string_view> data = absl::StrSplit(token, ":", absl::SkipEmpty());
      if (data.size() != 2) {
        continue;
      }
      int64_t item_id = 0;
      if (!absl::SimpleAtoi(data[0], &item_id)) {
        continue;
      }
      kuaishou::ad::tables::WTLiveParseFields_RegionList region_list;
      std::vector<absl::string_view> region_data = absl::StrSplit(data[1], ",", absl::SkipEmpty());
      for (const auto& region : region_data) {
        int64_t region_id = 0;
        if (!absl::SimpleAtoi(region, &region_id)) {
          continue;
        }
        region_list.add_regions(region_id);
      }
      exclude_regions_v1->insert({item_id, region_list});
    }
  };
  parse_recruit_jobs(proto->recruit_jobs());
  // 本地生活
  // 解析 local_life_goods_list
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, local_life_goods_list, ",");
  // 解析 local_life_poi_list
  SIMPLE_PARSE_INT64_USE_SPLLIT(proto, local_life_poi_list, ",");
  // 解析直播间本地经纬度
  const std::string& dis_value = proto->poi_point_list();
  if (!dis_value.empty()) {
    Json json(StringToJson(dis_value));
    // distance 格式："[{\"lat\":26.038841,\"lnt\":119.27562,\"dis\":6}]"
    int32_t count = 0;
    std::set<int64_t> distrinct_term;
    for (auto item : json.array()) {
      double lon = item->GetFloat("lnt", -1000);
      double lat = item->GetFloat("lat", -1000);
      // 最高 15km
      double dis = std::min(15000.0, item->GetFloat("dis", 0.0));
      std::vector<std::string> terms;
      if (dis <= 0 || !ks::ad_server::GetFastCoveringTerms(lat, lon, dis, &terms)) {
        LOG_EVERY_N(WARNING, 10000) << "Invalid poi_point_list: " << dis_value;
        continue;
      }
      for (const std::string& term : terms) {
        if (count++ > AdKconfUtil::wtLivePoiPointLimit()) {
          break;
        }
        uint64_t uint64_sign = base::CityHash64(term.data(), term.size());
        int64_t tmp = *(reinterpret_cast<int64_t*>(&uint64_sign));
        if (distrinct_term.count(tmp) > 0) {
          continue;
        }
        distrinct_term.insert(tmp);
        parse_fields->add_distance_s2_terms(tmp);
      }
      parse_fields->add_lat(lat);
      parse_fields->add_lon(lon);
      parse_fields->add_dis(dis);
    }
  }
  proto->clear_local_life_goods_list();
  proto->clear_local_life_poi_list();
  proto->clear_poi_point_list();
  proto->clear_recruit_jobs();
  parse_exclude_regions(proto->exclude_regions_v1());
  proto->clear_exclude_regions_v1();
  LOG_FIRST_N(INFO, 100) << parse_fields->ShortDebugString();
}

static void ParseLiveStreamInfoProto(kuaishou::ad::tables::LiveStreamUserInfo* proto) {
  if (proto == nullptr) { return; }
  return;
}

class WTLiveParser : public PbParser<kuaishou::ad::tables::WTLive> {
 public:
  virtual ~WTLiveParser() {}
  bool Parse(kuaishou::ad::tables::WTLive* proto) override {
    PARSE_BINARY_STRING_TO_MESSAGE(proto, live_stream_user_info);
    // ParseLiveStreamInfoProto(proto->mutable_live_stream_user_info());
    ParseExtendFields(proto);
    ParseStringFields(proto);
    return true;
  }
};

PARSER_REGISTER(WTLiveParser, google::protobuf::Message, kuaishou::ad::tables::WTLive);

}  // namespace index_builder
}  // namespace ks
