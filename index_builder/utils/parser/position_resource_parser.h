#pragma once
#include <algorithm>
#include <string>
#include <vector>

#include "teams/ad/ad_index/index/utils/public.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

class AdPositionResourceParser : public PbParser<kuaishou::ad::tables::AdPositionResource> {
 public:
  virtual ~AdPositionResourceParser() {}
  bool Parse(kuaishou::ad::tables::AdPositionResource* table) override {
    auto pos_ids = JsonStrToRepeatedInt(table->pos_ids());
    RepeatedAssign(table->mutable_extend_fields()->mutable_pos_ids(), pos_ids);
    return true;
  }
};

PARSER_REGISTER_WITH_PRIORITY(AdPositionResourceParser, google::protobuf::Message,
                              kuaishou::ad::tables::AdPositionResource, 101)

}  // namespace index_builder
}  // namespace ks
