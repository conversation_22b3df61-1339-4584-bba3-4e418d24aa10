#pragma once
#include <string>
#include <vector>
#include <map>
#include <algorithm>
#include <unordered_map>

#include "absl/strings/str_split.h"
#include "base/encoding/base64.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

static void ParseExtendFields(kuaishou::ad::tables::WTProduct* wt_product) {
  if (wt_product == nullptr) { return; }
}
static void ParseStringFields(kuaishou::ad::tables::WTProduct* wt_product) {
  if (wt_product == nullptr) { return; }
  auto* parse_fields =  wt_product->mutable_parse_fields();

  // 解析 dst_spu_id_list;
  SIMPLE_PARSE_INT64_USE_SPLLIT(wt_product, dst_spu_id_list, ",");
  // 解析 exclude_regions
  SIMPLE_PARSE_INT64_USE_SPLLIT(wt_product, exclude_regions, ",");
  // 解析 explaination_author
  SIMPLE_PARSE_INT64_USE_SPLLIT(wt_product, explaination_author, ",");
  // 解析 kg_tag_ids
  SIMPLE_PARSE_INT64_USE_SPLLIT(wt_product, kg_tag_ids, ",");

  LOG_FIRST_N(INFO, 100) << parse_fields->ShortDebugString();
}

class WTProductParser : public PbParser<kuaishou::ad::tables::WTProduct> {
 public:
  virtual ~WTProductParser() {}
  bool Parse(kuaishou::ad::tables::WTProduct* wt_product) override {
    PARSE_BINARY_STRING_TO_MESSAGE(wt_product, ad_dsp_merchant_product_info);
    ParseExtendFields(wt_product);
    ParseStringFields(wt_product);
    return true;
  }
};

PARSER_REGISTER(WTProductParser, google::protobuf::Message, kuaishou::ad::tables::WTProduct);

}  // namespace index_builder
}  // namespace ks
