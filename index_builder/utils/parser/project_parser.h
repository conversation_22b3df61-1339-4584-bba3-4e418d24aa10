#pragma once
#include <algorithm>
#include <string>
#include <vector>

#include "ks/util/json.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"

namespace ks {
namespace index_builder {

class PhotosInfosParserForProject : public PbParser<kuaishou::ad::tables::AdDspHostingProject> {
 public:
  virtual ~PhotosInfosParserForProject() {}
  bool Parse(kuaishou::ad::tables::AdDspHostingProject *project) override {
    std::string string_value = project->photo_infos();
    if (string_value.empty()) return true;
    auto *extend_fields = project->mutable_extend_fields();
    Json json(StringToJson(string_value));
    google::protobuf::util::JsonParseOptions options;
    options.ignore_unknown_fields = true;
    extend_fields->clear_photo_infos();
    for (auto item : json.array()) {
      auto photo_info = extend_fields->add_photo_infos();
      std::string json_str = base::JsonToString(item->get());
      auto status = google::protobuf::util::JsonStringToMessage(json_str, photo_info, options);
      if (!status.ok()) {
        LOG_EVERY_N(WARNING, 1000) << json_str;
      }
    }
    return true;
  }
};

class VerticalPhotoIdsParserForProject : public PbParser<kuaishou::ad::tables::AdDspHostingProject> {
 public:
  virtual ~VerticalPhotoIdsParserForProject() {}
  bool Parse(kuaishou::ad::tables::AdDspHostingProject *project) override {
    std::string string_value = project->vertical_photo_ids();
    if (string_value.empty()) return true;
    auto *extend_fields = project->mutable_extend_fields();
    Json json(StringToJson(string_value));
    if (!json.IsArray()) {
      LOG(ERROR) << string_value << " is not json array!";
      return true;
    }
    extend_fields->clear_vertical_photo_ids();
    for (size_t i = 0; i < json.size(); ++i) {
      int64_t v = 0;
      if (json.GetInt(i, &v)) {
        extend_fields->add_vertical_photo_ids(v);
      } else {
        LOG(ERROR) << "Parse json failed: " << string_value;
      }
    }
    return true;
  }
};

class HorizontalPhotoIdsParserForProject : public PbParser<kuaishou::ad::tables::AdDspHostingProject> {
 public:
  virtual ~HorizontalPhotoIdsParserForProject() {}
  bool Parse(kuaishou::ad::tables::AdDspHostingProject *project) override {
    std::string string_value = project->horizontal_photo_ids();
    if (string_value.empty()) return true;
    auto *extend_fields = project->mutable_extend_fields();
    Json json(StringToJson(string_value));
    if (!json.IsArray()) {
      LOG(ERROR) << string_value << " is not json array!";
      return true;
    }
    extend_fields->clear_horizontal_photo_ids();
    for (size_t i = 0; i < json.size(); ++i) {
      int64_t v = 0;
      if (json.GetInt(i, &v)) {
        extend_fields->add_horizontal_photo_ids(v);
      } else {
        LOG(ERROR) << "Parse json failed: " << string_value;
      }
    }
    return true;
  }
};

class StickerStylesParserForProject : public PbParser<kuaishou::ad::tables::AdDspHostingProject> {
 public:
  virtual ~StickerStylesParserForProject() {}
  bool Parse(kuaishou::ad::tables::AdDspHostingProject *project) override {
    std::string string_value = project->sticker_styles();
    if (string_value.empty()) return true;
    auto *extend_fields = project->mutable_extend_fields();
    Json json(StringToJson(string_value));
    if (!json.IsArray()) {
      LOG(ERROR) << string_value << " is not json array!";
      return true;
    }
    extend_fields->clear_sticker_styles();
    for (size_t i = 0; i < json.size(); ++i) {
      int64_t v = 0;
      if (json.GetInt(i, &v)) {
        extend_fields->add_sticker_styles(v);
      } else {
        LOG(ERROR) << "Parse json failed: " << string_value;
      }
    }
    return true;
  }
};

class AppStoreParserForProject : public PbParser<kuaishou::ad::tables::AdDspHostingProject> {
 public:
  virtual ~AppStoreParserForProject() {}
  bool Parse(kuaishou::ad::tables::AdDspHostingProject *project) override {
    std::string string_value = project->app_store();
    if (string_value.empty()) return true;
    auto *extend_fields = project->mutable_extend_fields();
    Json json(StringToJson(string_value));
    if (!json.IsArray()) {
      LOG(ERROR) << string_value << " is not json array!";
      return true;
    }
    extend_fields->clear_app_store();
    for (size_t i = 0; i < json.size(); ++i) {
      std::string s;
      if (json.GetString(i, &s)) {
        extend_fields->add_app_store(s);
      } else {
        LOG(ERROR) << "Parse json failed: " << string_value;
      }
    }
    return true;
  }
};

class CoversParserForProject : public PbParser<kuaishou::ad::tables::AdDspHostingProject> {
 public:
  virtual ~CoversParserForProject() {}
  bool Parse(kuaishou::ad::tables::AdDspHostingProject *project) override {
    std::string string_value = project->covers();
    if (string_value.empty()) return true;
    auto *extend_fields = project->mutable_extend_fields();
    Json json(StringToJson(string_value));
    if (!json.IsArray()) {
      LOG(ERROR) << string_value << " is not json array!";
      return true;
    }
    extend_fields->clear_covers();
    for (size_t i = 0; i < json.size(); ++i) {
      auto one = json.Get(i);
      if (one && one->IsObject()) {
        extend_fields->add_covers(one->ToString(0));
      }
    }
    return true;
  }
};

class CoverSlogansParserForProject : public PbParser<kuaishou::ad::tables::AdDspHostingProject> {
 public:
  virtual ~CoverSlogansParserForProject() {}
  bool Parse(kuaishou::ad::tables::AdDspHostingProject *project) override {
    std::string string_value = project->cover_slogans();
    if (string_value.empty()) return true;
    auto *extend_fields = project->mutable_extend_fields();
    Json json(StringToJson(string_value));
    if (!json.IsArray()) {
      LOG(ERROR) << string_value << " is not json array!";
      return true;
    }
    extend_fields->clear_cover_slogans();
    for (size_t i = 0; i < json.size(); ++i) {
      std::string s;
      if (json.GetString(i, &s)) {
        extend_fields->add_cover_slogans(s);
      } else {
        LOG(ERROR) << "Parse json failed: " << string_value;
      }
    }
    return true;
  }
};

class CaptionsParserForProject : public PbParser<kuaishou::ad::tables::AdDspHostingProject> {
 public:
  virtual ~CaptionsParserForProject() {}
  bool Parse(kuaishou::ad::tables::AdDspHostingProject *project) override {
    std::string string_value = project->captions();
    if (string_value.empty()) return true;
    auto *extend_fields = project->mutable_extend_fields();
    Json json(StringToJson(string_value));

    if (!json.IsArray()) {
      LOG(ERROR) << string_value << " is not json array!";
      return true;
    }
    extend_fields->clear_captions();
    for (size_t i = 0; i < json.size(); ++i) {
      std::string s;
      if (json.GetString(i, &s)) {
        extend_fields->add_captions(s);
      } else {
        LOG(ERROR) << "Parse json failed: " << string_value;
      }
    }
    return true;
  }
};

class PicInfosParserForProject : public PbParser<kuaishou::ad::tables::AdDspHostingProject> {
 public:
  virtual ~PicInfosParserForProject() {}
  bool Parse(kuaishou::ad::tables::AdDspHostingProject *project) override {
    std::string string_value = project->pic_infos();
    if (string_value.empty()) {
      return true;
    }
    auto *extend_fields = project->mutable_extend_fields();
    Json json(StringToJson(string_value));
    google::protobuf::util::JsonParseOptions options;
    options.ignore_unknown_fields = true;
    extend_fields->clear_pic_infos();
    for (auto item : json.array()) {
      kuaishou::ad::tables::PicInfo pic_info;
      std::string json_str = base::JsonToString(item->get());
      auto status = google::protobuf::util::JsonStringToMessage(json_str, &pic_info, options);
      if (!status.ok()) {
        LOG_EVERY_N(WARNING, 1000) << json_str << ", parse failed, error_msg:" << status.error_message();
        continue;
      }
      extend_fields->add_pic_infos()->Swap(&pic_info);
    }
    return true;
  }
};

class SceneIdsParserForProject : public PbParser<kuaishou::ad::tables::AdDspHostingProject> {
 public:
  virtual ~SceneIdsParserForProject() {}
  bool Parse(kuaishou::ad::tables::AdDspHostingProject *project) override {
    std::string string_value = project->scene_ids();
    if (string_value.empty()) return true;
    auto *extend_fields = project->mutable_extend_fields();
    Json json(StringToJson(string_value));
    extend_fields->clear_scene_ids();
    for (auto item : json.array()) {
      int64_t v = item->IntValue(-1);
      if (v > 0) {
        extend_fields->add_scene_ids(v);
      } else {
        LOG(ERROR) << "Parse json failed: " << string_value;
      }
    }
    return true;
  }
};

class HostingProjectSupportInfoParser : public PbParser<kuaishou::ad::tables::AdDspHostingProject> {
 public:
  virtual ~HostingProjectSupportInfoParser() {}
  bool Parse(kuaishou::ad::tables::AdDspHostingProject *project) override {
    if (project == nullptr) return true;
    auto project_support_info = project->mutable_support_info();
    auto extend_fields = project->mutable_support_info()->mutable_extend_fields();

    auto parse_splash_photos = [&]() {
      auto splash_photos_str = project_support_info->splash_photos();
      if (splash_photos_str.empty()) return;

      Json json(base::StringToJson(splash_photos_str));
      if (!json.IsArray() || json.size() <= 0) return;

      for (auto items : json.array()) {
        if (!items->IsArray() || items->size() <= 0) continue;

        auto splash_photos = extend_fields->add_splash_photos_vec();
        for (auto item : items->array()) {
          auto add_photo = splash_photos->add_items();
          add_photo->set_photo_id_val(item->GetInt("photoId", 0));
          add_photo->set_height(item->GetInt("height", 0));
          add_photo->set_width(item->GetInt("weight", 0));
        }
      }
      project_support_info->clear_splash_photos();
      LOG_EVERY_N(INFO, 100000) << "HostingProjectSupportInfoParser splash_photos="
                                << extend_fields->ShortDebugString();
    };

    auto parse_splash_pictures = [&]() {
      auto splash_pictures_str = project_support_info->splash_pictures();
      if (splash_pictures_str.empty()) return;

      Json json(base::StringToJson(splash_pictures_str));
      if (!json.IsArray() || json.size() <= 0) return;

      for (auto items : json.array()) {
        if (!items->IsArray() || items->size() <= 0) continue;
        auto splash_pictures = extend_fields->add_splash_pictures_vec();

        for (auto item : items->array()) {
          auto add_pictures = splash_pictures->add_items();
          add_pictures->set_cover_id_val(item->GetInt("coverId", 0));
          add_pictures->set_cover_url(item->GetString("coverUrl", ""));
          add_pictures->set_height(item->GetInt("height", 0));
          add_pictures->set_width(item->GetInt("width", 0));
        }
      }
      project_support_info->clear_splash_pictures();
      LOG_EVERY_N(INFO, 100000) << "HostingProjectSupportInfoParser parse_splash_pictures="
                                << extend_fields->ShortDebugString();
    };

    parse_splash_photos();
    parse_splash_pictures();
    return true;
  }
};

PARSER_REGISTER(PhotosInfosParserForProject, google::protobuf::Message,
                kuaishou::ad::tables::AdDspHostingProject)
PARSER_REGISTER(VerticalPhotoIdsParserForProject, google::protobuf::Message,
                kuaishou::ad::tables::AdDspHostingProject)
PARSER_REGISTER(HorizontalPhotoIdsParserForProject, google::protobuf::Message,
                kuaishou::ad::tables::AdDspHostingProject)
PARSER_REGISTER(StickerStylesParserForProject, google::protobuf::Message,
                kuaishou::ad::tables::AdDspHostingProject)
PARSER_REGISTER(AppStoreParserForProject, google::protobuf::Message,
                kuaishou::ad::tables::AdDspHostingProject)
PARSER_REGISTER(CoversParserForProject, google::protobuf::Message, kuaishou::ad::tables::AdDspHostingProject)
PARSER_REGISTER(CoverSlogansParserForProject, google::protobuf::Message,
                kuaishou::ad::tables::AdDspHostingProject)
PARSER_REGISTER(CaptionsParserForProject, google::protobuf::Message,
                kuaishou::ad::tables::AdDspHostingProject)
PARSER_REGISTER(PicInfosParserForProject, google::protobuf::Message,
                kuaishou::ad::tables::AdDspHostingProject)
PARSER_REGISTER(SceneIdsParserForProject, google::protobuf::Message,
                kuaishou::ad::tables::AdDspHostingProject)
PARSER_REGISTER(HostingProjectSupportInfoParser, google::protobuf::Message,
                kuaishou::ad::tables::AdDspHostingProject)
}  // namespace index_builder
}  // namespace ks
