#pragma once

#include <string>
#include <vector>
#include <algorithm>
#include <unordered_set>

#include "absl/strings/numbers.h"
#include "perfutil/perfutil.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/index_builder/utils/kconf.h"

#define SET_PARSE_STRING_FIELD(FIELD, SOURCE_KEY) {                                                         \
  auto* temp = item->Get(SOURCE_KEY);                                                                       \
  if (temp && temp->IsString()) {                                                                           \
    product_label->set_##FIELD(temp->StringValue());                                                        \
  } else {                                                                                                  \
    PerfUtil::CountLogStash(1, "ad.index_builder", "ad_style.esp_product_label", "set_error", SOURCE_KEY);  \
  }                                                                                                         \
}

#define SET_PARSE_INT_FIELD(FIELD, SOURCE_KEY) {                                                            \
  auto* temp = item->Get(SOURCE_KEY);                                                                       \
  if (temp && temp->IsInteger()) {                                                                          \
    product_label->set_##FIELD(temp->IntValue(-1));                                                         \
  } else {                                                                                                  \
    PerfUtil::CountLogStash(1, "ad.index_builder", "ad_style.esp_product_label", "set_error", SOURCE_KEY);  \
  }                                                                                                         \
}

namespace ks {
namespace index_builder {
using ks::infra::PerfUtil;

class AdEspProductLabelInfoParser
  : public PbParser<kuaishou::ad::tables::AdEspProductLabelInfo> {
 public:
  virtual ~AdEspProductLabelInfoParser() {}
  bool Parse(kuaishou::ad::tables::AdEspProductLabelInfo* product_info) override {
    if (!product_info) {
      return false;
    }
    LOG_EVERY_N(INFO, 1000) << "Parse AdEspProductLabelInfo, type: " << product_info->type();
    ParseEspProductLabel(product_info);
    return true;
  }
  bool ParseEspProductLabel(kuaishou::ad::tables::AdEspProductLabelInfo* product_info) {
    if (!product_info) {
      return false;
    }
    base::Json product_content(base::StringToJson(product_info->feature()));
    if (!product_content.IsArray()) {
      PerfUtil::CountLogStash(1, "ad.index_builder", "ad_style.esp_product_label",
                              "feature_not_array", std::to_string(product_info->type()));
      return false;
    }
    auto* parse_field = product_info->mutable_parse_field();
    switch (product_info->type()) {
      case 1 : {
        break;
      }
      case 2 : {  // 电商标题
        int index = -1;
        for (auto* item : product_content.array()) {
          index++;
          auto* product_label = parse_field->add_product_labels();
          if (!item || !item->IsObject()) {
            PerfUtil::CountLogStash(1, "ad.index_builder", "ad_style.esp_product_label", "not_object",
                                    std::to_string(product_info->type()));
            continue;
          }
          auto* title = item->Get("title");
          auto* desc = item->Get("desc");
          if (title && desc && title->IsString() && desc->IsString()) {
            product_label->set_title(title->StringValue());
            product_label->set_desc(desc->StringValue());
            product_label->set_index(index);
          }
        }
        LOG_EVERY_N(INFO, 10000) << "Parsed AdEspProductLabelInfo, type: " << product_info->type()
                                 << ", size: " << parse_field->product_labels_size();
        break;
      }
      case 3 : {  // AIGC 标题
        int index = -1;
        for (auto* item : product_content.array()) {
          index++;
          auto* product_label = parse_field->add_product_labels();
          if (!item || !item->IsObject()) {
            PerfUtil::CountLogStash(1, "ad.index_builder", "ad_style.esp_product_label", "not_object",
                                    std::to_string(product_info->type()));
            continue;
          }
          auto* title = item->Get("title");
          auto* grade = item->Get("grade");
          if (title && grade && title->IsString() && grade->IsString()) {
            product_label->set_title(title->StringValue());
            product_label->set_grade(grade->StringValue());
            product_label->set_index(index);
          }
          auto* desc = item->Get("desc");
          if (desc && desc->IsString()) {
            product_label->set_desc(desc->StringValue());
          }
        }
        LOG_EVERY_N(INFO, 10000) << "Parsed AdEspProductLabelInfo, type: " << product_info->type()
                                 << ", size: " << parse_field->product_labels_size();
        break;
      }
      case 5 : {  // 商品卡图片
        for (auto* item : product_content.array()) {
          auto* product_label = parse_field->add_product_labels();
          if (!item || !item->IsObject()) {
            PerfUtil::CountLogStash(1, "ad.index_builder", "ad_style.esp_product_label", "not_object",
                                    std::to_string(product_info->type()));
            continue;
          }
          SET_PARSE_STRING_FIELD(title, "title");
          SET_PARSE_STRING_FIELD(desc, "desc");
          SET_PARSE_STRING_FIELD(grade, "grade");
          SET_PARSE_INT_FIELD(user_id, "userId");
          SET_PARSE_INT_FIELD(pic_id, "picId");
          SET_PARSE_STRING_FIELD(pic_name, "picName");
          SET_PARSE_STRING_FIELD(pic_url, "picUrl");
          SET_PARSE_INT_FIELD(pic_source, "picSource");
          SET_PARSE_INT_FIELD(pic_width, "width");
          SET_PARSE_INT_FIELD(pic_height, "height");
        }
        LOG_EVERY_N(INFO, 10000) << "Parsed AdEspProductLabelInfo, type: " << product_info->type()
                                 << ", size: " << parse_field->product_labels_size();
        break;
      }
      default: {
        PerfUtil::CountLogStash(1, "ad.index_builder", "ad_style.esp_product_label", "type_unknown",
                                std::to_string(product_info->type()));
        return false;
      }
    }
    PerfUtil::CountLogStash(1, "ad.index_builder", "ad_style.esp_product_label", "success",
                            std::to_string(product_info->type()));
    return true;
  }
};
PARSER_REGISTER(AdEspProductLabelInfoParser, google::protobuf::Message,
                kuaishou::ad::tables::AdEspProductLabelInfo);
}  // namespace index_builder
}  // namespace ks
#undef SET_PARSE_STRING_FIELD
#undef SET_PARSE_INT_FIELD
