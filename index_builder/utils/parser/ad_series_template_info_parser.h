#pragma once

#include <algorithm>
#include <string>
#include <vector>
#include <string_view>

#include "teams/ad/ad_index/index/utils/public.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

class AdSeriesTemplateInfoParser : public PbParser<kuaishou::ad::tables::AdSeriesTemplateInfo> {
 public:
  virtual ~AdSeriesTemplateInfoParser() {}

  bool Parse(kuaishou::ad::tables::AdSeriesTemplateInfo* item) override {
    // 解析 commodity_list_str
    base::Json commodity_list(base::StringToJson(item->commodity_list_str()));
    for (auto commodity : commodity_list.array()) {
      if (!commodity->IsObject()) {
        continue;
      }

      uint32_t package_count = commodity->GetInt("packageCount", 0);
      uint32_t price = commodity->GetInt("price", 0);
      uint64_t underline_price = commodity->GetInt("underlinePrice", 0);

      LOG_EVERY_N(INFO, 10000) << "hh, package_count=" << package_count
        << ", price=" << price << ", underline_price" << underline_price;

      auto* ad_commodity = item->add_commodity_list();
      ad_commodity->set_package_count(package_count);
      ad_commodity->set_price(price);
      ad_commodity->set_underline_price(underline_price);
    }
    return true;
  }
};

PARSER_REGISTER(AdSeriesTemplateInfoParser,
    google::protobuf::Message,
    kuaishou::ad::tables::AdSeriesTemplateInfo);

}  // namespace index_builder
}  // namespace ks
