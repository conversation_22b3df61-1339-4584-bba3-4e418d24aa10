#pragma once
#include <string>
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
namespace ks {
namespace index_builder {

using namespace ks::ad_base::kconf;  // NOLINT

class ParserKconf {
 public:
  DEFINE_BOOL_KCONF_NODE(
      ad.index_builder, disableCreativeExtendParseFromDas,
      false);  // 禁止解析从 creative_score_ext 来的 creative_extend 信息
};

}  // namespace index_builder
}  // namespace ks
