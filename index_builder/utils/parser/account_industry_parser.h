#pragma once

#include <string>

#include "serving_base/jansson/json.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"

namespace ks {
namespace index_builder {

#define CATEGORY_INFO_PARSE(json, pb, key)      \
  if (json != nullptr && json->IsObject()) {    \
    auto id = json->GetInt("id", 0);            \
    auto name = json->GetString("name", "");    \
    pb->set_##key##_id(id);                     \
    pb->set_##key##_name(name);                 \
  }



class AccountIndustryParser : public PbParser<kuaishou::ad::tables::AccountIndustry> {
 public:
  virtual ~AccountIndustryParser() {}
  bool Parse(kuaishou::ad::tables::AccountIndustry* proto) override {
    ParseIndustryInfo(proto);
    ParseUserCategoryInfo(proto);
    ParseMarkCategoryInfo(proto);
    ParseExtendFields(proto);  // 注意，此函数前置依赖 ParseUserCategoryInfo 和 ParseMarkCategoryInfo
    return true;
  }

  void ParseUserCategoryInfo(kuaishou::ad::tables::AccountIndustry* proto) {
    if (proto->account_category().user_category().empty()) {
      return;
    }
    base::Json json(base::StringToJson(proto->account_category().user_category()));
    if (!json.IsArray()) {
      LOG_EVERY_N(ERROR, 1000) << "format error, not valid json string. string_value="
                               << proto->account_category().user_category();
      return;
    }
    if (json.size() == 0) {
      return;
    }
    for (auto* item : json.array()) {
      if (item == nullptr || !item->IsObject()) {
        continue;
      }
      auto* category_info = proto->add_user_category_info();
      auto* first_category_json = item->Get("firstCategory");
      CATEGORY_INFO_PARSE(first_category_json, category_info, first_category);
      auto* second_category_json = item->Get("secondCategory");
      CATEGORY_INFO_PARSE(second_category_json, category_info, second_category);
      auto* third_category_json = item->Get("thirdCategory");
      CATEGORY_INFO_PARSE(third_category_json, category_info, third_category);
    }
    LOG_FIRST_N(INFO, 100) << proto->ShortDebugString();
    proto->mutable_account_category()->clear_user_category();
  }

  void ParseMarkCategoryInfo(kuaishou::ad::tables::AccountIndustry* proto) {
    if (proto->account_category().mark_category().empty()) {
      return;
    }
    base::Json json(base::StringToJson(proto->account_category().mark_category()));
    if (!json.IsArray()) {
      LOG_EVERY_N(ERROR, 1000) << "format error, not valid json string. string_value="
                               << proto->account_category().mark_category();
      return;
    }
    if (json.size() == 0) {
      return;
    }
    for (auto* item : json.array()) {
      if (item == nullptr || !item->IsObject()) {
        continue;
      }
      auto* category_info = proto->add_mark_category_info();
      auto* first_category_json = item->Get("firstCategory");
      CATEGORY_INFO_PARSE(first_category_json, category_info, first_category);
      auto* second_category_json = item->Get("secondCategory");
      CATEGORY_INFO_PARSE(second_category_json, category_info, second_category);
      auto* third_category_json = item->Get("thirdCategory");
      CATEGORY_INFO_PARSE(third_category_json, category_info, third_category);
    }
    LOG_FIRST_N(INFO, 100) << proto->ShortDebugString();
    proto->mutable_account_category()->clear_mark_category();
  }

  void ParseIndustryInfo(kuaishou::ad::tables::AccountIndustry* proto) {
    if (proto->industry_info_str().empty()) { return; }
    base::Json json(base::StringToJson(proto->industry_info_str()));
    if (!json.IsArray()) {
        LOG_EVERY_N(ERROR, 1000) << "format error, not valid json string. string_value="
                                 << proto->industry_info_str();
        return;
    }
    if (json.size() == 0) { return; }
    auto* industry_info = proto->mutable_industry_info();
    for (auto* item : json.array()) {
      if (item == nullptr || !item->IsObject()) { continue; }
      kuaishou::ad::tables::AccountIndustryInfo info;
      auto version = item->GetString("version", "");
      if (version.empty()) { continue; }
      auto first_industry_id = item->GetInt("first_industry_id", 0);
      auto first_industry_name = item->GetString("first_industry_name", "");
      auto second_industry_id = item->GetInt("second_industry_id", 0);
      auto second_industry_name = item->GetString("second_industry_name", "");
      auto begin_version = item->GetFloat("begin_version", 0.0);
      auto end_version = item->GetFloat("end_version", 0.0);
      info.set_version(version);
      info.set_begin_version(begin_version);
      info.set_end_version(end_version);
      info.set_first_industry_id(first_industry_id);
      info.set_first_industry_name(first_industry_name);
      info.set_second_industry_id(second_industry_id);
      info.set_second_industry_name(second_industry_name);
      (*industry_info)[version] = info;
    }
    LOG_FIRST_N(INFO, 100) << proto->ShortDebugString();
    proto->clear_industry_info_str();
  }
  void ParseExtendFields(kuaishou::ad::tables::AccountIndustry* proto) {
    if (!proto) {
      return;
    }
    auto info_it = proto->industry_info().find("4.1");
    if (info_it != proto->industry_info().end()) {
      auto& info = info_it->second;
      proto->mutable_extend_fields()->set_first_industry_id_v4_1(info.first_industry_id());
      proto->mutable_extend_fields()->set_second_industry_id_v4_1(info.second_industry_id());
    }
    // 行业 5.0
    for (const auto& [k, v] : proto->industry_info()) {
      if (v.begin_version() <= 5.0 && v.end_version() > 5.0) {
        proto->mutable_extend_fields()->set_first_industry_id_v5(v.first_industry_id());
        proto->mutable_extend_fields()->set_second_industry_id_v5(v.second_industry_id());
        break;
      }
    }
    // 行业 5.1
    for (const auto& [k, v] : proto->industry_info()) {
      if (v.begin_version() <= 5.1 && v.end_version() > 5.1) {
        proto->mutable_extend_fields()->set_second_industry_id_v5_1(v.second_industry_id());
        break;
      }
    }
    // 行业 5.2
    for (const auto& [k, v] : proto->industry_info()) {
      if (v.begin_version() <= 5.2 && v.end_version() > 5.2) {
        proto->mutable_extend_fields()->set_first_industry_id_v5_2(v.first_industry_id());
        proto->mutable_extend_fields()->set_second_industry_id_v5_2(v.second_industry_id());
        break;
      }
    }
    // 行业 6.0
    for (const auto& [k, v] : proto->industry_info()) {
      if (v.begin_version() <= 6.0 && v.end_version() > 6.0) {
        proto->mutable_extend_fields()->set_first_industry_id_v6(v.first_industry_id());
        proto->mutable_extend_fields()->set_second_industry_id_v6(v.second_industry_id());
        LOG_EVERY_N(INFO, 1000) << "first_industry_id_v6=" << v.first_industry_id()
                                << ", second_industry_id_v6=" << v.second_industry_id();
        break;
      }
    }

    // 行业 6.3
    for (const auto& [k, v] : proto->industry_info()) {
      if (v.begin_version() <= 6.3 && v.end_version() > 6.3) {
        proto->mutable_extend_fields()->set_first_industry_id_v6_3(v.first_industry_id());
        proto->mutable_extend_fields()->set_second_industry_id_v6_3(v.second_industry_id());
        LOG_EVERY_N(INFO, 1000) << "first_industry_id_v6_3=" << v.first_industry_id()
                                << ", second_industry_id_v6_3=" << v.second_industry_id();
        break;
      }
    }

    auto& stable_category_third_id = *proto->mutable_extend_fields()->mutable_stable_category_third_id();
    for (const auto& info : proto->mark_category_info()) {
      proto->mutable_extend_fields()->add_mark_category_third_id(info.third_category_id());
      stable_category_third_id.Add(info.third_category_id());
    }

    for (const auto& info : proto->user_category_info()) {
      proto->mutable_extend_fields()->add_user_category_third_id(info.third_category_id());
      if (stable_category_third_id.empty()) {
        stable_category_third_id.Add(info.third_category_id());
      }
    }
    while (stable_category_third_id.size() > 2) { stable_category_third_id.RemoveLast(); }
  }
};

PARSER_REGISTER(AccountIndustryParser, google::protobuf::Message,
                kuaishou::ad::tables::AccountIndustry);

}  // namespace index_builder
}  // namespace ks
