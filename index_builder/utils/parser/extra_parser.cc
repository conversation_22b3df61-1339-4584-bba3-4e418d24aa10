#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include <algorithm>
#include <string>

#include "absl/strings/numbers.h"
namespace ks {
namespace index_builder {

bool GetPageIdFromURI(const std::string& uri, int64_t* page_id) {
  if (nullptr == page_id) return false;
  auto key_start = uri.npos;
  if ((key_start = uri.find("pageId=")) != uri.npos) {
    auto value_start = key_start + 7;
    auto value_end = uri.find('&', value_start) == uri.npos
                          ? uri.size()
                          : uri.find('&', value_start);
    if (value_end < value_start) {
      LOG_EVERY_N(ERROR, 10000) << "Find page id value pos error, uri is " << uri;
      return false;
    }
    auto len = value_end - value_start;
    auto value_str = uri.substr(value_start, len);
    if (!absl::SimpleAtoi(value_str, page_id)) {
      LOG_EVERY_N(ERROR, 10000)
          << "Parse pageid failed, " << value_str << ", uri is " << uri;
      return false;
    }
    return true;
  }
  return false;
}

std::vector<int64_t> JsonStrToRepeatedInt(const std::string& str) {
  std::vector<int64_t> ret;
  base::Json json(base::StringToJson(str));
  for (auto item : json.array()) {
    int64_t int_value = 0;
    if (item->IsInteger() && item->IntValue(&int_value)) {
      ret.emplace_back(int_value);
    }
  }
  return ret;
}

std::vector<std::string> JsonStrToRepeatedStr(const std::string& str) {
  std::vector<std::string> ret;
  base::Json json(base::StringToJson(str));
  for (auto item : json.array()) {
    if (item->IsString()) {
      ret.emplace_back(item->StringValue());
    }
  }
  return ret;
}

}  // namespace index_builder
}  // namespace ks
