#pragma once
#include <string>
#include <vector>
#include <set>
#include <map>
#include <algorithm>
#include <unordered_map>
#include "serving_base/jansson/json.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"
#include "teams/ad/engine_base/material_feature_type/material_feature_type.h"
namespace ks {
namespace index_builder {

const char kCreativePcvrField[] = "pcvr";
const char kCreativeContentPredictValueField[] = "contentPredictValue";
const char kCreativeOcpxActionTypeField[] = "ocpxActionType";
const char kCreativePcvrRankField[] = "pcvrRank";
const char kCreativeContentRatingField[] = "contentRating";

class PhotoModelScoresParser : public PbParser<kuaishou::ad::tables::Creative> {
 public:
  virtual ~PhotoModelScoresParser() {}
  bool Parse(kuaishou::ad::tables::Creative* creative) override {
    std::string string_value =
        creative->creative_support_info().photo_model_scores();
    if (string_value.empty()) return true;
    auto* extend_fields =
        creative->mutable_creative_support_extend_fields();
    base::Json json(base::StringToJson(string_value));
    if (!json.IsObject()) {
      LOG_EVERY_N(WARNING, 1000) << "Parse Error :" << string_value;
      return true;
    }
    kuaishou::ad::tables::PhotoModelScores* photo_model_scores = extend_fields->mutable_photo_model_scores();
    double number = 0;
    if (json.GetNumber(kCreativePcvrField, &number))
      photo_model_scores->set_pcvr(number);
    if (json.GetNumber(kCreativeContentPredictValueField, &number))
      photo_model_scores->set_content_predict_value(number);
    int64 value = 0;
    if (json.GetInt(kCreativeOcpxActionTypeField, &value))
      photo_model_scores->set_ocpx_action_type(value);
    if (json.GetInt(kCreativePcvrRankField, &value))
      photo_model_scores->set_pcvr_rank(value);
    if (json.GetInt(kCreativeContentRatingField, &value))
      photo_model_scores->set_content_rating(value);
    return true;
  }
};
class SplashInfoParser : public PbParser<kuaishou::ad::tables::Creative> {
 public:
  virtual ~SplashInfoParser() {}
  bool Parse(kuaishou::ad::tables::Creative* creative) override {
    std::string string_value =
        creative->creative_support_info().photo_model_scores();
    std::string splash_photos_string = creative->creative_support_info().splash_photos();
    std::string splash_pictures_string = creative->creative_support_info().splash_pictures();
    auto* extend_fields = creative->mutable_creative_support_extend_fields();
    // 解析 splash_photos & splash_pictures
    auto splash_parser = [&](
          const std::string& str, bool is_photo,
          google::protobuf::RepeatedPtrField<kuaishou::ad::tables::SplashMaterialInfo> *field) {
      base::Json json(base::StringToJson(str));
      if (!json.IsArray()) {
        return;
      }
      if (json.size() == 0) {
        LOG_EVERY_N(WARNING, 10000) << "json size is 0";
        return;
      }
      for (auto item : json.array()) {
        if (!item->IsObject()) {
          LOG_EVERY_N(WARNING, 1000) << "item is not an object";
          continue;
        }
        int64_t id = 0;
        if (is_photo) {
          id = item->GetInt("photoId", 0);
        } else {
          id = item->GetInt("picId", 0);
        }
        if (id == 0) {
          LOG_EVERY_N(WARNING, 1000) << "id is 0";
          continue;
        }
        int64_t height = item->GetInt("height", 0);
        int64_t width = item->GetInt("width", 0);
        auto* splash_info = field->Add();
        splash_info->set_id(id);
        splash_info->set_height(height);
        splash_info->set_width(width);
      }
    };
    auto* splash_photos = extend_fields->mutable_splash_photos();
    auto* splash_pictures = extend_fields->mutable_splash_pictures();
    splash_parser(splash_photos_string, true, splash_photos);
    splash_parser(splash_pictures_string, false, splash_pictures);
    return true;
  }
};
// 粉条标签频控字段解析
class CreativeFanstopTagParser : public PbParser<kuaishou::ad::tables::Creative> {
 public:
  virtual ~CreativeFanstopTagParser() {}
  bool Parse(kuaishou::ad::tables::Creative* creative) override {
    // 粉条标签频控字段解析
    if (creative->has_creative_support_info() &&
        creative->creative_support_info().has_freq_control_tag()) {
      const std::string& tag_str = creative->creative_support_info().freq_control_tag();
      // 样例  {"product_name": ["value1", "value2"], "jingle_bell": ["download"]}
      if (!tag_str.empty()) {
        base::Json json_obj(base::StringToJson(tag_str));
        if (!json_obj.IsObject()) {
          LOG_EVERY_N(ERROR, 100) << "Parsing Creative.freq_control_tag json failed, string = " << tag_str;
          return true;
        }
        auto* extend_fields = creative->mutable_extend_fields();
        extend_fields->clear_freq_control_tag();
        extend_fields->clear_freq_control_tag_hash();
        const std::unordered_map<std::string, base::Json *>& object_map = json_obj.objects();
        for (const auto& pair : object_map) {
          const std::string& key = pair.first;
          if (key.empty() || key.find('|') != std::string::npos) {  // 避免出现拼接符 '|'
            LOG_EVERY_N(ERROR, 10000) << "Creative.freq_control_tag has Invalid Key: " << key;
            continue;
          }
          base::Json* val_obj = pair.second;
          if (val_obj == nullptr || !val_obj->IsArray() || val_obj->size() == 0) {
            LOG_EVERY_N(ERROR, 10000) << "Creative.freq_control_tag value is not array";
            continue;
          }
          // 遍历 val list
          for (auto item : val_obj->array()) {
            if (!item->IsString() || item->StringValue().empty()) {
              LOG_EVERY_N(ERROR, 10000) << "Creative.freq_control_tag value array is invalid";
              continue;
            }
            const std::string val = item->StringValue();
            if (val.find('|') != std::string::npos) {  // 避免出现拼接符 '|'
              LOG_EVERY_N(ERROR, 10000) << "Creative.freq_control_tag val has Invalid Char "
                                        << "key: " << key << " val: " << val;
              continue;
            }
            const std::string key_val = absl::StrCat(key, "|", val);  // 明文
            const int64_t key_val_hash = std::hash<std::string>()(key_val) % INT64_MAX;  // hash 值
            extend_fields->add_freq_control_tag(key_val);
            extend_fields->add_freq_control_tag_hash(key_val_hash);
          }
        }
      }
    }
    return true;
  }
};


static void LabelParser(kuaishou::ad::tables::Creative* creative) {
  using LabelFunction =
    std::function<void(kuaishou::ad::tables::Creative*, const kuaishou::ad::tables::OneChannel&)>;
  LabelFunction dup_creative_id =
      [](kuaishou::ad::tables::Creative* creative,
         const kuaishou::ad::tables::OneChannel& one_channel) {
        if (one_channel.label_data().label_data().r_i64_size() > 0) {
          auto* extend = creative->mutable_extend_fields();
          extend->set_dup_creative_id(one_channel.label_data().label_data().r_i64(0));
        }
        LOG_FIRST_N(INFO, 100) << creative->extend_fields().ShortDebugString();
      };
  LabelFunction ecom_statistics_cost =
      [](kuaishou::ad::tables::Creative* creative,
         const kuaishou::ad::tables::OneChannel& one_channel) {
        if (one_channel.label_data().label_data().r_f_size() > 0) {
          auto* extend = creative->mutable_extend_fields();
          extend->set_statistics_cost(one_channel.label_data().label_data().r_f(0));
        }
        if (one_channel.label_data().label_data().r_i64_size() > 0) {
          auto* extend = creative->mutable_extend_fields();
          extend->set_statistics_merchant_order_num(one_channel.label_data().label_data().r_i64(0));
        }
        LOG_FIRST_N(INFO, 100) << creative->extend_fields().ShortDebugString();
      };
  LabelFunction pla_creative_id =
      [](kuaishou::ad::tables::Creative* creative,
         const kuaishou::ad::tables::OneChannel& one_channel) {
        if (one_channel.label_data().label_data().r_i64_size() > 0) {
          auto* extend = creative->mutable_extend_fields();
          extend->set_pla_creative_id(one_channel.label_data().label_data().r_i64(0));
        }
        LOG_FIRST_N(INFO, 100) << creative->extend_fields().ShortDebugString();
      };
  static std::map<std::string, LabelFunction> funtion_map{
      {"pla_creative_id", pla_creative_id},
      {"ecom_statistics_cost", ecom_statistics_cost},
      {"dup_creative_id", dup_creative_id}};
  for (const auto& one_channel :
       creative->extend_fields().level_label_data().channel_data()) {
    auto channel_name = one_channel.name();
    auto map_it = funtion_map.find(channel_name);
    if (map_it != funtion_map.end()) {
      auto func = map_it->second;
      func(creative, one_channel);
    }
  }
  // 已解析则去掉
  creative->mutable_extend_fields()->clear_level_label_data();
}

static void ParseExtendFields(kuaishou::ad::tables::Creative* creative) {
  if (!creative) {
    return;
  }
  LabelParser(creative);
  const int64_t dup_creative_id = creative->extend_fields().dup_creative_id();
  const int64_t pla_creative_id = creative->extend_fields().pla_creative_id();
  auto ecom_statistics_cost = creative->extend_fields().statistics_cost();
  auto statistics_merchant_order_num = creative->extend_fields().statistics_merchant_order_num();
  auto creative_extend_score =
      creative->extend_fields().creative_extend_score();
  base::JsonObject json_obj;
  if (!creative->display_info().empty()) {
    base::Json json_item(base::StringToJson(creative->display_info()));
    if (json_item.IsObject()) {
      json_obj.set("display_info", json_item);
    }
  }
  if (!creative->delivery_package().empty()) {
    base::Json json_item(base::StringToJson(creative->delivery_package()));
    if (json_item.IsObject()) {
      json_obj.set("delivery_package", json_item);
    }
  }
  if (!creative->extra_display_info().empty()) {
    base::Json json_item(base::StringToJson(creative->extra_display_info()));
    if (json_item.IsObject()) {
      json_obj.set("extra_display_info", json_item);
    }
  }
  if (!creative->review_through_cover_slogans_info().empty()) {
    base::Json json_item(
        base::StringToJson(creative->review_through_cover_slogans_info()));
    if (json_item.IsArray()) {
      json_obj.set("review_through_cover_slogans_info", json_item);
    }
  }
  if (!creative->description_titles_info().empty()) {
    base::Json json_item(
        base::StringToJson(creative->description_titles_info()));
    if (json_item.IsArray()) {
      json_obj.set("description_titles_info", json_item);
    }
  }
  if (!creative->creative_support_info().dpa_style_types().empty()) {
    base::Json json_item(base::StringToJson(creative->creative_support_info().dpa_style_types()));
    if (json_item.IsArray()) {
      json_obj.set("dpa_style_type", json_item);
    }
  }
  const std::string& json_str = json_obj.ToString();
  if (!json_str.empty()) {
    google::protobuf::util::JsonParseOptions options;
    options.ignore_unknown_fields = true;
    auto status = google::protobuf::util::JsonStringToMessage(
        json_str, creative->mutable_extend_fields(), options);
    if (!status.ok()) {
      ks::infra::PerfUtil::CountLogStash(
          1, "ad.index_builder", "parse_extend_fields_failed", "creative");
      LOG(ERROR) << "JsonStringToMessage failed. error: "
                 << status.error_message() << ", json_str: " << json_str;
      creative->clear_extend_fields();
    }
  }
  creative->mutable_extend_fields()->mutable_creative_extend_score()->CopyFrom(
      creative_extend_score);
  // 取出 mark server 的结果替换
  do {
    if (creative->ad_mark_new_creative_tag_key().empty()) break;
    auto* mutable_creative_ext = creative->mutable_extend_fields()->mutable_creative_extend_score();
    auto* tag_map = mutable_creative_ext->mutable_new_creative_tag();
    tag_map->clear();
    const auto& new_creative_tag_key = creative->ad_mark_new_creative_tag_key();
    const auto& new_creative_tag_value = creative->ad_mark_new_creative_tag_value();
    if (new_creative_tag_key.empty() || new_creative_tag_value.empty()) break;
    base::Json key_json(StringToJson(new_creative_tag_key));
    base::Json value_json(StringToJson(new_creative_tag_value));
    if (!key_json.IsArray() || !value_json.IsArray()) break;
    if (key_json.size() != value_json.size()) break;
    int size = key_json.size();
    for (int i = 0; i < size; i++) {
      int key = key_json.GetInt(i, 0);
      int value = value_json.GetInt(i, 0);
      if (key == 0 || value == 0) continue;
      (*tag_map)[key] = value;
    }
    creative->clear_ad_mark_new_creative_tag_key();
    creative->clear_ad_mark_new_creative_tag_value();
    LOG_FIRST_N(INFO, 10) << "new_creative_tag:" << mutable_creative_ext->ShortDebugString();
  } while (0);
  creative->mutable_extend_fields()->set_dup_creative_id(dup_creative_id);
  creative->mutable_extend_fields()->set_pla_creative_id(pla_creative_id);
  creative->mutable_extend_fields()->set_statistics_cost(ecom_statistics_cost);
  creative->mutable_extend_fields()->set_statistics_merchant_order_num(statistics_merchant_order_num);
  // 填充 circulation type
  if (ks::engine_base::IsCreativeInternalCirculation(creative->creative_feature())) {
    creative->mutable_extend_fields()->mutable_creative_extend_score()->set_circulation_type(
          kuaishou::ad::tables::CreativeExtendScore_CreativeCirculationType_INTERNAL_CIRCULATION_TYPE);
  } else {
    creative->mutable_extend_fields()->mutable_creative_extend_score()->set_circulation_type(
          kuaishou::ad::tables::CreativeExtendScore_CreativeCirculationType_EXTERNAL_CIRCULATION_TYPE);
  }
  if (!creative->sticker_styles().empty()) {
    auto styles =
        absl::StrSplit(creative->sticker_styles(), ",", absl::SkipEmpty());
    for (const auto& style : styles) {
      int64_t style_id = 0;
      if (absl::SimpleAtoi(style, &style_id)) {
        creative->mutable_extend_fields()->add_sticker_styles(style_id);
      }
    }
  }
  if (creative->has_creative_support_info()) {
    auto& creative_support_info = creative->creative_support_info();
    auto* highlight_info = creative->mutable_extend_fields()->mutable_highlight_info();
    if (creative_support_info.has_auto_deliver_type()) {
      highlight_info->set_auto_deliver_type(
                creative_support_info.auto_deliver_type());
    }
    if (creative_support_info.has_auto_deliver_related_id()) {
      highlight_info->set_auto_deliver_related_id(
                creative_support_info.auto_deliver_related_id());
    }
    if (creative_support_info.has_live_stream_id()) {
      highlight_info->set_live_stream_id(
                creative_support_info.live_stream_id());
    }
  }
  // 解析 risk labels
  auto* extend_fields = creative->mutable_extend_fields();
  base::Json json(StringToJson(creative->risk_labels()));
  if (!json.IsArray()) {
    LOG_EVERY_N(INFO, 10000)
      << "risk_labels field format error. risk_labels=" << creative->risk_labels();
  } else {
    for (auto item : json.array()) {
      int64_t risk_label = 0;
      if (!item->IntValue(&risk_label)) {
        continue;
      }
      extend_fields->add_risk_labels(risk_label);
    }
  }
  creative->clear_risk_labels();
  //
}
static void ParseStringField(kuaishou::ad::tables::Creative* creative) {
  if (!creative) {
    return;
  }
  base::Json json(base::StringToJson(creative->delivery_package()));
  do {
    auto sticker_titles = json.Get("stickerTitles");
    if (!sticker_titles || !sticker_titles->IsArray() ||
        sticker_titles->size() == 0) {
      break;
    }
    for (auto item : sticker_titles->array()) {
      if (!item->IsObject()) {
        continue;
      }
      int64_t id = item->GetInt("titleId", 0);
      std::string title = item->GetString("title", "");
      if (id == 0 || title.empty()) {
        continue;
      }
      creative->mutable_parse_field()->mutable_stick_titles()->insert(
          {id, title});
    }
  } while (false);
  do {
    auto description_titles = json.Get("descriptionTitles");
    if (!description_titles || !description_titles->IsArray() ||
        description_titles->size() == 0) {
      break;
    }
    for (auto item : description_titles->array()) {
      if (!item->IsObject()) {
        continue;
      }
      int64_t id = item->GetInt("descriptionId", 0);
      std::string title = item->GetString("description", "");
      if (id == 0 || title.empty()) {
        continue;
      }
      creative->mutable_parse_field()->mutable_description_titles()->insert(
          {id, title});
    }
  } while (false);
  base::Json json_extra_displays(
      base::StringToJson(creative->extra_display_info()));
  for (auto iter = json_extra_displays.object_begin();
       iter != json_extra_displays.object_end(); ++iter) {
    if (iter->first == "exposeTag") {
      creative->mutable_parse_field()->mutable_extra_displays()->insert(
          {iter->first, iter->second->StringValue()});
    } else if (iter->first == "newExposeTag") {
      if (iter->second->IsArray()) {
        auto iter_tag = iter->second->array_begin();
        for (; iter_tag != iter->second->array_end(); ++iter_tag) {
          if (!(*iter_tag)->IsObject()) {
            continue;
          }
          std::string text = (*iter_tag)->GetString("text", "");
          std::string url = (*iter_tag)->GetString("url", "");
          kuaishou::ad::tables::NewExposeTags* new_expose_tag =
              creative->mutable_parse_field()->add_expose_tags();
          new_expose_tag->set_text(text);
          new_expose_tag->set_url(url);
        }
      }
    } else if (iter->first == "gameOrderCard") {
      bool default_value = false;
      creative->mutable_parse_field()->mutable_extra_display_bool()->insert(
          {iter->first, iter->second->BooleanValue(default_value)});
    } else if (iter->first == "financialFrontCard") {
      bool default_value = false;
      creative->mutable_parse_field()->mutable_extra_display_bool()->insert(
          {iter->first, iter->second->BooleanValue(default_value)});
    } else if (iter->first == "cardAppearSecond") {
      int64_t default_value = 0;
      creative->mutable_parse_field()->mutable_extra_display_int()->insert(
          {iter->first, iter->second->IntValue(default_value)});
    } else if (iter->first == "preDescription") {
      creative->mutable_parse_field()->mutable_extra_displays()->insert(
          {iter->first, iter->second->StringValue()});
    }
  }
  // to do(liuwenlong03): 留黑白名单
  // creative->clear_extra_display_info();
  do {
    base::Json json(
        base::StringToJson(creative->review_through_cover_slogans_info()));
    if (!json.IsArray() || json.size() == 0) {
      break;
    }
    int64_t id = 0;
    std::string title;
    for (auto item : json.array()) {
      if (!item->IsObject()) {
        continue;
      }
      id = item->GetInt("sloganId", 0);
      title = item->GetString("slogan", "");
      if (id == 0 || title.empty()) {
        continue;
      }
      creative->mutable_parse_field()->mutable_stick_titles()->insert(
          {id, title});
    }
  } while (false);
  // to do(liuwenlong03): 留黑白名单
  // creative->clear_review_through_cover_slogans_info();
  do {
    base::Json json_desc_title(
        base::StringToJson(creative->description_titles_info()));
    if (!json_desc_title.IsArray() || json_desc_title.size() == 0) {
      break;
    }
    int64_t id = 0;
    std::string title;
    for (auto item : json_desc_title.array()) {
      if (!item->IsObject()) {
        continue;
      }
      id = item->GetInt("descriptionId", 0);
      title = item->GetString("description", "");
      if (id == 0 || title.empty()) {
        continue;
      }
      creative->mutable_parse_field()->mutable_description_titles()->insert(
          {id, title});
    }
  } while (false);
  // to do(liuwenlong03): 留黑白名单
  // creative->clear_description_titles_info();
  if (!creative->sticker_styles().empty()) {
    std::vector<absl::string_view> styles =
        absl::StrSplit(creative->sticker_styles(), ",", absl::SkipEmpty());
    int64_t style_id = 0;
    for (const auto& style : styles) {
      if (absl::SimpleAtoi(style, &style_id)) {
        creative->mutable_parse_field()->add_sticker_styles(style_id);
      }
    }
  }
  // 解析 risk_ext_param
  do {
    if (creative->risk_ext_param().empty()) {
      break;
    }
    base::Json json_risk_ext_param(base::StringToJson(creative->risk_ext_param()));
    auto json_photo_hash = json_risk_ext_param.GetString("photoHash", "");
    if (!json_photo_hash.empty()) {
      creative->mutable_parse_field()->set_risk_photo_hash(json_photo_hash);
      ks::infra::PerfUtil::CountLogStash(1, "ad.index_builder", "parse_risk_photo_hash", "creative");
    }
    auto json_risk_tag = json_risk_ext_param.Get("riskTag");
    if (!json_risk_tag || !json_risk_tag->IsArray() || json_risk_tag->size() == 0) {
      break;
    }
    for (auto item : json_risk_tag->array()) {
      int64_t risk_tag = 0;
      if (!item->IntValue(&risk_tag)) {
        continue;
      }
      creative->mutable_parse_field()->add_risk_tag(risk_tag);
    }
  } while (false);
}
static void ParseDownloadPageUrl(kuaishou::ad::tables::Creative* creative) {
  if (nullptr == creative) {
      return;
    }
    auto uri = creative->download_page_url();
    static std::set<std::string> host_set = {"https://moli.kuaishou.com", "https://chenzhongkj.com"};
    bool match = false;
    for (const auto& host : host_set) {
      if (uri.find(host) != uri.npos) {
        match = true;
        break;
      }
    }
    if (match) {
      int64_t page_id = 0;
      if (GetPageIdFromURI(uri, &page_id) && page_id > 0) {
        creative->set_is_site(true);
        if (creative->site_id() == 0) {
          creative->set_site_id(page_id);
        }
      }
    }
    return;
}
// 从 data_gate 迁移来，优先级最高排在最前面
class CreativeDefaultParser : public PbParser<kuaishou::ad::tables::Creative> {
 public:
  virtual ~CreativeDefaultParser() {}
  bool Parse(kuaishou::ad::tables::Creative* creative) override {
    ParseExtendFields(creative);
    ParseStringField(creative);
    ParseDownloadPageUrl(creative);
    PhotoModelScoresParser photo_parser;
    photo_parser.Parse(creative);
    CreativeFanstopTagParser fanstop_tag_parser;
    fanstop_tag_parser.Parse(creative);
    SplashInfoParser splash_parser;
    splash_parser.Parse(creative);
    return true;
  }
};
PARSER_REGISTER_WITH_PRIORITY(CreativeDefaultParser, google::protobuf::Message,
                kuaishou::ad::tables::Creative, 101)
}  // namespace index_builder
}  // namespace ks
