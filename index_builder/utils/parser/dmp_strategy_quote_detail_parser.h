#pragma once
#include <cstddef>
#include <set>
#include <string>
#include <vector>
#include <map>
#include <algorithm>
#include <unordered_map>

#include "absl/strings/str_split.h"
#include "base/encoding/base64.h"
#include "base/hash_function/city.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

static void ParseStringFields(kuaishou::ad::tables::AdDmpAccountStrategyQuoteDetail* proto) {
  if (proto == nullptr) { return; }
  auto* parse_field =  proto->mutable_parse_field();
  int rule_type = proto->rule_type();
  // 解析 rule_content
  const std::string& rule_content = proto->rule_content();
  if (rule_content.empty()) {
    return;
  }
  <PERSON><PERSON> json(StringToJson(rule_content));
  // 数据格式:
  // orientation ["1667","16036"]
  // ageSegment ["18-21","22-30"]
  // gender ["0","1"]
  // freAdcodes ["370103","123"]
  // 解析 orientation
  auto parse_int_string = [&]() -> void {
    for (auto* item : json.array()) {
      std::string val;
      int64_t id;
      if (item->StringValue(&val) && absl::SimpleAtoi(val, &id)) {
        parse_field->add_rule_content(id);
      }
    }
  };
  // 解析 ageSegment
  auto parse_age_segment = [&]() -> void {
    std::set<int64_t> ageList;
    for (auto* item : json.array()) {
      std::string val;
      if (item->StringValue(&val) && !val.empty()) {
        std::vector<absl::string_view> tokens = absl::StrSplit(val, "-", absl::SkipEmpty());
        if (tokens.size() != 2) {
          continue;
        }
        int64_t start = -1, end = -1;
        if (!absl::SimpleAtoi(tokens[0], &start) || !absl::SimpleAtoi(tokens[1], &end)) {
          continue;
        }
        if (start > end || start < 0 || start > 100 || end > 100 || end < 0) {
          continue;
        }
        for (int i = start; i <= end; ++i) {
          ageList.insert(i);
        }
      }
    }
    for (auto age : ageList) {
      parse_field->add_rule_content(age);
    }
  };
  switch (rule_type) {
    case 1:
    case 3:
    case 4:
      parse_int_string();
      break;
    case 2:
      parse_age_segment();
      break;
    default:
      break;
  }
  proto->clear_rule_content();
  LOG_FIRST_N(INFO, 100) << parse_field->ShortDebugString();
}


class AdDmpAccountStrategyQuoteDetailParser : public PbParser<kuaishou::ad::tables::AdDmpAccountStrategyQuoteDetail> {  // NOLINT
 public:
  virtual ~AdDmpAccountStrategyQuoteDetailParser() {}
  bool Parse(kuaishou::ad::tables::AdDmpAccountStrategyQuoteDetail* proto) override {
    ParseStringFields(proto);
    return true;
  }
};

PARSER_REGISTER(AdDmpAccountStrategyQuoteDetailParser, google::protobuf::Message,
                kuaishou::ad::tables::AdDmpAccountStrategyQuoteDetail);

}  // namespace index_builder
}  // namespace ks
