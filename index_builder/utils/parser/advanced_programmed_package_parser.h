#pragma once
#include <string>
#include <vector>
#include <algorithm>
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/ad_index/index/utils/public.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

class AdvancedProgramedPackageParser : public PbParser<kuaishou::ad::tables::AdDspAdvancedProgramedPackage> {
 public:
  virtual ~AdvancedProgramedPackageParser() {}
  static void ParseStringField(kuaishou::ad::tables::AdDspAdvancedProgramedPackage *package) {
    if (package == nullptr) {
      return;
    }

    base::JsonObject json_obj;
    if (!package->photo_infos().empty()) {
      base::Json json_item(base::StringToJson(package->photo_infos()));
      if (json_item.IsArray()) {
        json_obj.set("photo_infos", json_item);
      }
    }
    if (auto json_str = json_obj.ToString(); !json_str.empty()) {
      google::protobuf::util::JsonParseOptions options;
      options.ignore_unknown_fields = true;
      auto status = google::protobuf::util::JsonStringToMessage(json_str,
                                                                package->mutable_extend_fields(), options);
      if (!status.ok()) {
        ks::infra::PerfUtil::CountLogStash(
            1, "ad.index_builder", "parse_extend_fields_failed", "ad_dsp_advanced_programmed_package");
        package->clear_extend_fields();
      }
    }
  }

  bool Parse(kuaishou::ad::tables::AdDspAdvancedProgramedPackage *package) override {
    ParseStringField(package);
    return true;
  }
};
// TopicContentParser 解析简单 略过
PARSER_REGISTER_WITH_PRIORITY(AdvancedProgramedPackageParser, google::protobuf::Message,
                              kuaishou::ad::tables::AdDspAdvancedProgramedPackage, 101)

}  // namespace index_builder
}  // namespace ks
