#pragma once
#include <string>
#include <vector>
#include <algorithm>
#include "base/hash_function/city.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
namespace ks {
namespace index_builder {
class CampaignParser : public PbParser<kuaishou::ad::tables::Campaign> {
 public:
  virtual ~CampaignParser() {}

  bool ParseEffectPrediction(kuaishou::ad::tables::Campaign* campaign) {
    const std::string string_value = campaign->effect_prediction();
    if (string_value.empty()) {
       return false;
    }
    auto* effect_prediction = campaign->mutable_extend_fields()->mutable_effect_prediction();
    Json json(StringToJson(string_value));
    if (!json.IsObject()) {
      LOG_EVERY_N(INFO, 1000)
          << "effect_prediction format error, not valid json string. string_value="
          << string_value;
      return false;
    }
    int64_t min = json.GetInt("min", 0);
    int64_t max = json.GetInt("max", 0);
    int64_t timestamp = json.GetInt("timestamp", 0);
    effect_prediction->set_min(min);
    effect_prediction->set_max(max);
    effect_prediction->set_timestamp(timestamp);
    return true;
  }

  bool Parse(kuaishou::ad::tables::Campaign* campaign) override {
    ParseEffectPrediction(campaign);
    const std::string string_value = campaign->campaign_schedule();
    if (string_value.empty()) {
      return false;
    }
    auto* schedule_list_vec =
        campaign->mutable_extend_fields()->mutable_schedule_list_vec();
    Json json(StringToJson(string_value));
    if (!json.IsArray()) {
      LOG(ERROR)
          << "schedule field format error, not valid json string. string_value="
          << string_value;
      return false;
    }
    if (json.size() == 0) {
      return true;
    }
    if (json.size() != 7) {
      LOG(ERROR)
          << "schedule field format error, not valid json string. string_value="
          << string_value;
      return false;
    }
    uint32 day = 0;
    for (auto iter = json.array_begin(); iter != json.array_end();
         ++iter, ++day) {
      auto inner_json = *iter;
      if (!inner_json->IsArray()) {
        LOG(ERROR) << "schedule field format error, not valid json string. "
                      "string_value="
                   << string_value;
        return false;
      }
      for (auto inner_iter = inner_json->array_begin();
           inner_iter != inner_json->array_end(); ++inner_iter) {
        int64 pos = 0;
        if ((*inner_iter)->IntValue(&pos)) {
          if (pos < 0 || pos > 23) {
            LOG(ERROR) << "schedule field format error, not valid json string. "
                          "string_value="
                       << string_value;
            return false;
          }

          auto position = day * 24 + pos;
          schedule_list_vec->Add(static_cast<int16_t>(position));
          if (position > 65536) {
            LOG(FATAL) << "unexpect schedule "
                       << *(schedule_list_vec->rbegin());
          }
        } else {
          LOG(ERROR) << "schedule field format error, not valid json string. "
                        "string_value="
                     << string_value;
          return false;
        }
      }
    }
    std::sort(schedule_list_vec->begin(), schedule_list_vec->end());
    return true;
  }
};
PARSER_REGISTER(CampaignParser, google::protobuf::Message,
                kuaishou::ad::tables::Campaign)
}  // namespace index_builder
}  // namespace ks
