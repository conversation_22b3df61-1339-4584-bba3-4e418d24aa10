#pragma once
#include <algorithm>
#include <functional>
#include <map>
#include <string>
#include <vector>
#include "teams/ad/ad_base/src/better_enums/better_enums.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/target_parser.h"

namespace ks {
namespace index_builder {

BETTER_ENUM(AdDspHostingProjectTargetGender, uint8_t, Male = 1, Female, Both)
BETTER_ENUM(TargetUserType, uint8_t, RealTime = 0, Frequent, UnLimit, DistrictRealTime)

class HostingProjectParser
    : public PbParser<kuaishou::ad::tables::AdDspHostingProjectTarget> {
 public:
  virtual ~HostingProjectParser() {}

  static void ParseExtendFields(
      kuaishou::ad::tables::AdDspHostingProjectTarget* project_target) {
    if (!project_target) {
      return;
    }

#define SET_PROJECT_TARGET_JSON_ITEM(field, type)                      \
  if (!project_target->field().empty()) {                              \
    base::Json json_item(base::StringToJson(project_target->field())); \
    if (json_item.Is##type()) {                                        \
      json_obj.set(#field, json_item);                                 \
    }                                                                  \
  }

    base::JsonObject json_obj;

    // age，platform intelli_extend 这几个字段算法侧离线依赖，暂不清理
    SET_PROJECT_TARGET_JSON_ITEM(age, Array);
    SET_PROJECT_TARGET_JSON_ITEM(platform, Object);
    SET_PROJECT_TARGET_JSON_ITEM(paid_audience_real, Array);
    SET_PROJECT_TARGET_JSON_ITEM(population_real, Array);
    SET_PROJECT_TARGET_JSON_ITEM(exclude_population_real, Array);
    SET_PROJECT_TARGET_JSON_ITEM(intelli_extend, Object);
    SET_PROJECT_TARGET_JSON_ITEM(region_ids, Array);
    SET_PROJECT_TARGET_JSON_ITEM(behavior_interest_keyword, Array);

    ParseStringField(project_target, &json_obj);
    const std::string& json_str = json_obj.ToString();
    if (!json_str.empty()) {
      google::protobuf::util::JsonParseOptions options;
      options.ignore_unknown_fields = true;
      auto status = google::protobuf::util::JsonStringToMessage(
          json_str, project_target->mutable_extend_fields(), options);
      if (!status.ok()) {
        ks::infra::PerfUtil::CountLogStash(
            1, "ad.index_builder", "parse_extend_fields_failed", "target");
        LOG(ERROR) << "JsonStringToMessage failed. error: "
                   << status.error_message() << ", json_str: " << json_str;
        project_target->clear_extend_fields();
      }
    }
  }

  static void ParseStringField(
      kuaishou::ad::tables::AdDspHostingProjectTarget* project_target,
      base::JsonObject* extend_json) {
    if (project_target == nullptr || extend_json == nullptr) {
      return;
    }
    // 解析 gender
    if (!project_target->gender().empty()) {
      std::vector<int64_t> vec;
      if (project_target->gender() == "M") {
        vec.push_back(+TargetGender::Male);
      } else if (project_target->gender() == "F") {
        vec.push_back(+TargetGender::Female);
      }
      if (vec.size() == 1 && vec[0] == +AdDspHostingProjectTargetGender::Male) {
        extend_json->set("gender", +AdDspHostingProjectTargetGender::Male);
      } else if (vec.size() == 1 &&
                 vec[0] == +AdDspHostingProjectTargetGender::Female) {
        extend_json->set("gender", +AdDspHostingProjectTargetGender::Female);
      } else {
        extend_json->set("gender", +AdDspHostingProjectTargetGender::Both);
      }
    }
    return;
  }

  bool Parse(kuaishou::ad::tables::AdDspHostingProjectTarget* project_target)
      override {
    ParseExtendFields(project_target);
    return true;
  }
};

PARSER_REGISTER(HostingProjectParser, google::protobuf::Message,
                kuaishou::ad::tables::AdDspHostingProjectTarget)
}  // namespace index_builder
}  // namespace ks
