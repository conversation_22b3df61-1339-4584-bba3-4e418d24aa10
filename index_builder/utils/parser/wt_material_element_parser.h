#pragma once

#include <string>
#include <vector>
#include <algorithm>
#include <unordered_set>

namespace ks {
namespace index_builder {

class WTMaterialElement : public PbParser<kuaishou::ad::tables::WTMaterialElement> {
 public:
  virtual ~WTMaterialElement() {}
  bool Parse(kuaishou::ad::tables::WTMaterialElement* element) override {
    if (!element) { return false; }

    auto* source_info = element->mutable_parse_fields()->mutable_source_info();
    auto* element_tags = element->mutable_parse_fields()->mutable_element_tags();
    auto* element_info = element->mutable_parse_fields()->mutable_element_info();
    auto* embedding = element->mutable_parse_fields()->mutable_embedding();

    auto parse_label = [&](const std::string& label_string, auto* label_pb) -> bool {
      base::Json label_json(base::StringToJson(label_string));
      if (!label_json.IsObject() || !label_pb) {
        return false;
      }

      for (auto iter = label_json.object_begin(); iter != label_json.object_end(); ++iter) {
        std::string key = iter->first;
        if (iter->second->IsInteger()) {
          auto* int_data = label_pb->add_int_data();
          int_data->set_key(key);
          int_data->set_value(iter->second->IntValue(0L));
        } else if (iter->second->IsBoolean()) {
          auto* int_data = label_pb->add_int_data();
          int_data->set_key(key);
          int_data->set_value(iter->second->BooleanValue(false));
        } else if (iter->second->IsDouble()) {
          auto* double_data = label_pb->add_double_data();
          double_data->set_key(key);
          double_data->set_value(iter->second->FloatValue(0.0));
        } else if (iter->second->IsString()) {
          auto* string_data = label_pb->add_string_data();
          string_data->set_key(key);
          string_data->set_value(iter->second->StringValue(""));
        } else if (iter->second->IsArray()) {
          auto* int_vec_data = label_pb->add_int_vec_data();
          auto* double_vec_data = label_pb->add_double_vec_data();
          auto* string_vec_data = label_pb->add_string_vec_data();

          int_vec_data->set_key(key);
          double_vec_data->set_key(key);
          string_vec_data->set_key(key);

          for (auto* item : iter->second->array()) {
            if (item->IsInteger()) {
              int_vec_data->add_value(item->IntValue(0L));
            } else if (item->IsBoolean()) {
              int_vec_data->add_value(item->BooleanValue(false));
            } else if (item->IsDouble()) {
              double_vec_data->add_value(item->FloatValue(0.0));
            } else if (item->IsString()) {
              string_vec_data->add_value(item->StringValue(""));
            }
          }

          if (int_vec_data->value().empty()) {
            label_pb->mutable_int_vec_data()->RemoveLast();
          }
          if (double_vec_data->value().empty()) {
            label_pb->mutable_double_vec_data()->RemoveLast();
          }
          if (string_vec_data->value().empty()) {
            label_pb->mutable_string_vec_data()->RemoveLast();
          }
        } else {
          LOG(WARNING) << "not support json type:" << label_string;
        }
      }
      return true;
    };

    parse_label(element->source_info(), source_info);
    parse_label(element->element_tags(), element_tags);
    parse_label(element->element_info(), element_info);
    parse_label(element->embedding(), embedding);

    // 业务需要，暂时不清理
    // element->clear_source_info();
    // element->clear_element_tags();
    // element->clear_element_info();
    element->clear_embedding();

    LOG_FIRST_N(INFO, 5) << "DEBUG element:" << element->ShortDebugString();
    return true;
  }
};

PARSER_REGISTER(WTMaterialElement, google::protobuf::Message,
                kuaishou::ad::tables::WTMaterialElement)

}  // namespace index_builder
}  // namespace ks
