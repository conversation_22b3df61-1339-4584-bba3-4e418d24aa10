#pragma once
#include <algorithm>
#include <string>
#include <vector>

#include "teams/ad/ad_index/index/utils/public.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "teams/ad/index_builder/utils/parser/kconf.h"

namespace ks {
namespace index_builder {

class AdPositionParser : public PbParser<kuaishou::ad::tables::AdPosition> {
 public:
  virtual ~AdPositionParser() {}
  bool Parse(kuaishou::ad::tables::AdPosition* table) override {
    std::string creative_material_types_str = table->creative_material_types();
    if (creative_material_types_str.size() >= 2 && creative_material_types_str[0] == '"') {
      creative_material_types_str =
          creative_material_types_str.substr(1, creative_material_types_str.size() - 2);
    }
    auto creative_material_types = JsonStrToRepeatedInt(creative_material_types_str);
    RepeatedAssign(table->mutable_extend_fields()->mutable_creative_material_types(),
                   creative_material_types);
    return true;
  }
};

PARSER_REGISTER_WITH_PRIORITY(AdPositionParser, google::protobuf::Message, kuaishou::ad::tables::AdPosition,
                              101)

}  // namespace index_builder
}  // namespace ks
