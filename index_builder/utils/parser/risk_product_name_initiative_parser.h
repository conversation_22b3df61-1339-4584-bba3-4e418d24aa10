#pragma once
#include <string>
#include <vector>
#include "serving_base/jansson/json.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"

namespace ks {
namespace index_builder {

class RiskProductNameInitiativeParser : public PbParser<kuaishou::ad::tables::RiskProductNameInitiative> {
 public:
  ~RiskProductNameInitiativeParser() override {}

  static int64_t GetCityProductId(const std::string& product_name) {
    uint64_t uint64_sign = base::CityHash64(product_name.data(), product_name.size());
    return *(reinterpret_cast<int64_t*>(&uint64_sign));
  }

  bool Parse(kuaishou::ad::tables::RiskProductNameInitiative* risk_product_name_initiative) override {
    risk_product_name_initiative->set_city_product_id(
        GetCityProductId(risk_product_name_initiative->product_name()));
    return true;
  }
};
PARSER_REGISTER(RiskProductNameInitiativeParser, google::protobuf::Message,
                kuaishou::ad::tables::RiskProductNameInitiative)
}  // namespace index_builder
}  // namespace ks
