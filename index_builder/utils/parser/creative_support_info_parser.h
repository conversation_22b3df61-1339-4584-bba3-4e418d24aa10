#pragma once
#include <string>
#include <vector>
#include <algorithm>
#include <unordered_map>
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
namespace ks {
namespace index_builder {

const char kCreativeSupportInfoPcvrField[] = "pcvr";
const char kCreativeSupportInfoContentPredictValueField[] = "contentPredictValue";
const char kCreativeSupportInfoOcpxActionTypeField[] = "ocpxActionType";
const char kCreativeSupportInfoPcvrRankField[] = "pcvrRank";
const char kCreativeSupportInfoContentRatingField[] = "contentRating";

class CreativeSupportInfoPhotoModelScoresParser : public PbParser<kuaishou::ad::tables::CreativeSupportInfo> {
 public:
  virtual ~CreativeSupportInfoPhotoModelScoresParser() {}
  bool Parse(kuaishou::ad::tables::CreativeSupportInfo *creative_support_info) override {
    if (creative_support_info == nullptr) return true;
    std::string string_value = creative_support_info->photo_model_scores();
    if (string_value.empty()) return true;
    auto *extend_fields = creative_support_info->mutable_extend_fields();
    base::Json json(base::StringToJson(string_value));
    if (!json.IsObject()) {
      LOG_EVERY_N(WARNING, 1000) << "Parse Error :" << string_value;
      return true;
    }
    kuaishou::ad::tables::PhotoModelScores *photo_model_scores = extend_fields->mutable_photo_model_scores();
    double number = 0;
    if (json.GetNumber(kCreativeSupportInfoPcvrField, &number)) photo_model_scores->set_pcvr(number);
    if (json.GetNumber(kCreativeSupportInfoContentPredictValueField, &number))
      photo_model_scores->set_content_predict_value(number);
    int64 value = 0;
    if (json.GetInt(kCreativeSupportInfoOcpxActionTypeField, &value))
      photo_model_scores->set_ocpx_action_type(value);
    if (json.GetInt(kCreativeSupportInfoPcvrRankField, &value)) photo_model_scores->set_pcvr_rank(value);
    if (json.GetInt(kCreativeSupportInfoContentRatingField, &value))
      photo_model_scores->set_content_rating(value);

    // 解析 splash_photos & splash_pictures
    auto splash_parser = [&](
          const std::string& str, bool is_photo,
          google::protobuf::RepeatedPtrField<kuaishou::ad::tables::SplashMaterialInfo> *field) {
      base::Json json(base::StringToJson(str));
      if (!json.IsArray()) {
        return;
      }
      if (json.size() == 0) {
        LOG_EVERY_N(WARNING, 1000) << "json size is 0";
        return;
      }
      for (auto item : json.array()) {
        if (!item->IsObject()) {
          LOG_EVERY_N(WARNING, 1000) << "item is not an object";
          continue;
        }
        int64_t id = 0;
        if (is_photo) {
          id = item->GetInt("photoId", 0);
        } else {
          id = item->GetInt("picId", 0);
        }
        if (id == 0) {
          LOG_EVERY_N(WARNING, 1000) << "id is 0";
          continue;
        }
        int64_t height = item->GetInt("height", 0);
        int64_t width = item->GetInt("width", 0);
        auto* splash_info = field->Add();
        splash_info->set_id(id);
        splash_info->set_height(height);
        splash_info->set_width(width);
      }
    };
    std::string splash_photos_string = creative_support_info->splash_photos();
    std::string splash_pictures_string = creative_support_info->splash_pictures();
    auto* splash_photos = extend_fields->mutable_splash_photos();
    auto* splash_pictures = extend_fields->mutable_splash_pictures();
    splash_parser(splash_photos_string, true, splash_photos);
    splash_parser(splash_pictures_string, false, splash_pictures);
    return true;
  }
};

PARSER_REGISTER(CreativeSupportInfoPhotoModelScoresParser, google::protobuf::Message,
                kuaishou::ad::tables::CreativeSupportInfo)

}  // namespace index_builder
}  // namespace ks
