#pragma once

#include <map>
#include <unordered_map>
#include <string>
#include <vector>

#include "falcon/counter.h"
#include "ks/util/json.h"
#include "base/hash_function/city.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/data_build/schemafree/table_row.h"
#include "teams/ad/index_builder/utils/table_config.h"

namespace ks {
namespace index_builder {

bool GetPageIdFromURI(const std::string& uri, int64_t* page_id);
class TargetKeyConvertor {
 public:
  int64_t operator()(const std::string &str) const {
    uint64_t uint64_sign = base::CityHash64(str.data(), str.size());
    return *(reinterpret_cast<int64_t *>(&uint64_sign));
  }
};

static const TargetKeyConvertor convertor;

class HashConvertor {
 public:
  int64_t operator()(const std::string& str) const {
    if (str.empty()) {
      return 0;
    }
    return convertor(str);
  }
};

static const HashConvertor hash_convertor;

std::vector<int64_t> JsonStrToRepeatedInt(const std::string& str);
std::vector<std::string> JsonStrToRepeatedStr(const std::string& str);
template <typename FType, typename TType> void RepeatedAssign(TType* to, const FType& from) {
  if (!to) {
    return;
  }
  to->Clear();
  for (auto& f : from) { *(to->Add()) = f; }
}
/*
  common_tools 里面有具体类转义处理
*/

#define PARSER_REGISTER(parser, TData, TPb)                                   \
  __attribute__((constructor, weak)) void AddParser_##parser() {              \
    auto* parser_manager_ptr = TableParserManager<TData, TPb>::GetInstance(); \
    parser_manager_ptr->AddParser(new parser);                                \
    TPb tmp_inst;                                                             \
    auto type_name = tmp_inst.GetTypeName();                                  \
    ParseManagerByTypeName::GetInstance()->RegisterParseMananger(             \
        parser_manager_ptr, type_name);                                       \
  }
/*
  有一些 parser 解析时是覆盖的，故有可以带优先级的
  PRAIORITY > 100，小的先执行
*/
#define PARSER_REGISTER_WITH_PRIORITY(parser, TData, TPb, PRAIORITY)          \
  __attribute__((constructor(PRAIORITY), weak)) void AddParser_##parser() {   \
    auto* parser_manager_ptr = TableParserManager<TData, TPb>::GetInstance(); \
    parser_manager_ptr->AddParser(new parser);                                \
    TPb tmp_inst;                                                             \
    auto type_name = tmp_inst.GetTypeName();                                  \
    ParseManagerByTypeName::GetInstance()->RegisterParseMananger(             \
        parser_manager_ptr, type_name);                                       \
  }

// 通用的简单解析字符串到 repeated 字段宏
// 要求
//    1. 待解析的字段和目标字段名字一样
//    2. 目标字段为 parse_fields 字段的子字段
//    3. 目标字段为 repeated int64 类型
#define SIMPLE_PARSE_INT64_USE_SPLLIT(pb, src_field, token)                 \
  const auto& src_field = pb->src_field();                                  \
  if (!src_field.empty()) {                                                 \
    auto elems = absl::StrSplit(src_field, token, absl::SkipEmpty());       \
    for (const auto &elem : elems) {                                        \
      int64_t id = 0;                                                       \
      if (absl::SimpleAtoi(elem, &id)) {                                    \
        parse_fields->add_##src_field(id);                                  \
      }                                                                     \
    }                                                                       \
    pb->clear_##src_field();                                                \
    LOG_FIRST_N(INFO, 100) << parse_fields->ShortDebugString();             \
  }

// 通用的简单解析字符串到 repeated 字段宏
// 要求
//    1. 待解析的字段和目标字段名字一样
//    2. 目标字段为 parse_fields 字段的子字段
//    3. 目标字段为 repeated float 类型
#define SIMPLE_PARSE_FLOAT_USE_SPLLIT(pb, src_field, token)                 \
  const auto& src_field = pb->src_field();                                  \
  if (!src_field.empty()) {                                                 \
    auto elems = absl::StrSplit(src_field, token, absl::SkipEmpty());       \
    for (const auto &elem : elems) {                                        \
      float id = 0;                                                       \
      if (absl::SimpleAtof(elem, &id)) {                                    \
        parse_fields->add_##src_field(id);                                  \
      }                                                                     \
    }                                                                       \
    pb->clear_##src_field();                                                \
    LOG_FIRST_N(INFO, 100) << parse_fields->ShortDebugString();             \
  }

// 通用解析 proto 的二进制压缩字符串到对应字段的宏
#define PARSE_BINARY_STRING_TO_MESSAGE(proto, field)                        \
  const std::string& binary_string = proto->field##_binary();               \
  if (!binary_string.empty()) {                                             \
    std::string decode_str;                                                 \
    if (base::Base64Decode(binary_string, &decode_str)) {                   \
      proto->mutable_##field()->ParseFromString(decode_str);                \
    } else {                                                                \
      LOG_EVERY_N(INFO, 10000) << "string decode fails: " << binary_string; \
    }                                                                       \
    proto->clear_##field##_binary();                                        \
  }

// 通用解析 label rf 数据 宏
// 注意
// 1. 待解析的字段在 proto 的第一层
// 2. 目标字段为 proto 下 parse_fields 字段的子字段
// 3. 两个字段同名
// 4. 只解析第一个元素
#define SIMPLE_PARSE_RF_LABEL_DATA(proto, field)                            \
  SIMPLE_PARSE_LABEL_DATA(proto, field, r_f)

// 通用解析 label r_i64 数据 宏
// 注意
// 1. 待解析的字段在 proto 的第一层
// 2. 目标字段为 proto 下 parse_fields 字段的子字段
// 3. 两个字段同名
// 4. 只解析第一个元素
#define SIMPLE_PARSE_RI_LABEL_DATA(proto, field)                            \
  SIMPLE_PARSE_LABEL_DATA(proto, field, r_i64)

// 通用解析 label data 数据 宏
// 注意
// 1. 待解析的字段在 proto 的第一层
// 2. 目标字段为 proto 下 parse_fields 字段的子字段
// 3. 两个字段同名
// 4. 只解析第一个元素
#define SIMPLE_PARSE_LABEL_DATA(proto, field, label_field)                  \
  const auto& field = proto->field();                                       \
  if (!field.empty()) {                                                     \
    kuaishou::ad::tables::LabelData label_data;                             \
    std::string decode_string;                                              \
    if (base::Base64Decode(field, &decode_string)) {                        \
      label_data.ParseFromString(decode_string);                            \
      if (label_data.label_field##_size() > 0) {                            \
        parse_fields->set_##field(label_data.label_field(0));               \
      }                                                                     \
    }                                                                       \
    proto->clear_##field();                                                 \
    LOG_FIRST_N(INFO, 100) << parse_fields->ShortDebugString();             \
  }

class BaseParser {
 public:
  virtual ~BaseParser() {}
};

template <typename TPb>
class PbParser : public BaseParser {
 public:
  virtual ~PbParser() {}
  virtual bool Parse(TPb* ptr) = 0;
  virtual bool ParseTableRow(ad::build_service::TableRow*) { return true; }
};

// TData AdInstance
template <typename TData>
class BaseParserManager {
 public:
  static BaseParserManager* GetInstance() { return nullptr; }
  virtual ~BaseParserManager() {}
  virtual bool Parse(TData*) = 0;
  virtual void AddParser(BaseParser*) = 0;
  virtual bool ParseTableRow(ad::build_service::TableRow*) = 0;
};

// TData AdInstance
// TPb kuaishou::ad::tables::Target 等
template <typename TData, typename TPb>
class TableParserManager : public BaseParserManager<TData> {
 public:
  static BaseParserManager<TData>* GetInstance() {
    static TableParserManager<TData, TPb> instance;
    return &instance;
  }
  virtual ~TableParserManager() {
    for (const auto parse_ptr : parser_hold_vec_) {
      delete parse_ptr;
    }
  }
  bool Parse(TData* ad) override {
    TPb* target_ptr = dynamic_cast<TPb*>(ad);
    if (!target_ptr) {
      LOG(INFO) << "dynamic_cast fail";
      return false;
    }
    for (auto parser_ptr : parser_hold_vec_) {
      auto ret = parser_ptr->Parse(target_ptr);
      if (ret == false) return ret;
    }
    return true;
  }
  bool ParseTableRow(ad::build_service::TableRow* row) override {
    if (row == nullptr) {
      LOG(INFO) << "TableRow nullptr";
      return false;
    }
    for (auto parser_ptr : parser_hold_vec_) {
      auto ret = parser_ptr->ParseTableRow(row);
      if (ret == false) return ret;
    }
    return true;
  }
  void AddParser(BaseParser* parser) override {
    auto* dest_parser = dynamic_cast<PbParser<TPb>*>(parser);
    if (dest_parser) {
      parser_hold_vec_.push_back(dest_parser);
    }
  }

 private:
  std::vector<PbParser<TPb>*> parser_hold_vec_;
};


class ParseManagerByTypeName {
 public:
  static ParseManagerByTypeName* GetInstance() {
    static ParseManagerByTypeName inst;
    return &inst;
  }
  void RegisterParseMananger(
      BaseParserManager<google::protobuf::Message>* mananger_ptr,
      std::string type_name) {
    manager_holder.emplace(type_name, mananger_ptr);
  }
  void Process(google::protobuf::Message* msg) {
    const google::protobuf::Reflection* extend_ref = msg->GetReflection();
    const google::protobuf::Descriptor* extend_desc = msg->GetDescriptor();
    // sub field process
    for (int i = 0; i < extend_desc->field_count(); i++) {
      const google::protobuf::FieldDescriptor* to_field = extend_desc->field(i);
      if (!to_field || to_field->is_repeated()) {
        continue;
      }
      // 第一层嵌套
      if (to_field->cpp_type() ==
          google::protobuf::FieldDescriptor::CPPTYPE_MESSAGE) {
        google::protobuf::Message* sub_message =
            extend_ref->MutableMessage(msg, to_field);
        // 第二层嵌套
        const google::protobuf::Reflection* sub_extend_ref = sub_message->GetReflection();
        const google::protobuf::Descriptor* sub_extend_desc = sub_message->GetDescriptor();
        for (int j = 0; j < sub_extend_desc->field_count(); j++) {
          const google::protobuf::FieldDescriptor* sub_to_field = sub_extend_desc->field(j);
          if (!sub_to_field || sub_to_field->is_repeated()) {
            continue;
          }
          if (sub_to_field->cpp_type() == google::protobuf::FieldDescriptor::CPPTYPE_MESSAGE) {
            google::protobuf::Message* node_message =
              sub_extend_ref->MutableMessage(sub_message, sub_to_field);
            auto type_name = node_message->GetTypeName();
            auto map_it = manager_holder.find(type_name);
            if (map_it != manager_holder.end()) {
              map_it->second->Parse(node_message);
            }
          }
        }
        auto type_name = sub_message->GetTypeName();
        auto map_it = manager_holder.find(type_name);
        if (map_it != manager_holder.end()) {
          map_it->second->Parse(sub_message);
        }
      }
    }
    auto type_name = msg->GetTypeName();
    auto map_it = manager_holder.find(type_name);
    if (map_it != manager_holder.end()) {
      map_it->second->Parse(msg);
    }
  }

  void ProcessTableRow(ad::build_service::TableRow* row) {
    if (row == nullptr) { return; }
    const auto& table_name = row->GetTableName();
    if (table_name.empty()) {
      LOG(ERROR) << "table name empty";
      return;
    }
    auto type_name = TableConfigManager::GetInstance()->GetPbNameByTableName(table_name);
    if (type_name.empty()) {
      LOG(ERROR) << "Get pb name from table name err, table name:" << table_name;
      return;
    }
    auto map_it = manager_holder.find(type_name);
    if (map_it != manager_holder.end()) {
      map_it->second->ParseTableRow(row);
    }
  }

 private:
  std::unordered_map<std::string, BaseParserManager<google::protobuf::Message>*>
      manager_holder;
};

}  // namespace index_builder
}  // namespace ks
