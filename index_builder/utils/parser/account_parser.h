#pragma once
#include <string>
#include <vector>
#include <map>
#include <algorithm>
#include <unordered_set>

#include "base/hash_function/city.h"
#include "absl/strings/substitute.h"
#include "base/encoding/base64.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
#include "ks/base/abtest/metrics/abtest_metric.h"
#include "teams/ad/index_builder/utils/kconf.h"
#include "teams/ad/index_builder/utils/parser/account_industry_parser.h"

namespace ks {
namespace index_builder {

static void AccountLabelParser(kuaishou::ad::tables::Account* account) {
  using LabelFunction =
    std::function<void(kuaishou::ad::tables::Account* account,
                       const kuaishou::ad::tables::OneChannel& one_channel)>;
  LabelFunction operation_author_tag = [](kuaishou::ad::tables::Account* account,
      const kuaishou::ad::tables::OneChannel& one_channel) {
    if (one_channel.label_data().label_data().r_f_size() > 0) {
      if (one_channel.label_data().label_data().r_f(0)) {
        account->mutable_extend_fields()->set_ecom_is_operation_author(1);
      } else {
        account->mutable_extend_fields()->set_ecom_is_operation_author(0);
      }
    }
    if (account->extend_fields().ecom_is_operation_author() != 0) {
      LOG_FIRST_N(INFO, 100) << "AccountLabelParser, operation_author_tag " << account->ShortDebugString();
    }
  };
  LabelFunction ecom_is_new_account = [](kuaishou::ad::tables::Account* account,
      const kuaishou::ad::tables::OneChannel& one_channel) {
    if (one_channel.label_data().label_data().r_f_size() > 0) {
      if (one_channel.label_data().label_data().r_f(0)) {
        account->mutable_extend_fields()->set_ecom_is_new_account(true);
      } else {
        account->mutable_extend_fields()->set_ecom_is_new_account(false);
      }
    }
    if (account->extend_fields().ecom_is_operation_author()) {
      LOG_FIRST_N(INFO, 100) << "AccountLabelParser, ecom_is_new_account " << account->ShortDebugString();
    }
  };
  static std::map<std::string, LabelFunction> function_map {
    {"ecom_is_operation_author", operation_author_tag},
    {"ecom_is_new_account", ecom_is_new_account}
  };
  for (const auto& one_channel : account->extend_fields().level_label_data().channel_data()) {
    auto channel_name = one_channel.name();
    auto map_it = function_map.find(channel_name);
    if (map_it != function_map.end()) {
      auto func = map_it->second;
      func(account, one_channel);
    }
  }
  // 已解析则去掉
  account->mutable_extend_fields()->clear_level_label_data();
}

static void ParseKeyActionSwitch(kuaishou::ad::tables::Account* account) {
  auto* account_support_info = account->mutable_account_support_info();
  base::Json json(base::StringToJson(account_support_info->key_action_switch()));
  if (!json.IsArray()) {
    LOG_EVERY_N(ERROR, 100000)
        << "format error, key_action_switch not valid json array string. string_value="
        << account_support_info->key_action_switch();
    return;
  }
  auto* mutable_extend_fields = account_support_info->mutable_extend_fields();
  int64 action_type = 0;
  for (auto iter = json.array_begin(); iter != json.array_end(); ++iter) {
    if ((*iter)->IntValue(&action_type)) {
      mutable_extend_fields->add_key_action_switch(action_type);
    }
  }
}

class AdDspAccountParser : public PbParser<kuaishou::ad::tables::Account> {
 public:
  virtual ~AdDspAccountParser() {}
  static int64_t GetCityProductId(const std::string& product_name) {
    uint64_t uint64_sign =
        base::CityHash64(product_name.data(), product_name.size());
    return *(reinterpret_cast<int64_t*>(&uint64_sign));
  }
  static void ParseExpectCostInfoForExtendFields(kuaishou::ad::tables::Account* account) {
    auto& expect_cost_info = account->account_support_info().expect_cost_info();
    if (expect_cost_info.empty()) { return; }
    base::Json json(base::StringToJson(expect_cost_info));
    if (!json.IsArray()) { return; }
    auto* extend_fields = account->mutable_extend_fields();
    for (auto* item : json.array()) {
      if (item == nullptr) { continue; }
      auto* info = extend_fields->add_expect_cost_info();
      info->set_ocpx_action_type(item->GetInt("ocpxActionType", 0));
      info->set_value(item->GetFloat("value", 0.0));
      info->set_constraint_ocpx_action_type(item->GetInt("constraintOcpxActionType", 0));
      info->set_constraint_value(item->GetFloat("constraintValue", 0.0));
    }
    account->mutable_account_support_info()->clear_expect_cost_info();
  }

  static void ParseAccountIdAbtestHit(kuaishou::ad::tables::Account* account) {
    static const std::unordered_set<std::string> kWorldList = {"w_n_kuaishou_apps_sid_20",
                                                               "w_n_kuaishou_apps_sid_21"};
    ExperimentInfo exp_info = abtest::GetExperimentInfo(account->id(), std::to_string(account->id()),
                                                        std::to_string(account->id()), kWorldList);
    for (const auto& kv : exp_info) {
      if (kv.second.experimentId.empty() || kv.second.groupId.empty()) continue;
      auto* hit_info = account->mutable_extend_fields()->add_account_id_abtest_hit_info();
      hit_info->set_experiment_id(kv.second.experimentId);
      hit_info->set_group_id(kv.second.groupId);
    }
    LOG_EVERY_N(INFO, 1000) << "account hit exp: " << account->ShortDebugString();
  }
  static void ParseMinCostInfoForExtendFields(kuaishou::ad::tables::Account* account) {
    if (AdKconfUtil::enableAccountMinCostInfoParse()) {
      if (account == nullptr) return;
      auto& min_cost_info = account->account_support_info().min_cost_info();
      if (min_cost_info.empty()) return;
      base::Json json(base::StringToJson(min_cost_info));
      if (!json.IsArray()) return;
      auto* extend_fields = account->mutable_extend_fields();
      for (auto* item : json.array()) {
        if (item == nullptr) continue;
        auto* info = extend_fields->add_min_cost_info();
        info->set_ocpx_action_type(item->GetInt("ocpxActionType", 0));
        info->set_deep_conversion_type(item->GetInt("deepConversionType", 0));
        info->set_min_cost(item->GetFloat("minCost", 0.0));
        info->set_auto_mode(item->GetInt("autoMode", 0));
        LOG_EVERY_N(INFO, 1000) << "min_cost_info=" << info->ShortDebugString();
      }
      account->mutable_account_support_info()->clear_min_cost_info();
      // 测试时打点
      ks::infra::PerfUtil::CountLogStash(1, "ad.index_builder", "parse_account_min_cost_info");
    }
  }
  static void ParseAdjustPriceCaliberForExtendFields(kuaishou::ad::tables::Account* account) {
    if (AdKconfUtil::enableAdjustPriceCaliberParse()) {
      if (account == nullptr) return;
      auto& adjust_price_caliber = account->account_support_info().adjust_price_caliber();
      if (adjust_price_caliber.empty()) return;
      base::Json json(base::StringToJson(adjust_price_caliber));
      if (!json.IsObject()) return;
      auto* extend_fields = account->mutable_extend_fields();
      for (const auto& [k, v] : json.objects()) {
        if (v == nullptr) {
          LOG_EVERY_N(ERROR, 100000)
              << "ParseAdjustPriceCaliberForExtendFields json error adjust_price_caliber "
              << adjust_price_caliber;
          continue;
        } else {
          auto* info = extend_fields->add_adjust_price_caliber_info();
          info->set_shadow_opt_goal(k);
          int64_t int_value;
          if (v->IntValue(&int_value)) {
            info->set_price_caliber_type(int_value);
          } else {
            LOG_EVERY_N(ERROR, 100000)
                << "ParseAdjustPriceCaliberForExtendFields value error, adjust_price_caliber "
                << adjust_price_caliber;
            continue;
          }
        }
      }
      account->mutable_account_support_info()->clear_adjust_price_caliber();
      // 测试时打点
      ks::infra::PerfUtil::CountLogStash(1, "ad.index_builder", "parse_adjust_price_caliber_info");
    }
  }

  static void ParseAccountIndustry(kuaishou::ad::tables::Account* account) {
    if (account == nullptr) return;
    auto* account_industry = account->mutable_account_industry();
    if (!account->industry_info_str().empty()) {
      account_industry->set_industry_info_str(account->industry_info_str());
      account->clear_industry_info_str();
    }
    if (!account->user_category().empty()) {
      account_industry->mutable_account_category()->set_user_category(account->user_category());
      account->clear_user_category();
    }
    if (!account->mark_category().empty()) {
      account_industry->mutable_account_category()->set_mark_category(account->mark_category());
      account->clear_mark_category();
    }
    AccountIndustryParser parser;
    parser.Parse(account_industry);
    account->clear_account_industry_binary();
  }

  static void ParseOcpxActionTypeConstraint(kuaishou::ad::tables::Account* account) {
    if (account == nullptr) return;
    auto* account_support_info = account->mutable_account_support_info();
    base::Json json(base::StringToJson(account_support_info->ocpx_action_type_constraint()));
    if (!json.IsArray()) {
      LOG_EVERY_N(ERROR, 100000)
          << "format error, ocpx_action_type_constraint not valid json array string. string_value="
          << account_support_info->ocpx_action_type_constraint();
      return;
    }

    // 获取 extend_fields
    auto* extend_fields = account_support_info->mutable_extend_fields();
    // 清空当前内容
    extend_fields->clear_constraint_info_ocpx_action_type();
    extend_fields->clear_constraint_info_value();

    // 遍历 JSON 数组并解析内容
    for (const auto& item : json.array()) {
        extend_fields->add_constraint_info_ocpx_action_type(item->GetInt("ocpxActionType", 0));
        extend_fields->add_constraint_info_value(item->GetNumber("value", 0.0));
    }
    LOG_FIRST_N(INFO, 10) << "ParseOcpxActionTypeConstraint account_support_info extend_fields "
        << account->ShortDebugString();
    account->mutable_account_support_info()->clear_ocpx_action_type_constraint();
  }

  static void ParseIncExploreInfos(kuaishou::ad::tables::Account* account) {
      if (account == nullptr) return;
      auto* account_support_info = account->mutable_account_support_info();
      base::Json json(base::StringToJson(account_support_info->inc_explore_infos()));

      if (!json.IsArray()) {
          LOG_EVERY_N(ERROR, 100000)
              << "format error, inc_explore_infos not valid json array string. string_value="
              << account_support_info->inc_explore_infos();
          return;
      }

      auto* extend_fields = account_support_info->mutable_extend_fields();
      extend_fields->clear_inc_explore_info();

      for (const auto& item : json.array()) {
          auto* inc_explore_info = extend_fields->add_inc_explore_info();
          inc_explore_info->set_inc_explore_start_time(item->GetInt("incExploreStartTime", 0));
          inc_explore_info->set_inc_explore_end_time(item->GetInt("incExploreEndTime", 0));
          inc_explore_info->set_ocpx_action_type(item->GetInt("ocpxActionType", 0));
          inc_explore_info->set_deep_conversion_type(item->GetInt("deepConversionType", 0));
          inc_explore_info->set_auto_mode(item->GetInt("autoMode", 0));
          inc_explore_info->set_explore_budget(item->GetNumber("exploreBudget", 0.0));
          inc_explore_info->set_is_paused(item->GetBoolean("isPaused", false));
      }

      LOG_FIRST_N(INFO, 10) << "ParseIncExploreInfos account_support_info extend_fields "
          << account->ShortDebugString();
      account_support_info->clear_inc_explore_infos();
  }

  bool Parse(kuaishou::ad::tables::Account* account) override {
    if (account->city_product_id() == 0) {
      account->set_city_product_id(GetCityProductId(account->product_name()));
    }
    ParseExpectCostInfoForExtendFields(account);
    ParseAccountIdAbtestHit(account);
    AccountLabelParser(account);
    ParseKeyActionSwitch(account);
    ParseMinCostInfoForExtendFields(account);
    ParseAdjustPriceCaliberForExtendFields(account);
    ParseAccountIndustry(account);
    ParseOcpxActionTypeConstraint(account);
    ParseIncExploreInfos(account);
    {
      auto* extend_fields = account->mutable_extend_fields();
      extend_fields->set_city_corporation_id(hash_convertor(account->corporation_name()));
      extend_fields->set_define_product_hash(
          hash_convertor(account->ad_crm_account_operator_define_label().define_product()));
      extend_fields->set_define_product_type_hash(
          hash_convertor(account->ad_crm_account_operator_define_label().define_product_type()));
      extend_fields->set_city_license_no(hash_convertor(account->license_no()));
      if (extend_fields->account_id_abtest_hit_info_size() == 2) {
        auto& tmp_hit_info = extend_fields->account_id_abtest_hit_info(0);
        extend_fields->set_account_by_ab_hash_a(
            convertor(absl::Substitute("$0_$1", tmp_hit_info.experiment_id(), tmp_hit_info.group_id())));
        auto& tmp_hit_info_b = extend_fields->account_id_abtest_hit_info(1);
        extend_fields->set_account_by_ab_hash_b(
            convertor(absl::Substitute("$0_$1", tmp_hit_info_b.experiment_id(), tmp_hit_info_b.group_id())));
      }
    }
    return true;
  }
};
PARSER_REGISTER(AdDspAccountParser, google::protobuf::Message,
                kuaishou::ad::tables::Account)
}  // namespace index_builder
}  // namespace ks
