#pragma once
#include <string>
#include <vector>
#include <algorithm>
#include "absl/strings/str_join.h"
#include "base/strings/string_number_conversions.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"

namespace ks {
namespace index_builder {



/* ad_dsp_ecom_hosting_project parser area */

class ItemInfoParser : public PbParser<kuaishou::ad::tables::AdDspEcomHostingProject> {
 public:
  virtual ~ItemInfoParser() {}
  bool Parse(kuaishou::ad::tables::AdDspEcomHostingProject* project) override {
    std::string string_value = project->item_infos();
    if (string_value.empty()) return true;
    Json json(StringToJson(string_value));
    for (auto item : json.array()) {
      int64_t id = item->IntValue(-1);
      if (id > 0) {
        project->mutable_extend_fields()->add_item_id(id);
      }
    }
    project->clear_item_infos();
    // 解析 product_roi
    std::string str_val = project->product_roi();
    if (str_val.empty()) {
      return true;
    }
    auto* extend_fields = project->mutable_extend_fields();
    Json roi_json(StringToJson(str_val));
    for (auto item : roi_json.array()) {
      auto product_id_str = item->GetString("productId", "");
      int64_t product_id = 0;
      if (!product_id_str.empty()) {
        if (base::StringToInt64(product_id_str, &product_id)) {
          extend_fields->add_product_ids(product_id);
        }
      }
      auto min_roi = item->GetFloat("minRoiRatio", 0.0);
      auto max_roi = item->GetFloat("maxRoiRatio", 0.0);
      auto roi_ratio = item->GetFloat("roiRatio", 0.0);
      auto cpa_bid = item->GetInt("cpaBid", 0);
      extend_fields->add_min_rois(min_roi);
      extend_fields->add_max_rois(max_roi);
      extend_fields->add_roi_ratios(roi_ratio);
      extend_fields->add_cpa_bids(cpa_bid);
      // 解析 photoIds
      auto* origin_photo_ids = item->Get("photoIds");
      if (origin_photo_ids == nullptr) continue;
      if (origin_photo_ids->IsArray()) {
        std::vector<std::string> tmp;
        for (auto item : origin_photo_ids->array()) {
          auto str = item->StringValue("");
          if (!str.empty()) {
            tmp.push_back(str);
          }
        }
        std::string photo_ids_str = absl::StrJoin(tmp, ",");
        extend_fields->add_photo_ids(photo_ids_str);
        LOG_FIRST_N(INFO, 10) << "photo_ids_str=" << photo_ids_str;
      }
      LOG_FIRST_N(INFO, 10) << "product_roi parse, product_id=" << product_id
                            << ", max_roi=" << max_roi << ", min_roi=" << min_roi
                            << ", roi_ratio=" << roi_ratio << ", cpa_bid=" << cpa_bid;
    }
    return true;
  }
};

PARSER_REGISTER(ItemInfoParser, google::protobuf::Message,
                kuaishou::ad::tables::AdDspEcomHostingProject)

}  // namespace index_builder
}  // namespace ks
