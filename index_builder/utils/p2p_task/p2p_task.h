#pragma once
#include <map>
#include <string>
#include <vector>

/*
  p2p 任务集中推送 附加到索引产出目录
*/

namespace ks {
namespace index_builder {

std::string GetLastValidName(const std::string& file_dir);
std::string GetlatestValidDir(const std::string& hdfs_path);
std::vector<std::string> GetFilesByDir(const std::string& hdfs_path);

class P2pTaskDetector {
 public:
  explicit P2pTaskDetector(const std::string& hdfs_path);
  bool IsValid();
  void PrintLog();
  std::string GetTaskName() { return dir_name_; }
  std::vector<std::string> GetP2pFiles() { return p2p_files_; }
  std::string GetSrcDir() { return file_dir_; }
 protected:
  std::string file_dir_;
  std::string dir_name_;
  std::vector<std::string> p2p_files_;
};

class P2pTaskManager {
 public:
  static P2pTaskManager* GetInstance() {
    static P2pTaskManager instance;
    return &instance;
  }
  void PrintLog();
  void RegisterTask(const std::string& server_name, const std::string& local_dir,
                    const std::string& update_dir);
  void Init();
  void DoUploadTask();
  bool GetStatusByServerName(const std::string& server_name);
 protected:
  void DoP2pTask(std::string server_name);
  void P2pTaskImp(std::string server_name, std::string src_hdfs_path,
                  std::string local_path, std::string dest_hdfs_path);
  // server_name -> p2p tasks
  std::vector<P2pTaskDetector> sn_p2p_tasks;
  std::map<std::string, std::string> sn_local_path;
  std::map<std::string, std::string> sn_update_path;
  std::map<std::string, bool> sn_status;
 private:
  P2pTaskManager() = default;
  ~P2pTaskManager() = default;
};

}  // namespace index_builder
}  // namespace ks
