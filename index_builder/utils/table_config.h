#pragma once
#include <unordered_map>
#include <string>
#include "teams/ad/index_builder/utils/builder.pb.h"
#include "teams/ad/index_builder/utils/kconf.h"
#include "teams/ad/index_builder/multiform_filter/proto_tools.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/meta.pb.h"

/*
  note:
    横级联以后, table-enum-pb 关系将不完整
    这个 manager 用于维护这个关系
    暂时只提供 table<->pb table<->enums,pb<->enums 通过 common_tools.h 获取
    enum = enum.AdInstanceType 中枚举
    pb = ad.tables.Creative
*/
namespace ks {
namespace index_builder {
class TableConfigManager {
 public:
  TableConfigManager() {}
  static TableConfigManager* GetInstance() {
    static TableConfigManager inst;
    return &inst;
  }

  void InitByKconf() {
    auto config = AdKconfUtil::tableConfig()->data();
    for (auto &item : config.table_dump_configs()) {
      tablename2proto_.emplace(item.table_name(), GetPbNameByAdInstanceType(item.type()));
      pb2table_name_.emplace(GetPbNameByAdInstanceType(item.type()), item.table_name());
    }
  }

  template<typename DumpConfig>
  void Init(const DumpConfig& dump_conf) {
    for (auto& item : dump_conf.table_dump_configs()) {
      tablename2proto_.emplace(item.table_name(),
                               GetPbNameByAdInstanceType(item.type()));
      pb2table_name_.emplace(GetPbNameByAdInstanceType(item.type()),
                             item.table_name());
    }
    InitByKconf();
  }

  void InitByDumpInfo(const kuaishou::ad::tables::DumpInfo dump_info) {
    for (const auto& info : dump_info.info()) {
      const auto& table_name = info.table_name();
      const auto& proto_name = info.proto_name();
      tablename2proto_.emplace(table_name,
                               proto_name);
      pb2table_name_.emplace(proto_name, table_name);
    }
  }

  std::string GetPbNameByTableName(const std::string& table_name) {
    if (tablename2proto_.find(table_name) != tablename2proto_.end()) {
      return tablename2proto_.at(table_name);
    }
    return std::string();
  }

  std::string GetTableNameByPbName(const std::string& pb_name) {
    if (pb2table_name_.find(pb_name) != pb2table_name_.end()) {
      return pb2table_name_.at(pb_name);
    }
    return std::string();
  }

  std::string GetTableNameByEnumName(const std::string& enums) {
    auto pb_name = GetPbNameByAdInstanceType(enums);
    return GetTableNameByPbName(pb_name);
  }

  std::string GetEnumNameByTableName(const std::string& table_name) {
    auto pb_name = GetPbNameByTableName(table_name);
    return GetEnumNameByPbName(pb_name);
  }

 private:
  std::unordered_map<std::string, std::string> tablename2proto_;
  std::unordered_map<std::string, std::string> pb2table_name_;
};
}  // namespace index_builder
}  // namespace ks
