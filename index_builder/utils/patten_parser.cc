#include "teams/ad/index_builder/utils/patten_parser.h"

#include <utility>

#include "serving_base/utility/time_helper.h"
#include "base/strings/string_split.h"
#include "base/time/timestamp.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"

using kuaishou::ad::AdEnum;

namespace ks {
namespace index_builder {

PattenParser::PattenParser() {
  for (int i = 0; i < 100; i++) {
    std::string key = "{" + std::to_string(i) + "_days_before}";
    relate_time_pattens_[key] = i * 86400;
  }

  for (int i = 0; i < 30 * 24; i++) {
    std::string key = "{" + std::to_string(i) + "_hours_before}";
    relate_time_pattens_[key] = i * 3600;
  }

  for (int64_t i = 0; i < 7 * 24 * 60; i++) {
    std::string key = "{" + std::to_string(i) + "_minutes_before}";
    relate_time_pattens_[key] = i * 60;
  }

  for (int64_t i = 0; i < 1024; i++) {
    std::string key = "{" + std::to_string(i) + "_seconds_before}";
    relate_time_pattens_[key] = i;
  }
}

std::string PattenParser::ParseQuery(const std::string& query, const std::string& shard_name) {
  std::string sql_query = query;
  if (sql_query.find("{shard_suffix}") != sql_query.npos) {
    size_t pos = shard_name.rfind("_");
    assert(pos != shard_name.npos);
    std::string shard_suffix = shard_name.substr(pos);
    if (shard_suffix.size() < 2 || shard_suffix.at(1) < '0' || shard_suffix.at(1) > '9') {
      sql_query = base::StringReplace(sql_query, "{shard_suffix}", "", true);
    } else {
      sql_query = base::StringReplace(sql_query, "{shard_suffix}", shard_suffix, true);
    }
  }

  while (true) {
    size_t begin = sql_query.find("{");
    if (begin == std::string::npos) {
      break;
    }

    size_t end = sql_query.find("}", begin + 1);
    if (end == std::string::npos) {
      break;
    }

    std::string patten = sql_query.substr(begin, end + 1 - begin);
    int64_t patten_value;
    if (ParsePattenValue(patten, &patten_value)) {
      sql_query = base::StringReplace(sql_query, patten, std::to_string(patten_value), true);
    } else {
      LOG(INFO) << "patten_parser.ParsePattenValue(" << patten << ") failed.";
      break;
    }
  }

  return std::move(sql_query);
}

int64 PattenParser::GetCurrentDayMs() {
  int64 ts = base::GetTimestamp();
  std::string current_day_string;
  CHECK(serving_base::TimeHelper::TimestampToString(ts,
      serving_base::TimeHelper::kDay, &current_day_string));

  uint64 current_day_ts = 0;
  CHECK(serving_base::TimeHelper::StringToTimestamp(current_day_string,
      serving_base::TimeHelper::kDay, &current_day_ts));

  return current_day_ts / 1000;
}

bool PattenParser::ParsePattenValue(const std::string& patten, int64_t* value) {
  auto it = relate_time_pattens_.find(patten);
  if (it != relate_time_pattens_.end()) {
    *value = GetCurrentDayMs() - it->second * 1000;
    return true;
  }

  std::vector<std::string> vec;
  base::SplitStringUsingSubstr(patten, "::", &vec);
  if (vec.size() != 3 || vec[0] != "{AdEnum") {
    LOG(ERROR) << "pattern: " << patten << " maybe invalid.";
    return false;
  }

  std::string enum_value = vec[2].substr(0, vec[2].size() - 1);
  if (vec[1] == "PutStatus") {
    AdEnum::PutStatus put_status;
    if (!kuaishou::ad::AdEnum_PutStatus_Parse(enum_value, &put_status)) {
      return false;
    }
    *value = put_status;
  } else if (vec[1] == "ReviewStatus") {
    AdEnum::ReviewStatus review_status;
    if (!kuaishou::ad::AdEnum_ReviewStatus_Parse(enum_value, &review_status)) {
      return false;
    }
    *value = review_status;
  } else if (vec[1] == "AdDspAccountFrozenStatus") {
    AdEnum::AdDspAccountFrozenStatus account_status;
    if (!kuaishou::ad::AdEnum_AdDspAccountFrozenStatus_Parse(enum_value, &account_status)) {
      return false;
    }
    *value = account_status;
  } else {
    return false;
  }

  return true;
}

}  // namespace index_builder
}  // namespace ks

