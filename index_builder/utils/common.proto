syntax = "proto3";

package ks.index_builder;

// adapter 场景分类
enum AdapterScene {
  default = 0;
  external = 1;
  internal = 2;
  search = 3;
  universe = 4;
  cold = 5;
  style = 6;
  hosting = 7;
  dpa = 8;
}

// 字段裁剪枚举
enum FieldFilterEnum {
  collective = 0;
}

// 索引对应环境
enum IndexEnv {
  unknow_env = 0;
  prod = 1;
  preonline = 2;  // 索引隔离环境
  candidate = 3;  // 测试环境索引
  dryrun = 4;
}

// 索引类型
enum IndexType {
  unknow_index_type = 0;
  all_data = 1;
  no_creative = 2;
}

message TableShardOption {
  string shard_key = 1;
}

message ShardOptions {
  int32 shard_num = 1;
  map<string, TableShardOption> shard_table = 2;
}
