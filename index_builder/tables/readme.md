# Container\<Type>
  对于 struct Type, Container\<Type> 具备以下功能:
  序列化与反序列化: 支持序列化必须满足 std::is_trivial\<Type>::value == true
  1. 只允许有默认构造  Type() = default;
  2. 不允许有析构、拷贝构造等
  3. 不允许继承
  4. 不允许有不满足以上条件的成员变量
# Demo of Type
  ```C++
  struct Demo {
    uint16_t id;
    // ATTENTION 必须叫这个名字, container 中有使用
    AttachInfo attach;
    static Demo gen_from_proto(const google::protobuf::Message &pb, bool is_attach) {
      Demo tmp = create();
      const PbDemo *pb_ptr = nullptr;
      if (pb_ptr->GetDescriptor() == PbDemo::default_instance().GetDescriptor()) {
        pb_ptr = dynamic_cast<const PbDemo *>(&pb);
        tmp.id = pb_ptr->id();
        if (is_attach && tmp.key_id() != 0) {
          std::string str;
          if (pb_ptr->SerializeToString(&str)) {
            tmp.attach.data = new AttachData(str.c_str(), str.length());
          }
        }
      }
      return std::forward<Demo>(tmp);
    }
    // ATTENTION 必须有这个函数
    static Demo create() {
      Type tmp;
      ::bzero(&tmp, sizeof(tmp));
      return std::forward<Demo>(tmp);
    }
    // ATTENTION 必须有这个函数
    void clean_attach() {
      if (!attach.is_static && attach.data != nullptr) {
        delete attach.data;
        attach.data = nullptr;
      }
    }
    // ATTENTION 必须有这个函数
    uint64_t key_id() {return id;}
  };
  ```
# AttachInfo
  数据制作时, 单个attach文件大小必须 < 4GB
  ```C++
  struct AttachInfo {
    union {
    struct {
      uint64_t is_static:1;  // 0:从 data 中取数据; 1:从静态文件中取数据
      uint64_t idx:7;  // 最多拆分 128 份, 占用内存 128*4 GB
      uint64_t length:24;
      uint64_t offset:32;
    };
    AttachData * data;
    };
  };
  ```

# 测试
  参考 data_gate_test, 在 teams/ad/index_builder/test/data_gate_test.cc 添加以下代码即可
  ``` C++
  void AccountPbInit(kuaishou::ad::tables::Account &pb, int id, std::string &&str) {
    pb.set_id(id);
    pb.set_product_name(str);
  }
  TEST_OF_TABLE(Account, AccountPbInit)
  ```
  编译
  ``` shell
  # 生成 Makefile
  ./gen_makefile.sh teams/ad/index_builder/BUILD
  # 编译测试用例
  kmake test -j
  ```
