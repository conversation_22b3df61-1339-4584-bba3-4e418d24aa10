#pragma once
#include <set>
#include <string>

#include "base/hash_function/city.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_base/src/hash/hash.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/engine_base/creative_score_util/creative_score_util.h"

namespace tables {
namespace util {

class BudgetSchedule {
 public:
  static bool Parse(const std::string &json_str, int64_t budget_schedule[]);
};

#define SET_FIELD_BY_PB(proto_ptr, struct_ref, field_name, default_value) \
  if (proto_ptr->has_##field_name())                                      \
    struct_ref.field_name = proto_ptr->field_name();                      \
  else                                                                    \
    struct_ref.field_name = default_value;

#define SET_FIELD_BY_PB_NAME(proto_ptr, struct_ref, struct_field_name, pb_field_name, default_value) \
  if (proto_ptr->has_##pb_field_name())                                      \
    struct_ref.struct_field_name = proto_ptr->pb_field_name();                      \
  else                                                                    \
    struct_ref.struct_field_name = default_value;

static int64_t GenDateIdKey(int64_t day_timestamp, int64_t level_id) {
  base::uint128 union_key;
  union_key.first = day_timestamp;
  union_key.second = level_id;
  uint64_t sign = base::Hash128to64(union_key);
  return *(reinterpret_cast<int64_t*>(&sign));
}

static const int kWeekDays = 7;
static const int kScheduleBitVecNumbers = 3;

static bool ParseForbiddenSchedule(const std::string& json_value,
    int32_t* forbidden_array, int array_size = kWeekDays) {
  if (array_size != kWeekDays) {
    return false;
  }

  memset(forbidden_array, 0, sizeof(int) * array_size);

  base::Json json(base::StringToJson(json_value));
  if (!json.IsArray() || json.size() != kWeekDays) {
    return false;
  }

  for (int i = 0; i < kWeekDays; i++) {
    base::Json* inner_json = json.Get(i);
    if (!inner_json->IsArray()) {
      LOG(ERROR) << "The [" << i << "]th item invalid. value: " << json_value;
      memset(forbidden_array, 0, sizeof(int) * array_size);
      return false;
    }

    for (auto* inner_item : inner_json->array()) {
      int64_t pos = 0;
      if (inner_item->IntValue(&pos)) {
        if (pos < 0 || pos > 23) {
          LOG(ERROR) << "The [" << i << "]th item invalid. value: " << json_value;
          memset(forbidden_array, 0, sizeof(int) * array_size);
          return false;
        }

        forbidden_array[i] |= 1 << pos;
      }
    }
  }

  return true;
}

static bool ParseBudgetSchedule(const std::string& json_value, int64_t* budget_schedule,
    int array_size = kWeekDays) {
  if (json_value.empty() || array_size != kWeekDays) {
    return false;
  }

  memset(budget_schedule, 0, sizeof(int64_t) * array_size);

  base::Json json(base::StringToJson(json_value));
  if (!json.IsArray() || json.size() != kWeekDays) {
    return false;
  }

  for (int i = 0; i < kWeekDays; i++) {
    int64_t budget = 0;
    if (!json.Get(i)->IntValue(&budget) || budget < 0) {
      LOG(ERROR) << "json_value invalid. value: " << json_value << ", i: " << i;
      memset(budget_schedule, 0, sizeof(int64_t) * array_size);
      continue;
    }

    // [周- ... 周日] --> [周日 周一 ... 周六]
    budget_schedule[(i+1) % kWeekDays] = budget;
  }

  return true;
}

// 根据素材尺寸计算模板类型
static void CalWidthHeightRatioType(double ratio, int64_t* width_height_ratio_type) {
  // type 1
  if (ratio >= 1 && ratio < 1.75) {
    *width_height_ratio_type |= 1;
  }

  // type 3
  if (ratio >= 1.75) {
    *width_height_ratio_type |= 4;
  }

  // type 4
  if (ratio >= 1 && ratio < 2) {
    *width_height_ratio_type |= 8;
  }

  // type 5
  if (ratio >= 1.55 && ratio <= 2.05) {
    *width_height_ratio_type |= 16;
  }
}

static const double hash_factor = 1.5;
static const size_t max_hash_table_size = 1 << 19;

// 计算哈希表槽位数
static size_t GetHashTableSize(size_t record_num) {
  size_t n = record_num * hash_factor - 1;
  n |= n >> 1;
  n |= n >> 2;
  n |= n >> 4;
  n |= n >> 8;
  n |= n >> 16;
  n |= n >> 32;

  return (n > max_hash_table_size) ? max_hash_table_size : n + 1;
}

}  // namespace util
}  // namespace tables

