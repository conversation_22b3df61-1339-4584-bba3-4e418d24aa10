#pragma once

#include "teams/ad/index_builder/tables/data_gate/risk_photo_initiative.h"
#include "teams/ad/index_builder/tables/data_gate/ad_creative_preview.h"
#include "teams/ad/index_builder/tables/data_gate/campaign_day_charge.h"
#include "teams/ad/index_builder/tables/data_gate/campaign_day_charge_tag.h"
#include "teams/ad/index_builder/tables/data_gate/target_paid_audience.h"
#include "teams/ad/index_builder/tables/data_gate/agent.h"
#include "teams/ad/index_builder/tables/data_gate/ad_position_resource.h"
#include "teams/ad/index_builder/tables/data_gate/risk_unit_initiative.h"
#include "teams/ad/index_builder/tables/data_gate/account_day_charge.h"
#include "teams/ad/index_builder/tables/data_gate/account_day_charge_tag.h"
#include "teams/ad/index_builder/tables/data_gate/risk_industry_white_account.h"
#include "teams/ad/index_builder/tables/data_gate/upload_population_orientation.h"
#include "teams/ad/index_builder/tables/data_gate/account_balance.h"
#include "teams/ad/index_builder/tables/data_gate/ad_app.h"
#include "teams/ad/index_builder/tables/data_gate/risk_target.h"
#include "teams/ad/index_builder/tables/data_gate/cert.h"
#include "teams/ad/index_builder/tables/data_gate/unit.h"
#include "teams/ad/index_builder/tables/data_gate/account.h"
#include "teams/ad/index_builder/tables/data_gate/ad_app_release.h"
#include "teams/ad/index_builder/tables/data_gate/ad_position.h"
#include "teams/ad/index_builder/tables/data_gate/ad_position_strategy.h"

#include "teams/ad/index_builder/tables/data_gate/agent_account.h"
#include "teams/ad/index_builder/tables/data_gate/campaign.h"
#include "teams/ad/index_builder/tables/data_gate/card_show_data.h"
#include "teams/ad/index_builder/tables/data_gate/creative.h"
#include "teams/ad/index_builder/tables/data_gate/industry_v3.h"
#include "teams/ad/index_builder/tables/data_gate/material.h"
#include "teams/ad/index_builder/tables/data_gate/photo_status.h"
#include "teams/ad/index_builder/tables/data_gate/risk_account_initiative.h"
#include "teams/ad/index_builder/tables/data_gate/risk_creative_target.h"
#include "teams/ad/index_builder/tables/data_gate/risk_industry_initiative.h"
#include "teams/ad/index_builder/tables/data_gate/site_ext_info.h"
#include "teams/ad/index_builder/tables/data_gate/target.h"
#include "teams/ad/index_builder/tables/data_gate/trace_api_detection.h"
#include "teams/ad/index_builder/tables/data_gate/trace_util.h"
#include "teams/ad/index_builder/tables/data_gate/unit_day_charge.h"
#include "teams/ad/index_builder/tables/data_gate/unit_day_charge_tag.h"
#include "teams/ad/index_builder/tables/data_gate/unit_target.h"

#include "teams/ad/index_builder/tables/data_gate/account_support_info.h"
#include "teams/ad/index_builder/tables/data_gate/unit_support_info.h"
#include "teams/ad/index_builder/tables/data_gate/creative_preview.h"
#include "teams/ad/index_builder/tables/data_gate/account_preview.h"
#include "teams/ad/index_builder/tables/data_gate/unit_small_shop_merchant_support_info.h"
#include "teams/ad/index_builder/tables/data_gate/cover.h"
#include "teams/ad/index_builder/tables/data_gate/creative_support_info.h"
#include "teams/ad/index_builder/tables/data_gate/adv_card.h"
#include "teams/ad/index_builder/tables/data_gate/landing_page.h"

#include "teams/ad/index_builder/tables/data_gate/creative_fanstop_support_info.h"
#include "teams/ad/index_builder/tables/data_gate/campaign_fanstop_support_info.h"
#include "teams/ad/index_builder/tables/data_gate/unit_fanstop_support_info.h"
#include "teams/ad/index_builder/tables/data_gate/small_shop_spu.h"
#include "teams/ad/index_builder/tables/data_gate/live_stream_user_info.h"
#include "teams/ad/index_builder/tables/data_gate/ad_dsp_ecom_hosting_project.h"
#include "teams/ad/index_builder/tables/data_gate/fanstop_live_hosting_project.h"
#include "teams/ad/index_builder/tables/data_gate/fanstop_unit_range_charge_tag.h"
#include "teams/ad/index_builder/tables/data_gate/fanstop_campaign_range_charge_tag.h"
#include "teams/ad/index_builder/tables/data_gate/style_material.h"
#include "teams/ad/index_builder/tables/data_gate/style_material_bind.h"
#include "teams/ad/index_builder/tables/data_gate/style_template.h"
#include "teams/ad/index_builder/tables/data_gate/style_template_rule.h"
#include "teams/ad/index_builder/tables/data_gate/winfo.h"
#include "teams/ad/index_builder/tables/data_gate/play_info.h"
#include "teams/ad/index_builder/tables/data_gate/hosting_project_fiction.h"
#include "teams/ad/index_builder/tables/data_gate/hosting_project.h"
#include "teams/ad/index_builder/tables/data_gate/ad_crm_account_operator_define_label.h"
#include "teams/ad/index_builder/tables/data_gate/ad_dsp_hosting_project_target.h"
#include "teams/ad/index_builder/tables/data_gate/mini_app.h"
#include "teams/ad/index_builder/tables/data_gate/ad_dpa_style.h"
#include "teams/ad/index_builder/tables/data_gate/matrix_style_material.h"
#include "teams/ad/index_builder/tables/data_gate/matrix_style_material_rule.h"
#include "teams/ad/index_builder/tables/data_gate/ad_dpa_library.h"
#include "teams/ad/index_builder/tables/data_gate/ad_dsp_merchant_product_info.h"
#include "teams/ad/index_builder/tables/data_gate/esp_combo_order_info.h"
#include "teams/ad/index_builder/tables/data_gate/advanced_programed_package.h"
#include "teams/ad/index_builder/tables/data_gate/ad_magic_site_page_das.h"
#include "teams/ad/index_builder/tables/data_gate/dpa_dynamic_lp_product_info.h"
#include "teams/ad/index_builder/tables/data_gate/algo_il_live.h"
#include "teams/ad/index_builder/tables/data_gate/algo_il_photo.h"
#include "teams/ad/index_builder/tables/data_gate/ad_brand_creative.h"
#include "teams/ad/index_builder/tables/data_gate/ad_brand_unit.h"
#include "teams/ad/index_builder/tables/data_gate/ad_brand_campaign.h"
#include "teams/ad/index_builder/tables/data_gate/ad_brand_account.h"
#include "teams/ad/index_builder/tables/data_gate/bonus_support_group.h"
#include "teams/ad/index_builder/tables/data_gate/bonus_support_project.h"
#include "teams/ad/index_builder/tables/data_gate/pec_white_set.h"
#include "teams/ad/index_builder/tables/data_gate/pec_right_info.h"
#include "teams/ad/index_builder/tables/data_gate/account_industry.h"
#include "teams/ad/index_builder/tables/data_gate/ad_brand_order_plan.h"
#include "teams/ad/index_builder/tables/data_gate/ad_brand_order.h"
#include "teams/ad/index_builder/tables/data_gate/ad_brand_target.h"
#include "teams/ad/index_builder/tables/data_gate/ad_infra_user_benefit_threshold_record.h"
#include "teams/ad/index_builder/tables/data_gate/account_status.h"
#include "teams/ad/index_builder/tables/data_gate/ad_dpa_product.h"
#include "teams/ad/index_builder/tables/data_gate/ad_esp_product_label_info.h"
#include "teams/ad/index_builder/tables/data_gate/ad_im_bluev_app_link.h"
#include "teams/ad/index_builder/tables/data_gate/ad_auax_simple_project.h"
#include "teams/ad/index_builder/tables/data_gate/na_book_panel.h"
#include "teams/ad/index_builder/tables//data_gate/ad_series_template_info.h"

// 宽表
#include "teams/ad/index_builder/tables/data_gate/wt_account.h"
#include "teams/ad/index_builder/tables/data_gate/wt_author.h"
#include "teams/ad/index_builder/tables/data_gate/wt_photo.h"
#include "teams/ad/index_builder/tables/data_gate/wt_product.h"
#include "teams/ad/index_builder/tables/data_gate/wt_live.h"
#include "teams/ad/index_builder/tables/data_gate/wt_unit.h"
#include "teams/ad/index_builder/tables/data_gate/wt_creative.h"
#include "teams/ad/index_builder/tables/data_gate/wt_material_element.h"
#include "teams/ad/index_builder/tables/data_gate/ad_live_clip_photo_info.h"
#include "teams/ad/index_builder/tables/data_gate/ad_esp_account_bid_config.h"
#include "teams/ad/index_builder/tables/data_gate/ad_kstube_tube_pool.h"
#include "teams/ad/index_builder/tables/data_gate/ad_kstube_tube_episode.h"
#include "teams/ad/index_builder/tables/data_gate/wt_campaign.h"
#include "teams/ad/index_builder/tables/data_gate/wt_live_product.h"
#include "teams/ad/index_builder/tables/data_gate/bs_live_sku_to_das.h"
#include "teams/ad/index_builder/tables/data_gate/wt_multi_table.h"
#include "teams/ad/index_builder/tables/data_gate/ad_magic_site_page_sku_das.h"
#include "teams/ad/index_builder/tables/data_gate/wt_poi.h"
