#pragma once

#include <string>
#include <utility>
#include <unordered_set>

#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct DpaDynamicLpProductInfo {
  enum __capicity { kCapicity = 1 << 20, kDataVersion = 2 };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t product_id;
  int64_t library_id;
  kuaishou::ad::AdEnum::DpaProductStatus status;
  int64_t check_status;

  DEFINE_common_of_container(DpaDynamicLpProductInfo, kuaishou::ad::tables::AdDpaDynamicLpProductInfo);

  static DpaDynamicLpProductInfo gen_from_proto(const google::protobuf::Message* pb, bool is_attach) {
    const kuaishou::ad::tables::AdDpaDynamicLpProductInfo* pb_ptr = nullptr;
    DpaDynamicLpProductInfo tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::AdDpaDynamicLpProductInfo::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdDpaDynamicLpProductInfo*>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, product_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, library_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, status, kuaishou::ad::AdEnum_DpaProductStatus_PAUSE_PRODUCT)
      SET_FIELD_BY_PB(pb_ptr, tmp, check_status, 0)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<DpaDynamicLpProductInfo>(tmp);
  }

  uint64_t key_id() {
    return product_id;
  }

  static int64_t key_id_of(const ProtoType& msg) {
    return msg.product_id();
  }

  bool IsValid() const {
    return status == kuaishou::ad::AdEnum_DpaProductStatus_OPEN_PRODUCT && check_status == 0;
  }
};

}  // namespace data_gate
}  // namespace tables
