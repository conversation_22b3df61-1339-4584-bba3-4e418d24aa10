#pragma once

#include <stdint.h>
#include <string>
#include <utility>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct AdCampaignRangeChargeTag {
  enum capicity { kCapicity = 1 << 21, kDataVersion = 3 };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t campaign_id;

  DEFINE_common_of_container(AdCampaignRangeChargeTag, kuaishou::ad::tables::AdCampaignRangeChargeTag);

  static AdCampaignRangeChargeTag gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    using namespace kuaishou::ad::tables;  // NOLINT
    const kuaishou::ad::tables::AdCampaignRangeChargeTag* pb_ptr = nullptr;
    AdCampaignRangeChargeTag tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::AdCampaignRangeChargeTag::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdCampaignRangeChargeTag*>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, campaign_id, 0)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<AdCampaignRangeChargeTag>(tmp);
  }

  // 对 proto 进行一些解析修改
  static void ProcessProto(ProtoType *pb_ptr) {
    ParseStringField(pb_ptr);
  }

  static void ParseStringField(kuaishou::ad::tables::AdCampaignRangeChargeTag*) {
    return;
  }

  uint64_t key_id() {
    return campaign_id;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.campaign_id();
  }
};

}  // namespace data_gate
}  // namespace tables
