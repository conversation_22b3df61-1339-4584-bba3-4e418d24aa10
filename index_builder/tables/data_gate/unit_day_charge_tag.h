#pragma once

#include <stdint.h>
#include <string>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct AdUnitDailyChargeTag {
  enum __capicity {
    kCapicity = 1 << 26,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int64_t account_id;
  int64_t campaign_id;
  int64_t unit_id;
  int64_t charge_date;
  int64_t charge_tag;
  int64_t charged;
  int64_t create_time;
  int64_t update_time;

  // TODO
  // int64_t binlog_create_time;
  DEFINE_common_of_container(AdUnitDailyChargeTag, kuaishou::ad::tables::AdUnitDailyChargeTag);

  static AdUnitDailyChargeTag gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdUnitDailyChargeTag *pb_ptr = nullptr;
    AdUnitDailyChargeTag tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::AdUnitDailyChargeTag::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdUnitDailyChargeTag *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, account_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, campaign_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, unit_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, charge_date, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, charge_tag, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, charged, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, create_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, update_time, 0)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }

    return std::forward<AdUnitDailyChargeTag>(tmp);
  }

  uint64_t key_id() {return id;}

  bool IsValid() {
    return id > 0;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }
};

}  // namespace data_gate
}  // namespace tables

