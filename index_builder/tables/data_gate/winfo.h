#pragma once

#include <stdint.h>
#include <string>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct Winfo {
  enum __capicity {
    kCapicity = 1 << 27,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t winfo_id;
  int64_t account_id;
  int64_t campaign_id;
  int64_t unit_id;
  int64_t word_id;
  int64_t create_time;

  kuaishou::ad::AdEnum::PutStatus put_status;
  kuaishou::ad::AdEnum::ReviewStatus review_status;
  DEFINE_common_of_container(Winfo, kuaishou::ad::tables::AdDspWinfo);

  static Winfo gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdDspWinfo *pb_ptr = nullptr;
    Winfo tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::AdDspWinfo::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdDspWinfo*>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, winfo_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, account_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, campaign_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, unit_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, word_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, create_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, put_status, kuaishou::ad::AdEnum::UNKNOWN_PUT_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, review_status, kuaishou::ad::AdEnum::UNKNOWN_REVIEW_STATUS)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<Winfo>(tmp);
  }

  uint64_t key_id() {return winfo_id;}

  bool IsValid() {
    return put_status == ::kuaishou::ad::AdEnum_PutStatus_PUT_STATUS_OPEN &&
        review_status == ::kuaishou::ad::AdEnum_ReviewStatus_REVIEW_THROUGH;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.winfo_id();
  }
};

}  // namespace data_gate
}  // namespace tables

