#pragma once

#include <stdint.h>

#include <string>
#include <utility>

#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/index_builder/tables/table_util.h"

namespace tables {
namespace data_gate {

struct WTAuthor {
  enum __capicity { kCapicity = 1 << 26, kDataVersion = 9 };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int64_t is_novice_seller;
  int32_t has_live_hosting_project_ad;
  int32_t author_local_life_live_cate;
  double last_30d_cost;
  int32_t gmv_level;
  int32_t is_vh;
  int32_t industry;
  double history_roi0;
  double storewide_gmv_ratio;
  double roi_convert_ratio;
  int64_t live_year_max_cost_level;
  int64_t live_year_max_gmv_level;
  double live_index_creative_cnt_per_10k;
  double live_base_index_creative_cnt_per_10k;
  int64_t live_storewide_unit_cnt;

  DEFINE_common_of_container(WTAuthor, kuaishou::ad::tables::WTAuthor);

  static WTAuthor gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::WTAuthor *pb_ptr = nullptr;
    WTAuthor tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::WTAuthor::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::WTAuthor *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, is_novice_seller, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, has_live_hosting_project_ad, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, author_local_life_live_cate, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, last_30d_cost, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, gmv_level, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, is_vh, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, industry, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, history_roi0, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, storewide_gmv_ratio, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, roi_convert_ratio, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, live_year_max_cost_level, 100);
      SET_FIELD_BY_PB(pb_ptr, tmp, live_year_max_gmv_level, 100);
      SET_FIELD_BY_PB(pb_ptr, tmp, live_index_creative_cnt_per_10k, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, live_base_index_creative_cnt_per_10k, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, live_storewide_unit_cnt, 0);

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<WTAuthor>(tmp);
  }

  uint64_t key_id() { return id; }

  bool IsValid() { return id > 0; }

  static int64_t key_id_of(const ProtoType &msg) { return msg.id(); }
};

}  // namespace data_gate
}  // namespace tables
