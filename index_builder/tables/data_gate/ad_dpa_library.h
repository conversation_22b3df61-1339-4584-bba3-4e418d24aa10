#pragma once

#include <stdint.h>
#include <utility>
#include <string>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct AdDpaLibrary {
  enum __capicity {
    kCapicity = 1 << 21,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int32_t dpa_id;
  int32_t industry_id;
  kuaishou::ad::AdEnum::LibraryStatus status;
  int64_t account_id;
  int64_t product_count;
  int32_t open_age_target;
  int32_t open_region_target;
  int32_t open_sex_target;

  // int64_t seq_no;
  DEFINE_common_of_container(AdDpaLibrary, kuaishou::ad::tables::AdDpaLibrary);

  static AdDpaLibrary gen_from_proto(const google::protobuf::Message *pb,
                                 bool is_attach) {
    const kuaishou::ad::tables::AdDpaLibrary *pb_ptr = nullptr;
    AdDpaLibrary tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::AdDpaLibrary::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdDpaLibrary *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, dpa_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, industry_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, status, kuaishou::ad::AdEnum::UNKNOWN_LIBRARY_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, account_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, product_count, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, open_age_target, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, open_region_target, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, open_sex_target, 0)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data =
            ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<AdDpaLibrary>(tmp);
  }

  uint64_t key_id() {return id;}

  bool IsValid() const {
    return id > 0;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }
};

}  // namespace data_gate
}  // namespace tables

