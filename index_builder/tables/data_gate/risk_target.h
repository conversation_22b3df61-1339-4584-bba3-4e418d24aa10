#pragma once

#include <stdint.h>
#include <string>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct RiskTarget {
  enum __capicity {
    kCapicity = 1 << 18,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int64_t create_time;
  double rate_limiter_percent;
  double explore_rate_limiter_percent;
  double nearby_rate_limiter_percent;
  DEFINE_common_of_container(RiskTarget, kuaishou::ad::tables::RiskTarget);

  static RiskTarget gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::RiskTarget *pb_ptr = nullptr;
    RiskTarget tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::RiskTarget::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::RiskTarget *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, create_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, rate_limiter_percent, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, explore_rate_limiter_percent, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, nearby_rate_limiter_percent, 0)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<RiskTarget>(tmp);
  }

  uint64_t key_id() {return id;}

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }
};

}  // namespace data_gate
}  // namespace tables

