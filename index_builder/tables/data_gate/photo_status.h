#pragma once

#include <stdlib.h>
#include <stdint.h>
#include <string>
#include <utility>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct PhotoStatus {
  enum __capicity {
    kCapicity = 1 << 24,
    kDataVersion = 5
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t photo_id;
  int32_t visible;
  int64_t photo_upload_time;
  DEFINE_common_of_container(PhotoStatus, kuaishou::ad::tables::PhotoStatus);

  static PhotoStatus gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::PhotoStatus *pb_ptr = nullptr;
    PhotoStatus tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::PhotoStatus::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::PhotoStatus *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, photo_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, visible, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, photo_upload_time, 0)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }

    return std::forward<PhotoStatus>(tmp);
  }

  // 对 proto 进行一些解析修改
  static void ProcessProto(ProtoType *pb_ptr) {
    // ParseStringField(pb_ptr);
  }

  static void ParseStringField(kuaishou::ad::tables::PhotoStatus* photo_status) {
    if (!photo_status) {
      return;
    }

    if (photo_status->mutable_parse_field()->top5_color_size() == 0) {
      base::Json json(base::StringToJson(photo_status->top5_color()));
      for (auto* item : json.array()) {
        if (item->size() != 4) {
          continue;
        }

        int64_t r = 0, g = 0, b = 0;
        double ratio = 0.0;
        auto& color_array = item->array();
        color_array[0]->IntValue(&r);
        color_array[1]->IntValue(&g);
        color_array[2]->IntValue(&b);
        color_array[3]->FloatValue(&ratio);
        auto* color_item =
            photo_status->mutable_parse_field()->add_top5_color();
        color_item->set_r(r);
        color_item->set_g(g);
        color_item->set_b(b);
        color_item->set_ratio(ratio);
      }
    }
    photo_status->clear_top5_color();

    const std::string& photo_md5 = photo_status->md5();
    if (!photo_md5.empty()) {
      auto* parse_field = photo_status->mutable_parse_field();
      if (photo_md5.length() > 16) {
        std::string&& md5_str = photo_md5.substr(photo_md5.length() - 16);
        parse_field->set_md5_uint(strtoul(md5_str.c_str(), nullptr, 16));
      } else {
        parse_field->set_md5_uint(strtoul(photo_md5.c_str(), nullptr, 16));
      }
      photo_status->clear_md5();
    }
  }

  uint64_t key_id() {return photo_id;}

  bool IsValid() const {
    return photo_id > 0;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.photo_id();
  }
};

}  // namespace data_gate
}  // namespace tables

