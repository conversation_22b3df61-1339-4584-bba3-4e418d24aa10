#pragma once

#include <stdint.h>

#include <string>
#include <utility>

#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/index_builder/tables/table_util.h"

namespace tables {
namespace data_gate {

struct WTCampaign {
  enum __capicity { kCapicity = 1 << 24, kDataVersion = 5 };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int64_t project_cost_1_day;
  double project_target_cost_1_day;
  int64 project_store_cost_1_day;
  double project_store_target_cost_1_day;
  int64 project_cost_7_day;
  double project_target_cost_7_day;
  int64 project_store_cost_7_day;
  double project_store_target_cost_7_day;
  double project_store_merchant_target_cost_1_day;
  double project_store_merchant_target_cost_7_day;
  int64_t last_week_cost;
  int64_t last_month_cost;

  DEFINE_common_of_container(WTCampaign, kuaishou::ad::tables::WTCampaign);

  static WTCampaign gen_from_proto(const google::protobuf::Message* pb, bool is_attach) {
    const kuaishou::ad::tables::WTCampaign* pb_ptr = nullptr;
    WTCampaign tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::WTCampaign::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::WTCampaign*>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_cost_1_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_target_cost_1_day, 0.0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_store_cost_1_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_store_target_cost_1_day, 0.0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_cost_7_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_target_cost_7_day, 0.0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_store_cost_7_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_store_target_cost_7_day, 0.0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_store_merchant_target_cost_1_day, 0.0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_store_merchant_target_cost_7_day, 0.0);
      SET_FIELD_BY_PB(pb_ptr, tmp, last_week_cost, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, last_month_cost, 0);

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<WTCampaign>(tmp);
  }

  uint64_t key_id() { return id; }

  bool IsValid() { return id > 0; }

  static int64_t key_id_of(const ProtoType& msg) { return msg.id(); }
};

}  // namespace data_gate
}  // namespace tables
