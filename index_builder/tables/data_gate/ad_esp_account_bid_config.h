#pragma once

#include <stdint.h>
#include <string>
#include <utility>

#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct AdEspAccountBidConfig {
  enum __capicity {
    kCapicity = 1 << 16,
    kDataVersion = 1
  };

  int64_t uniq_id;  // 唯一 ID（hash(account_id+type+ocpx_action_type)）
  int64_t account_id;  // 账户 id
  int32_t type;  // 营销目标：短视频推广 13，直播推广 14
  int32_t ocpx_action_type;  // 优化目标：商品购买 395，卖货 ROI192，涨粉 72，进入直播间 740
  double ratio;  // 系数
  int32_t effect_status;  // 生效状态：初始化 0，生效中 1，已失效 2
  int64_t effect_start_time;  // 生效开始时间
  int64_t effect_end_time;  // 生效结束时间

  DEFINE_common_of_container(AdEspAccountBidConfig, kuaishou::ad::tables::AdEspAccountBidConfig);

  static AdEspAccountBidConfig gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdEspAccountBidConfig *pb_ptr = nullptr;
    AdEspAccountBidConfig tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::AdEspAccountBidConfig::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdEspAccountBidConfig*>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, uniq_id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, account_id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, type, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, ocpx_action_type, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, ratio, 0.0);
      SET_FIELD_BY_PB(pb_ptr, tmp, effect_status, 2);
      SET_FIELD_BY_PB(pb_ptr, tmp, effect_start_time, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, effect_end_time, 0);

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<AdEspAccountBidConfig>(tmp);
  }

  uint64_t key_id() { return uniq_id; }

  bool IsValid() {
    return effect_status == 0 || effect_status == 1;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.uniq_id();
  }
};

}  // namespace data_gate
}  // namespace tables

