#pragma once

#include <stdint.h>

#include <string>
#include <utility>

#include "base/hash_function/city.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/index_builder/tables/table_util.h"

namespace tables {
namespace data_gate {
struct Account {
  enum __capicity {
    kCapicity = 1 << 19,
    kDataVersion = 5
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int64_t user_id;
  int64_t industry_id;
  int64_t new_industry_id;
  int64_t industry_id_v3;
  int64_t city_product_id;
  int64_t day_budget;  // = 0 为不限预算
  int32_t test_type;  // 非 0 是测试账号
  int32_t industry_group;
  int32_t coop_status;
  kuaishou::ad::AdEnum::AdDspAccountType account_type;
  kuaishou::ad::AdEnum::PutStatus put_status;
  kuaishou::ad::AdEnum::ReviewStatus review_status;
  kuaishou::ad::AdEnum::AdDspAccountFrozenStatus frozen_status;
  kuaishou::ad::AdEnum::AdCertType cert_type;  // 资质类型（企业或个人）
  kuaishou::ad::AdEnum::AdDspDeliveryTypeEnum delivery_type;
  bool config_budget;
  int64_t budget_schedule[util::kWeekDays];
  int64_t licence_id_num;
  kuaishou::ad::AdEnum::AccountMark account_mark;  // 用户标记 头/腰/尾
  kuaishou::ad::AdEnum::AccountAutoManage account_auto_manage;  // 账号智投模式开关
  kuaishou::ad::AdEnum::AutoManageType auto_manage_type;  // 账户智投类型

  DEFINE_common_of_container(Account, kuaishou::ad::tables::Account);

  // 对 proto 进行一些解析修改
  static void ProcessProto(ProtoType *pb_ptr) {
    /*
    if (pb_ptr->city_product_id() == 0) {
      pb_ptr->set_city_product_id(GetCityProductId(pb_ptr->product_name()));
    }
    */
  }

  static Account gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::Account *pb_ptr = nullptr;
    Account tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::Account::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::Account *>(pb);

      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, user_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, industry_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, new_industry_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, industry_id_v3, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, city_product_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, day_budget, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, test_type, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, coop_status, 0)
      // SET_FIELD_BY_PB(pb_ptr, tmp, industry_group, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, account_type, kuaishou::ad::AdEnum::UNKNOWN_AdDspACCOUNT_TYPE)
      SET_FIELD_BY_PB(pb_ptr, tmp, put_status, kuaishou::ad::AdEnum::UNKNOWN_PUT_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, review_status, kuaishou::ad::AdEnum::UNKNOWN_REVIEW_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, frozen_status, kuaishou::ad::AdEnum::UNKNOWN_FROZEN_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, cert_type, kuaishou::ad::AdEnum::UNKNOWN_CERT_TYPE)
      SET_FIELD_BY_PB(pb_ptr, tmp, delivery_type, kuaishou::ad::AdEnum::METHOD_UNKNOWN)
      SET_FIELD_BY_PB(pb_ptr, tmp, licence_id_num, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, account_mark, kuaishou::ad::AdEnum::ACCOUNT_MARK_UNKNOWN)
      auto *account_support_info = &(pb_ptr->account_support_info());
      SET_FIELD_BY_PB(account_support_info, tmp, account_auto_manage,
                      kuaishou::ad::AdEnum::ACCOUNT_AUTO_MANAGE_CLOSE);
      SET_FIELD_BY_PB(account_support_info, tmp, auto_manage_type,
                      kuaishou::ad::AdEnum::AUTO_MANAGE_DEFAULT);

      util::ParseBudgetSchedule(pb_ptr->budget_schedule(), tmp.budget_schedule, util::kWeekDays);
      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<Account>(tmp);
  }
  uint64_t key_id() {return id;}

  bool IsValid() const {
    return id > 0 && put_status == ::kuaishou::ad::AdEnum_PutStatus_PUT_STATUS_OPEN
      && review_status == ::kuaishou::ad::AdEnum_ReviewStatus_REVIEW_THROUGH
      && frozen_status == ::kuaishou::ad::AdEnum_AdDspAccountFrozenStatus_FROZEN_STATUS_CLOSED
      && user_id > 0;
  }

  static int64_t GetCityProductId(const std::string &product_name) {
    uint64_t uint64_sign = base::CityHash64(product_name.data(), product_name.size());
    return *(reinterpret_cast<int64_t*>(&uint64_sign));
  }

  // TODO(liming) 确认实现正确
  int64_t GetDayBudget(size_t wd) const {
    return budget_schedule[wd % util::kWeekDays];
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }
};

}  // namespace data_gate
}  // namespace tables
