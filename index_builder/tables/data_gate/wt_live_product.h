#pragma once

#include <stdint.h>
#include <string>
#include <utility>

#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct WTLiveProduct {
  enum __capicity {
    kCapicity = 1 << 21,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t item_id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(item_id);
  }

  int64_t item_id;
  int64_t seller_id;
  int64_t shelf_gmv;
  int64_t shelf_price;
  int64_t shelf_order;

  DEFINE_common_of_container(WTLiveProduct, kuaishou::ad::tables::WTLiveProduct);

  static WTLiveProduct gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::WTLiveProduct *pb_ptr = nullptr;
    WTLiveProduct tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::WTLiveProduct::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::WTLiveProduct*>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, item_id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, seller_id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, shelf_gmv, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, shelf_price, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, shelf_order, 0);

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<WTLiveProduct>(tmp);
  }

  uint64_t key_id() { return item_id; }

  bool IsValid() {
    return item_id > 0;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.item_id();
  }
};

}  // namespace data_gate
}  // namespace tables

