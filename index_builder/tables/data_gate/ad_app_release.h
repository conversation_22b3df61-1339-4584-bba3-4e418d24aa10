#pragma once

#include <stdint.h>
#include <string>
#include <utility>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct AdAppRelease {
  enum __capicity {
    kCapicity = 1 << 22,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t package_id;
  int64_t app_id;
  int32_t app_score;
  bool h5_app;
  DEFINE_common_of_container(AdAppRelease, kuaishou::ad::tables::AdAppRelease);

  static AdAppRelease gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdAppRelease *pb_ptr = nullptr;
    AdAppRelease tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::AdAppRelease::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdAppRelease*>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, package_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, app_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, app_score, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, h5_app, false)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<AdAppRelease>(tmp);
  }

  uint64_t key_id() {return package_id;}

  bool IsValid() const {
    return package_id > 0;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.package_id();
  }
};

}  // namespace data_gate
}  // namespace tables

