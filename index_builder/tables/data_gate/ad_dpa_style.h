#pragma once

#include <stdint.h>
#include <utility>
#include <string>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct AdDpaStyle {
  enum __capicity {
    kCapicity = 1 << 21,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int64_t product_id;
  int64_t library_id;
  int64_t data_version;
  int64_t style_type;
  int64_t resource_type;
  kuaishou::ad::AdEnum::PutStatus put_status;
  kuaishou::ad::AdEnum::ReviewStatus review_status;

  // int64_t seq_no;
  DEFINE_common_of_container(AdDpaStyle, kuaishou::ad::tables::AdDpaStyle);

  static AdDpaStyle gen_from_proto(const google::protobuf::Message *pb,
                                 bool is_attach) {
    const kuaishou::ad::tables::AdDpaStyle *pb_ptr = nullptr;
    AdDpaStyle tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::AdDpaStyle::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdDpaStyle *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, product_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, library_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, data_version, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, style_type, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, resource_type, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, review_status, kuaishou::ad::AdEnum::UNKNOWN_REVIEW_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, put_status, kuaishou::ad::AdEnum::UNKNOWN_PUT_STATUS)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data =
            ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<AdDpaStyle>(tmp);
  }

  uint64_t key_id() {return id;}

  bool IsValid() const {
    return id > 0 && put_status == kuaishou::ad::AdEnum_PutStatus_PUT_STATUS_OPEN;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }
};

}  // namespace data_gate
}  // namespace tables

