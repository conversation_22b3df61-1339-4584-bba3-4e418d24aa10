#pragma once

#include <stdint.h>
#include <string>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct AdApp {
  enum __capicity {
    kCapicity = 1 << 22,
    kDataVersion = 4
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t app_id;
  int32_t app_score;
  bool h5_app;
  DEFINE_common_of_container(AdApp, kuaishou::ad::tables::AdApp);

  static AdApp gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdApp *pb_ptr = nullptr;
    AdApp tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::AdApp::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdApp *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, app_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, app_score, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, h5_app, false)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<AdApp>(tmp);
  }

  uint64_t key_id() {return app_id;}

  bool IsValid() const {
    return app_id > 0;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.app_id();
  }
};

}  // namespace data_gate
}  // namespace tables

