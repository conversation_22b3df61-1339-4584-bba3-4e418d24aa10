#pragma once

#include <string>
#include <utility>
#include <vector>
#include "serving_base/jansson/json.h"
#include "falcon/counter.h"
#include "base/strings/string_split.h"
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/kconf.h"

namespace tables {
namespace data_gate {

struct StyleMaterial {
  enum __capicity {
    kCapicity = 1 << 23,
    kDataVersion = 14
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int32_t style_type;
  int64_t resource_type;
  kuaishou::ad::AdEnum::PutStatus put_status;
  kuaishou::ad::AdEnum::ReviewStatus review_status;
  kuaishou::ad::AdEnum::CreativeMaterialType material_type;
  int64_t rule_id;
  int64_t template_id;
  int64_t exp_tag;
  int64_t library_id;
  int64_t material_id;
  int64_t duration;
  int32_t target_city_id;
  int32_t target_gender;
  int64_t target_age_low;
  int64_t target_age_high;
  int64_t target_province_low;
  int64_t target_province_high;
  kuaishou::ad::AdEnum::ExeuntStatus exeunt_flag;
  uint64_t tags;
  int32_t cover_width;
  int32_t cover_height;

  DEFINE_common_of_container(StyleMaterial, kuaishou::ad::tables::AdStyleMaterial);

  static StyleMaterial gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdStyleMaterial *pb_ptr = nullptr;
    StyleMaterial tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::AdStyleMaterial::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdStyleMaterial *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, style_type, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, resource_type, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, material_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, put_status, kuaishou::ad::AdEnum_PutStatus_UNKNOWN_PUT_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, review_status, kuaishou::ad::AdEnum_ReviewStatus_UNKNOWN_REVIEW_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, library_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, exeunt_flag, kuaishou::ad::AdEnum_ExeuntStatus_EXEUNT_UNKNOWN)

      // ParseStyleContent(tmp, pb_ptr);
      ParseFromExtendField(&tmp, *pb_ptr);
      LOG_EVERY_N(INFO, 1000000)
          << "Style bin info: "
          << "tmp.rule_id " << tmp.rule_id << "tmp.template_id "
          << tmp.template_id << "tmp.material_type" << tmp.material_type
          << "tmp.exp_tag" << tmp.exp_tag << "tmp.current_timestamp";
      ParseTarget(tmp, pb_ptr);

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }

    return std::forward<StyleMaterial>(tmp);
  }

  // 对 proto 进行一些解析修改
  static void ProcessProto(ProtoType *pb_ptr) {
    // ParseStringField(pb_ptr);
    // ParseMaterialTarget(pb_ptr);
  }

  uint64_t key_id() {return id;}

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }

  bool IsValid() const {
    return put_status == kuaishou::ad::AdEnum_PutStatus_PUT_STATUS_OPEN &&
           review_status == kuaishou::ad::AdEnum_ReviewStatus_REVIEW_THROUGH;
  }
  static void ParseFromExtendField(
      StyleMaterial *tmp, const kuaishou::ad::tables::AdStyleMaterial &style) {
    if (tmp == nullptr) {
      return;
    }
    const auto &extend_field = style.parse_field();
    LOG_EVERY_N(INFO, 1000000)
        << "Style parse field: " << extend_field.ShortDebugString();
    tmp->rule_id = extend_field.rule_id();
    tmp->template_id = extend_field.template_id();
    tmp->material_type = extend_field.material_type();
    tmp->exp_tag = extend_field.exp_tag();
    tmp->duration = extend_field.duration();
    tmp->tags = 0;
    for (const auto& tag : extend_field.tags()) {
      if (tag > 0 && tag <= 64) {
        tmp->tags |= 1 << (tag - 1);
      }
    }
    tmp->cover_width = extend_field.cover_width();
    tmp->cover_height = extend_field.cover_height();
    return;
  }
  static void ParseStyleContent(StyleMaterial &tmp, const kuaishou::ad::tables::AdStyleMaterial *style) {  // NOLINT
    if (nullptr == style) {
      return;
    }
    base::Json style_content(base::StringToJson(style->style_content()));
    if (!style_content.IsObject()) {
      falcon::Inc("index_builder.ad_style.style_content_error");
      return;
    }

    do {
      tmp.rule_id = style_content.GetInt("ruleId", 0);
      tmp.template_id = style_content.GetInt("templateId", 0);
      tmp.material_type =
        kuaishou::ad::AdEnum_CreativeMaterialType(style_content.GetInt("materialType", 0));
      tmp.exp_tag = style_content.GetInt("materialExpTag", 0);
      tmp.duration = style_content.GetInt("duration", 0);
    } while (false);
  }

  static void ParseTarget(StyleMaterial &tmp, const kuaishou::ad::tables::AdStyleMaterial *style) {  // NOLINT
    if (nullptr == style) {
      return;
    }
    const auto& parsed_target = style->parse_field().material_target();
    if (parsed_target.city_id() > 0) {
      tmp.target_city_id = parsed_target.city_id();
    }
    if (parsed_target.gender() > 0) {
      tmp.target_gender = parsed_target.gender();
    }
    for (const int32_t& a : parsed_target.age()) {
      if (a < 51) {
        tmp.target_age_low |= ((int64_t)0x1 << a);
      } else if (a <= 100) {
        tmp.target_age_high |= ((int64_t)0x1 << (a - 51));
      }
    }
    for (const int32_t& city : parsed_target.city_ids()) {
      if (city < 51) {
        tmp.target_province_low |= ((int64_t)0x1 << city);
      } else if (city <= 100) {
        tmp.target_province_high |= ((int64_t)0x1 << (city - 51));
      }
    }
  }

  static void ParseStringField(kuaishou::ad::tables::AdStyleMaterial* style) {
    base::Json style_content(base::StringToJson(style->style_content()));
    if (!style_content.IsObject()) {
      falcon::Inc("index_builder.ad_style.style_content_error");
      return;
    }
    do {
      auto* parse_field = style->mutable_parse_field();
      auto material_type = kuaishou::ad::AdEnum_CreativeMaterialType(style_content.GetInt("materialType", 0));
      if (parse_field) {
        // 共用字段
        parse_field->set_material_type(material_type);
        parse_field->set_rule_id(style_content.GetInt("ruleId", 0));
        parse_field->set_template_id(style_content.GetInt("templateId", 0));
        parse_field->set_cover_height(style_content.GetInt("coverHeight", 0));
        parse_field->set_cover_width(style_content.GetInt("coverWidth", 0));
        parse_field->set_exp_tag(style_content.GetInt("materialExpTag", 0));
        // 视频素材
        if (material_type == kuaishou::ad::AdEnum::VERTICAL_SCREEN ||
            material_type == kuaishou::ad::AdEnum::HORIZONTAL_SCREEN) {
          parse_field->set_duration(style_content.GetInt("duration", 0));
          parse_field->set_photo_id(style_content.GetInt("adapterPhotoId", 0));
          parse_field->set_video_url(style_content.GetString("videoUrl", ""));
          parse_field->set_cover_url(style_content.GetString("coverUrl", ""));
        }
        // 图片素材
        if (material_type == kuaishou::ad::AdEnum::VERTICAL_IMAGE ||
            material_type == kuaishou::ad::AdEnum::HORIZONTAL_IMAGE ||
            material_type == kuaishou::ad::AdEnum::BANNER_IMAGE) {
          parse_field->set_material_url(style_content.GetString("materialUrl", ""));
          parse_field->set_compress_material_url(style_content.GetString("compressMaterialUrl", ""));
        }
        // 竖版图片标准宽高素材 额外规格素材
        if (material_type == kuaishou::ad::AdEnum::VERTICAL_IMAGE) {
          parse_field->set_cover_standard(style_content.GetString("coverStandard", ""));
          parse_field->set_compress_cover_standard(style_content.GetString("compressCoverStandard", ""));
          parse_field->mutable_ext_material_info()->set_ext_cover_url(
              style_content.GetString("extCoverUrl", ""));
          parse_field->mutable_ext_material_info()->set_ext_cover_height(
              style_content.GetInt("extCoverHeight", 0));
          parse_field->mutable_ext_material_info()->set_ext_cover_width(
              style_content.GetInt("extCoverWidth", 0));
        }
        // 素材生成时间
        parse_field->set_create_timestamp(style_content.GetInt("currentTimeMills", 0));
      }
    } while (false);

    if (ks::index_builder::AdKconfUtil::enableStyleMaterialClear()) {
      style->clear_style_content();
      style->clear_parse_field();
      base::JsonObject new_style_content;
      for (auto iter : style_content.objects()) {
        if (ks::index_builder::AdKconfUtil::styleMaterialWhiteSet()->count(iter.first)) {
          if (iter.second->IsBoolean()) {
            new_style_content.set(iter.first, iter.second->BooleanValue(false));
          } else if (iter.second->IsString()) {
            new_style_content.set(iter.first, iter.second->StringValue(""));
          } else if (iter.second->IsInteger()) {
            new_style_content.set(iter.first, iter.second->IntValue(0L));
          } else if (iter.second->IsDouble()) {
            new_style_content.set(iter.first, iter.second->FloatValue(0.0));
          } else if (iter.second->Get(iter.first) != nullptr) {
            new_style_content.set(iter.first, *(iter.second->Get(iter.first)));
          }
        }
      }
      style->set_style_content(new_style_content.ToString());
      LOG_FIRST_N(INFO, 10) << "style.style_content:" << style->style_content();
    }
  }

  static void ParseMaterialTarget(
      kuaishou::ad::tables::AdStyleMaterial* style) {
    base::Json material_target(base::StringToJson(style->material_target()));
    if (!material_target.IsObject()) {
      falcon::Inc("index_builder.ad_style.material_target_error");
      return;
    }
    auto* parse_field = style->mutable_parse_field();
    std::string adcode_str = material_target.GetString("region", "");
    int32_t city_id{0};
    if (adcode_str.empty()) {
    } else if (adcode_str.size() <= 4) {
      auto ok = absl::SimpleAtoi(adcode_str, &city_id);
      parse_field->mutable_material_target()->set_city_id(city_id);
    } else {
      auto ok = absl::SimpleAtoi(adcode_str.substr(0, 4), &city_id);
      parse_field->mutable_material_target()->set_city_id(city_id);
    }
    auto gender_str = material_target.GetString("gender", "");
    int32_t gender{0};
    if (absl::SimpleAtoi(gender_str, &gender)) {
      parse_field->mutable_material_target()->set_gender(gender);
    }
    std::vector<std::string> age_vec;
    base::SplitString(material_target.GetString("age", ""), std::string(","),
                      &age_vec);
    for (const auto& age : age_vec) {
      int32_t age_id{0};
      if (absl::SimpleAtoi(age, &age_id)) {
        parse_field->mutable_material_target()->add_age(age_id);
      }
    }
  }
};

}  // namespace data_gate
}  // namespace tables
