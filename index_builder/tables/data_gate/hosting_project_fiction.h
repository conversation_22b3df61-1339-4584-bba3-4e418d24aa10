#pragma once

#include <stdint.h>
#include <string>
#include <utility>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"

namespace tables {
namespace data_gate {

struct AdDspHostingProjectFiction {
  enum capicity { kCapicity = 1 << 18, kDataVersion = 4 };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int64_t fiction_id;
  int64_t project_id;
  bool smart_cover;
  int64_t site_id;

  DEFINE_common_of_container(AdDspHostingProjectFiction, kuaishou::ad::tables::AdDspHostingProjectFiction);

  static AdDspHostingProjectFiction gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdDspHostingProjectFiction *pb_ptr = nullptr;
    AdDspHostingProjectFiction tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::AdDspHostingProjectFiction::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdDspHostingProjectFiction *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, fiction_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, project_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, smart_cover, false)
      SET_FIELD_BY_PB(pb_ptr, tmp, site_id, 0)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<AdDspHostingProjectFiction>(tmp);
  }
  // 对 proto 进行一些解析修改
  static void ProcessProto(ProtoType *pb_ptr) {
    /*
    auto ad_dsp_hosting_project_fiction_parser_manager = ks::index_builder::TableParserManager<
        google::protobuf::Message, kuaishou::ad::tables::AdDspHostingProjectFiction>::GetInstance();
    ad_dsp_hosting_project_fiction_parser_manager->Parse(pb_ptr);
    */
    ParseStringField(pb_ptr);
  }

  static void ParseStringField(kuaishou::ad::tables::AdDspHostingProjectFiction *) {
    return;
  }

  uint64_t key_id() {
    return id;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }
};

}  // namespace data_gate
}  // namespace tables
