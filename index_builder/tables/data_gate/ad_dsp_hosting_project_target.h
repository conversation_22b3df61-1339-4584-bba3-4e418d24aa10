#pragma once

#include <stdint.h>
#include <string>
#include <vector>
#include <utility>
#include "serving_base/jansson/json.h"
#include "google/protobuf/util/json_util.h"
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_base/src/better_enums/better_enums.h"

namespace tables {
namespace data_gate {
BETTER_ENUM(AdDspHostingProjectTargetGender, uint8_t, Male = 1, Female, Both)
BETTER_ENUM(TargetUserType, uint8_t, RealTime = 0, Frequent, UnLimit, DistrictRealTime)
struct AdDspHostingProjectTarget {
  enum __capicity {
    kCapicity = 1 << 22,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t project_id;
  int64_t account_id;
  int32_t user_type;
  int32_t filter_converted_level;


  DEFINE_common_of_container(AdDspHostingProjectTarget, kuaishou::ad::tables::AdDspHostingProjectTarget);

  static AdDspHostingProjectTarget gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdDspHostingProjectTarget *pb_ptr = nullptr;
    AdDspHostingProjectTarget tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::AdDspHostingProjectTarget::default_instance()
            .GetDescriptor()) {
      pb_ptr =
          dynamic_cast<const kuaishou::ad::tables::AdDspHostingProjectTarget*>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, account_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, user_type, TargetUserType::RealTime)
      SET_FIELD_BY_PB(
          pb_ptr, tmp, filter_converted_level,
          kuaishou::ad::AdEnum_DspConvFilterLevel_UNKNOWN_FILTER_TYPE)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }

    return std::forward<AdDspHostingProjectTarget>(tmp);
  }

  // 对 proto 进行一些解析修改
  static void ProcessProto(ProtoType *pb_ptr) {
    ParseExtendFields(pb_ptr);
  }

  static void ParseExtendFields(kuaishou::ad::tables::AdDspHostingProjectTarget* project_target) {
    if (!project_target) {
      return;
    }

#define SET_PROJECT_TARGET_JSON_ITEM(field, type) \
  if (!project_target->field().empty()) { \
    base::Json json_item(base::StringToJson(project_target->field())); \
    if (json_item.Is##type()) { \
      json_obj.set(#field, json_item); \
    } \
    std::string field_name(#field); \
    if (field_name != "age" && field_name != "platform" && field_name != "intelli_extend") { \
      project_target->clear_##field(); \
    } \
  }

    base::JsonObject json_obj;

    // age，platform intelli_extend 这几个字段算法侧离线依赖，暂不清理
    SET_PROJECT_TARGET_JSON_ITEM(age, Array);
    SET_PROJECT_TARGET_JSON_ITEM(platform, Object);
    SET_PROJECT_TARGET_JSON_ITEM(paid_audience_real, Array);
    SET_PROJECT_TARGET_JSON_ITEM(population_real, Array);
    SET_PROJECT_TARGET_JSON_ITEM(exclude_population_real, Array);
    SET_PROJECT_TARGET_JSON_ITEM(intelli_extend, Object);
    SET_PROJECT_TARGET_JSON_ITEM(region_ids, Array);
    SET_PROJECT_TARGET_JSON_ITEM(behavior_interest_keyword, Array);

    ParseStringField(project_target, &json_obj);
    const std::string& json_str = json_obj.ToString();
    if (!json_str.empty()) {
      google::protobuf::util::JsonParseOptions options;
      options.ignore_unknown_fields = true;
      auto status = google::protobuf::util::JsonStringToMessage(json_str,
          project_target->mutable_extend_fields(), options);
      if (!status.ok()) {
        ks::infra::PerfUtil::CountLogStash(1, "ad.index_builder", "parse_extend_fields_failed", "target");
        LOG(ERROR) << "JsonStringToMessage failed. error: " << status.error_message()
                   << ", json_str: " << json_str;
        project_target->clear_extend_fields();
      }
    }
  }

  static void ParseStringField(
      kuaishou::ad::tables::AdDspHostingProjectTarget* project_target,
      base::JsonObject* extend_json) {
    if (project_target == nullptr || extend_json == nullptr) {
      return;
    }
    // 解析 gender
    if (!project_target->gender().empty()) {
      std::vector<int64_t> vec;
      if (project_target->gender() == "M") {
        vec.push_back(+TargetGender::Male);
      } else if (project_target->gender() == "F") {
        vec.push_back(+TargetGender::Female);
      }
      if (vec.size() == 1 && vec[0] == +AdDspHostingProjectTargetGender::Male) {
        extend_json->set("gender", +AdDspHostingProjectTargetGender::Male);
      } else if (vec.size() == 1 && vec[0] == +AdDspHostingProjectTargetGender::Female) {
        extend_json->set("gender", +AdDspHostingProjectTargetGender::Female);
      } else {
        extend_json->set("gender", +AdDspHostingProjectTargetGender::Both);
      }
    }
    return;
  }
  // project_id 主键
  uint64_t key_id() {return project_id;}

  bool IsValid() {
    return project_id > 0;
  }
  static int64_t key_id_of(const ProtoType &msg) {
    return msg.project_id();
  }
};

}  // namespace data_gate
}  // namespace tables

