#pragma once
#include <stdint.h>
#include <set>
#include <string>
#include <utility>
#include <vector>
#include <unordered_map>
#include "perfutil/perfutil.h"
#include "absl/strings/numbers.h"
#include "absl/strings/str_split.h"
#include "serving_base/jansson/json.h"
#include "google/protobuf/util/json_util.h"
#include "teams/ad/ad_base/src/better_enums/better_enums.h"
#include "teams/ad/ad_base/src/common/basis_enum_types.h"
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {
BETTER_ENUM(CreativeTimeTag, int32_t, kAuditTime = 0, kCreateTime = 1)

struct Creative {
  enum __capicity {
    kCapicity = 1 << 27,
    kDataVersion = 13
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int64_t unit_id;
  int64_t campaign_id;
  int64_t account_id;
  int64_t photo_id;
  int64_t cover_id;
  int64_t create_time;
  int32_t live_creative_type;
  int64_t dup_photo_id;
  int64_t first_audit_passtime;  // 首次通过审核时间
  int64_t dup_cover_id;
  int64_t dup_photo_id_a;
  int64_t dup_photo_id_b;
  int64_t creative_material_type;  // 创意的物料类型
  bool derivative_creative_flag;
  int64_t auto_deliver_related_id;
  int64_t live_stream_id;
  int64_t auto_deliver_type;
  int32_t creative_photo_source;


  kuaishou::ad::AdEnum::PutStatus put_status;
  kuaishou::ad::AdEnum::ReviewStatus review_status;
  kuaishou::ad::AdEnum::CreativeSourceType create_source_type;
  kuaishou::ad::AdEnum::ReviewStatus community_review_status;
  kuaishou::ad::AdEnum::AdDspCreativeFeature creative_feature;
  kuaishou::ad::AdEnum::AdDspCreativeType creative_type;

  DEFINE_common_of_container(Creative, kuaishou::ad::tables::Creative);

  static Creative gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::Creative *pb_ptr = nullptr;
    Creative tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::Creative::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::Creative *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, unit_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, campaign_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, account_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, photo_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, cover_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, create_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, live_creative_type, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, dup_photo_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, first_audit_passtime, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, dup_cover_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, dup_photo_id_a, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, dup_photo_id_b, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, creative_material_type, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, derivative_creative_flag, 0)

      SET_FIELD_BY_PB(pb_ptr, tmp, put_status, kuaishou::ad::AdEnum::UNKNOWN_PUT_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, review_status, kuaishou::ad::AdEnum::UNKNOWN_REVIEW_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, create_source_type, kuaishou::ad::AdEnum::OLD_TYPE_CREATIVE)
      SET_FIELD_BY_PB(pb_ptr, tmp, creative_type, kuaishou::ad::AdEnum::UNKNOWN)
      SET_FIELD_BY_PB(pb_ptr, tmp, community_review_status,
                      kuaishou::ad::AdEnum::UNKNOWN_REVIEW_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, creative_feature,
                      kuaishou::ad::AdEnum::UNKNOWN_CREATIVE_FEATURE)
      ParseCreativeSupportInfo(pb_ptr, tmp);
      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }

    return std::forward<Creative>(tmp);
  }
  static void ParseCreativeSupportInfo(
      const kuaishou::ad::tables::Creative *creative_pb_ptr, Creative &creative) {  // NOLINT
    if (nullptr == creative_pb_ptr || !creative_pb_ptr->has_creative_support_info()) {
      return;
    }
    auto *creative_support_info = &(creative_pb_ptr->creative_support_info());
    SET_FIELD_BY_PB(creative_support_info, creative, auto_deliver_related_id, 0)
    SET_FIELD_BY_PB(creative_support_info, creative, live_stream_id, 0)
    SET_FIELD_BY_PB(creative_support_info, creative, auto_deliver_type, 0)
    SET_FIELD_BY_PB(creative_support_info, creative, creative_photo_source, 0)
  }
    // 对 proto 进行一些解析修改
  static void ProcessProto(ProtoType *pb_ptr) {
    // ParseExtendFields(pb_ptr);
    // ParseStringField(pb_ptr);
  }

  static void ParseExtendFields(kuaishou::ad::tables::Creative* creative) {
    if (!creative) {
      return;
    }

    auto creative_extend_score = creative->extend_fields().creative_extend_score();

    base::JsonObject json_obj;
    if (!creative->display_info().empty()) {
      base::Json json_item(base::StringToJson(creative->display_info()));
      if (json_item.IsObject()) {
        json_obj.set("display_info", json_item);
      }
    }

    if (!creative->delivery_package().empty()) {
      base::Json json_item(base::StringToJson(creative->delivery_package()));
      if (json_item.IsObject()) {
        json_obj.set("delivery_package", json_item);
      }
    }

    if (!creative->extra_display_info().empty()) {
      base::Json json_item(base::StringToJson(creative->extra_display_info()));
      if (json_item.IsObject()) {
        json_obj.set("extra_display_info", json_item);
      }
    }

    if (!creative->review_through_cover_slogans_info().empty()) {
      base::Json json_item(base::StringToJson(creative->review_through_cover_slogans_info()));
      if (json_item.IsArray()) {
        json_obj.set("review_through_cover_slogans_info", json_item);
      }
    }

    if (!creative->description_titles_info().empty()) {
      base::Json json_item(base::StringToJson(creative->description_titles_info()));
      if (json_item.IsArray()) {
        json_obj.set("description_titles_info", json_item);
      }
    }

    if (!creative->creative_support_info().dpa_style_types().empty()) {
      base::Json json_item(base::StringToJson(
          creative->creative_support_info().dpa_style_types()));
      if (json_item.IsArray()) {
        json_obj.set("dpa_style_type", json_item);
      }
    }

    const std::string& json_str = json_obj.ToString();
    if (!json_str.empty()) {
      google::protobuf::util::JsonParseOptions options;
      options.ignore_unknown_fields = true;
      auto status = google::protobuf::util::JsonStringToMessage(json_str,
          creative->mutable_extend_fields(), options);
      if (!status.ok()) {
        ks::infra::PerfUtil::CountLogStash(1, "ad.index_builder", "parse_extend_fields_failed", "creative");
        LOG(ERROR) << "JsonStringToMessage failed. error: " << status.error_message()
                   << ", json_str: " << json_str;
        creative->clear_extend_fields();
      }
    }

    if (!creative->sticker_styles().empty()) {
      auto styles = absl::StrSplit(creative->sticker_styles(), ",", absl::SkipEmpty());
      for (const auto& style : styles) {
        int64_t style_id = 0;
        if (absl::SimpleAtoi(style, &style_id)) {
          creative->mutable_extend_fields()->add_sticker_styles(style_id);
        }
      }
    }

    /*
    const auto& creative_score_ext = creative->creative_score_ext();

    if (!creative_score_ext.empty()) {
      auto* extend_fields = creative->mutable_extend_fields();
      auto* creative_extend_score =
          extend_fields->mutable_creative_extend_score();
      google::protobuf::util::JsonParseOptions options;
      options.ignore_unknown_fields = true;
      auto status = google::protobuf::util::JsonStringToMessage(
          creative_score_ext, creative_extend_score, options);
      if (!status.ok()) {
        ks::infra::PerfUtil::CountLogStash(1, "ad.index_builder",
                                           "creative_score_ext", "creative");
        LOG(ERROR) << "creative_score_ext parse fail: " << creative_score_ext;
      }
    }
    */

    // 粉条标签频控字段解析
    if (creative->has_creative_support_info() &&
        creative->creative_support_info().has_freq_control_tag()) {
      const std::string& tag_str = creative->creative_support_info().freq_control_tag();
      // 样例  {"product_name": ["value1", "value2"], "jingle_bell": ["download"]}
      if (!tag_str.empty()) {
        base::Json json_obj(base::StringToJson(tag_str));
        if (!json_obj.IsObject()) {
          LOG_EVERY_N(ERROR, 100) << "Parsing Creative.freq_control_tag json failed, string = " << tag_str;
          return;
        }
        auto* extend_fields = creative->mutable_extend_fields();
        extend_fields->clear_freq_control_tag();
        extend_fields->clear_freq_control_tag_hash();
        const std::unordered_map<std::string, base::Json *>& object_map = json_obj.objects();
        for (const auto& pair : object_map) {
          const std::string& key = pair.first;
          if (key.empty() || key.find('|') != std::string::npos) {  // 避免出现拼接符 '|'
            LOG_EVERY_N(ERROR, 10000) << "Creative.freq_control_tag has Invalid Key: " << key;
            continue;
          }
          base::Json* val_obj = pair.second;
          if (val_obj == nullptr || !val_obj->IsArray() || val_obj->size() == 0) {
            LOG_EVERY_N(ERROR, 10000) << "Creative.freq_control_tag value is not array";
            continue;
          }
          // 遍历 val list
          for (auto item : val_obj->array()) {
            if (!item->IsString() || item->StringValue().empty()) {
              LOG_EVERY_N(ERROR, 10000) << "Creative.freq_control_tag value array is invalid";
              continue;
            }
            const std::string val = item->StringValue();
            if (val.find('|') != std::string::npos) {  // 避免出现拼接符 '|'
              LOG_EVERY_N(ERROR, 10000) << "Creative.freq_control_tag val has Invalid Char "
                                        << "key: " << key << " val: " << val;
              continue;
            }
            const std::string key_val = absl::StrCat(key, "|", val);  // 明文
            const int64_t key_val_hash = std::hash<std::string>()(key_val) % INT64_MAX;  // hash 值
            extend_fields->add_freq_control_tag(key_val);
            extend_fields->add_freq_control_tag_hash(key_val_hash);
          }
        }
      }
    }

    if (creative->has_creative_support_info()) {
      auto& creative_support_info = creative->creative_support_info();
      auto* highlight_info = creative->mutable_extend_fields()->mutable_highlight_info();
      if (creative_support_info.has_auto_deliver_type()) {
        highlight_info->set_auto_deliver_type(
                  creative_support_info.auto_deliver_type());
      }
      if (creative_support_info.has_auto_deliver_related_id()) {
        highlight_info->set_auto_deliver_related_id(
                  creative_support_info.auto_deliver_related_id());
      }
      if (creative_support_info.has_live_stream_id()) {
        highlight_info->set_live_stream_id(
                  creative_support_info.live_stream_id());
      }
    }

    creative->mutable_extend_fields()
        ->mutable_creative_extend_score()
        ->CopyFrom(creative_extend_score);
  }

  static void ParseStringField(kuaishou::ad::tables::Creative *creative) {
    if (!creative) {
      return;
    }

    // has been set before
    if (creative->mutable_parse_field()->ByteSizeLong() > 0) {
      return;
    }

    base::Json json(base::StringToJson(creative->delivery_package()));
    do {
      auto sticker_titles = json.Get("stickerTitles");
      if (!sticker_titles || !sticker_titles->IsArray() || sticker_titles->size() == 0) {
        break;
      }
      for (auto item : sticker_titles->array()) {
        if (!item->IsObject()) {
          continue;
        }
        int64_t id = item->GetInt("titleId", 0);
        std::string title = item->GetString("title", "");
        if (id == 0 || title.empty()) {
          continue;
        }

        creative->mutable_parse_field()->mutable_stick_titles()->insert({id, title});
      }
    } while (false);

    do {
      auto description_titles = json.Get("descriptionTitles");
      if (!description_titles|| !description_titles->IsArray() || description_titles->size() == 0) {
        break;
      }
      for (auto item : description_titles->array()) {
        if (!item->IsObject()) {
          continue;
        }
        int64_t id = item->GetInt("descriptionId", 0);
        std::string title = item->GetString("description", "");
        if (id == 0 || title.empty()) {
          continue;
        }

        creative->mutable_parse_field()->mutable_description_titles()->insert({id, title});
      }
    } while (false);

    base::Json json_extra_displays(base::StringToJson(creative->extra_display_info()));
    for (auto iter = json_extra_displays.object_begin(); iter != json_extra_displays.object_end(); ++iter) {
      if (iter->first == "exposeTag") {
        creative->mutable_parse_field()->mutable_extra_displays()->insert(
            {iter->first, iter->second->StringValue()});
      } else if (iter->first == "newExposeTag") {
        if (iter->second->IsArray()) {
          auto iter_tag = iter->second->array_begin();
          for (; iter_tag != iter->second->array_end(); ++iter_tag) {
            if (!(*iter_tag)->IsObject()) {
              continue;
            }
            std::string text = (*iter_tag)->GetString("text", "");
            std::string url = (*iter_tag)->GetString("url", "");
            kuaishou::ad::tables::NewExposeTags *new_expose_tag =
                creative->mutable_parse_field()->add_expose_tags();
            new_expose_tag->set_text(text);
            new_expose_tag->set_url(url);
          }
        }
      } else if (iter->first == "gameOrderCard") {
        bool default_value = false;
        creative->mutable_parse_field()->mutable_extra_display_bool()->insert(
            {iter->first, iter->second->BooleanValue(default_value)});
      } else if (iter->first == "financialFrontCard") {
        bool default_value = false;
        creative->mutable_parse_field()->mutable_extra_display_bool()->insert(
            {iter->first, iter->second->BooleanValue(default_value)});
      } else if (iter->first == "cardAppearSecond") {
        int64_t default_value = 0;
        creative->mutable_parse_field()->mutable_extra_display_int()->insert(
            {iter->first, iter->second->IntValue(default_value)});
      } else if (iter->first == "preDescription") {
        creative->mutable_parse_field()->mutable_extra_displays()->insert(
            {iter->first, iter->second->StringValue()});
      }
    }
    creative->clear_extra_display_info();

    do {
      base::Json json(base::StringToJson(creative->review_through_cover_slogans_info()));
      if (!json.IsArray() || json.size() == 0) {
        break;
      }

      int64_t id = 0;
      std::string title;
      for (auto item : json.array()) {
        if (!item->IsObject()) {
          continue;
        }
        id = item->GetInt("sloganId", 0);
        title = item->GetString("slogan", "");
        if (id == 0 || title.empty()) {
          continue;
        }
        creative->mutable_parse_field()->mutable_stick_titles()->insert({id, title});
      }
    } while (false);
    creative->clear_review_through_cover_slogans_info();

    do {
      base::Json json_desc_title(base::StringToJson(creative->description_titles_info()));
      if (!json_desc_title.IsArray() || json_desc_title.size() == 0) {
        break;
      }

      int64_t id = 0;
      std::string title;
      for (auto item : json_desc_title.array()) {
        if (!item->IsObject()) {
          continue;
        }
        id = item->GetInt("descriptionId", 0);
        title = item->GetString("description", "");
        if (id == 0 || title.empty()) {
          continue;
        }
        creative->mutable_parse_field()->mutable_description_titles()->insert({id, title});
      }
    } while (false);
    creative->clear_description_titles_info();

    if (!creative->sticker_styles().empty()) {
      std::vector<absl::string_view> styles =
          absl::StrSplit(creative->sticker_styles(), ",", absl::SkipEmpty());
      int64_t style_id = 0;
      for (const auto& style : styles) {
        if (absl::SimpleAtoi(style, &style_id)) {
          creative->mutable_parse_field()->add_sticker_styles(style_id);
        }
      }
    }
  }

  uint64_t key_id() {return id;}

  bool IsValid() const {
  static const std::set<kuaishou::ad::AdEnum::ReviewStatus> review_pass_status{
      kuaishou::ad::AdEnum::REVIEW_THROUGH,
      kuaishou::ad::AdEnum::REVIEW_BASIC_THROUGH,
      kuaishou::ad::AdEnum::REVIEW_NEBULA_THROUGH};
  return put_status == ::kuaishou::ad::AdEnum_PutStatus_PUT_STATUS_OPEN &&
         (review_pass_status.count(review_status) ||
          review_pass_status.count(community_review_status));
  }

  bool IsProgrammaticCreative() const {
    return create_source_type == kuaishou::ad::AdEnum::CUSTOMIZED_PROGRAMMATIC_CREATIVE ||
           create_source_type == kuaishou::ad::AdEnum::PACKAGE_PROGRAMMATIC_CREATIVE;
  }

  bool IsAdvancedProgramCreative() const {
    return create_source_type == kuaishou::ad::AdEnum::ADVANCED_PROGRAMMED_CREATIVE;
  }

  int64_t GetCreateTime(int32_t tag) const {
    return ((tag == +CreativeTimeTag::kAuditTime) ? first_audit_passtime : create_time);
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }
};

}  // namespace data_gate
}  // namespace tables

