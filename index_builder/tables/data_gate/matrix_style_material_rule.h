#pragma once

#include <string>
#include <utility>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct MatrixStyleMaterialRule {
  enum __capicity {
    kCapicity = 1 << 15,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int32_t type;
  int64_t rule_id;
  int32_t flow_scenes;
  int64_t ad_page_id;
  kuaishou::ad::AdEnum::PutStatus put_status;

  DEFINE_common_of_container(MatrixStyleMaterialRule, kuaishou::ad::tables::AdMatrixStyleMaterialSceneRule);

  static MatrixStyleMaterialRule gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdMatrixStyleMaterialSceneRule *pb_ptr = nullptr;
    MatrixStyleMaterialRule tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::AdMatrixStyleMaterialSceneRule::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdMatrixStyleMaterialSceneRule*>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, type, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, rule_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, flow_scenes, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, ad_page_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, put_status, kuaishou::ad::AdEnum_PutStatus_UNKNOWN_PUT_STATUS)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<MatrixStyleMaterialRule>(tmp);
  }

  uint64_t key_id() { return id; }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }

  bool IsValid() const {
    return put_status == kuaishou::ad::AdEnum_PutStatus_PUT_STATUS_OPEN;
  }
};

}  // namespace data_gate
}  // namespace tables
