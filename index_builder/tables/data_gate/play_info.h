#pragma once

#include <stdint.h>
#include <string>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct PlayInfo {
  enum __capicity {
    kCapicity = 1 << 20,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t play_id;
  int64_t account_id;   //账号id

  kuaishou::ad::AdEnum::AdAssetPlayOrientation play_orientation;  //试玩支持的横竖版类型： 0:横竖均可 1:竖屏 2:横屏
  kuaishou::ad::AdEnum::AdAssetPlayTypeEnum play_type;  //试玩类型 1:轻度，2：重度
  int64_t play_time;  // 试玩时长，0表示不限时长
  kuaishou::ad::AdEnum::AdAssetPlaySource upload_source;  //试玩来源 1:本地，2:联盟
  int64_t create_time;  // 创建时间
  int64_t update_time;  // 更新时间

  DEFINE_common_of_container(PlayInfo, kuaishou::ad::tables::AdDspPlayInfo);

  static PlayInfo gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdDspPlayInfo *pb_ptr = nullptr;
    PlayInfo tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::AdDspPlayInfo::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdDspPlayInfo*>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, play_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, account_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, play_orientation, kuaishou::ad::AdEnum::ALL_ALLOW)
      SET_FIELD_BY_PB(pb_ptr, tmp, play_type, kuaishou::ad::AdEnum::UNKNOWN_PLAY_TYPE)
      SET_FIELD_BY_PB(pb_ptr, tmp, play_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, upload_source, kuaishou::ad::AdEnum::PLAY_SOURCE_UNKNOWN)
      SET_FIELD_BY_PB(pb_ptr, tmp, create_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, update_time, 0)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<PlayInfo>(tmp);
  }

  uint64_t key_id() {return play_id;}

  bool IsValid() {
    return play_id > 0 && play_type != kuaishou::ad::AdEnum::UNKNOWN_PLAY_TYPE &&
            upload_source != kuaishou::ad::AdEnum::PLAY_SOURCE_UNKNOWN &&
            create_time != 0 && update_time != 0;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.play_id();
  }
};

}  // namespace data_gate
}  // namespace tables

