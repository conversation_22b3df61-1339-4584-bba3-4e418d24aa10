#pragma once

#include <stdint.h>

#include <string>
#include <utility>

#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/index_builder/tables/table_util.h"

namespace tables {
namespace data_gate {

struct WTUnit {
  enum __capicity {
    kCapicity = 1 << 24,
    kDataVersion = 5
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int64_t app_id;
  kuaishou::ad::AdEnum::PutStatus put_status;
  kuaishou::ad::AdEnum::ReviewStatus review_status;
  kuaishou::ad::AdEnum::ReviewStatus community_review_status;
  int64_t first_index_time;
  int64_t campaign_id;
  int64_t account_id;
  int32_t target_expand_stage;
  float roi_flow_hard;
  float roi_incentive_photo;
  float roi_incentive_live;
  int64_t cost_past_two_days;
  float playlet_min_charge;
  int32_t is_ual_narrowtaget;
  int64_t cost_total_7_day;
  int64_t cost_7_day;
  int64_t item_impression_7_day;
  double callback_purchase_amount_7_day;
  int64_t target_cost_7_day;
  int32_t is_cold;
  int64_t cost_1_day;
  int64_t target_cost_1_day;
  int64_t cost_2_day;
  int64_t target_cost_2_day;
  int64_t project_cost_1_day;
  double project_target_cost_1_day;
  int64 project_store_cost_1_day;
  double project_store_target_cost_1_day;
  int64 project_cost_7_day;
  double project_target_cost_7_day;
  int64 project_store_cost_7_day;
  double project_store_target_cost_7_day;
  double project_store_merchant_target_cost_1_day;
  double project_store_merchant_target_cost_7_day;
  int64_t pc_target_cost_today_sum_timestamp;
  int64_t pc_target_cost_today_sum_price;
  int64_t pc_target_cost_today_sum_expect_cost;
  int64_t special_unit_type;
  int64_t aigc_update_timestamp_today;
  int64_t cost_total_aigc_today;
  int64_t cost_total_aigc_1_day;
  int64_t cost_total_aigc_2_day;
  int64_t target_cost_aigc_today;
  int64_t target_cost_aigc_1_day;
  int64_t target_cost_aigc_2_day;

  DEFINE_common_of_container(WTUnit, kuaishou::ad::tables::WTUnit);

  static WTUnit gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::WTUnit *pb_ptr = nullptr;
    WTUnit tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::WTUnit::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::WTUnit *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, app_id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, put_status, kuaishou::ad::AdEnum::UNKNOWN_PUT_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, review_status, kuaishou::ad::AdEnum::UNKNOWN_REVIEW_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, community_review_status, kuaishou::ad::AdEnum::UNKNOWN_REVIEW_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, first_index_time, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, campaign_id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, account_id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, target_expand_stage, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, roi_flow_hard, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, roi_incentive_photo, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, roi_incentive_live, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, cost_past_two_days, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, playlet_min_charge, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, is_ual_narrowtaget, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, cost_total_7_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, cost_7_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, item_impression_7_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, callback_purchase_amount_7_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, target_cost_7_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, is_cold, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, cost_1_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, target_cost_1_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, cost_2_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, target_cost_2_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_cost_1_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_target_cost_1_day, 0.0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_store_cost_1_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_store_target_cost_1_day, 0.0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_cost_7_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_target_cost_7_day, 0.0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_store_cost_7_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_store_target_cost_7_day, 0.0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_store_merchant_target_cost_1_day, 0.0);
      SET_FIELD_BY_PB(pb_ptr, tmp, project_store_merchant_target_cost_7_day, 0.0);
      SET_FIELD_BY_PB(pb_ptr, tmp, pc_target_cost_today_sum_timestamp, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, pc_target_cost_today_sum_price, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, pc_target_cost_today_sum_expect_cost, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, special_unit_type, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, aigc_update_timestamp_today, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, cost_total_aigc_today, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, cost_total_aigc_1_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, cost_total_aigc_2_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, target_cost_aigc_today, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, target_cost_aigc_1_day, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, target_cost_aigc_2_day, 0);
      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<WTUnit>(tmp);
  }

  uint64_t key_id() { return id; }

  bool IsValid() { return id > 0; }

  static int64_t key_id_of(const ProtoType &msg) { return msg.id(); }
};

}  // namespace data_gate
}  // namespace tables
