#pragma once

#include <stdint.h>

#include <string>
#include <utility>

#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/index_builder/tables/table_util.h"

namespace tables {
namespace data_gate {

struct AdEspProductLabelInfo {
  enum __capicity {
    kCapicity = 1 << 24,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int64_t product_id;
  int32_t type;
  int32_t is_deleted;

  DEFINE_common_of_container(AdEspProductLabelInfo, kuaishou::ad::tables::AdEspProductLabelInfo);

  static AdEspProductLabelInfo gen_from_proto(const google::protobuf::Message* pb, bool is_attach) {
    const kuaishou::ad::tables::AdEspProductLabelInfo* pb_ptr = nullptr;
    AdEspProductLabelInfo tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::AdEspProductLabelInfo::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdEspProductLabelInfo*>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, product_id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, type, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, is_deleted, 0);

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<AdEspProductLabelInfo>(tmp);
  }

  uint64_t key_id() { return id; }

  bool IsValid() { return id > 0 && is_deleted == 0; }

  static int64_t key_id_of(const ProtoType &msg) { return msg.id(); }
};

}  // namespace data_gate
}  // namespace tables
