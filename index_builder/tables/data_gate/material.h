#pragma once

#include <stdint.h>
#include <string>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct Material {
  enum __capicity {
    kCapicity = 1 << 22,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int64_t cover_width;
  int64_t cover_height;
  int64_t photo_id;
  int64_t cover_type;
  int32_t data_version;

  kuaishou::ad::AdEnum::AdMaterialType type;
  kuaishou::ad::AdEnum::AdPosType pos_type;
  DEFINE_common_of_container(Material, kuaishou::ad::tables::Material);

  static Material gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::Material *pb_ptr = nullptr;
    Material tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::Material::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::Material *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, cover_width, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, cover_height, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, photo_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, data_version, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, type, kuaishou::ad::AdEnum::UNKONWUN_MATERIAL_TYPE)
      SET_FIELD_BY_PB(pb_ptr, tmp, pos_type, kuaishou::ad::AdEnum::UNKNOWN_POS_TYPE)

      if (tmp.cover_height > 0) {
        double ratio = tmp.cover_width * 1.0 / tmp.cover_height;
        util::CalWidthHeightRatioType(ratio, &tmp.cover_type);
      }

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<Material>(tmp);
  }

  uint64_t key_id() {return id;}

  bool IsValid() const {
    return id > 0;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }
};

}  // namespace data_gate
}  // namespace tables

