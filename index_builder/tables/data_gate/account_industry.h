#pragma once

#include <stdint.h>
#include <string>
#include <utility>

#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct AccountIndustry {
  enum capicity { kCapicity = 1 << 21, kDataVersion = 6 };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t account_id;
  int64_t first_industry_id_v6;  // 行业 6.0 一级
  int64_t second_industry_id_v6;  // 行业 6.0 二级

  DEFINE_common_of_container(AccountIndustry, kuaishou::ad::tables::AccountIndustry);

  static AccountIndustry gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AccountIndustry *pb_ptr = nullptr;
    AccountIndustry tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::AccountIndustry::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AccountIndustry *>(pb);

      SET_FIELD_BY_PB(pb_ptr, tmp, account_id, 0)
      auto *extend_fields = &(pb_ptr->extend_fields());
      tmp.first_industry_id_v6 = extend_fields->first_industry_id_v6();
      tmp.second_industry_id_v6 = extend_fields->second_industry_id_v6();

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<AccountIndustry>(tmp);
  }

  uint64_t key_id() { return account_id; }

  static int64_t key_id_of(const ProtoType &msg) { return msg.account_id(); }
};

}  // namespace data_gate
}  // namespace tables
