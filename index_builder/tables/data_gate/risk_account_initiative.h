#pragma once

#include <stdint.h>
#include <string>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct RiskAccountInitiative {
  enum __capicity {
    kCapicity = 1 << 15,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t account_id;
  int64_t target_id;
  int64_t task_id;
  int64_t create_time;
  int64_t update_time;
  double rate_limiter_percent;
  int32_t traffic_source_type;
  bool mark_deletion;

  int32_t schedule_dist[util::kWeekDays];

  // TODO
  // int64_t seq_no;
  DEFINE_common_of_container(RiskAccountInitiative, kuaishou::ad::tables::RiskAccountInitiative);

  static RiskAccountInitiative gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::RiskAccountInitiative *pb_ptr = nullptr;
    RiskAccountInitiative tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::RiskAccountInitiative::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::RiskAccountInitiative *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, account_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, target_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, task_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, create_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, update_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, rate_limiter_percent, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, traffic_source_type, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, mark_deletion, 0)

      util::ParseForbiddenSchedule(pb_ptr->forbidden_schedule(), tmp.schedule_dist, util::kWeekDays);

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<RiskAccountInitiative>(tmp);
  }

  uint64_t key_id() {return account_id;}

  bool IsValid() {
    return account_id > 0;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.account_id();
  }
};

}  // namespace data_gate
}  // namespace tables

