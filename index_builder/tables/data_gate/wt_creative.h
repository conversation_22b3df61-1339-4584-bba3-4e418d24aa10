#pragma once

#include <stdint.h>
#include <set>
#include <string>
#include <utility>

#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/index_builder/tables/table_util.h"

namespace tables {
namespace data_gate {

struct WTCreative {
  enum __capicity { kCapicity = 1 << 27, kDataVersion = 1 };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  kuaishou::ad::AdEnum::PutStatus put_status;
  kuaishou::ad::AdEnum::ReviewStatus review_status;
  kuaishou::ad::AdEnum::ReviewStatus community_review_status;

  int64_t first_index_time;

  DEFINE_common_of_container(WTCreative, kuaishou::ad::tables::WTCreative);

  static WTCreative gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::WTCreative *pb_ptr = nullptr;
    WTCreative tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::WTCreative::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::WTCreative *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, first_index_time, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, put_status, kuaishou::ad::AdEnum::UNKNOWN_PUT_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, review_status, kuaishou::ad::AdEnum::UNKNOWN_REVIEW_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, community_review_status, kuaishou::ad::AdEnum::UNKNOWN_REVIEW_STATUS)
      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<WTCreative>(tmp);
  }

  uint64_t key_id() { return id; }

  bool IsValid() const {
    static const std::set<kuaishou::ad::AdEnum::ReviewStatus> review_pass_status{
        kuaishou::ad::AdEnum::REVIEW_THROUGH,
        kuaishou::ad::AdEnum::REVIEW_BASIC_THROUGH,
        kuaishou::ad::AdEnum::REVIEW_NEBULA_THROUGH};
    return put_status == ::kuaishou::ad::AdEnum_PutStatus_PUT_STATUS_OPEN &&
          (review_pass_status.count(review_status) ||
            review_pass_status.count(community_review_status));
  }

  static int64_t key_id_of(const ProtoType &msg) { return msg.id(); }
};

}  // namespace data_gate
}  // namespace tables
