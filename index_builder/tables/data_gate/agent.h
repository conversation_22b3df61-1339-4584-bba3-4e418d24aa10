#pragma once

#include <stdint.h>
#include <string>
#include <utility>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/kconf.h"

namespace tables {
namespace data_gate {

struct Agent {
  enum __capicity {
    kCapicity = 1 << 15,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t agent_id;
  int32_t agent_type;
  int64_t user_id;
  int64_t update_time;
  DEFINE_common_of_container(Agent, kuaishou::ad::tables::Agent);

  static Agent gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::Agent *pb_ptr = nullptr;
    Agent tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::Agent::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::Agent *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, agent_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, user_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, update_time, 0)
      tmp.agent_type = pb_ptr->crm_agent_type();
      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<Agent>(tmp);
  }

  uint64_t key_id() {return agent_id;}
  static int64_t key_id_of(const ProtoType &msg) {
    return msg.agent_id();
  }
};

}  // namespace data_gate
}  // namespace tables

