#pragma once
#include <stdint.h>
#include <set>
#include <string>
#include <utility>
#include <vector>
#include <unordered_map>
#include "perfutil/perfutil.h"
#include "absl/strings/numbers.h"
#include "absl/strings/str_split.h"
#include "serving_base/jansson/json.h"
#include "google/protobuf/util/json_util.h"
#include "teams/ad/ad_base/src/better_enums/better_enums.h"
#include "teams/ad/ad_base/src/common/basis_enum_types.h"
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct AdDspMerchantProductInfo {
  enum __capicity {
    kCapicity = 1 << 20,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  uint64_t product_id;
  uint64_t event_time;
  uint64_t create_time;
  uint64_t update_time;

  kuaishou::ad::AdEnum::ProductType product_type;
  kuaishou::ad::AdEnum::ProductStatus product_status;

  kuaishou::ad::AdEnum::AdMerchantProductStockLevelEnum stock_level;   // 库存级别
  kuaishou::ad::AdEnum::AdMerchantProductVisibleEnum visible;  // 复合状态，表示一般场景下商品状态是否可投

  DEFINE_common_of_container(AdDspMerchantProductInfo, kuaishou::ad::tables::AdDspMerchantProductInfo);

  static AdDspMerchantProductInfo gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdDspMerchantProductInfo *pb_ptr = nullptr;
    AdDspMerchantProductInfo tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::AdDspMerchantProductInfo::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdDspMerchantProductInfo *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, product_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, event_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, create_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, update_time, 0)

      SET_FIELD_BY_PB(pb_ptr, tmp, product_type, kuaishou::ad::AdEnum::PRODUCT_UNKNOWN_TYPE)
      SET_FIELD_BY_PB(pb_ptr, tmp, product_status, kuaishou::ad::AdEnum::PRODUCT_UNKNOWN_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, stock_level, kuaishou::ad::AdEnum::MERCHANT_PRODUCT_STOCK_LEVEL_UNKNOWN)
      SET_FIELD_BY_PB(pb_ptr, tmp, visible, kuaishou::ad::AdEnum::MERCHANT_PRODUCT_UNKNOWN)
      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<AdDspMerchantProductInfo>(tmp);
  }

  uint64_t key_id() {
    return product_id;
  }

  bool IsValid() const {
    return product_id > 0 &&
      product_type == kuaishou::ad::AdEnum::PRODUCT_SMALL_SHOP_TYPE &&
      visible == kuaishou::ad::AdEnum::MERCHANT_PRODUCT_VISIBLE;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.product_id();
  }
};

}  // namespace data_gate
}  // namespace tables

