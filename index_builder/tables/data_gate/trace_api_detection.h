#pragma once

#include <stdint.h>
#include <string>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct TraceApiDetection {
  enum __capicity {
    kCapicity = 1 << 15,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int64_t convert_id;
  int64_t account_id;
  int64_t user_id;
  int64_t create_time;
  int64_t expire_time;
  bool detect_all;
  DEFINE_common_of_container(TraceApiDetection, kuaishou::ad::tables::TraceApiDetection);

  static TraceApiDetection gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::TraceApiDetection *pb_ptr = nullptr;
    TraceApiDetection tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::TraceApiDetection::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::TraceApiDetection *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, convert_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, account_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, user_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, create_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, expire_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, detect_all, false)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<TraceApiDetection>(tmp);
  }

  uint64_t key_id() {return id;}

  bool IsValid() {
    return id > 0;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }
};

}  // namespace data_gate
}  // namespace tables

