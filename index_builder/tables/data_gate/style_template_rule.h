#pragma once

#include <string>
#include <utility>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct StyleTemplateRule {
  enum __capicity {
    kCapicity = 1 << 10,
    kDataVersion = 3
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;

  DEFINE_common_of_container(StyleTemplateRule, kuaishou::ad::tables::AdStyleTemplateSceneRule);

  static StyleTemplateRule gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdStyleTemplateSceneRule *pb_ptr = nullptr;
    StyleTemplateRule tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::AdStyleTemplateSceneRule::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdStyleTemplateSceneRule *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }

    return std::forward<StyleTemplateRule>(tmp);
  }

  uint64_t key_id() {return id;}

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }
};

}  // namespace data_gate
}  // namespace tables
