#pragma once
#include <stdint.h>
#include <set>
#include <string>
#include <utility>
#include <vector>
#include <unordered_map>
#include "perfutil/perfutil.h"
#include "absl/strings/numbers.h"
#include "absl/strings/str_split.h"
#include "serving_base/jansson/json.h"
#include "google/protobuf/util/json_util.h"
#include "teams/ad/ad_base/src/better_enums/better_enums.h"
#include "teams/ad/ad_base/src/common/basis_enum_types.h"
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct EspComboOrderInfo {
  enum __capicity {
    kCapicity = 1 << 18,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }
  uint64_t id;
  uint64_t account_id;
  uint64_t combo_id;
  kuaishou::ad::AdEnum::EspOrderPutStatus put_status;
  kuaishou::ad::AdEnum::EspComboType combo_type;
  uint64_t budget;
  uint64_t put_start_time;
  uint64_t put_end_time;

  DEFINE_common_of_container(EspComboOrderInfo, kuaishou::ad::tables::EspComboOrderInfo);

  static EspComboOrderInfo gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::EspComboOrderInfo *pb_ptr = nullptr;
    EspComboOrderInfo tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::EspComboOrderInfo::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::EspComboOrderInfo *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, account_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, combo_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, budget, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, put_start_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, put_end_time, 0)

      SET_FIELD_BY_PB(pb_ptr, tmp, put_status, kuaishou::ad::AdEnum::ORDER_PUT_STATUS_DEFAULT)
      SET_FIELD_BY_PB(pb_ptr, tmp, combo_type, kuaishou::ad::AdEnum::COMBO_DEFAULT)
      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<EspComboOrderInfo>(tmp);
  }

  uint64_t key_id() {
    return id;
  }

  bool IsValid() const {
    if (id == 0) {
      return false;
    }
    if (put_status == kuaishou::ad::AdEnum::ORDER_FINISH_STATUS ||
      put_status == kuaishou::ad::AdEnum::ORDER_DELETED_STATUS) {
      return false;
    }
    return true;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }
};

}  // namespace data_gate
}  // namespace tables

