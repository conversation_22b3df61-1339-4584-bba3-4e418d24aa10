#pragma once

#include <stdint.h>

#include <string>
#include <utility>

#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/index_builder/tables/table_util.h"

namespace tables {
namespace data_gate {

struct AdKstubeTubeEpisode {
  enum __capicity { kCapicity = 1 << 20, kDataVersion = 1 };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int64_t photo_id;
  int64_t tube_id;
  DEFINE_common_of_container(AdKstubeTubeEpisode, kuaishou::ad::tables::AdKstubeTubeEpisode);

  static AdKstubeTubeEpisode gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdKstubeTubeEpisode *pb_ptr = nullptr;
    AdKstubeTubeEpisode tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::AdKstubeTubeEpisode::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdKstubeTubeEpisode *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, photo_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, tube_id, 0)
      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<AdKstubeTubeEpisode>(tmp);
  }

  uint64_t key_id() { return id; }

  bool IsValid() const { return id > 0; }

  static int64_t key_id_of(const ProtoType &msg) { return msg.id(); }
};

}  // namespace data_gate
}  // namespace tables
