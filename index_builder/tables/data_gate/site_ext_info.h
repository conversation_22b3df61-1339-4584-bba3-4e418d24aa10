#pragma once

#include <stdint.h>
#include <string>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_base/src/common/os_version.h"

namespace tables {
namespace data_gate {

struct SiteExtInfo {
  enum __capicity {
    kCapicity = 1 << 15,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int64_t site_id;
  int64_t site_type;
  int64_t create_time;

  // parse from SiteExtInfo.version
  int64_t ios_version;
  int64_t android_version;
  int64_t nebula_ios_version;
  int64_t nebula_android_version;

  // TODO
  // int64_t seq_no;
  DEFINE_common_of_container(SiteExtInfo, kuaishou::ad::tables::SiteExtInfo);

  static SiteExtInfo gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::SiteExtInfo *pb_ptr = nullptr;
    SiteExtInfo tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::SiteExtInfo::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::SiteExtInfo *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, site_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, site_type, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, create_time, 0)

      tmp.ParseVersion(pb_ptr->version());

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<SiteExtInfo>(tmp);
  }

  // version 字段格式：{"androidApp":"2.6","iosApp":"2.7","iosNebula":"1.5","androidNebula":"2.0"}
  void ParseVersion(const std::string& version) {
    if (version.empty()) {
      return;
    }

    base::Json json(base::StringToJson(version));
    for (auto& item : json.objects()) {
      const std::string& app_version = item.second->StringValue();
      if (item.first == "androidApp") {
        android_version = ks::ad_base::OsVersionTrans(app_version);
      } else if (item.first == "iosApp") {
        ios_version = ks::ad_base::OsVersionTrans(app_version);
      } else if (item.first == "iosNebula") {
        nebula_ios_version = ks::ad_base::OsVersionTrans(app_version);
      } else if (item.first == "androidNebula") {
        nebula_android_version = ks::ad_base::OsVersionTrans(app_version);
      }
    }
  }

  uint64_t key_id() {return id;}

  bool IsValid() {
    return id > 0;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }
};

}  // namespace data_gate
}  // namespace tables

