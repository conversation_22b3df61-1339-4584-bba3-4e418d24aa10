#pragma once

#include <stdint.h>
#include <string>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct CampaignDayCharge {
  enum __capicity {
    kCapicity = 1 << 15,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t campaign_id;
  int64_t charged;
  int64_t charge_date;
  int64_t rebate_real_charged;
  int64_t real_charged;
  int64_t pre_rebate_real_charged;
  int64_t direct_rebate_real_charged;
  int64_t contract_rebate_real_charged;
  int64_t credit_real_charged;
  int64_t extended_real_charged;
  DEFINE_common_of_container(CampaignDayCharge, kuaishou::ad::tables::CampaignDayCharge);

  static CampaignDayCharge gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::CampaignDayCharge *pb_ptr = nullptr;
    CampaignDayCharge tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::CampaignDayCharge::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::CampaignDayCharge *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, campaign_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, charged, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, charge_date, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, rebate_real_charged, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, real_charged, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, pre_rebate_real_charged, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, direct_rebate_real_charged, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, contract_rebate_real_charged, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, credit_real_charged, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, extended_real_charged, 0)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<CampaignDayCharge>(tmp);
  }

  uint64_t key_id() {return campaign_id;}
  static int64_t key_id_of(const ProtoType &msg) {
    return msg.campaign_id();
  }
};

}  // namespace data_gate
}  // namespace tables

