#pragma once

#include <stdint.h>
#include <string>
#include <utility>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/parser/extra_parser.h"
namespace tables {
namespace data_gate {

struct AdDspHostingProject {
  enum capicity { kCapicity = 1 << 21, kDataVersion = 8 };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  kuaishou::ad::AdEnum::HostingScene hosting_scene;
  kuaishou::ad::AdEnum::PutStatus put_status;
  kuaishou::ad::AdEnum::CampaignType campaign_type;
  int64_t campaign_id;
  int64_t account_id;

  // unit
  int64_t app_id;
  bool use_app_market;
  int64_t target_id;

  // creative
  bool smart_cover;

  int64_t begin_time;
  int64_t end_time;
  int64_t day_budget;
  int16_t ocpx_action_type;
  int64_t cpa_bid;
  int16_t deep_conversion_type;
  int64_t deep_conversion_bid;
  double roi_ratio;
  bool asset_mining;
  bool auto_target;

  int64_t create_time;
  int64_t update_time;
  bool auto_create_photo;

  kuaishou::ad::AdEnum::BidType bid_type;
  int64_t constraint_cpa;
  int64_t last_pause_time;
  kuaishou::ad::AdActionType constraint_action_type;
  kuaishou::ad::AdEnum::ProjectType project_type;

  DEFINE_common_of_container(AdDspHostingProject, kuaishou::ad::tables::AdDspHostingProject);

  static AdDspHostingProject gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdDspHostingProject *pb_ptr = nullptr;
    AdDspHostingProject tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::AdDspHostingProject::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdDspHostingProject *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      if (pb_ptr->has_hosting_scene()) {
        tmp.hosting_scene =
            static_cast<const kuaishou::ad::AdEnum::HostingScene>(
                pb_ptr->hosting_scene());
      } else {
        tmp.hosting_scene =
            kuaishou::ad::AdEnum_HostingScene_COMMON_HOSTING_SCENE;
      }
      SET_FIELD_BY_PB(pb_ptr, tmp, put_status, kuaishou::ad::AdEnum_PutStatus_UNKNOWN_PUT_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, campaign_type, kuaishou::ad::AdEnum_CampaignType_UNKNOWN_CAMPAIGN_TYPE)
      SET_FIELD_BY_PB(pb_ptr, tmp, campaign_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, account_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, app_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, use_app_market, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, target_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, smart_cover, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, begin_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, end_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, day_budget, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, ocpx_action_type, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, cpa_bid, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, deep_conversion_type, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, deep_conversion_bid, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, roi_ratio, 0.0)
      SET_FIELD_BY_PB(pb_ptr, tmp, asset_mining, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, auto_target, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, create_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, update_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, auto_create_photo, 0)

      SET_FIELD_BY_PB(pb_ptr, tmp, bid_type,
                      kuaishou::ad::AdEnum_BidType_UNKNOWN_BID_TYPE)
      SET_FIELD_BY_PB(pb_ptr, tmp, constraint_cpa, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, last_pause_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, constraint_action_type,
                      kuaishou::ad::AdActionType::UNKNOWN_ACTION_TYPE)
      SET_FIELD_BY_PB(pb_ptr, tmp, project_type,
                      kuaishou::ad::AdEnum::NORMAL_PROJECT)
      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<AdDspHostingProject>(tmp);
  }
  // 对 proto 进行一些解析修改
  static void ProcessProto(ProtoType *pb_ptr) {
    /*
    auto ad_dsp_hosting_project_parser_manager =
        ks::index_builder::TableParserManager<google::protobuf::Message,
                                              kuaishou::ad::tables::AdDspHostingProject>::GetInstance();
    ad_dsp_hosting_project_parser_manager->Parse(pb_ptr);
    */
    ParseStringField(pb_ptr);
  }

  static void ParseStringField(kuaishou::ad::tables::AdDspHostingProject *pb) {
  }

  uint64_t key_id() {
    return id;
  }
  std::string to_sting() const {
    return absl::Substitute("id:$0, is_static:$1, idx:$2, length:$3, offset:$4, is_shared: $5", id,
                            attach.is_static, attach.idx, attach.length, attach.offset, attach.is_shared);
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }
};

}  // namespace data_gate
}  // namespace tables
