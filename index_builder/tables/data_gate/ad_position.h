#pragma once

#include <stdint.h>
#include <string>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct AdPosition {
  enum __capicity {
    kCapicity = 1 << 19,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int64_t page_id;
  int64_t sub_page_id;
  int32_t ad_place_position;
  int32_t action;
  int64_t support_flags;
  int64_t create_time;
  kuaishou::ad::AdEnum::Status status;
  kuaishou::ad::AdEnum::AdScene report_scene;
  DEFINE_common_of_container(AdPosition, kuaishou::ad::tables::AdPosition);

  static AdPosition gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdPosition *pb_ptr = nullptr;
    AdPosition tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::AdPosition::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdPosition *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, page_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, sub_page_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, ad_place_position, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, action, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, support_flags, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, create_time, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, status, kuaishou::ad::AdEnum_Status_UNKNOW_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, report_scene, kuaishou::ad::AdEnum_AdScene_UNKNOWN_SCENE)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<AdPosition>(tmp);
  }

  uint64_t key_id() {return id;}

  bool IsValid() const {
    return id > 0;
  }
  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }
};

}  // namespace data_gate
}  // namespace tables

