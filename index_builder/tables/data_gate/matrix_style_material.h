#pragma once

#include <string>
#include <utility>
#include <unordered_set>

#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/index_builder/utils/kconf.h"

namespace tables {
namespace data_gate {

struct MatrixStyleMaterial {
  enum __capicity {
    kCapicity = 1 << 24,
    kDataVersion = 7
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int32_t type;
  int64_t rule_id;
  kuaishou::ad::AdEnum::PutStatus put_status;
  kuaishou::ad::AdEnum::ReviewStatus review_status;
  kuaishou::ad::AdEnum::CreativeMaterialType material_type;
  int64_t material_rule_id;
  int64_t template_id;
  int64_t exp_tag;
  int64_t duration;
  int32_t card_type;
  uint64_t tags;
  int32_t cover_width;
  int32_t cover_height;

  DEFINE_common_of_container(MatrixStyleMaterial, kuaishou::ad::tables::AdMatrixStyleMaterial);

  static MatrixStyleMaterial gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdMatrixStyleMaterial *pb_ptr = nullptr;
    MatrixStyleMaterial tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::AdMatrixStyleMaterial::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdMatrixStyleMaterial*>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, type, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, rule_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, put_status, kuaishou::ad::AdEnum_PutStatus_UNKNOWN_PUT_STATUS)
      SET_FIELD_BY_PB(pb_ptr, tmp, review_status, kuaishou::ad::AdEnum_ReviewStatus_UNKNOWN_REVIEW_STATUS)

      ParseStyleContent(tmp, pb_ptr);

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<MatrixStyleMaterial>(tmp);
  }

  uint64_t key_id() { return id; }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }

  bool IsValid() const {
    return put_status == kuaishou::ad::AdEnum_PutStatus_PUT_STATUS_OPEN;
  }

  static void ParseStyleContent(MatrixStyleMaterial &tmp, const kuaishou::ad::tables::AdMatrixStyleMaterial *style) {  // NOLINT
    if (nullptr == style ||
        ks::index_builder::AdKconfUtil::adMatrixTypeMaterialSet()->count(style->type()) == 0) {
      return;
    }

    const auto& parse_field = style->parse_field();
    do {
      tmp.material_rule_id = parse_field.rule_id();
      tmp.template_id = parse_field.template_id();
      tmp.material_type = parse_field.material_type();
      tmp.card_type = parse_field.card_type();
      tmp.exp_tag = parse_field.exp_tag();
      tmp.duration = parse_field.duration();
      tmp.tags = 0;
      for (const auto& tag : parse_field.tags()) {
        if (tag > 0 && tag <= 64) {
          tmp.tags |= 1 << (tag - 1);
        }
      }
      tmp.cover_width = parse_field.cover_width();
      tmp.cover_height = parse_field.cover_height();
    } while (false);
  }
};

}  // namespace data_gate
}  // namespace tables
