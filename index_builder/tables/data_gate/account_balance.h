#pragma once

#include <stdint.h>
#include <string>
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct AccountBalance {
  enum __capicity {
    kCapicity = 1 << 20,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t account_id;
  int64_t balance;
  int64_t rebate;
  int64_t direct_rebate;
  int64_t pre_rebate;
  int64_t contract_rebate;
  int64_t credit_balance;
  int64_t extended_balance;
  int64_t balance_enough;
  DEFINE_common_of_container(AccountBalance, kuaishou::ad::tables::AccountBalance);

  static AccountBalance gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AccountBalance *pb_ptr = nullptr;
    AccountBalance tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::AccountBalance::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AccountBalance *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, account_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, balance, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, rebate, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, direct_rebate, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, pre_rebate, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, contract_rebate, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, credit_balance, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, extended_balance, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, balance_enough, -1)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<AccountBalance>(tmp);
  }

  uint64_t key_id() {return account_id;}

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.account_id();
  }

};

}  // namespace data_gate
}  // namespace tables

