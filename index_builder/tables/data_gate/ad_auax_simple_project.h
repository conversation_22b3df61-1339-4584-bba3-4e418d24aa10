#pragma once

#include <stdint.h>

#include <string>
#include <utility>

#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/index_builder/tables/table_util.h"

namespace tables {
namespace data_gate {

struct AdAuaxSimpleProject {
  enum __capicity {
    kCapicity = 1 << 19,
    kDataVersion = 2
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;

  DEFINE_common_of_container(AdAuaxSimpleProject, kuaishou::ad::tables::AdAuaxSimpleProject);


  static AdAuaxSimpleProject gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdAuaxSimpleProject *pb_ptr = nullptr;
    AdAuaxSimpleProject tmp = create();
    if (pb->GetDescriptor()
        == kuaishou::ad::tables::AdAuaxSimpleProject::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdAuaxSimpleProject *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0);

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<AdAuaxSimpleProject>(tmp);
  }

  uint64_t key_id() { return id; }

  bool IsValid() { return id > 0; }

  static int64_t key_id_of(const ProtoType &msg) { return msg.id(); }
};

}  // namespace data_gate
}  // namespace tables

