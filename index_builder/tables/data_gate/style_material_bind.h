#pragma once

#include <string>
#include <utility>
#include "serving_base/jansson/json.h"
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct StyleMaterialBind {
  enum __capicity {
    kCapicity = 1 << 23,
    kDataVersion = 4
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int64_t style_material_id;
  int32_t bind_level;
  int64_t creative_id;
  int64_t unit_id;
  int64_t campaign_id;
  int64_t account_id;
  kuaishou::ad::AdEnum::PutStatus put_status;

  DEFINE_common_of_container(StyleMaterialBind, kuaishou::ad::tables::AdStyleMaterialBind);

  static StyleMaterialBind gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::AdStyleMaterialBind *pb_ptr = nullptr;
    StyleMaterialBind tmp = create();
    if (pb->GetDescriptor() ==
        kuaishou::ad::tables::AdStyleMaterialBind::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::AdStyleMaterialBind *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, style_material_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, bind_level, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, creative_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, unit_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, campaign_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, account_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, put_status, kuaishou::ad::AdEnum_PutStatus_UNKNOWN_PUT_STATUS)

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }

    return std::forward<StyleMaterialBind>(tmp);
  }

  uint64_t key_id() {return id;}

  bool IsValid() const {
    return put_status == 1;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }
};

}  // namespace data_gate
}  // namespace tables
