#pragma once

#include <stdint.h>
#include <string>
#include <utility>

#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace tables {
namespace data_gate {

struct WTProduct {
  enum __capicity {
    kCapicity = 1 << 20,
    kDataVersion = 8
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t product_id;
  int32_t product_tag;
  int32_t has_product_hosting_project_ad;
  int64_t category_level_1_id;
  int64_t category_level_2_id;
  int64_t category_level_3_id;
  int64_t category_level_4_id;
  int64_t spu_id_v1;
  int64_t payment_per_order;
  int64_t item_price;
  int64_t event_order_paid_cnt;
  double rl_pay_amount_per_order;
  int64_t basic_price;
  int64_t seller_id;

  DEFINE_common_of_container(WTProduct, kuaishou::ad::tables::WTProduct);

  static WTProduct gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::WTProduct *pb_ptr = nullptr;
    WTProduct tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::WTProduct::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::WTProduct*>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, product_id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, category_level_1_id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, category_level_2_id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, category_level_3_id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, product_tag, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, category_level_4_id, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, spu_id_v1, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, has_product_hosting_project_ad, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, payment_per_order, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, item_price, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, event_order_paid_cnt, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, rl_pay_amount_per_order, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, basic_price, 0);
      SET_FIELD_BY_PB(pb_ptr, tmp, seller_id, 0);
      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }
    return std::forward<WTProduct>(tmp);
  }

  uint64_t key_id() {return product_id;}

  bool IsValid() {
    return product_id > 0;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.product_id();
  }
};

}  // namespace data_gate
}  // namespace tables

