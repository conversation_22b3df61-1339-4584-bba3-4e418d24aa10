#pragma once

#include <stdint.h>
#include <string>
#include <vector>
#include <utility>
#include "serving_base/jansson/json.h"
#include "google/protobuf/util/json_util.h"
#include "teams/ad/index_builder/tables/table_util.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/container.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_base/src/better_enums/better_enums.h"

namespace tables {
namespace data_gate {
BETTER_ENUM(TargetGender, uint8_t, Male = 1, Female, Both)

struct Target {
  enum __capicity {
    kCapicity = 1 << 22,
    kDataVersion = 3
  };

  // 修改 hash_func 必须修改 kDataVersion, 不然加载 hash 文件会 core
  static size_t hash_func(uint64_t id) {
    static ks::ad_base::FNVHash hasher;
    return hasher(id);
  }

  int64_t id;
  int64_t target_engine_id;
  int32_t business_interest_type;
  int32_t package_name_size;

  // ad_server 原始定义中有覆盖问题？
  double min_ios_version;
  double max_ios_version;
  double min_android_version;
  double max_android_version;




  DEFINE_common_of_container(Target, kuaishou::ad::tables::Target);

  static Target gen_from_proto(const google::protobuf::Message *pb, bool is_attach) {
    const kuaishou::ad::tables::Target *pb_ptr = nullptr;
    Target tmp = create();
    if (pb->GetDescriptor() == kuaishou::ad::tables::Target::default_instance().GetDescriptor()) {
      pb_ptr = dynamic_cast<const kuaishou::ad::tables::Target *>(pb);
      SET_FIELD_BY_PB(pb_ptr, tmp, id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, target_engine_id, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, business_interest_type, 0)
      SET_FIELD_BY_PB(pb_ptr, tmp, package_name_size, 0)

      tmp.ParsePlatformLimit(pb_ptr->platform());

      if (is_attach && tmp.key_id()) {
        std::string str = pb->SerializeAsString();
        tmp.attach.data = ::ks::ad_base::shared_mem::GenAttachData(str.c_str(), str.length());
      }
    }

    return std::forward<Target>(tmp);
  }
  // 对 proto 进行一些解析修改
  static void ProcessProto(ProtoType *pb_ptr) {
    // ParseExtendFields(pb_ptr);
  }

  void ParsePlatformLimit(const std::string& platform) {
    if (platform.empty()) {
      return;
    }

    base::Json json(base::StringToJson(platform));
    for (auto& item : json.objects()) {
      if (item.first == "ios") {
        min_ios_version = item.second->GetFloat("min", 0.0);
        max_ios_version = item.second->GetFloat("max", 0.0);
      } else if (item.first == "android") {
        min_android_version = item.second->GetFloat("min", 0.0);
        max_android_version = item.second->GetFloat("max", 0.0);
      }
    }
  }

  static void ParseExtendFields(kuaishou::ad::tables::Target* target) {
    if (!target) {
      return;
    }

#define SET_JSON_ITEM(field, type) \
  if (!target->field().empty()) { \
    base::Json json_item(base::StringToJson(target->field())); \
    if (json_item.Is##type()) { \
      json_obj.set(#field, json_item); \
    } \
    std::string field_name(#field); \
    if (field_name != "age" && field_name != "platform" && field_name != "intelli_extend") { \
      target->clear_##field(); \
    } \
  }

    base::JsonObject json_obj;

    // age，platform。intelli_extend 这几个字段算法侧离线依赖，暂不清理
    SET_JSON_ITEM(age, Array);
    SET_JSON_ITEM(interest, Array);
    SET_JSON_ITEM(network, Array);
    SET_JSON_ITEM(platform, Object);
    SET_JSON_ITEM(region, Array);
    SET_JSON_ITEM(audience, Array);
    SET_JSON_ITEM(paid_audience, Array);
    SET_JSON_ITEM(population, Array);
    SET_JSON_ITEM(exclude_population, Array);
    SET_JSON_ITEM(device_price, Array);
    SET_JSON_ITEM(device_brand, Array);
    SET_JSON_ITEM(interest_video, Array);
    SET_JSON_ITEM(package_name, Array);
    SET_JSON_ITEM(fans_star, Array);
    SET_JSON_ITEM(business_interest, Array);
    SET_JSON_ITEM(intelli_extend, Object);
    SET_JSON_ITEM(social_star_label, Array);
    SET_JSON_ITEM(behavior_interest_keyword, Array);
    SET_JSON_ITEM(region_ids, Array);
    SET_JSON_ITEM(district_ids, Array);
    SET_JSON_ITEM(language, Array);
    SET_JSON_ITEM(page, Array);
    SET_JSON_ITEM(seed_population, Array);

    ParseStringField(target, &json_obj);
    const std::string& json_str = json_obj.ToString();
    if (!json_str.empty()) {
      google::protobuf::util::JsonParseOptions options;
      options.ignore_unknown_fields = true;
      auto status = google::protobuf::util::JsonStringToMessage(json_str,
          target->mutable_extend_fields(), options);
      if (!status.ok()) {
        ks::infra::PerfUtil::CountLogStash(1, "ad.index_builder", "parse_extend_fields_failed", "target");
        LOG(ERROR) << "JsonStringToMessage failed. error: " << status.error_message()
                   << ", json_str: " << json_str;
        target->clear_extend_fields();
      }
    }
  }

  static void ParseStringField(kuaishou::ad::tables::Target* target, base::JsonObject* extend_json) {
    if (target == nullptr || extend_json == nullptr) {
      return;
    }
      // 解析 gender
    if (!target->gender().empty()) {
      std::vector<int64_t> vec;
      if (target->gender() == "M") {
        vec.push_back(+TargetGender::Male);
      } else if (target->gender() == "F") {
        vec.push_back(+TargetGender::Female);
      }
      if (vec.size() == 1 && vec[0] == +TargetGender::Male) {
        extend_json->set("gender", +TargetGender::Male);
      } else if (vec.size() == 1 && vec[0] == +TargetGender::Female) {
        extend_json->set("gender", +TargetGender::Female);
      } else {
        extend_json->set("gender", +TargetGender::Both);
      }
    }
    return;
  }
  uint64_t key_id() {return id;}

  bool IsValid() {
    return id > 0;
  }

  static int64_t key_id_of(const ProtoType &msg) {
    return msg.id();
  }
};

}  // namespace data_gate
}  // namespace tables

