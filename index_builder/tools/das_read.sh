#!/bin/bash

if [ -f /home/<USER>/software/bashrc ]; then
  source /home/<USER>/software/bashrc
fi

export CLASSPATH=$(hadoop classpath --glob)
export LD_LIBRARY_PATH=/serving/mysql/lib/:$(dirname $(find  $(dirname $(readlink -f $(which javac)))/../ -type f -name libjvm.so
)):$(dirname $(find  $(dirname $(readlink -f $(which hadoop)))/../ -type f -name libhdfs.a))

# /home/<USER>/benchmark_test/base/2021-02-25_160000/dump_info 文件
# 获取文件名及类型 进行解析
#{
#    "fileName": "ad_dsp_campaign-0~0-43-0",
#    "protoName": "kuaishou.ad.tables.Campaign",
#    "recordNum": 7180,
#    "tableName": "ad_dsp_campaign"
#}
# log在log/UNKNOWN.INFO

./check_das_message --hdfs_url=$hdfs_path/$fileName --pb_type=$proteName --flagfile=../config/server_static.flags