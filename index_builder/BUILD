cc_library(
  name = 'proto_common',
  srsc = [],
  deps = [
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_tables_all__proto",
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_index2hive__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_dpa_enums__proto',
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_inc__proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_tables_meta__proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_pid_tag_enum__proto",
  ]
)

cc_library(
  name = 'p2p_task',
  srcs = ['utils/cache_loader/*.cc'],
  deps = [
      '//third_party/glog/BUILD:glog',
      "//teams/ad/data_push/BUILD:p2p_data_push_client_lib",
      "//teams/ad/ad_base/src/file/BUILD:utility",
      "//teams/ad/ad_base/src/timer_task/BUILD:bg_task",
      "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
      "//third_party/nlohmann_json/BUILD:nlohmann_json",
  ],
  cppflags = [
      '-Ithird_party/boost',
      '-Ithird_party/double-conversion',
      "-std=gnu++17",
      "-Ithird_party/nlohmann_json/include",
  ]
)

cc_library(
  name = 'ad_instance_proto_helpers',
  srcs = ['utils/ad_instance_proto_helpers.cc'],
  hdrs = ['utils/ad_instance_proto_helpers.h'],
  deps = [
      ":builder_proto",
      ":proto_common",
      '//teams/ad/ad_index/BUILD:ad_index_helper',
      '//third_party/abseil/BUILD:abseil',
      '//third_party/glog/BUILD:glog',
  ],
  cppflags = [
      '-Ithird_party/boost',
      '-Ithird_party/double-conversion',
      "-std=gnu++17",
  ]
)

cc_test(
  name = 'ad_instance_proto_helpers_test',
srcs = ['test/ad_instance_proto_helpers_test.cc'],
  deps = [
    ':ad_instance_proto_helpers',
    '//base/testing/BUILD:test_main',
  ]
)
cc_test(
  name = 'builder_parser_test',
srcs = ['test/builder_parser_test.cc'],
  deps = [
    '//serving_base/jansson/BUILD:json',
    '//base/testing/BUILD:test_main',
  ]
)
cc_binary(name = 'check_message',
srcs = [
    'utils/check_message.cc'
  ],
  deps = [
    ':ad_instance_proto_helpers',
    ':message_stream',
    '//third_party/abseil/BUILD:abseil',
    '//third_party/gflags/BUILD:gflags',
    '//third_party/glog/BUILD:glog',
    "//teams/ad/ad_table_lite/BUILD:ad_table_lite",
  ],
  cppflags = [
    '-Ithird_party/boost',
    '-Ithird_party/double-conversion',
  ]
)

cc_binary(name = 'check_das_message',
srcs = [
    'utils/check_das_message.cc'
  ],
  deps = [
    ':message_stream',
    '//third_party/abseil/BUILD:abseil',
    '//third_party/gflags/BUILD:gflags',
    '//third_party/glog/BUILD:glog',
  ],
  cppflags = [
    '-Ithird_party/boost',
    '-Ithird_party/double-conversion',
  ]
)

proto_library(
  name = 'builder_proto',
  srcs = [
    'utils/builder.proto',
  ],
  deps = [
    ":proto_common",
    ":multiform_kconf",
    ':builder_config_proto',
  ],
)

cc_library(
  name = 'builder_common',
  srcs = [
  ],
  deps = [
    ':builder_proto',
    ':multiform_kconf',
    ':multiform_filter_proto_tools',
    '//serving_base/jansson/BUILD:json',
    '//teams/ad/ad_base/src/kconf/BUILD:kconf_node',
    '//teams/ad/ad_base/src/allocator/BUILD:shared_mem',
    '//teams/ad/ad_base/src/container/BUILD:shared_cuckoo',
    '//teams/ad/ad_base/src/common/BUILD:os_version',
  ],
)

proto_library(
  name = 'kafka2hive_proto',
  srcs = [
    'utils/kafka2hive/kafka2hive.proto',
  ],
  deps = [
    "//teams/ad/index_message_proxy/BUILD:proxy_proto",
    '//teams/ad/engine_base/BUILD:is_valid_table',
  ]
)

cc_library(
  name = 'kafka2hive',
  srcs = [
    'utils/kafka2hive/kafka2hive_manager.cc',
  ],
  deps = [
    ':kafka2hive_proto',
    ":proto_common",
    '//teams/ad/index_message_proxy/BUILD:kafka_tools',
  ],
)




proto_library(
  name = 'creative_adapter_proto',
  srcs = [
    'utils/extra_adapter/kconf.proto',
  ],
  deps = [
    '//teams/ad/ad_creative_server/BUILD:ad_creative_service_proto',
    ":proto_common",
    '//teams/ad/engine_base/BUILD:valid_kconf_proto',
  ],
)

cc_library(
  name = 'creative_adapter',
  srcs = [
    'utils/extra_adapter/*.cc',
  ],
  deps=[
    ":creative_adapter_proto",
    '//third_party/abseil/BUILD:abseil',
    "//infra/redis_proxy_client/BUILD:redis_client",
    ":proto_common",
    "//teams/ad/index_adapter/BUILD:stream_map",
    "//teams/ad/index_adapter/BUILD:hdfs_util",
    "//teams/ad/index_adapter/BUILD:perf_manager",
    "//teams/ad/ad_label_server/BUILD:common",
    "//base/thread/BUILD:thread",
    "//teams/ad/data_build/BUILD:build_job_config_proto",
  ],
)

proto_library(
  name = 'multiform_kconf',
  srcs = [
    'multiform_filter/multiform_kconf.proto',
  ],
)

cc_library(
  name = 'multiform_kconf_lib',
  srcs = [
    'multiform_filter/multiform_kconf.cc',
  ],
  deps = [
    ':multiform_kconf',
    "//teams/ad/ad_base/src/kconf/BUILD:kconf_node",
  ],
)

cc_library(
  name='parser_common',
  srcs=[
    'utils/parser/*.cc',
  ],
  deps=[
    '//teams/ad/ad_base/src/kconf/BUILD:kconf_node',
    ":proto_common",
    '//teams/ad/ad_index/BUILD:ad_index_helper',
    "//teams/ad/ad_index/BUILD:ad_index_api",
    "//base/strings/BUILD:strings",
    '//third_party/abseil/BUILD:abseil',
    '//ks/base/abtest/BUILD:common',
    '//teams/ad/data_build/BUILD:table_row',
  ],
  link_all_symbols = True,
)

cc_library(
  name='multiform_filter',
  srcs=[
    'multiform_filter/*.cc',
  ],
  excludes = [
    'multiform_filter/proto_tools.cc',
  ],
  deps=[
    ":parser_common",
    ":multiform_kconf",
    ":p2p_task",
    ":ad_instance_proto_helpers",
    ":multiform_filter_proto_tools",
    ":proto_common",
    '//teams/ad/ad_index/BUILD:ad_index_helper',
    '//third_party/glog/BUILD:glog',
    '//teams/ad/dpa_server/BUILD:kconf_proto',
    '//teams/ad/dpa_server/BUILD:index_kconf_proto',
    '//serving_base/jansson/BUILD:json',
    '//teams/ad/engine_base/BUILD:material_feature_type',
  ],
  cppflags = [
    "-std=gnu++17",
  ]
)

cc_library(
  name='multiform_filter_proto_tools',
  srcs = [
    'multiform_filter/proto_tools.cc',
  ],
  deps=[
    ":proto_common",
    '//third_party/glog/BUILD:glog',
  ]
)

proto_library(
  name='admit_filter_proto',
  srcs=[
    'admit_filters/admit_filter.proto',
  ]
)

cc_library(
  name = 'admit_filters',
  srcs = [
    'admit_filters/*.cc',
  ],
  hdrs = ["admit_filters/*.h"],
  deps = [
    ":proto_common",
    "//teams/ad/ad_index/BUILD:ad_index_helper",
    "//teams/ad/index_builder/BUILD:multiform_filter",
    "//teams/ad/index_builder/BUILD:admit_filter_proto",
    "//teams/ad/data_build/BUILD:table_row",
  ]
)

cc_library(
  name = 'message_stream',
  srcs = [
    'utils/message_stream.cc',
    'utils/patten_parser.cc',
  ],
  hdrs = ['utils/message_stream.h'],
  deps = [
    ':builder_common',
    ':multiform_filter',
    "//teams/ad/index_adapter/BUILD:hdfs_util",
    '//teams/ad/dpa_server/BUILD:kconf_proto',
    '//base/file/BUILD:file',
    '//serving_base/hdfs_read/BUILD:hdfs_read',
    '//infra/perfutil/BUILD:perfutil',
    '//serving_base/mysql_util/BUILD:mysql_util',
    '//third_party/abseil/BUILD:abseil',
    '//third_party/gflags/BUILD:gflags',
    '//third_party/glog/BUILD:glog',
    '//teams/ad/ad_base/src/kconf/BUILD:kconf_node',
    ":proto_common",
    '//teams/ad/ad_base/src/pb_helper/BUILD:pb_helper',
    '//teams/ad/ad_base/src/kconf/BUILD:kconf_node',
  ],
  cppflags = [
    '-Ithird_party/boost',
    '-Ithird_party/double-conversion',
  ]
)

cc_library(
  name='md5_helper',
  srcs=[
    'utils/md5_helper.cc',
  ],
  deps=[
  ],
  cppflags = [
    '-Ithird_party/boost',
  ],
)

cc_binary(
    name = 'diff_hdfs_protos_main',
    srcs = [
        'scripts/diff_hdfs_protos_main.cc',
    ],
    deps = [
        ':ad_instance_proto_helpers',
        '//base/file/BUILD:file',
        '//serving_base/hdfs_read/BUILD:hdfs_read',
        '//teams/ad/ad_index/BUILD:ad_index_helper',
        ":proto_common",
        '//third_party/abseil/BUILD:abseil',
        '//third_party/gflags/BUILD:gflags',
        '//third_party/glog/BUILD:glog',
    ],
    cppflags = [
        '-Ithird_party/boost',
        '-Ithird_party/double-conversion',
    ]
)

# base lib
proto_library(
  name = 'index_read_proto',
  srcs = [
    'base/index_read/index_read_proto.proto',
  ],
)


cc_library(
  name='index_read_lib',
  srcs=[
    'base/index_read/*.cc',
  ],
  deps=[
    ':index_read_proto',
    ":multiform_filter_proto_tools",
    '//teams/ad/ad_base/src/kconf/BUILD:kconf_node',
    '//teams/ad/index_adapter/BUILD:kconf_proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_dpa_enums__proto',
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_tables_meta__proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_inc__proto",
    '//third_party/glog/BUILD:glog',
    '//serving_base/hdfs_read/BUILD:hdfs_read',
    '//base/file/BUILD:file',
  ],
)

proto_library(
  name = 'builder_config_proto',
  srcs = [
    'utils/adapter_config.proto',
    'utils/index_config.proto',
    "utils/common.proto",
  ],
  deps = [
    ":proto_common",
    ":multiform_kconf",
    ":admit_filter_proto",
  ],
)

cc_library(
  name = 'unified_config_manager',
  srcs = [
    'utils/unified_config_manager/*.cc'
  ],
  deps = [
    ':builder_config_proto',
    "//teams/ad/index_adapter/BUILD:kconf_proto",
    "//teams/ad/index_message_proxy/BUILD:proxy_proto",
    "//teams/ad/ad_base/src/kconf/BUILD:kconf_node",
  ]
)
