#pragma once

#include <string>
#include <map>
#include <memory>
#include "kconf/kconf.h"
#include "dragon/src/processor/base/common_reco_base_mixer.h"

DEFINE_bool(enable_mock_switch, false, "enable_mock_switch");
namespace ks {
namespace front_server {

template <class K>
using KeyParser = std::function<bool(const std::string&, K* key)>;
class SwitchForce {
 public:
  SwitchForce(std::string const& key,
                std::shared_ptr<std::map<std::string, bool>> default_val,
                KeyParser<std::string> key_parser) :
                key_(key),
                default_val_(default_val),
                key_parser_(key_parser) {
    // 先获取 KsConfig 对象, 获取配置依赖这个对象
    ks_config_ =
      ks::infra::KConf().GetMap<std::string, bool>(key_, default_val_, key_parser_);
  }

  std::shared_ptr<std::map<std::string, bool>> GetMapValue() {
    if (ks_config_ == nullptr) {
      return default_val_;
    }
    return ks_config_->Get();
  }
 private:
  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<std::map<std::string, bool>>>> ks_config_;
  std::shared_ptr<std::map<std::string, bool>> default_val_;
  std::string key_;
  KeyParser<std::string> key_parser_;
};

class CommonSwitchMockMixer : public ::ks::platform::CommonRecoBaseMixer {
 public:
  CommonSwitchMockMixer() {}

  void Mix(ks::platform::AddibleRecoContextInterface* context) override;

 private:
  bool InitProcessor() override {
    if (!FLAGS_enable_mock_switch) {
      return true;
    }

    // kconf 解析
    std::string key = "adqa.dragon.forceMap";
    auto default_val = std::make_shared<std::map<std::string, bool>>();
    auto parser = [] (const std::string& key, std::string* val) -> bool {
      *val = key;
      return true;
    };
    KeyParser<std::string> key_parser = parser;

    map_instance_
      = std::make_shared<SwitchForce>(key, default_val, key_parser);
    return true;
  }

 private:
  std::shared_ptr<SwitchForce> map_instance_{nullptr};

 private:
  DISALLOW_COPY_AND_ASSIGN(CommonSwitchMockMixer);
};

}  // namespace front_server
}  // namespace ks

