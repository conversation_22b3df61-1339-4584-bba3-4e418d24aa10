#pragma once
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <memory>

#include "base/common/basic_types.h"
#include "serving_base/utility/system_util.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_info.pb.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/front_server_universe/engine/server/init.h"
#include "dragon/src/processor/base/common_reco_base_mixer.h"

namespace ks {
namespace front_server {

class AdFrontProcessPostMixer : public ::ks::platform::CommonRecoBaseMixer {
 public:
  AdFrontProcessPostMixer();
  ~AdFrontProcessPostMixer() = default;
  // dragon 接口
  void Mix(ks::platform::AddibleRecoContextInterface* context) override;

 private:
  bool InitProcessor() override;

  // 后处理
  void Post(const FrontServerResponse& front_server_response, const ContextData& context_data);

  void SendLogByAdPack(ContextData* context);
  std::string GetAdPackServiceName(ContextData* context);

  // 记录日志
  void LogInfo(const kuaishou::ad::FrontServerRequest& front_server_request,
               const kuaishou::ad::FrontServerResponse& front_server_response,
               ContextData* context_data) const;
  void LogDryrunInfo(const kuaishou::ad::FrontServerRequest& front_server_request,
               const kuaishou::ad::FrontServerResponse& front_server_response,
               ContextData* context_data) const;

  // 给 ad_pack 透传前，清理并统计一下数据大小
  void CleanTransparentPackInfo(const ContextData& context_data,
                                kuaishou::ad::TransparentPackInfo* trans_info);
  void BuildAdPackRequest(ContextData* context);
  void RequestAdPackService(ContextData& context_data,    // NOLINT
                            kuaishou::ad::FrontServerResponse* front_server_response);
  void SendComputilityData(ContextData* context_data);
  void SendUniverseQcpxDeliveryLogInfo(ContextData* context_data);

  void StatisticTransparentPackInfoSize(const ContextData& context_data,
                                        const kuaishou::ad::TransparentPackInfo& trans_info);

  const std::string host_{serving_base::GetHostName()};

  DISALLOW_COPY_AND_ASSIGN(AdFrontProcessPostMixer);
};  // class AdFrontProcessPostMixer

}  // namespace front_server
}  // namespace ks
