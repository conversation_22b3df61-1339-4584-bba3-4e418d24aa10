syntax  = "proto3";
option cc_enable_arenas = true;
package ks.front_server;

enum FormulaType {
  UnknownFormulaType = 0;
  NormalPriceRatioFormula = 1;
  NativePriceRatioFormula = 2;
  CalcNormalPriceRatioFormula = 3;
  CalcNativePriceRatioFormula = 4;
  CalcNormalOriginPriceFormula = 5;
  CalcNativeOriginPriceFormula = 6;
  CalcNormalFinalPriceFormula = 7;
  CalcNativeFinalPriceFormula = 8;
  CalcNormalOriginMixBenefitFormula = 9;
  CalcNativeOriginMixBenefitFormula = 10;
  MixBenefitRatioFormula = 11;
  MerchantPriceRatioFormula = 12;
  CalcMerchantPriceRatioFormula = 13;
  CalcMerchantOriginPriceFormula = 14;
  CalcMerchantFinalPriceFormula = 15;
  CalcMerchantOriginMixBenefitFormula = 16;
}

// end
