#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <utility>
#include "dragon/src/processor/base/common_reco_base_mixer.h"

namespace ks {
namespace front_server {

class FormulaDotMixer : public ::ks::platform::CommonRecoBaseMixer {
 public:
  FormulaDotMixer() {}

  void Mix(ks::platform::AddibleRecoContextInterface* context) override;

 private:
  struct DotInfo {
    std::string item_attr_name;
    std::string dot_name;
    std::string describe;
    std::string dragon_dot_info;
    std::string formula;
    int coefficient;
    ks::platform::ItemAttr* item_attr;
  };

  bool InitProcessor() override {
    item_table_name_ = GetTableName();
    auto dot_info_config = config()->Get("dot_info");
    if (!dot_info_config || !dot_info_config->IsArray()) {
      LOG(ERROR) << "FormulaDotMixer"
                 << " init failed! Missing \"dot_info\" config"
                 << " or it is not an array.";
      return false;
    }

    dot_info_list_.clear();
    for (const auto *dot_json : dot_info_config->array()) {
      if (!dot_json->IsObject()) {
        LOG(ERROR) << "FormulaDotMixer"
                   << " init failed! Item of mappings should be a dict!"
                   << " Value found: " << dot_json->ToString();
        return false;
      }

      DotInfo dot_info;
      dot_info.item_attr_name = dot_json->GetString("item_attr", "");
      dot_info.dot_name = dot_json->GetString("dot_name", "");
      dot_info.describe = dot_json->GetString("describe", "");
      dot_info.formula = dot_json->GetString("formula", "");
      dot_info.coefficient = dot_json->GetInt("coefficient", 1);
      dot_info.dragon_dot_info = absl::StrCat(dot_info.dot_name +
             dot_info.describe + "(*" + base::Int64ToString(dot_info.coefficient) + ")");
      dot_info_list_.push_back(std::move(dot_info));
    }
    /*
    module_name_ = absl::StrCat("module::",
         ::ks::platform::CommonRecoBaseMixer::GetName().substr(0,
         ::ks::platform::CommonRecoBaseMixer::GetName().find_last_of("::") - 1));
    */
    return true;
  }

  bool Init(ks::platform::AddibleRecoContextInterface* context);
  void Dot(ks::platform::AddibleRecoContextInterface* context);

 private:
  std::string item_table_name_{""};
  std::string module_name_{""};
  std::vector<DotInfo> dot_info_list_;
  // table
  ks::platform::AttrTable* input_table_{nullptr};
};
}  // namespace front_server
}  // namespace ks
