#include <vector>
#include "perfutil/perfutil.h"
#include "teams/ad/front_server_universe/processor/common_switch_mock_mixer.h"
#include "teams/ad/ad_base/src/perf/perf.h"

namespace ks {
namespace ad_base {
DECLARE_string(perf_name_space);
}  // namespace ad_base
}  // namespace ks

namespace ks {
namespace front_server {

void CommonSwitchMockMixer::Mix(ks::platform::AddibleRecoContextInterface* context) {
  // gflags 开关未打开，直接跳过算子执行
  if (!FLAGS_enable_mock_switch) {
    return;
  }
  if (nullptr == map_instance_) {
    return;
  }
  // 获取配置
  auto map_vals = map_instance_->GetMapValue();

  if (!map_vals) {
    return;
  }
  if (map_vals->empty()) {
    return;
  }
  // 参数 mock 核心逻辑
  for (const auto &val : *map_vals) {
    auto common_attr = context->GetCommonAttrAccessor(val.first);
    common_attr->SetIntValue(val.second ? 1 : 0);
    common_attr->MarkReadOnly();
    ks::ad_base::AdPerf::IntervalLogStash(val.second ? 1 : 0, ad_base::FLAGS_perf_name_space, "dragon_gray",
                                               val.first, "", "", "", "", "");
    ks::ad_base::AdPerf::IntervalLogStash(common_attr->overwrite_counter, ad_base::FLAGS_perf_name_space,
                                          "switch_check", val.first, "", "", "", "", "");
  }
  return;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonSwitchMockMixer,
                 CommonSwitchMockMixer)

}  // namespace front_server
}  // namespace ks

