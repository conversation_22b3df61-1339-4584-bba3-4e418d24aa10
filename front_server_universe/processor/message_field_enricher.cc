#include "teams/ad/front_server_universe/processor/message_field_enricher.h"

#include <algorithm>
#include <iterator>
#include <utility>

namespace ks::front_server {
void FillMessageFieldEnricher::Enrich(platform::MutableRecoContextInterface *context,
                                      platform::RecoResultConstIter begin,
                                      platform::RecoResultConstIter end) {
  if (is_common_attr_) {
    bool attrs_not_empty = false;
    for (auto& conf : attr_configs_) {
      conf.src_column_value = context->GetCommonAttr(conf.src_column_name);
      if (conf.src_column_value != nullptr) {
        attrs_not_empty = true;
      }
    }
    input_message_attr_ = context->GetCommonAttr(input_message_column_);
    if (input_message_attr_ == nullptr || !attrs_not_empty) {
      LOG_EVERY_N(INFO, 100) << "fill_message_attr cancelled: get attr fail";
      return;
    }
    auto* ptr = context->GetPtrCommonAttr<google::protobuf::Message>(input_message_column_);
    if (!ptr) {
      LOG_EVERY_N(WARNING, 100) << "message is null.";
      return;
    }
    auto* msg = const_cast<google::protobuf::Message *>(ptr);
    for (auto& conf : attr_configs_) {
      FillMessageField(msg, conf, conf.field_path);
    }
  } else {
    auto* table = context->GetOrInsertDataTable(GetTableName());
    if (table == nullptr || table->GetCommonRecoResults().size() == 0) {
      LOG_EVERY_N(INFO, 100) << "fill_message_attr cancelled: empty items";
      return;
    }
    bool attrs_not_empty = false;
    for (auto& conf : attr_configs_) {
      conf.src_column_value = table->GetAttr(conf.src_column_name);
      if (conf.src_column_value != nullptr) {
        attrs_not_empty = true;
      }
    }
    input_message_attr_ = table->GetAttr(input_message_column_);
    if (input_message_attr_ == nullptr || !attrs_not_empty) {
      LOG_EVERY_N(INFO, 100) << "fill_message_attr cancelled: get attr fail";
      return;
    }
    std::for_each(begin, end, [&](const platform::CommonRecoResult& item) {
      auto* ptr = item.GetPtrAttr<google::protobuf::Message>(input_message_attr_);
      if (!ptr) {
        LOG_EVERY_N(WARNING, 100) << "message is null.";
        return;
      }
      auto* msg = const_cast<google::protobuf::Message *>(ptr);
      for (const auto& conf : attr_configs_) {
        if (conf.src_column_value == nullptr || conf.src_column_name.empty() || conf.field_path.empty()) {
          continue;
        }
        FillMessageField(msg, conf, conf.field_path, item.GetAttrIndex());
      }
    });
  }
}

#define FILL_INT_FIELD(cpp_type, origin_type)                                           \
  case cpp_type: {                                                                      \
    if (auto int_val = conf.src_column_value->GetIntValue(idx)) {                       \
      ref->Set##origin_type(msg, f_des, *int_val);                                      \
      fill_success = true;                                                              \
    }                                                                                   \
  } break;

#define FILL_REPEATED_INT_FIELD(cpp_type, origin_type)                                  \
  case cpp_type: {                                                                      \
    if (conf.append) {                                                                  \
      if (auto int_val = conf.src_column_value->GetIntValue(idx)) {                     \
        ref->Add##origin_type(msg, f_des, *int_val);                                    \
        fill_success = true;                                                            \
      }                                                                                 \
    } else if (auto int_list_val = conf.src_column_value->GetIntListValue(idx)) {       \
      for (int64 int_val : *int_list_val) {                                             \
        ref->Add##origin_type(msg, f_des, int_val);                                     \
        fill_success = true;                                                            \
      }                                                                                 \
    }                                                                                   \
  } break;

#define FILL_STRING_FIELD(cpp_type, origin_type)                                        \
  case cpp_type: {                                                                      \
    if (auto str_val = conf.src_column_value->GetStringValue(idx)) {                    \
      ref->Set##origin_type(msg, f_des, std::string(*str_val));                         \
      fill_success = true;                                                              \
    }                                                                                   \
  } break;

#define FILL_REPEATED_STRING_FIELD(cpp_type, origin_type)                               \
  case cpp_type: {                                                                      \
    if (conf.append) {                                                                  \
      if (auto str_val = conf.src_column_value->GetStringValue(idx)) {                  \
        ref->Add##origin_type(msg, f_des, std::string(*str_val));                       \
        fill_success = true;                                                            \
      }                                                                                 \
    } else if (auto str_list_val = conf.src_column_value->GetStringListValue(idx)) {    \
      for (auto str_val : *str_list_val) {                                              \
        ref->Add##origin_type(msg, f_des, std::string(str_val));                        \
        fill_success = true;                                                            \
      }                                                                                 \
    }                                                                                   \
  } break;

void FillMessageFieldEnricher::FillMessageField(::google::protobuf::Message* msg,
                                                  const MessageFillConf& conf,
                                                  const std::string& cur_path,
                                                  int64_t idx) {
  if (cur_path == " " || msg == nullptr) {
    return;
  }
  auto* des = msg->GetDescriptor();
  auto* ref = msg->GetReflection();
  auto dot_pos = cur_path.find('.');
  if (dot_pos != std::string::npos && dot_pos != cur_path.size() - 1) {
    auto s_field = cur_path.substr(dot_pos + 1);
    auto p_field = cur_path.substr(0, dot_pos);
    auto* f_des = des->FindFieldByName(p_field);
    if (f_des == nullptr) {
      LOG(ERROR) << "msg not find name" << ", name:" << conf.src_column_name << ", msg:" << msg->ShortDebugString();  // NOLINT
      return;
    }
    if (f_des->is_repeated()) {
      auto* s_msg = ref->MutableRepeatedMessage(msg, f_des, 0);
      return FillMessageField(s_msg, conf, s_field, idx);
    } else {
      auto* s_msg = ref->MutableMessage(msg, f_des);
      return FillMessageField(s_msg, conf, s_field, idx);
    }
  } else {
    bool fill_success = false;
    const auto* f_des = des->FindFieldByName(cur_path);
    if (f_des == nullptr) {
      LOG(ERROR) << "msg not find name" << ", name:" << conf.src_column_name
                 << ", msg:" << msg->ShortDebugString();
      return;
    }
    if (f_des->is_repeated()) {
      if (!conf.append) {
        ref->ClearField(msg, f_des);
      }
      switch (f_des->cpp_type()) {
        FILL_REPEATED_INT_FIELD(FieldDescriptor::CPPTYPE_INT64, Int64);
        FILL_REPEATED_INT_FIELD(FieldDescriptor::CPPTYPE_INT32, Int32);
        FILL_REPEATED_INT_FIELD(FieldDescriptor::CPPTYPE_UINT32, UInt32);
        FILL_REPEATED_INT_FIELD(FieldDescriptor::CPPTYPE_UINT64, UInt64);
        FILL_REPEATED_INT_FIELD(FieldDescriptor::CPPTYPE_ENUM, EnumValue);
        FILL_REPEATED_INT_FIELD(FieldDescriptor::CPPTYPE_BOOL, Bool);
        FILL_REPEATED_STRING_FIELD(FieldDescriptor::CPPTYPE_STRING, String);
        default:
          LOG_EVERY_N(ERROR, 100) << "match err type" << ", full_path:" << conf.field_path
                                  << ", type:" << f_des->type_name();
          break;
      }
    } else {
      switch (f_des->cpp_type()) {
        FILL_INT_FIELD(FieldDescriptor::CPPTYPE_INT64, Int64);
        FILL_INT_FIELD(FieldDescriptor::CPPTYPE_INT32, Int32);
        FILL_INT_FIELD(FieldDescriptor::CPPTYPE_UINT32, UInt32);
        FILL_INT_FIELD(FieldDescriptor::CPPTYPE_UINT64, UInt64);
        FILL_INT_FIELD(FieldDescriptor::CPPTYPE_ENUM, EnumValue);
        FILL_INT_FIELD(FieldDescriptor::CPPTYPE_BOOL, Bool);
        FILL_STRING_FIELD(FieldDescriptor::CPPTYPE_STRING, String);
          break;
        default:
          LOG_EVERY_N(ERROR, 100) << "match err type" << ", full_path:" << conf.field_path
                                    << ", type:" << f_des->type_name();
          break;
      }
    }
    LOG_EVERY_N(INFO, 10000) << "FillMessageFieldFromItem"
                             << ", name: " << conf.src_column_name
                             << ", full_path:" << conf.field_path
                             << ", type:" << f_des->type_name()
                             << ", is_repeated: " << f_des->is_repeated()
                             << ", fill_success: " << fill_success;
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FillMessageFieldEnricher, FillMessageFieldEnricher)
}  // namespace ks::front_server
