#!/usr/bin/env python
# coding=utf-8
import os
import sys
import json
# 当前 adrank 开始迁移到 dragonfly
# 做了配置拆分, 修改配置如下：
# step1: 先改 teams/ad/front_server_universe/skydome/adfront_config.py
# step2: 在 teams/ad/front_server_universe/skydome 目录下 运行 adfront.py 生产 config.json
dirname,filename = os.path.split(os.path.abspath(__file__))
config_json_path = os.path.join(dirname, 'config.json')
with open(config_json_path) as user_file:
  file_contents = user_file.read()
config = json.loads(file_contents)


if __name__ == "__main__":
  ksn = os.getenv('KWS_SERVICE_NAME')
  if ksn == 'ad-front-server-search':
    config['grpc']['client_map']['AdBrandGrpcService'] = 'ad-brand-search'

  thread_num_map = {
      "ad-front-server-archimedes": 1500,
  }
  config['grpc']['server']['thread_num'] = thread_num_map.get(
      os.getenv('KWS_SERVICE_NAME'), config['grpc']['server']['thread_num'])
  this_kess_name = ksn
  if config['grpc']['custom_client_level'].has_key(this_kess_name):
    if len(config['grpc']['custom_client_level'][this_kess_name]) > 0:
      config['grpc']['client_level'] = config['grpc']['custom_client_level'][this_kess_name]
  config['grpc'].pop('custom_client_level')

  print json.dumps(config, indent=2, sort_keys=True)
