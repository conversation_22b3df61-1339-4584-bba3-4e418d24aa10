--log_dir=../log/
--dynamic_json_config_filename=../config/dynamic_json_config.json
--server_show_log_producer_config_key=server_show_log_producer_config
--server_show_log_producer_config_key_test=server_show_log_producer_config_test
--server_show_log_ads_producer_config_key=server_show_log_ads_producer_config
--server_show_log_ads_producer_config_key_test=server_show_log_ads_producer_config_test
--picasso_use_kess_new_client=true
--enable_debug_log=false

--region_dict_filename=../data/region_dict.dat
--infra_region_term_dict_filename=../data/adcode_region_dict.dat
--region_searcher_point_file=../data/geography.data
--ip_region_dict_file=../data/ip_region.dat

--ip_data_file=../data/ipdata
--dynamic_index_static_dict_filename=../data/static_dict.dat

--infra_region_dict_filename=../data/adcode_region.txt
--infra_location_local_geohash_file=../data/geohash5.txt
--infra_bd_dict_filename=../data/bdcode_district.txt
--infra_bd_location_local_geohash_file=../data/geohash6_bd.txt

--infra_location_is_search_redis=true
--infra_location_is_find_neighbors=true
--data_push_root_dir=../../data_push
--perf_name_space=ad.ad_front
--enable_abtest_session_context_instance_proxy=true
--enable_p2p_subscriber=true
--enable_p2p_subscriber_maaping=false
--p2p_release_kconf_node=p2p_adfront
--return_common_attr_in_table_only=true
--logging_switch_uid_mod_divisor=0
--early_return_if_timeout_kconf_key=ad.frontserver.dragonExecuteFailFast
--logging_switch_tail_number_kconf_key=ad.frontserver.dragonLogConfig
--perf_report_rate_kconf_key=ad.adRank2.dragonReportRate
--abtest_biz_name=AD_DSP
--abtest_mocker_json_file_in_ad=../config/abtest_mocker.json
--save_desired_attr_type=false
--abtest_mocker_json_file_in_ad=../config/abtest_mocker.json

--enable_kthp_process=true
--prevent_runtime_compaction=true
--defragment_time=0