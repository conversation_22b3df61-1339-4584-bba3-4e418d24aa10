#include <vector>
#include "teams/ad/front_server_universe/bg_task/universe_crowd_strategy/universe_crowd_strategy.h"
#include "base/strings/string_number_conversions.h"

namespace ks {
namespace front_server {

bool UniverseCrowdStrategy::GetUserRatio(const std::string& user_id,
    double *ks_cpm_ratio,
    double *payment_ratio,
    double *fusion_ratio) const {
  bool ret = false;
  (*ks_cpm_ratio) = 1.0;
  (*payment_ratio) = 1.0;
  (*fusion_ratio) = 1.0;
  auto data_ptr = GetData();
  if (data_ptr) {
    auto iter = data_ptr->find(user_id);
    if (iter != data_ptr->end()) {
      std::vector<std::string> info_arr = absl::StrSplit(iter->second, "_");
      if (info_arr.size() != 3) {
        return ret;
      }
      if (base::StringToDouble(info_arr[0], ks_cpm_ratio)
          && base::StringToDouble(info_arr[1], payment_ratio)
          && base::StringToDouble(info_arr[2], fusion_ratio)) {
        ret = true;
      }
    }
  }
  return ret;
}

}  // namespace front_server
}  // namespace ks

