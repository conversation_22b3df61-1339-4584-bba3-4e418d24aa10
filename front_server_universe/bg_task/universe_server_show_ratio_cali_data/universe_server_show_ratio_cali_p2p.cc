#include "teams/ad/front_server_universe/bg_task/universe_server_show_ratio_cali_data/universe_server_show_ratio_cali_p2p.h"
#include <algorithm>

namespace ks {
namespace front_server {

static const uint64_t kDimPosIdPrefix = 1ULL;
static const uint64_t kDimAppXAdStylePrefix = 3ULL;
static const uint64_t kDimMediumSubIndXAdStylePrefix = 4ULL;
static const uint64_t kDimMediumIndXAdStylePrefix = 5ULL;
static const uint64_t kDimBackUpPrefix = 0ULL;

bool UniverseServerShowRatioCaliP2p::GetServerShowCaliDataInternal(
    const absl::flat_hash_map<std::pair<uint64_t, uint64_t>, ServerShowCaliData, hash_pair>& dict,
    const std::pair<uint64_t, uint64_t>& dimension_key, ServerShowCaliData *data) {
  bool ret = false;
  auto iter = dict.find(dimension_key);
  if (iter != dict.end()) {
    data->ratio = iter->second.ratio;
    data->dimension = iter->second.dimension;
    ret = true;
  }

  return ret;
}

bool UniverseServerShowRatioCaliP2p::GetServerShowCaliData(
    const std::string &exp_tag, uint64_t pos_id, uint64_t app_id, uint64_t ad_style,
    uint64_t new_medium_industry_id, uint64_t new_medium_sub_industry_id, ServerShowCaliData *data) {
  bool ret = false;
  auto scope_ptr = GetData();
  std::pair<uint64_t, uint64_t> dim_key;
  if (scope_ptr) {
    auto iter = scope_ptr->find(ks::engine_base::GetP2pHashKey(exp_tag));
    if (iter != scope_ptr->end()) {
      // pos_id 维度
      dim_key.first = kDimPosIdPrefix + pos_id * 100;
      dim_key.second = 0;
      ret = GetServerShowCaliDataInternal(iter->second, dim_key, data);

      if (!ret) {
        // app_id x ad_style 维度
        dim_key.first = kDimAppXAdStylePrefix + (app_id * 1000 + ad_style) * 100;
        dim_key.second = 0;
        ret = GetServerShowCaliDataInternal(iter->second, dim_key, data);
      }

      if (!ret) {
        // new_medium_sub_industry_id x ad_style 维度
        dim_key.first = kDimMediumSubIndXAdStylePrefix +
                        (new_medium_sub_industry_id * 1000 + ad_style) * 100;
        dim_key.second = 0;
        ret = GetServerShowCaliDataInternal(iter->second, dim_key, data);
      }

      if (!ret) {
        // new_medium_industry_id x ad_style 维度
        dim_key.first = kDimMediumIndXAdStylePrefix +
                        (new_medium_industry_id * 1000 + ad_style) * 100;
        dim_key.second = 0;
        ret = GetServerShowCaliDataInternal(iter->second, dim_key, data);
      }

      if (!ret) {
        // back up 维度
        dim_key.first = kDimBackUpPrefix;
        dim_key.second = 0;
        ret = GetServerShowCaliDataInternal(iter->second, dim_key, data);
      }

      if (ret) {
        LOG_EVERY_N(INFO, 10000000) << "yyx debug. GetServerShowCaliData."
                                  << " exp_tag: " << exp_tag
                                  << ", pos_id: " << pos_id
                                  << ", app_id: " << app_id
                                  << ", ad_style: " << ad_style
                                  << ", new_medium_industry_id: " << new_medium_industry_id
                                  << ", new_medium_sub_industry_id: " << new_medium_sub_industry_id
                                  << ", ratio: " << data->ratio
                                  << ", dimension: " << data->dimension;
      }
    }
  }

  return ret;
}


}  // namespace front_server
}  // namespace ks
