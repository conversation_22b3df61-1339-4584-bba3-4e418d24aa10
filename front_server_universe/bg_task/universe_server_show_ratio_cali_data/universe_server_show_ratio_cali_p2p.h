#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>
#include <functional>
#include "absl/container/flat_hash_map.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader_data.pb.h"

namespace ks {
namespace front_server {

struct ServerShowCaliData {
  double ratio{0.0};
  std::string dimension;

  ServerShowCaliData() {
  }

  ServerShowCaliData(double ratio, std::string dim)
      : ratio(ratio), dimension(dim) {
  }
};

struct hash_pair {
  template <class T1, class T2>
  size_t operator()(const std::pair<T1, T2>& p) const {
    auto hash1 = std::hash<T1>{}(p.first);
    auto hash2 = std::hash<T2>{}(p.second);
    return hash1 ^ hash2;
  }
};

class UniverseServerShowRatioCaliInnerContainer :
  public absl::flat_hash_map<int64_t, absl::flat_hash_map<std::pair<uint64_t, uint64_t>, ServerShowCaliData, hash_pair>> { // NOLINT
 public:
  using value_type = std::pair<int64_t, ks::ad_base::UniverseServerShowRatioCaliPb>;
  void insert(const value_type& ele) {
    ServerShowCaliData data;
    auto &msg = ele.second;
    data.ratio = msg.ratio();
    data.dimension = msg.dimension();
    (*this)[ele.first][std::make_pair(msg.media_key(), msg.bin_index())] = data;
  }
};

class UniverseServerShowRatioCaliContainer {
 public:
  using StructData = absl::flat_hash_map<std::pair<uint64_t, uint64_t>, ServerShowCaliData, hash_pair>;
  using ProtoData = ks::ad_base::UniverseServerShowRatioCaliPb;
  using Container = UniverseServerShowRatioCaliInnerContainer;
  static bool line_to_proto(const std::string& line,
                            std::pair<int64_t, std::unique_ptr<google::protobuf::Message>>* pb) {
    if (!pb) {
      return false;
    }
    std::vector<absl::string_view> tokens = absl::StrSplit(line, ",", absl::SkipEmpty());
    // 数据格式为 exp_tag,media_key,bin_index,ratio,dimension
    if (tokens.size() != 5) {
      LOG_EVERY_N(ERROR, 10) << "UniverseServerShowRatioCaliP2p: token size = " << tokens.size();
      return true;
    }

    int64_t media_key = 0;
    int64_t bin_index = 0;
    double ratio = 1.0;

    // media_key
    if (!absl::SimpleAtoi(tokens[1], &media_key)) {
      LOG_EVERY_N(ERROR, 10) << "UniverseServerShowRatioCaliP2p: media_key: " << tokens[1]
                            << ", is invalid, not integer!";
      return false;
    }

    // bin_index
    if (!absl::SimpleAtoi(tokens[2], &bin_index)) {
      LOG_EVERY_N(ERROR, 10) << "UniverseServerShowRatioCaliP2p: bin_index: " << tokens[2]
                            << ", is invalid, not integer!";
      return false;
    }

    // ratio
    if (!absl::SimpleAtod(tokens[3], &ratio)) {
      LOG_EVERY_N(ERROR, 10) << "UniverseCpmCorrectionV2. ratio: " << tokens[3]
                            << ", is invalid, not double!";
      return false;
    }

    LOG_EVERY_N(INFO, 2000) << "UniverseServerShowRatioCaliP2p. parse line success:" << " raw_data: " << line;

    std::unique_ptr<ProtoData> msg(new ProtoData());
    msg->set_media_key(media_key);
    msg->set_bin_index(bin_index);
    msg->set_ratio(ratio);
    msg->set_dimension(std::string(tokens[4].data(), tokens[4].size()));

    pb->first = ks::engine_base::GetP2pHashKey(std::string(tokens[0].data(), tokens[0].size()));
    pb->second = std::move(msg);
    return true;
  }

  static Container::value_type pb_to_record(int64_t key, std::unique_ptr<google::protobuf::Message> pb) {
    static ProtoData pd;
    if (pb) {
      auto* msg = static_cast<ProtoData*>(pb.get());
      return std::make_pair(key, *msg);
    }
    return std::make_pair(key, pd);
  }
};

class UniverseServerShowRatioCaliP2p :
    public ad_base::P2pCacheLoaderHelper<UniverseServerShowRatioCaliContainer> {
 public:
  static UniverseServerShowRatioCaliP2p* GetInstance() {
    static UniverseServerShowRatioCaliP2p instance;
    return &instance;
  }

  bool GetServerShowCaliData(const std::string &exp_tag, uint64_t pos_id, uint64_t app_id, uint64_t ad_style,
      uint64_t new_medium_industry_id, uint64_t new_medium_sub_industry_id, ServerShowCaliData *data);

 private:
  UniverseServerShowRatioCaliP2p(): ad_base::P2pCacheLoaderHelper<UniverseServerShowRatioCaliContainer>(
      10 * 60 * 1000, "universe_server_show_ratio_cali_p2p",
      ks::engine_base::DependDataLevel::WEAK_DEPEND) {}

  bool GetServerShowCaliDataInternal(
      const absl::flat_hash_map<std::pair<uint64_t, uint64_t>, ServerShowCaliData, hash_pair>& dict,
      const std::pair<uint64_t, uint64_t>& dimension_key, ServerShowCaliData *data);
};
}  // namespace front_server
}  // namespace ks
