#include "teams/ad/front_server_universe/bg_task/universe_new_creative_data/universe_new_creative_data.h"

#include <vector>
#include "teams/ad/ad_rank/common/ad_common_constant.h"

namespace ks {
namespace front_server {

void UniverseNewCreativeData::GetPosCxr(const std::string &key, double *cxr) {
  // default_cxr 将用来与 ctr 比较
  static const double default_cxr = 1e-2;
  auto scope_ptr = GetData();
  if (scope_ptr) {
    int64_t id = engine_base::GetP2pHashKey(key);
    auto iter = scope_ptr->find(id);
    if (iter != scope_ptr->end()) {
      (*cxr) = iter->second.cxr;
      falcon::Inc("ad_server.new_creative_cold_start.pos_cxr.sucess");
      LOG_EVERY_N(INFO, 10000) << "universe_new_creative_bonus pos_cxr success, key -> " << key << ", val -> "
                               << (*cxr);
    } else {
      (*cxr) = default_cxr;
      falcon::Inc("ad_server.new_creative_cold_start.pos_cxr.miss");
      LOG_EVERY_N(INFO, 10000) << "universe_new_creative_bonus pos_cxr miss, key -> " << key << ", val -> "
                               << (*cxr);
    }
  }
}

void UniverseNewCreativeData::GetIndustryCxr(const std::string &key, double *cxr) {
  auto scope_ptr = GetData();
  if (scope_ptr) {
    int64_t id = engine_base::GetP2pHashKey(key);
    auto iter = scope_ptr->find(id);
    if (iter != scope_ptr->end()) {
      (*cxr) = iter->second.industry_cxr;
      falcon::Inc("ad_server.new_creative_cold_start.industry_cxr.sucess");
      LOG_EVERY_N(INFO, 10000) << "universe_new_creative_bonus industry_cxr success, key -> " << key
                               << ", val -> " << (*cxr);
    }
  }
}

void UniverseNewCreativeData::GetPosCpm(const std::string &key, int64_t default_cpm, int64_t *cpm) {
  // static const int64_t default_cpm = 5 * kBenifitFactor;
  auto scope_ptr = GetData();
  if (scope_ptr) {
    int64_t id = engine_base::GetP2pHashKey(key);
    auto iter = scope_ptr->find(id);
    if (iter != scope_ptr->end()) {
      *cpm = iter->second.cpm;
      falcon::Inc("ad_server.new_creative_cold_start.pos_cpm.sucess");
      LOG_EVERY_N(INFO, 10000) << "universe_new_creative_bonus pos_cpm success, key -> " << key << ", val -> "
                               << (*cpm);
    } else {
      *cpm = default_cpm;
      falcon::Inc("ad_server.new_creative_cold_start.pos_cpm.miss");
      LOG_EVERY_N(INFO, 10000) << "universe_new_creative_bonus pos_cpm miss, key -> " << key << ", val -> "
                               << (*cpm);
    }
  }
}
}  // namespace front_server
}  // namespace ks
