#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader_data.pb.h"
#include "teams/ad/data_push/utils/kconf/kconf.h"

namespace ks {
namespace front_server {

struct NewCreativePosOcpxData {
  double cxr {0};
  double industry_cxr {0};
  int64_t cpm {0};
  int64_t impression_cnt {0};
};


class UniverseNewCreativeDataContainer {
 public:
  using StructData = NewCreativePosOcpxData;
  using ProtoData = ks::ad_base::NewCreativePosOcpxDataPb;
  using Container = std::unordered_map<int64_t, StructData>;
  static bool line_to_proto(const std::string& line,
                            std::pair<int64_t, std::unique_ptr<google::protobuf::Message>>* pb) {
    if (!pb) return false;
    std::vector<absl::string_view> tokens = absl::StrSplit(line, "\t", absl::SkipEmpty());
    if (tokens.size() != 2) {
      return false;
    }
    ignore_result(absl::StripAsciiWhitespace(tokens[0]));
    ignore_result(absl::StripAsciiWhitespace(tokens[1]));

    const std::string key(tokens[0].data(), tokens[0].size());
    int64_t cpm = 0;
    int64_t raw_cxr = 0;
    int64_t industry_raw_cxr = 0;
    int64_t impression_cnt = 0;
    std::vector<absl::string_view> val_tokens = absl::StrSplit(tokens[1], ",", absl::SkipEmpty());
    if (val_tokens.size() != 3 && val_tokens.size() != 1) {
      return false;
    }
    if (val_tokens.size() == 1) {
      if (!absl::SimpleAtoi(val_tokens[0], &industry_raw_cxr)) {
        LOG_EVERY_N(WARNING, 10) << "industry cxr value: " << val_tokens[0] << ", is invalid, not int!";
        return false;
      }
    } else if (val_tokens.size() == 3) {
      if (!absl::SimpleAtoi(val_tokens[0], &cpm)) {
        LOG_EVERY_N(WARNING, 10) << " cpm value: " << val_tokens[0] << ", is invalid, not int!";
        return false;
      }
      if (!absl::SimpleAtoi(val_tokens[1], &raw_cxr)) {
        LOG_EVERY_N(WARNING, 10) << " cxr value: " << val_tokens[1] << ", is invalid, not int!";
        return false;
      }
      if (!absl::SimpleAtoi(val_tokens[2], &impression_cnt)) {
        LOG_EVERY_N(WARNING, 10) << " impression value: " << val_tokens[2] << ", is invalid, not int!";
        return false;
      }
    }

    double cxr = raw_cxr * 1.0 / 1e12;
    double industry_cxr = industry_raw_cxr * 1.0 / 1e12;
    LOG_EVERY_N(INFO, 10000) << "key: " << key << ", cxr: " << cxr << ", cpm: " << cpm
                           << ", impression: " << impression_cnt << ", industry_cxr: " << industry_cxr;
    std::unique_ptr<ProtoData> msg(new ProtoData());
    msg->set_cxr(cxr);
    msg->set_industry_cxr(industry_cxr);
    msg->set_cpm(cpm);
    msg->set_impression_cnt(impression_cnt);
    pb->first = engine_base::GetP2pHashKey(key);
    pb->second = std::move(msg);
    return true;
  }
  static Container::value_type pb_to_record(int64_t key,
                                            std::unique_ptr<google::protobuf::Message> pb) {
    NewCreativePosOcpxData data;
    if (pb) {
      auto* msg = static_cast<ProtoData*>(pb.get());
      data.cxr = msg->cxr();
      data.industry_cxr = msg->industry_cxr();
      data.cpm = msg->cpm();
      data.impression_cnt = msg->impression_cnt();
    }
    return std::make_pair(key, data);
  }
};

// key posId_ocpcActionType
// val {cpm, cxr}
class UniverseNewCreativeData
    : public ad_base::P2pCacheLoaderHelper<UniverseNewCreativeDataContainer> {
 public:
  static UniverseNewCreativeData *GetInstance() {
    static UniverseNewCreativeData instance;
    return &instance;
  }

 public:
  void GetPosCpm(const std::string &key, int64_t default_cpm, int64_t *cpm);
  void GetPosCxr(const std::string &key, double *cxr);
  void GetIndustryCxr(const std::string &key, double *cxr);

 private:
  UniverseNewCreativeData()
      : ad_base::P2pCacheLoaderHelper<UniverseNewCreativeDataContainer>(
            15 * 60 * 1000,
            *::ad::data_push::DataPushKconfUtil::universeNewCreativeDataP2pName(),
            ks::engine_base::DependDataLevel::WEAK_DEPEND) {
  }
};

}  // namespace front_server
}  // namespace ks
