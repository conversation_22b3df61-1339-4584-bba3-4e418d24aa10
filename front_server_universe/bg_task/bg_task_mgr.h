#pragma once

#include <string>
#include <utility>
#include <vector>

#include "taskflow/taskflow.hpp"
#include "base/common/sleep.h"
#include "teams/ad/3rdparty/nameof/nameof.hpp"
#include "teams/ad/ad_base/src/common/scoped_guard.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "teams/ad/ad_base/src/perf/perf.h"

namespace ks {
namespace front_server {

constexpr int32_t kWaitSleepInterval = 100;
class BgTaskStopMgr {
 public:
  static BgTaskStopMgr& Instance() {
    static BgTaskStopMgr instance;
    return instance;
  }

  void AddTask(std::function<void()> stop_cb) const {
    std::lock_guard lck(mtx_);
    stop_cbs_.emplace_back(std::move(stop_cb));
  }

  void AddTask(std::function<void()> stop_cb, std::function<void()> wait_cb) const {
    std::lock_guard lck(mtx_);
    stop_cbs_.emplace_back(std::move(stop_cb));
    scope_guard += wait_cb;
  }

  void AddTaskWithStopWait(std::function<void()> stop_cb, std::function<void()> wait_cb) const {
    std::lock_guard lck(mtx_);
    stop_wait_cbs_.emplace_back(std::move(stop_cb));
    scope_guard += wait_cb;
  }

  void Stop() {
    for (auto it = stop_cbs_.rbegin(); it != stop_cbs_.rend(); ++it) {
      (*it)();
    }
    // 会阻塞的 stop 放最后
    int64_t start_ms = base::GetTimestamp() / 1000;
    for (auto it = stop_wait_cbs_.rbegin(); it != stop_wait_cbs_.rend(); ++it) {
      (*it)();
    }
    int64_t end_ms = base::GetTimestamp() / 1000;
    ks::ad_base::AdPerf::IntervalLogStash(end_ms - start_ms, "ad.ad_front", "wait_bg_task_lantency");
  }

  void WaitForReady() { scope_guard.Refresh(); }

  BgTaskStopMgr(const BgTaskStopMgr&) = delete;
  BgTaskStopMgr& operator=(const BgTaskStopMgr&) = delete;

 private:
  BgTaskStopMgr() {
    stop_cbs_.reserve(1024);
    stop_wait_cbs_.reserve(16);
  }

  mutable std::mutex mtx_;
  mutable std::vector<std::function<void()>> stop_cbs_;
  mutable std::vector<std::function<void()>> stop_wait_cbs_;
  mutable ks::ad_base::ScopeGuard scope_guard;
};

template <typename Data>
void StartTask(Data* data, std::function<void(Data*)> start_func) {
  auto task_name = std::string(NAMEOF_SHORT_TYPE_EXPR(*data));
  start_func(data);
  LOG(INFO) << "Start " << task_name << " success";
  BgTaskStopMgr::Instance().AddTask([data, task_name]() {
    LOG(INFO) << "Stop " << task_name;
    data->Stop();
  });
}

template <typename Data>
tf::Task StartTask(tf::Subflow &subflow, Data *data) {  // NOLINT
  auto task_name = std::string(NAMEOF_SHORT_TYPE_EXPR(*data));
  ks::infra::PerfUtil::CountLogStash(1, "ad.ad_front_universe", "bg_task_stat", task_name);
  return subflow
      .emplace([data, task_name]() {
        LOG(INFO) << "Start " << task_name;
        data->Start();

        while (!data->IsReady()) {
          base::SleepForMilliseconds(kWaitSleepInterval);
        }

        // add callback
        BgTaskStopMgr::Instance().AddTask([data, task_name]() {
          LOG(INFO) << "Stop " << task_name;
          data->Stop();
        });
      })
      .name(task_name);
}

template <typename Data>
tf::Task StartWeakTask(tf::Subflow &subflow, Data *data) {  // NOLINT
  auto task_name = std::string(NAMEOF_SHORT_TYPE_EXPR(*data));
  ks::infra::PerfUtil::CountLogStash(1, "ad.ad_front_universe", "bg_task_stat", task_name);
  return subflow
      .emplace([data, task_name]() {
        LOG(INFO) << "Start " << task_name;
        data->Start();

        // add callback
        BgTaskStopMgr::Instance().AddTask([data, task_name]() {
          LOG(INFO) << "Stop " << task_name;
          data->Stop();
        });
      })
      .name(task_name);
}

template <typename Data>
void StartTask(Data& data, std::function<void(const Data&)> start_func) {  // NOLINT
  auto task_name = std::string(NAMEOF_SHORT_TYPE_EXPR(data));
  start_func(data);
  LOG(INFO) << "Start " << task_name << " success";
  BgTaskStopMgr::Instance().AddTask([&data, task_name]() {
    LOG(INFO) << "Stop " << task_name;
    data.Stop();
  });
}

template <typename Data>
void StartTask(Data* data) {
  auto task_name = std::string(NAMEOF_SHORT_TYPE_EXPR(*data));
  data->Start();
  LOG(INFO) << "Start " << task_name << " success";
  BgTaskStopMgr::Instance().AddTask([data, task_name]() {
    LOG(INFO) << "Stop " << task_name;
    data->Stop();
  });
}

template <typename Data>
void StartTask(Data& data) {  // NOLINT
  auto task_name = std::string(NAMEOF_SHORT_TYPE_EXPR(data));
  data.Start();
  LOG(INFO) << "Start " << task_name << " success";
  BgTaskStopMgr::Instance().AddTask([&data, task_name]() {
    LOG(INFO) << "Stop " << task_name;
    data.Stop();
  });
}

template <typename Data>
void StartWeakP2pTask(Data* data) {
  auto max_wait_time = FrontKconfUtil::maxStartWaitTime();
  static int64_t start_ms = base::GetTimestamp() / 1000;

  auto task_name = std::string(NAMEOF_SHORT_TYPE_EXPR(*data));
  LOG(INFO) << "Start " << task_name << " Load";
  data->Start();
  auto wait_callback = [data, task_name]() {
    auto max_wait_time = FrontKconfUtil::maxStartWaitTime();
    LOG(INFO) << "Start wait " << task_name << " ready!";
    while (!data->IsReady()) {
      if (base::GetTimestamp() / 1000 - start_ms > max_wait_time) {
        ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_front", "p2p_data_load_hit_time_thr", task_name);
        break;
      }
      VLOG(4) << task_name << " is not ready";
    }
    LOG(INFO) << task_name << " Finish, latency:" << data->GetInitLatency();
  };
  auto stop_callback = [data, task_name]() {
    LOG(INFO) << "Stop " << task_name;
    data->Stop();
  };
  // add callback
  BgTaskStopMgr::Instance().AddTask(stop_callback);
}

template <typename Data>
void StartWeakP2pTaskWithStopWait(Data *data) {
  auto max_wait_time = FrontKconfUtil::maxStartWaitTime();
  static int64_t start_ms = base::GetTimestamp() / 1000;

  auto task_name = std::string(NAMEOF_SHORT_TYPE_EXPR(*data));
  LOG(INFO) << "Start " << task_name << " Load";
  data->Start();
  auto wait_callback = [data, task_name]() {
    auto max_wait_time = FrontKconfUtil::maxStartWaitTime();
    LOG(INFO) << "Start wait " << task_name << " ready!";
    while (!data->IsReady()) {
      if (base::GetTimestamp() / 1000 - start_ms > max_wait_time) {
        ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_front", "p2p_data_load_hit_time_thr", task_name);
        break;
      }
      VLOG(4) << task_name << " is not ready";
    }
    LOG(INFO) << task_name << " Finish, latency:" << data->GetInitLatency();
  };
  auto stop_callback = [data, task_name]() {
    LOG(INFO) << "Stop " << task_name;
    data->StopAndWait();
  };
  // add callback
  BgTaskStopMgr::Instance().AddTaskWithStopWait(stop_callback, wait_callback);
}

template <typename Data>
void StartWeakP2pTask(Data& data) {  // NOLINT
  static int64_t start_ms = base::GetTimestamp() / 1000;

  auto task_name = std::string(NAMEOF_SHORT_TYPE_EXPR(data));
  LOG(INFO) << "Start " << task_name << " Load";
  data.Start();
  auto wait_callback = [&data, task_name]() {
    auto max_wait_time = FrontKconfUtil::maxStartWaitTime();
    LOG(INFO) << "Start wait " << task_name << " ready!";
    while (!data.IsReady()) {
      if (base::GetTimestamp() / 1000 - start_ms > max_wait_time) {
        ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_front", "p2p_data_load_hit_time_thr", task_name);
        break;
      }
      VLOG(4) << task_name << " is not ready";
    }
    LOG(INFO) << task_name << " Finish, latency:" << data.GetInitLatency();
  };

  auto stop_callback = [&data, task_name]() {
    LOG(INFO) << "Stop " << task_name;
    data.Stop();
  };
  // add callback
  BgTaskStopMgr::Instance().AddTask(stop_callback, wait_callback);
}

}  // namespace front_server
}  // namespace ks
