#pragma once

#include <vector>
#include <string>
#include <algorithm>

#include "teams/ad/engine_base/universe/operation_config/operation_config_group.h"
#include "teams/ad/engine_base/universe/operation_config/operation_config_flow_control.h"

namespace ks {
namespace front_server {

class BoostPriceConfigGroup :
  public engine_base::OperationConfigGroup<engine_base::OperationConfigFlowControl> {
 public:
  using TType = engine_base::OperationConfigFlowControl;
  BoostPriceConfigGroup() {}
  ~BoostPriceConfigGroup() {}
  // 生成有效配置
  bool MediaProcess(int64_t uid, const std::string& app_id, int64_t pos_id, int64_t ad_style,
                    int8_t os_type, std::vector<const TType*>* valid_configs) const {
    if (!valid_configs) {
      return false;
    }
    for (const auto& t : config_group) {
      if (t.isValid() && t.isTargetMedia(uid, app_id, pos_id, ad_style, os_type)) {
        valid_configs->push_back(&t);
      }
    }
    // 新增策略优先级更高
    std::sort(valid_configs->begin(), valid_configs->end(), [](const TType* lhs, const TType* rhs){
          return std::tie(lhs->update_time, lhs->id) >
                 std::tie(rhs->update_time, rhs->id);
        });
    return true;
  }

  // 获取配置指针
  bool AdProcess(const std::string& pt_type, int64_t first_industry_id,
                 int64_t second_industry_id, int32_t ocpx_action_type,
                 int32_t deep_conversion_type, const std::string& product_name,
                 int64_t account_id, int64_t unit_id, int32_t bid_type,
                 int32_t campaign_type, const std::vector<const TType*>& valid_configs,
                 const TType** out) const {
    if (!out) {
      return false;
    }

    for (const auto* t : valid_configs) {
      if (t && t->isTargetAd(pt_type, first_industry_id, second_industry_id, ocpx_action_type,
                             deep_conversion_type, product_name, account_id, unit_id, bid_type,
                             campaign_type)) {
        *out = t;
        return true;
      }
    }
    return false;
  }
};

}  // namespace front_server
}  // namespace ks
