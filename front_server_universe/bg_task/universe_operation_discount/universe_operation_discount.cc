#include "teams/ad/front_server_universe/bg_task/universe_operation_discount/universe_operation_discount.h"

#include <string>

namespace ks {
namespace front_server {

ks::ad_base::P2pCacheLoaderHelper<UniverseOperationDiscountContainer>*
                                                GetUniverseOperationDiscountContainer() {
  static ks::ad_base::P2pCacheLoaderHelper<UniverseOperationDiscountContainer> ins(
                                          3 * 60 * 1000, "universe_operation_discount",
                                          ks::engine_base::DependDataLevel::STRONG_DEPEND);
  return &ins;
}

}  // namespace front_server
}  // namespace ks
