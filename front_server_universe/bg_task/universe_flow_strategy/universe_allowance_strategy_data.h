#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>
#include "base/hash_function/city.h"
#include "base/strings/string_split.h"

#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader_data.pb.h"

#include "teams/ad/data_push/utils/kconf/kconf.h"

namespace ks {
namespace front_server {

class UniverseAllowanceStrategyItem {
 public:
  int64_t virtual_cost_total = 0;
};

class UniverseAllowanceStrategyItemContainer {
 public:
  using StructData = UniverseAllowanceStrategyItem;
  using ProtoData = ad_base::UniverseAllowanceStrategyData;
  using Container = std::unordered_map<int64_t, StructData>;

  static bool line_to_proto(const std::string& line,
                            std::pair<int64_t, std::unique_ptr<google::protobuf::Message>>* pb) {
    if (!pb) return false;
    std::vector<absl::string_view> tokens = absl::StrSplit(line, "\t", absl::SkipEmpty());
    if (tokens.size() < 2) {
      LOG(WARNING) << "Bad UniverseAllowanceStrategyData line:" << line;
      return true;
    }
    ignore_result(absl::StripAsciiWhitespace(tokens[0]));
    ignore_result(absl::StripAsciiWhitespace(tokens[1]));
    std::string strategy_tag{tokens[0]};
    std::string strategy_value{tokens[1]};
    std::unique_ptr<ProtoData> msg(new ProtoData());
    std::vector<absl::string_view> items = absl::StrSplit(strategy_value, ",", absl::SkipEmpty());
    UniverseAllowanceStrategyItem value;
    int64_t virtual_cost_total;
    if (!absl::SimpleAtoi(items[0], &virtual_cost_total) || virtual_cost_total < 0) {
      LOG(WARNING) << "UniverseAllowanceStrategyData SimpleAtoi virtual_cost_total failed"
                   << ", strategy_tag:" << strategy_tag << ", raw value: \"" << items[0] << "\""
                   << ", parsed value: " << virtual_cost_total;
      return true;
    }
    msg->set_virtual_cost_total(virtual_cost_total);
    pb->first = engine_base::GetP2pHashKey(strategy_tag);
    pb->second = std::move(msg);
    return true;
  }

  static Container::value_type pb_to_record(int64_t key, std::unique_ptr<google::protobuf::Message> pb) {
    StructData data;
    if (pb) {
      auto* msg = static_cast<ProtoData*>(pb.get());
      data.virtual_cost_total = msg->virtual_cost_total();
    }
    return std::make_pair(key, data);
  }
};


class UniverseAllowanceStrategyData
    : public ad_base::P2pCacheLoaderHelper<UniverseAllowanceStrategyItemContainer> {
 public:
  static UniverseAllowanceStrategyData *GetInstance() {
    static UniverseAllowanceStrategyData instance;
    return &instance;
  }

 public:
  bool Get(const std::string &strategy_tag, UniverseAllowanceStrategyItem *data);
  bool GetVirtualCostTotal(const std::string &strategy_tag, int64_t &value);  // NOLINT

 private:
  UniverseAllowanceStrategyData()
      : ad_base::P2pCacheLoaderHelper<UniverseAllowanceStrategyItemContainer>(
            1 * 60 * 1000,
            *::ad::data_push::DataPushKconfUtil::universeBonusP2pName(),
            ks::engine_base::DependDataLevel::WEAK_DEPEND) {
  }
};

}  // namespace front_server
}  // namespace ks
