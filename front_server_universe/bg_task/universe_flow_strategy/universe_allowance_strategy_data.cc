#include "teams/ad/front_server_universe/bg_task/universe_flow_strategy/universe_allowance_strategy_data.h"
#include <string>

namespace ks {
namespace front_server {

bool UniverseAllowanceStrategyData::Get(const std::string &strategy_tag,
                                        UniverseAllowanceStrategyItem *data) {
  bool rst = false;
  auto scoped_ptr = this->GetData();
  if (scoped_ptr == nullptr) {
    rst = false;
  } else {
    auto id = engine_base::GetP2pHashKey(strategy_tag);
    auto iter = scoped_ptr->find(id);
    if (iter != scoped_ptr->end()) {
      data->virtual_cost_total = iter->second.virtual_cost_total;
      rst = true;
    }
  }
  if (!rst) {
      LOG_EVERY_N(INFO, 100000) << "get_universe_allowance_strategy data failed"
                                << ", strategy_tag:" << strategy_tag;
  } else {
    LOG_EVERY_N(INFO, 100000) << "get_universe_allowance_strategy data succ:"
                              << " strategy_tag:" << strategy_tag
                              << ", virtual_cost_total:" << data->virtual_cost_total;
  }
  return rst;
}

bool UniverseAllowanceStrategyData::GetVirtualCostTotal(const std::string &strategy_tag, int64_t &value) {
  bool rst = false;
  auto scoped_ptr = this->GetData();
  if (scoped_ptr == nullptr) {
    rst = false;
  } else {
    auto id = base::CityHash64(strategy_tag.c_str(), strategy_tag.length());
    auto iter = scoped_ptr->find(id);
    if (iter != scoped_ptr->end()) {
      value = iter->second.virtual_cost_total;
      rst = true;
    }
  }
  if (!rst) {
    LOG_EVERY_N(INFO, 100000) << "get_universe_allowance_strategy virtual_cost_total failed"
                              << ", strategy_tag:" << strategy_tag;
  } else {
    LOG_EVERY_N(INFO, 100000) << "get_universe_allowance_strategy virtual_cost_total succ:"
                              << " strategy_tag:" << strategy_tag << ", virtual_cost_total:" << value;
  }
  return rst;
}

}  // namespace front_server
}  // namespace ks
