#pragma once

#include <string>
#include <vector>
#include <utility>

#include "base/common/logging.h"
#include "base/common/closure.h"
#include "base/thread/thread_pool.h"
#include "libcuckoo/cuckoohash_map.hh"
#include "redis_proxy_client/redis_proxy_client.h"
#include "third_party/folly/MPMCQueue.h"

#include "teams/ad/front_server_universe/util/kconf/kconf.h"

#include "teams/ad/ad_base/src/common/condition_time_wait.h"

#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.kess.grpc.pb.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"

namespace ks {
namespace front_server {

class UniverseWinRateModelData final {
 public:
  void WriteWinRateModelDataToRedis(ContextData * session_data);

  static UniverseWinRateModelData* GetInstance() {
    static UniverseWinRateModelData instance;
    return &instance;
  }

  ~UniverseWinRateModelData() {}

 private:
  UniverseWinRateModelData() {}

  DISALLOW_COPY_AND_ASSIGN(UniverseWinRateModelData);
};
}  // namespace front_server
}  // namespace ks
