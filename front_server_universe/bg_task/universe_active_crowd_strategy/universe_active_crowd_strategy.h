#pragma once

#include <string>
#include <unordered_map>
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"

namespace ks {
namespace front_server {

class UniverseActiveCrowdStrategy :
    public ad_base::P2pCacheLoader<std::unordered_map<std::string, std::string>,
                                     ad_base::DeserializeFileEnum::Basic> {
 public:
  static UniverseActiveCrowdStrategy* GetInstance() {
    static UniverseActiveCrowdStrategy instance;
    return &instance;
  }

  bool GetUserRatio(const std::string& user_id,
      double *ks_cpm_ratio,
      double *payment_ratio,
      double *fusion_ratio) const;

 private:
  UniverseActiveCrowdStrategy() :
      ad_base::P2pCacheLoader<std::unordered_map<std::string, std::string>,
      ad_base::DeserializeFileEnum::Basic> (
      120 * 60 * 1000, "universe_ks_30day_active_user_ratio",
      ks::engine_base::DependDataLevel::WEAK_DEPEND) {}
};

}  // namespace front_server
}  // namespace ks

