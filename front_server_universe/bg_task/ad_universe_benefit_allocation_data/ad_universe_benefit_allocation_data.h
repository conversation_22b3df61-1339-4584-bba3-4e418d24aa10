#pragma once

#include <string>
#include <unordered_map>
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"

namespace ks {
namespace front_server {

class UniverseBenefitAllocationData :
    public ad_base::P2pCacheLoader<std::unordered_map<std::string, double>,
                                     ad_base::DeserializeFileEnum::Basic> {
 public:
  static UniverseBenefitAllocationData* GetInstance() {
    static UniverseBenefitAllocationData instance;
    return &instance;
  }

  bool GetPosBonusRatio(const std::string& time_pos_strategy) const;

 private:
  UniverseBenefitAllocationData() :
            ad_base::P2pCacheLoader<std::unordered_map<std::string, double>,
                                    ad_base::DeserializeFileEnum::Basic> (
            60 * 1000, "ad_universe_benefit_allocation_data",
            ks::engine_base::DependDataLevel::STRONG_DEPEND) {}
};

}  // namespace front_server
}  // namespace ks
