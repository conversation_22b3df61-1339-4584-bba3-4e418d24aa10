#include "teams/ad/front_server_universe/bg_task/ad_universe_benefit_allocation_data/ad_universe_benefit_allocation_data.h"

namespace ks {
namespace front_server {

bool UniverseBenefitAllocationData::GetPosBonusRatio
    (const std::string& time_pos_strategy) const {
  auto data_ptr = GetData();
  if (data_ptr) {
    auto iter = data_ptr->find(time_pos_strategy);
    if (iter != data_ptr->end()) {
      return true;
    }
  }
  return false;
}

}  // namespace front_server
}  // namespace ks
