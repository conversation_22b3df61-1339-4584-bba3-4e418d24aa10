#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
#include "teams/ad/engine_base/data_stability/depend_data_level.h"
#include "teams/ad/front_server_universe/bg_task/universe_pos_timeout_data/universe_pos_timeout_data.h"

namespace ks {
namespace front_server {

class UniversePosTimeoutDataContainerV2 {
 public:
  using StructData = PosTimeoutData;
  using ProtoData = ad_base::UniverseTimeoutData;
  using Container = std::unordered_map<int64_t, StructData>;
  static bool line_to_proto(const std::string& line,
                            std::pair<int64_t, std::unique_ptr<google::protobuf::Message>>* pb) {
    if (!pb) return false;
    std::vector<absl::string_view> tokens = absl::StrSplit(line, ",", absl::SkipEmpty());
    if (tokens.size() != 5) {
      return false;
    }
    for (int i=0; i < tokens.size(); i++) {
      ignore_result(absl::StripAsciiWhitespace(tokens[i]));
    }

    int64_t pos_id = 0;
    int64_t pv_count = 0;
    double p99 = 0;
    double p995 = 0;
    double max = 0;
    if (!absl::SimpleAtoi(tokens[0], &pos_id)) {
      LOG_EVERY_N(WARNING, 10) << " pos_id value: " << tokens[0] << ", is invalid, not int!";
      return false;
    }
    if (!absl::SimpleAtoi(tokens[1], &pv_count)) {
      LOG_EVERY_N(WARNING, 10) << " pv_count value: " << tokens[1] << ", is invalid, not int!";
      return false;
    }
    if (!absl::SimpleAtod(tokens[2], &p99) || p99 > 480 || p99 < 200) {
      LOG_EVERY_N(WARNING, 10) << " p99 value: " << tokens[2] << ", is invalid, not int!";
      return false;
    }
    if (!absl::SimpleAtod(tokens[3], &p995) || p995 > 480 || p995 < 200) {
      LOG_EVERY_N(WARNING, 10) << " p995 value: " << tokens[1] << ", is invalid, not int!";
      return false;
    }
    if (!absl::SimpleAtod(tokens[4], &max) || max > 480 || max < 200) {
      LOG_EVERY_N(WARNING, 10) << " max value: " << tokens[2] << ", is invalid, not int!";
      return false;
    }
    LOG_EVERY_N(INFO, 100000) << "pos_id: " << pos_id << ", pv_count: " << pv_count << ", p99: " << p99
                           << ", p995: " << p995 << ", max: " << max;
    std::unique_ptr<ProtoData> msg(new ProtoData());
    msg->set_pv_count(pv_count);
    msg->set_p99(static_cast<int64_t>(p99));
    msg->set_p995(static_cast<int64_t>(p995));
    msg->set_max(static_cast<int64_t>(max));
    pb->first = pos_id;
    pb->second = std::move(msg);
    return true;
  }
  static Container::value_type pb_to_record(int64_t key,
                                            std::unique_ptr<google::protobuf::Message> pb) {
    StructData data;
    if (pb) {
      auto* msg = static_cast<ProtoData*>(pb.get());
      data.pv_count = msg->pv_count();
      data.p99 = msg->p99();
      data.p995 = msg->p995();
      data.max = msg->max();
    }
    return std::make_pair(key, data);
  }
};

class UniversePosTimeoutDataV2
    : public ad_base::P2pCacheLoaderHelper<UniversePosTimeoutDataContainerV2> {
 public:
  static UniversePosTimeoutDataV2* GetInstance() {
    static UniversePosTimeoutDataV2 instance;
    return &instance;
  }

 public:
  bool GetTimeoutByPos(int64_t pos, int64_t* timeout, TimeoutLevel level = TimeoutLevel::P995);

 private:
  UniversePosTimeoutDataV2()
      : ad_base::P2pCacheLoaderHelper<UniversePosTimeoutDataContainerV2>(
          5 * 60 * 1000, "universe_pos_timeout_data_v2", engine_base::DependDataLevel::WEAK_DEPEND) {}
};

}  // namespace front_server
}  // namespace ks
