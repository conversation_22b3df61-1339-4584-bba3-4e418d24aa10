#include <string>

#include "teams/ad/front_server_universe/bg_task/universe_pos_timeout_data/universe_pos_timeout_data.h"
#include "perfutil/perfutil.h"

namespace ks {
namespace front_server {

bool UniversePosTimeoutData::GetTimeoutByPos(int64 pos_id, int64* timeout, TimeoutLevel level) {
  bool rst = false;
  auto scoped_ptr = this->GetData();
  if (scoped_ptr == nullptr) {
    rst = false;
  } else {
    auto itr = scoped_ptr->find(pos_id);
    if (itr != scoped_ptr->end()) {
      if (level == TimeoutLevel::P99) {
        *timeout = itr->second.p99;
      } else if (level == TimeoutLevel::P995) {
        *timeout = itr->second.p995;
      } else if (level == TimeoutLevel::MAX) {
        *timeout = itr->second.max;
      } else {
        *timeout = itr->second.p995;
      }
      rst = true;
    }
  }
  return rst;
}

}  // namespace front_server
}  // namespace ks
