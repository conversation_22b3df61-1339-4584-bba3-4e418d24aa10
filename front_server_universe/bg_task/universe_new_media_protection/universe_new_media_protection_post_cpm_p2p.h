#pragma once

#include <cstdint>
#include <unordered_map>
#include <string>

#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
#include "teams/ad/engine_base/data_stability/depend_data_level.h"

namespace ks {
namespace front_server {

class UniverseNewMediaProtectionPostCpm
  : public ad_base::P2pCacheLoader<std::unordered_map<std::string, double>,
             ad_base::DeserializeFileEnum::Basic> {
 public:
  static UniverseNewMediaProtectionPostCpm* GetInstance() {
    static UniverseNewMediaProtectionPostCpm instance;
    return &instance;
  }
 public:
    bool GetUniverseXsceneCpm(int64_t pos_id,
                              std::string app_id,
                              int32_t ad_style,
                              int64 second_industry_id,
                              double *avg_cpm);
    bool GetNewMediaXsceneCpm(std::string app_id,
                              int32_t ad_style,
                              int64 second_industry_id,
                              double *avg_cpm);
 private:
  UniverseNewMediaProtectionPostCpm()
      : ad_base::P2pCacheLoader<std::unordered_map<std::string, double>, ad_base::DeserializeFileEnum::Basic>(
            5 * 60 * 1000, "universe_new_media_protection_post_cpm_p2p",
            ks::engine_base::DependDataLevel::STRONG_DEPEND) {
  }
};

}  // namespace front_server
}  // namespace ks
