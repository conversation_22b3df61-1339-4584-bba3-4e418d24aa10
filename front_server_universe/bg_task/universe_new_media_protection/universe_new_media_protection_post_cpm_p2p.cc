#include "teams/ad/front_server_universe/bg_task/universe_new_media_protection/universe_new_media_protection_post_cpm_p2p.h"

#include <string>
namespace ks {
namespace front_server {
bool UniverseNewMediaProtectionPostCpm::GetUniverseXsceneCpm(int64_t pos_id,
                                                             std::string app_id,
                                                             int32_t ad_style,
                                                             int64 second_industry_id,
                                                             double *avg_cpm) {
  auto data_ptr = GetData();
  if (data_ptr) {
    std::string pos_id_str = absl::StrCat(pos_id);
    std::string app_id_scene_str = absl::StrCat(app_id, "_", ad_style);
    std::string second_industry_id_scene_str = absl::StrCat(second_industry_id, "_", ad_style);
    std::string scene_str = absl::StrCat(ad_style);

    auto pos_id_iter = data_ptr->find(pos_id_str);
    auto app_id_scene_iter = data_ptr->find(app_id_scene_str);
    auto second_industry_id_scene_iter = data_ptr->find(second_industry_id_scene_str);
    auto scene_iter = data_ptr->find(scene_str);
    if (pos_id_iter != data_ptr->end()) {
      *avg_cpm = pos_id_iter->second;
      return true;
    } else if (app_id_scene_iter != data_ptr->end()) {
      *avg_cpm = app_id_scene_iter->second;
      return true;
    } else if (second_industry_id_scene_iter != data_ptr->end()) {
      *avg_cpm = second_industry_id_scene_iter->second;
      return true;
    } else if (scene_iter != data_ptr->end()) {
      *avg_cpm = scene_iter->second;
      return true;
    }
  }
  return false;
}

bool UniverseNewMediaProtectionPostCpm::GetNewMediaXsceneCpm(std::string app_id,
                                                             int32_t ad_style,
                                                             int64 second_industry_id,
                                                             double *avg_cpm) {
  auto data_ptr = GetData();
  if (data_ptr) {
    std::string app_id_scene_str = absl::StrCat(app_id, "_", ad_style);
    std::string second_industry_id_scene_str = absl::StrCat(second_industry_id, "_", ad_style);
    std::string scene_str = absl::StrCat(ad_style);

    auto app_id_scene_iter = data_ptr->find(app_id_scene_str);
    auto second_industry_id_scene_iter = data_ptr->find(second_industry_id_scene_str);
    auto scene_iter = data_ptr->find(scene_str);
    if (app_id_scene_iter != data_ptr->end()) {
      *avg_cpm = app_id_scene_iter->second;
      return true;
    } else if (second_industry_id_scene_iter != data_ptr->end()) {
      *avg_cpm = second_industry_id_scene_iter->second;
      return true;
    } else if (scene_iter != data_ptr->end()) {
      *avg_cpm = scene_iter->second;
      return true;
    }
  }
  return false;
}

}  // namespace front_server
}  // namespace ks
