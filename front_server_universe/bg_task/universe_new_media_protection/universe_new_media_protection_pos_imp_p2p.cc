#include "teams/ad/front_server_universe/bg_task/universe_new_media_protection/universe_new_media_protection_pos_imp_p2p.h"


namespace ks {
namespace front_server {

bool UniverseNewMediaProtectionPosImp::GetNewMediaPosImp(int64 pos_id, int64 *impression) {
  auto data_ptr = GetData();
  if (data_ptr) {
    auto iter = data_ptr->find(pos_id);
    if (iter != data_ptr->end()) {
      *impression = iter->second;
      return true;
    }
  }
  return false;
}

}  // namespace front_server
}  // namespace ks
