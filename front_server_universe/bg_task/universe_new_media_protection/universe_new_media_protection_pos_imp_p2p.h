#pragma once

#include <unordered_map>

#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
#include "teams/ad/engine_base/data_stability/depend_data_level.h"

namespace ks {
namespace front_server {

class UniverseNewMediaProtectionPosImp
  : public ad_base::P2pCacheLoader<std::unordered_map<int64, int64>, ad_base::DeserializeFileEnum::Basic> {
 public:
  static UniverseNewMediaProtectionPosImp* GetInstance() {
    static UniverseNewMediaProtectionPosImp instance;
    return &instance;
  }
 public:
    bool GetNewMediaPosImp(int64 pos_id, int64 *impression);
 private:
  UniverseNewMediaProtectionPosImp()
      : ad_base::P2pCacheLoader<std::unordered_map<int64, int64>, ad_base::DeserializeFileEnum::Basic>(
            5 * 60 * 1000, "universe_new_media_protection_pos_imp_p2p",
            ks::engine_base::DependDataLevel::STRONG_DEPEND) {
  }
};

}  // namespace front_server
}  // namespace ks
