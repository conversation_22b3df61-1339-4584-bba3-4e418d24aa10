#include <string>

#include "teams/ad/front_server_universe/bg_task/universe_app_package2product/universe_app_package2product.h"
#include "third_party/abseil/absl/strings/numbers.h"
#include "perfutil/perfutil.h"

namespace ks {
namespace front_server {

bool UniverseAppPackage2Product::GetProductName(std::string package_name, std::string* product_name) {
  bool rst = false;
  auto scoped_ptr = this->GetData();
  if (scoped_ptr == nullptr) {
    rst = false;
  } else {
    auto itr = scoped_ptr->find(package_name);
    if (itr != scoped_ptr->end()) {
      *product_name = itr->second;
      rst = true;
    }
  }
  return rst;
}

}  // namespace front_server
}  // namespace ks
