#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
#include "teams/ad/engine_base/data_stability/depend_data_level.h"

namespace ks {
namespace front_server {

class UniverseAppPackage2Product
    : public ad_base::P2pCacheLoader<
      std::unordered_map<std::string, std::string>, ad_base::DeserializeFileEnum::Basic> {
 public:
  static UniverseAppPackage2Product* GetInstance() {
    static UniverseAppPackage2Product instance;
    return &instance;
  }

 public:
  bool GetProductName(std::string package_name, std::string* product_name);

 private:
  UniverseAppPackage2Product()
      : ad_base::P2pCacheLoader<
        std::unordered_map<std::string, std::string>, ad_base::DeserializeFileEnum::Basic>(5 * 60 * 1000,
          "universe_app_store_package2product_name", engine_base::DependDataLevel::STRONG_DEPEND) {}
};

}  // namespace front_server
}  // namespace ks
