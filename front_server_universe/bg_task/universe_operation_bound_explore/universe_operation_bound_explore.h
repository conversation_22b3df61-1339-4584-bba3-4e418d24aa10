#pragma once

#include <vector>
#include <string>
#include <memory>
#include <utility>

#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
#include "teams/ad/front_server_universe/bg_task/universe_operation_bound_explore/universe_operation_bound_explore_data.h"
#include "teams/ad/ad_base/src/perf/perf.h"

namespace ks {
namespace front_server {

// 线上使用 struct
class UniverseOperationBoundExploreContainer {
 public:
  using StructData = BoundExploreConfigGroup;
  using ProtoData = engine_base::operation_config::ConfigInfoResponse;
  // note: 在这里进行在线使用的数据结构的切换
  using Container = absl::flat_hash_map<int64_t, StructData>;
  // using Container = std::unordered_map<int64_t, ProtoData>; // 2.线上使用 pb 结构
  // 文件中一行转换为 pb
  static bool line_to_proto(const std::string& line,
                            std::pair<int64_t, std::unique_ptr<google::protobuf::Message>>* pb) {
    if (!pb) {
      return false;
    }
    std::unique_ptr<ProtoData> msg(new ProtoData());
    if (!ks::ad_base::pb_utils::ParseBase64PB(*msg, line)) {
      ks::ad_base::AdPerf::CountLogStash(1, "ad.engine", "UniverseOperationConfig.fail",
                                        "parse_failed", "universe_operation_bound_explore");
      return false;
    }
    LOG_EVERY_N(INFO, 1) << "UniverseOperationConfig:" << msg->ShortDebugString();

    if (msg->status() != 1) {
      ks::ad_base::AdPerf::CountLogStash(1, "ad.engine", "UniverseOperationConfig.fail",
                                        "status_error", "universe_operation_bound_explore");
      return false;
    }
    pb->first = 0;
    pb->second = std::move(msg);
    return true;
  }
    static Container::value_type pb_to_record(int64_t key, std::unique_ptr<google::protobuf::Message> pb) {
    StructData struct_data;
    if (pb) {
      int32_t data_num = 0;
      static constexpr int32_t kMaxDataNum = 50;
      auto* msg = static_cast<ProtoData*>(pb.get());
      for (const auto& one_data : msg->flow_control()) {
        if (one_data.status() != 0 || one_data.type() != 7 || data_num >= kMaxDataNum) {
          continue;
        }
        int64_t cur_timestamp = base::GetTimestamp() / 1000;
        if ((one_data.begin_time() == 0 || cur_timestamp > one_data.begin_time()) &&
            (one_data.end_time() == 0 || cur_timestamp < one_data.end_time())) {
          struct_data.AddData(one_data);
          ++data_num;
        }
      }
      struct_data.Debug();
    }
    return std::make_pair(key, struct_data);
  }
};

using BoundExploreHelper = ks::ad_base::P2pCacheLoaderHelper<UniverseOperationBoundExploreContainer>;
BoundExploreHelper* GetUniverseOperationBoundExploreContainer();

}  // namespace front_server
}  // namespace ks
