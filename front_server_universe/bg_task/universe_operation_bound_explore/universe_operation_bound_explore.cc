#include "teams/ad/front_server_universe/bg_task/universe_operation_bound_explore/universe_operation_bound_explore.h"

#include <string>

namespace ks {
namespace front_server {

ks::ad_base::P2pCacheLoaderHelper<UniverseOperationBoundExploreContainer>*
                                                GetUniverseOperationBoundExploreContainer() {
  static ks::ad_base::P2pCacheLoaderHelper<UniverseOperationBoundExploreContainer> ins(
                                          3 * 60 * 1000, "universe_operation_bound_explore",
                                          ks::engine_base::DependDataLevel::STRONG_DEPEND);
  return &ins;
}

}  // namespace front_server
}  // namespace ks
