#pragma once

#include <vector>
#include <string>
#include <algorithm>
#include "teams/ad/front_server_universe/util/kconf/kconf.h"

#include "teams/ad/engine_base/universe/operation_config/operation_config_group.h"
#include "teams/ad/engine_base/universe/operation_config/operation_config_bound_explore.h"

namespace ks {
namespace front_server {

class BoundExploreConfigGroup :
  public engine_base::OperationConfigGroup<engine_base::OperationConfigBoundExplore> {
 public:
  using TType = engine_base::OperationConfigBoundExplore;
  BoundExploreConfigGroup() {}
  ~BoundExploreConfigGroup() {}
  bool IsIntersect(const absl::flat_hash_set<int64_t>& user_tag_conf,
                   const absl::flat_hash_set<int64_t>& user_tags) const {
    bool is_intersect = false;
    for (int64_t tag : user_tag_conf) {
      if (user_tags.count(tag)) {
        is_intersect = true;
        break;
      }
    }
    return is_intersect;
  }

  bool PvCheck(const TType& t, int64_t medium_uid, const std::string& app_id, int64_t pos_id,
               const absl::flat_hash_set<int64_t>& user_tags) const {
    if (t.is_all_media || t.media_cluster_id == 0) {
      return true;
    }
    // 配置中人群包为空 或 与用户标签有交集 则匹配成功
    if ((t.media_uids.contains(medium_uid) ||
        t.media_app_ids.contains(app_id) ||
        t.media_pos_ids.contains(pos_id)) &&
        (t.user_tag_conf.empty() ||
        IsIntersect(t.user_tag_conf, user_tags))) {
      return true;
    }
    return false;
  }

  // 获取有效配置
  bool GetValidConfigs(int64_t medium_uid, const std::string& app_id, int64_t pos_id,
    const absl::flat_hash_set<int64_t>& user_tags,  std::vector<const TType*>* valid_configs) const {
    if (!valid_configs) {
      return false;
    }
    for (const auto& t : config_group) {
      if (t.isValid() && PvCheck(t, medium_uid, app_id, pos_id, user_tags)) {
        valid_configs->push_back(&t);
      }
    }
    // 新增策略优先级更高
    std::sort(valid_configs->begin(), valid_configs->end(), [](const TType* lhs, const TType* rhs){
          return std::tie(lhs->update_time, lhs->id) >
                 std::tie(rhs->update_time, rhs->id);
        });
    return true;
  }

  // 判断某配置是否命中 对某维度 不配置 = 全部放开
  bool AdCheck(const TType* t, int64_t unit_id, int64_t account_id, int64_t author_id,
               int32_t campaign_type, int64_t second_industry_id, int32_t item_type,
               int64_t ocpx_action_type, int64_t pt_type, double real_diff_ratio) const {
    if (t->upper_bonus_rate < real_diff_ratio) {
      return false;
    }
    if (!t->ad_placements_int64.contains(pt_type)) {
      return false;
    }
    if (!t->ad_ocpx_action_types.empty() && !t->ad_ocpx_action_types.contains(ocpx_action_type)) {
      return false;
    }
    if (!t->ad_campaign_types.empty() && !t->ad_campaign_types.contains(campaign_type)) {
      return false;
    }
    if (!t->ad_item_types.empty() && !t->ad_item_types.contains(item_type)) {
      return false;
    }
    if (t->is_all_ad) {
      return true;
    }
    if (t->ad_account_ids.contains(account_id) ||
        t->ad_unit_ids.contains(unit_id) ||
        t->ad_author_ids.contains(author_id)) {
      return true;
    }
    return false;
  }

  // 获取命中配置指针
  bool AdMatch(int64_t unit_id, int64_t account_id, int64_t author_id,
               int32_t campaign_type, int64_t second_industry_id, int32_t item_type,
               int64_t ocpx_action_type, int64_t pt_type,
               double real_diff_ratio,
               const std::vector<const TType*>& valid_configs,
               const TType** out) const {
    if (!out) {
      return false;
    }
    for (const auto* t : valid_configs) {
      if (t && AdCheck(t, unit_id, account_id, author_id, campaign_type, second_industry_id,
                       item_type, ocpx_action_type, pt_type, real_diff_ratio)) {
        *out = t;
        return true;
      }
    }
    return false;
  }
};

}  // namespace front_server
}  // namespace ks
