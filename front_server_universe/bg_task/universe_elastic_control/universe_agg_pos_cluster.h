#pragma once

#include <unordered_map>

#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"

namespace ks {
namespace front_server {

class UniverseAggPosCluster : public ad_base::P2pCacheLoader<
                              std::unordered_map<int64_t, int64_t>,
                              ad_base::DeserializeFileEnum::Basic> {
 public:
  static UniverseAggPosCluster* GetInstance() {
    static UniverseAggPosCluster instance;
    return &instance;
  }

  void GetClusterPosId(int64_t pos_id, int64_t* cluster_pos_id) {
    auto scope_ptr = GetData();
    if (scope_ptr) {
      auto iter = scope_ptr->find(pos_id);
      if (iter != scope_ptr->end()) {
        *cluster_pos_id = iter->second;
        LOG_EVERY_N(INFO, 10000) << "GetClusterPosId success, pos_id:" << pos_id
                                 << " cluster_pos_id:" << *cluster_pos_id;
      }
    }
  }

 private:
  UniverseAggPosCluster()
    : ad_base::P2pCacheLoader<std::unordered_map<int64_t, int64_t>, ad_base::DeserializeFileEnum::Basic>(
        60 * 1000 * 5, "universe_agg_pos_cluster_v2", ks::engine_base::DependDataLevel::STRONG_DEPEND) {
    }
};

}  // namespace front_server
}  // namespace ks
