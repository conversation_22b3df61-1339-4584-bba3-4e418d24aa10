#pragma once
#include <string>
#include <memory>
#include <vector>

#include "base/common/basic_types.h"
#include "teams/ad/ad_base/src/redis_cache_loader/redis_cache_loader.h"
#include "teams/ad/engine_base/kconf/kconf.h"

namespace ks::front_server {
class UniverseBenefitStat : public ks::ad_base::RedisCacheLoader<int64_t, double> {
 public:
  static UniverseBenefitStat *GetInstance() {
    static UniverseBenefitStat instance;
    return &instance;
  }
  bool GetTotalBenefit(int64_t union_strategy_tag_id, double* total_benefit);
 private:
  UniverseBenefitStat()
      : ks::ad_base::RedisCacheLoader<int64_t, double>(
            60000, ks::ad_base::AdRedisHolderType::UNIVERSE_BENEFIT_STAT,
            "adUniverseBenefitStat",
            ad_base::DependDataLevel::WEAK_DEPEND) {
  }
  bool GetOriginalIds() override;
  std::string KeyGenerator(int64_t) const;
  bool ValueParser(std::size_t, DoubleBuffCache *) const override;
  void Prepare() override;

 private:
  std::shared_ptr<ks::engine_base::UniverseMaxBonusCfg> universe_max_bonus_config_ptr_;
  std::string date_;
  DISALLOW_COPY_AND_ASSIGN(UniverseBenefitStat);
};
}  // namespace ks::front_server
