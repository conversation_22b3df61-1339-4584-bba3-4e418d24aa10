#include "teams/ad/front_server_universe/bg_task/universe_benefit_stats/universe_benefit_stats.h"

#include "absl/time/time.h"
#include "absl/time/clock.h"

namespace ks::front_server {
static const char* UNIV_BENEFIT_PREFIX = "ubdnn_";

bool UniverseBenefitStat::GetOriginalIds() {
  if (!ks::engine_base::AdKconfUtil::enableUniverseBenefitStatCacheLoader()) {
    return false;
  }
  universe_max_bonus_config_ptr_->GetStrategyIds(&original_ids_);
  ks::infra::PerfUtil::IntervalLogStash(original_ids_.size(),
            "ad.ad_front", "universe_benefit_stats", "original_ids_size");
  return !original_ids_.empty();
}

bool UniverseBenefitStat::GetTotalBenefit(int64_t union_strategy_tag_id, double* total_benefit) {
  ScopedPtr scope_ptr;
  bool ret = false;
  if (GetDoubleCached(&scope_ptr)) {
    auto iter = scope_ptr->ptr.find(union_strategy_tag_id);
    if (iter != scope_ptr->ptr.end()) {
      *total_benefit = iter->second;
      ret = true;
    }
  }
  return ret;
}

std::string UniverseBenefitStat::KeyGenerator(int64_t id) const {
  return absl::StrCat(UNIV_BENEFIT_PREFIX, date_, "_universe_system_bonus_", id);
}

bool UniverseBenefitStat::ValueParser(std::size_t index, DoubleBuffCache *cache) const {
  if (this->values_.size() <= index || this->values_.at(index).empty()) {
    LOG_EVERY_N(INFO, 10000) << "get UniverseBenefitStat value failed, key: "
                             << this->original_ids_.at(index);
    ks::infra::PerfUtil::CountLogStash(1, "ad.ad_front", "universe_benefit_stats", "value_parse_noexist");
    return false;
  }
  std::string v = this->values_.at(index);
  double total_benefit = 0.0;
  if (absl::SimpleAtod(v, &total_benefit)) {
    cache->ptr.emplace(this->original_ids_.at(index), total_benefit);
    LOG_EVERY_N(INFO, 1) << "UniverseBenefitStat key: " << this->original_ids_.at(index)
                         << " value: " << total_benefit;
    ks::infra::PerfUtil::CountLogStash(1, "ad.ad_front", "universe_benefit_stats", "value_parse_succ");
    return true;
  }
  ks::infra::PerfUtil::CountLogStash(1, "ad.ad_front", "universe_benefit_stats", "value_parse_fail");
  return false;
}

void UniverseBenefitStat::Prepare() {
  // deep copy
  universe_max_bonus_config_ptr_ = std::make_shared<ks::engine_base::UniverseMaxBonusCfg>(
    ks::engine_base::AdKconfUtil::universeMaxBonusConfig()->data());
  // 每次执行时初始化日期 可能会有滞后
  date_ = absl::FormatTime("%Y-%m-%d", absl::Now(), absl::LocalTimeZone());
}

}  // namespace ks::front_server

