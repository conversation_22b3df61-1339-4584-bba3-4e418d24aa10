#include "teams/ad/front_server_universe/util/grpc/grpc_client.h"

#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/front_server_universe/util/utility/utils.h"
#include "teams/ad/ad_base/src/math/random/random.h"

namespace ks {
namespace front_server {

using kuaishou::ad::kess::AdGrpcService;
using kuaishou::ad::kess::AdUserProfileService;
using kuaishou::fanstop::kess::FansTopNearByGrpcService;
using kuaishou::fanstop::kess::FansTopGrpcService;
using kuaishou::ad::fanstop::kess::FansTopFollowService;
using kuaishou::ad::social::kess::AdSocialFollowRecoService;

namespace {
const ::grpc::Status& PassPreCheck(const std::string &filter_name,
    const std::function<bool()> &prepare) {
  if (prepare && !prepare()) {
    static const ::grpc::Status status(::grpc::StatusCode::DO_NOT_USE, "prepare_filter");
    return status;  // Ignore request
  }
  static const ::grpc::Status status(::grpc::StatusCode::OK, "OK");
  return status;
}
}  // namespace

#define GRPC_WARPPER_IMPL_BEGIN(Service, Method, ReqType, RespType) \
::grpc::Status GrpcClient::Service##_##Method(const std::string &client_key, /*NOLINT*/\
    const ReqType *request, RespType *response, \
    std::function<bool()> prepare, \
    std::function<void(const ::grpc::Status&, RespType*)> finish) { \
  auto client = ks::ad_base::AdKessClient::ClientOfKey<Service>(client_key); \
  if (!client.second) { \
    static const ::grpc::Status status(::grpc::StatusCode::NOT_FOUND, \
        "no kess service for: " + client_key); \
    return status; \
  } \
  const ::grpc::Status& pre_status = PassPreCheck(client.first->kess_name, prepare); \
  if (!pre_status.ok()) { \
    if (finish) finish(pre_status, response); \
    return pre_status; \
  }

#define GRPC_WARPPER_IMPL_END(Service, Method, ReqType, RespType) \
  const auto options = ks::ad_base::OptionsFromMilli(client.first->time_out); \
  auto stub = client.second->SelectByHash(hash); \
  if (finish) { \
    stub->Async##Method(options, *request, response, client.second->GetEventLoop()) \
        .Submit([finish](const ::grpc::Status& status, RespType* reply) { \
          finish(status, reply); \
        }); \
    return pre_status; \
  } else { \
    return stub->Method(options, *request, response); \
  } \
}

GRPC_WARPPER_IMPL_BEGIN(AdUserProfileService, GetProfile,
    AdUserProfileRequest, AdUserProfileResponse)
  uint64_t hash = request->request_hash().key_id();
GRPC_WARPPER_IMPL_END(AdUserProfileService, GetProfile,
    AdUserProfileRequest, AdUserProfileResponse)

GRPC_WARPPER_IMPL_BEGIN(AdUserProfileService, GetKnewsUid,
    AdUserProfileRequest, AdUserProfileResponse)
  uint64_t hash = ad_base::AdRandom::GetInt(1, 123631);
GRPC_WARPPER_IMPL_END(AdUserProfileService, GetKnewsUid,
    AdUserProfileRequest, AdUserProfileResponse)
}  // namespace front_server
}  // namespace ks
