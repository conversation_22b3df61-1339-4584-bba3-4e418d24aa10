#pragma once
#include <functional>
#include <string>
#include "teams/ad/ad_proto/kuaishou/ad/ad_grpc_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_user_profile_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/adsocial/ad_social_follow_reco.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/fanstop/fans_top_follow_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/fanstop/fans_top_near_by_grpc_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/fanstop/fans_top_grpc_service.kess.grpc.pb.h"

namespace ks {
namespace front_server {

using kuaishou::ad::AdRequest;
using kuaishou::ad::AdResponse;

using kuaishou::fanstop::FansTopRequest;
using kuaishou::fanstop::FansTopResponse;

using kuaishou::ad::FollowFanstopRequest;
using kuaishou::ad::FollowFanstopResponse;

using kuaishou::ad::FollowSocialRequest;
using kuaishou::ad::FollowSocialResponse;

using ::kuaishou::ad::AdUserProfileRequest;
using ::kuaishou::ad::AdUserProfileResponse;

// prepare 执行成功才会发送请求
// 如果被过滤，或者 !prepare, 则提前调用 finish, code = ::grpc::StatusCode::DO_NOT_USE
// finish 为 nullptr 时执行同步调用, 直接返回调用的结果
class GrpcClient {
 public:
  #define GRPC_WARPPER_DECLARE(Service, Method, ReqType, RespType) \
  static ::grpc::Status Service##_##Method(const std::string &client_key, /*NOLINT*/\
    const ReqType *request, RespType *response, \
    std::function<bool()> prepare = nullptr, \
    std::function<void(const ::grpc::Status&, RespType*)> finish = nullptr)

  GRPC_WARPPER_DECLARE(AdGrpcService, GetAdResult, AdRequest, AdResponse);
  GRPC_WARPPER_DECLARE(FansTopNearByGrpcService, GetFansTopResult, FansTopRequest, FansTopResponse);
  GRPC_WARPPER_DECLARE(FansTopGrpcService, GetFansTopResult, FansTopRequest, FansTopResponse);
  GRPC_WARPPER_DECLARE(FansTopFollowService, GetFansTopFollowFeed,
    FollowFanstopRequest, FollowFanstopResponse);
  GRPC_WARPPER_DECLARE(AdSocialFollowRecoService, GetAdSocialFollowReco,
    FollowSocialRequest, FollowSocialResponse);
  GRPC_WARPPER_DECLARE(AdUserProfileService, GetProfile, AdUserProfileRequest, AdUserProfileResponse);
  GRPC_WARPPER_DECLARE(AdUserProfileService, GetKnewsUid, AdUserProfileRequest, AdUserProfileResponse);

  #undef GRPC_WARPPER_DECLARE
};

}  // namespace front_server
}  // namespace ks
