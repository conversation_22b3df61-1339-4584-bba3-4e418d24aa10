#include "teams/ad/front_server_universe/util/universe_client_limiter_controller/universe_client_limiter_controller.h"

#include "base/time/timestamp.h"

#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/engine_base/client_limiter/client_limiter.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"

namespace ks {
namespace front_server {
bool UniverseClientLimiterController::Allow(const std::string& who) {
  uint32_t service_instance_num = ks::ad_base::AdKessClient::Instance().GetServiceInstanceNum();
  auto iter = FrontKconfUtil::universeClientLimitRatio()->find(who);
  if (iter != FrontKconfUtil::universeClientLimitRatio()->end()) {
      service_instance_num *= iter->second;
  }
  bool allow = ks::engine_base::ClientLimiterWrapper::Allow(
      who, service_instance_num);
  if (!allow) {
    last_disallow_us_ = base::GetTimestamp();
    return false;
  }
  return true;
}
}  // namespace front_server
}  // namespace ks
