#pragma once

#include <stdint.h>
#include <string>

#include "base/common/basic_types.h"

namespace ks {
namespace front_server {
class UniverseClientLimiterController {
 public:
  static UniverseClientLimiterController* Instance() {
    static UniverseClientLimiterController tmp;
    return &tmp;
  }
  bool Allow(const std::string& who);
  int64_t GetLastDisallowUS() { return last_disallow_us_; }

 private:
  int64_t last_disallow_us_{0};

 private:
  UniverseClientLimiterController() {}
  DISALLOW_COPY_AND_ASSIGN(UniverseClientLimiterController);
};
}  // namespace front_server
}  // namespace ks
