#pragma once

#include "teams/ad/ad_base/src/perf/perf.h"

#define PERF_INTERVAL(count, subtag, ...)                                               \
  do {                                                                                  \
    ks::ad_base::AdPerf::IntervalLogStash(count, "ad.ad_front", subtag, ##__VA_ARGS__); \
  } while (0)

#define PERF_COUNT(count, subtag, ...)                                               \
  do {                                                                               \
    ks::ad_base::AdPerf::CountLogStash(count, "ad.ad_front", subtag, ##__VA_ARGS__); \
  } while (0)

#define PERF_SET(count, subtag, ...)                                               \
  do {                                                                             \
    ks::ad_base::AdPerf::SetLogStash(count, "ad.ad_front", subtag, ##__VA_ARGS__); \
  } while (0)
