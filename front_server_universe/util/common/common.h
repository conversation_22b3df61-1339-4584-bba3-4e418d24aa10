#pragma once

#include "base/common/basic_types.h"
#include "teams/ad/ad_base/src/common/common.h"

namespace ks {
namespace front_server {

// 未登录用户生成 mock user_id 偏移量， mock_user_id = offset + gid
// 参考 wiki: https://wiki.corp.kuaishou.com/pages/viewpage.action?pageId=237288048
static constexpr uint64 kUnlogInUserIdOffSet = 300UL * 10000UL * 10000UL * 10000UL;
static constexpr uint64 kUnlogInNebulaUserIdOffSet = 350UL * 10000UL * 10000UL * 10000UL;
static constexpr uint64 kUnlogInFakeUserIdOffSet = 500UL * 10000UL * 10000UL * 10000UL;
static constexpr uint64 kUnlogInNebulaFakeUserIdOffSet = 550UL * 10000UL * 10000UL * 10000UL;

enum ClientId {
  UNKNOWN = 0,
  IPHONE,
  ANDROID,
  WEB,
  SERVER_NULL,
  WINPHONE,
  PC,
  WWW,
  IPHONE_LIVE_MATE,
  ANDROID_LIVE_MATE,
  ANDROID_COSMIC_VIDEO,
  IPHONE_COSMIC_VIDEO,
  CLIENT_ID_NUM,
};

enum AppType {
  IOS,  // Iphone 海外和国内是一个版本
  ANDROID_CN,  // Android 国内版
  ANDROID_PRO,  // Android 海外 pro 版，和国内版包名一样，但单独发版，版本号区分开
  ANDROID_LITE,  // Android 海外 lite 版
};

enum AdPosAction {
  kDefaultAction = 0,
  kSlideUp,
  kSlideDown,
  kSlideLeft,
  kSlideRight,
  kFirstRefresh = 20,
};

// 正排过滤原因
enum InvalidType {
  VALID = 0,
  EMPTY_ACCOUNT = 1,
  EMPTY_CAMPAIGN = 2,
  EMPTY_UNIT = 3,
  EMPTY_CREATIVE = 4,
  EMPTY_TARGET = 5,
  ACCOUNT_ID_LESS_ZERO = 6,
  CAMPAIGN_ID_LESS_ZERO = 7,
  UNIT_ID_LESS_ZERO = 8,
  CREATIVE_ID_LESS_ZERO = 9,
  INVALID_LIVE_STREAM_USER_INFO = 10,
  INVALID_FANSTOP_CAMPAIGN_SUPPORT_INFO = 11,
  INVALID_FANSTOP_CREATIVE_FEATURE = 12,
  TARGET_CHECK_SCHEDULE_INVALID = 13,
  TARGET_CHECK_REGION_INVALID = 14,
  REQUEST_FAILED = 15,
  FORWARD_INDEX_RESP_NOT_FOUND = 16,
  TARGET_CHECK_AGE_INVALID = 17,
  TARGET_CHECK_GENDER_INVALID = 18,
  TARGET_CHECK_PLATFORM_INVALID = 19,
  TARGET_CHECK_LIVE_STATUS_INVALID = 20,
  TARGET_CHECK_EXCLUDE_POPULATION_INVALID = 21,
  INVALID_APP_RELEASE = 22,
  TARGET_CHECK_DISTANCE_INVALID = 23
};

enum ReqAdapterCode {
  kAdapterSuccess = 0,    // 格式化成功
  kNullPointerError = 1,  // 参数空指针
  kGetPosError = 2        // 获取 pos_id 失败
};

// 回调点流量场景, 后续可以放到 proto 里
enum FrontServerScene {
  EXPLORE  = 100001,
  FOLLOW   = 100002,
  DETAIL   = 100003,
  NEARBY   = 100004,
  SPLASH   = 100005,
  GALAXY   = 100006,
  UNIVERSE = 100007,
  SEARCH   = 100008,
  MERCHANT = 100009,
  TRACEAPI = 100010,
};

// 联盟系统补贴类型
enum UniverseSystemBonusTag {
  GENERAL_BONUS_EXPLORE = 1,   //  通用广告叉乘流量顶价探索
  UNFILLED_FLOW_EXPLORE = 2,   //  未填充流量探索
  DARK_UNFILLED_EXPLORE = 3,   //  暗投顶价未填充流量探索
  PRODUCT_BONUS_EXPLORE = 4,   //  iaa 类产品补贴
};

// 广告库类型
enum AdDBType {
  DSP = 1,
  FANSTOP = 2,  // 粉条（新旧粉条+速推）
  BRAND = 3,   // 品牌广告
  MAX = 4
};

enum RecommendType {
  NO_RECOMMEND = 0,
  DEFAULT_RECOMMEND = 1,
  NEW_USER_RECOMMEND = 2
};

enum class DegradeLevel: int32_t {
  kLevel1 = 1,
  kLevel2 = 2,
  kLevelDefault = 3
};

// url macro place holders
constexpr char kWinPriceKeywords[] = "${WIN_PRICE}";
constexpr char kFirstPriceKeywords[] = "${FIRST_PRICE}";
constexpr char gdtWinPriceKeywords[] = "__AUCTION_PRICE__";
constexpr char kDeviceIdwords[] = "__DEVICEID__";
constexpr char kUserIdWords[] = "__USERID__";
constexpr char kImeiKeywords[] = "__IMEI__";
constexpr char kIdfaKeywords[] = "__IDFA__";
constexpr char kCaidKeywords[] = "__KENYID_CAA__";
constexpr char kTimstampKeyWords[] = "__TS__";
constexpr char kCSITEKeyWords[] = "__CSITE__";  // 广告场景 resource_id 宏替换
constexpr char kAIDKeyWords[] = "__AID__";  // unit id
constexpr char kCIDKeyWords[] = "__CID__";  // creative id
constexpr char kSplashCIDKeyWords[] = "__REQUESTCREATIVEID__";  // splash creative id
constexpr char kDIDKeyWords[] = "__DID__";  // campaign id
constexpr char kDNAMEKeyWords[] = "__DNAME__";  // campaign name
constexpr char kOAIDKeyWords[] = "__OAID__";
constexpr char kVIDKeyWords[] = "__VID__";  // exp id
constexpr char kRequestKeyword[] = "__REQUESTID__";
constexpr char kRtaCpaValidKeyword[] = "__RTACPAVALID__";
constexpr char kAccountKeyword[] = "__ACCOUNTID__";
constexpr char kBrandIdKeyword[] = "__BRAND__";
constexpr char kMediaIndustryKeyword[] = "__MEDIA_INDUSTRY__";
constexpr char kProductIdKeyWords[] = "__PRODID__";  // product id
constexpr char kACCREATIVEKeywords[] = "__AC_CREATIVE__";  // 高级创意标识
constexpr char kTraceReqIdKeyWords[] = "__TRACEREQID__";  // rta trace request id
constexpr char kRtaBidKeyWords[] = "__RBID__";  // rta bid strategy id
constexpr char kRtaFeatureIdKeyWords[] = "__FEATUREID__";  // rta feature id
constexpr char kIdfa2KeyWords[] = "__IDFA2__";
constexpr char kImei2KeyWords[] = "__IMEI2__";
constexpr char kAndroidIdKeyWords[] = "__ANDROIDID__";
constexpr char kAndroidId2KeyWords[] = "__ANDROIDID2__";
constexpr char kOSKeyWords[] = "__OS__";
constexpr char kIPKeyWords[] = "__IP__";
constexpr char kUAKeyWords[] = "__UA__";
constexpr char kOAID2KeyWords[] = "__OAID2__";
constexpr char kWinfoIdKeyWords[] = "__WINFOID__";  // winfo id
constexpr char kAliOuterDCKeyWords[] = "__DCFLAG__";
constexpr char kTouchTypeKeyWords[] = "__touch_type__";
constexpr char kWechatSmallGameUrlProtocol[] = "__DEEP_LINK_PROTOCOL__";    // 微信小游戏直调协议
constexpr char kRtaBidRatio[] = "__RTABIDRATIO__";    // rta bid ratio
constexpr char kBidWord[] = "__BID_WORD__";  // bidword
constexpr char kBidWordId[] = "__BID_WORD_ID__";  // bidword_id
constexpr char kSearchQuery[] = "__QUERY__";  // query
constexpr char kDPADynamicParm[] = "__KS_DPA_DYNAMIC_PARM__";  // dpa dynamic params
constexpr char kAggSiteId[] = "__AGG_SITE_ID__";  // 聚合落地页 id
constexpr char kLandingPageId[] = "__LP_ID__";  // lp_id

// exp_tag reason 部分
static constexpr int32_t kNativeAdReason = 57;
static constexpr int32_t kNewInnerMerchantPhoto = 38340;
static constexpr int32_t kNewInnerMerchantLive = 61490;

// other common params
static constexpr char kUserHistoryRequestKeyPrefix[] = "uhrts_";
static constexpr char kUserHistoryRequestRedisName[] = "adUserRequestTimestamp";
}  // namespace front_server
}  // namespace ks
