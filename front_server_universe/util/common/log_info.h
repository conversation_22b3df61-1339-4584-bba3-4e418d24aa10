#pragma once

#include <ostream>

#include "teams/ad/ad_proto/kuaishou/ad/front_service.pb.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_base.h"

namespace ks {
namespace front_server {

std::ostream& operator<<(std::ostream& os, const ks::ad_base::PosManagerBase& pos_manager) {
  os << "|ad_request_type: " << pos_manager.GetAdRequestType()
     << "|creative_template_types:[";
  for (auto& imp_info : pos_manager.request_imp_infos) {
    os << imp_info.ToString();
  }
  os << "]";
  return os;
}

inline std::ostream& operator<<(std::ostream& os, const AdRequest& ad_request) {
  auto& ad_user_info = ad_request.ad_user_info();
  os << "|user_id:" << ad_user_info.id()
    << "|real_id:" << ad_request.universe_ad_request_info().real_user_id()
    << "|ad_request_type:" << ad_request.ad_request_type()
    << "|device_id:" << ad_user_info.device_id()
    << "|browsed_set:" << ad_request.reco_user_info().browsed_photo_ids_size()
    << "|platform:" << ad_user_info.platform()
    << "|platform_version:" << ad_user_info.platform_version()
    << "|gender:" << ad_user_info.gender()
    << "|age:" << ad_user_info.age()
    << "|city_region:" << ad_request.ad_user_info().region()
    << "|region:" << ad_user_info.region()
    << "|network:" << ad_user_info.network()
    << "|page:" << ad_user_info.page()
    << "|language:" << ad_user_info.language()
    << "|user_region: " << ad_user_info.region()
    << "|imp_info_size:" << ad_request.universe_ad_request_info().imp_info_size()
    << "|interests:";
  for (auto& interest : ad_user_info.interest()) {
    os << interest << ",";
  }

  auto audience_greater = [](uint64_t audience) {return audience > 0;};
  int audience_size = std::count_if(ad_user_info.audience().begin(), ad_user_info.audience().end(),
      audience_greater);
  int paid_audience_size = std::count_if(ad_user_info.tp_paid_audience().begin(),
      ad_user_info.tp_paid_audience().end(), audience_greater);
  os << "|audience_size:" << audience_size
     << "|paid_audience_size:" << paid_audience_size
     << "|ad_browsed_ads_num_v2:" << ad_user_info.ad_browsed_info_size()
     << "|browsed_ads:";
  if (FrontKconfUtil::enableLogBrowseAd()) {
    for (auto& item : ad_user_info.ad_browsed_info()) {
      os << "<timestamp:" << (item.timestamp() / 1000) << ">browsed::";
      for (auto& ad : item.ad_detail_info()) {
        os << ad.creative_id() << ",";
      }
    }
  }

  return os;
}

std::ostream& operator<<(std::ostream& os, const kuaishou::fanstop::FansTopResult& result) {
  os << result.fanstop_unit_info().ShortDebugString();
  return os;
}

std::ostream& operator<<(std::ostream& os, const kuaishou::ad::AdResult& result) {
  auto& ad_deliver_info = result.ad_deliver_info();
  auto& ad_base_info = ad_deliver_info.ad_base_info();
  os << "|creative_id:" << ad_base_info.creative_id()
     << "|unit_id:" << ad_base_info.unit_id()
     << "|target_id:" << ad_base_info.target_id()
     << "|photo_id:" << ad_base_info.photo_id()
     << "|unit_type:" << ad_base_info.unit_type()
     << "|material_id:" << ad_base_info.material_id()
     << "|cover_id:" << ad_base_info.cover_id()
     << "|ad_source_type: " << result.ad_source_type()
     << "|predict_charge:" << ad_deliver_info.predict_charge()
     << "|price:" << ad_deliver_info.price()
     << "|pos:" << ad_deliver_info.pos()
     << "|material_info: " << ad_base_info.material_info().ShortDebugString()
     << "|url: " << ad_base_info.url()
     << "|cpm: " << ad_deliver_info.cpm()
     << "|brand_pos: " << ad_deliver_info.universe_ad_deliver_info().pos_id();
  return os;
}

std::ostream& operator<<(std::ostream& os, const kuaishou::ad::FrontServerResponse& response) {
  if (response.type() == kuaishou::ad::EXPLORE_REQUEST) {
    auto& explore_resp = response.explore_response();
    os << "|ad_num<ad_dsp:" << explore_resp.ad_dsp_size()
       << ",dsp_template:" << explore_resp.dsp_template_size() << ">"
       << "|ad_dsp_result:";
    if (explore_resp.ad_dsp_size() > 0) {
      os << "<pos:" << explore_resp.ad_dsp(0).pos()
         << explore_resp.ad_dsp(0).ad_result()
         << ">";
    }
    os << "|dsp_template:";
    if (explore_resp.dsp_template_size() > 0) {
      os << "<pos:" << explore_resp.dsp_template(0).pos()
         << explore_resp.dsp_template(0).ad_result()
         << ">";
    }
  } else if (response.type() == kuaishou::ad::FOLLOW_REQUEST) {
    auto& follow_resp = response.follow_response();
    os << "|follow_resp:" << follow_resp.ShortDebugString();
  } else if (response.type() == kuaishou::ad::NEARBY_REQUEST) {
    auto& nearby_resp = response.nearby_response();
    os << "|ad_num<ad_dsp:" << nearby_resp.ad_dsp_size() << ">"
       << "|ad_dsp_result:";
    if (nearby_resp.ad_dsp_size() > 0) {
      os << "<pos:" << nearby_resp.ad_dsp(0).pos()
         << nearby_resp.ad_dsp(0).ad_result()
         << ">";
    }
    os << "|ad_fanstop_result:";
  }
  return os;
}

}  // namespace front_server
}  // namespace ks

