#pragma once
#include <memory>
#include <string>
#include <utility>
#include "base/common/basic_types.h"
#include "ks/base/perfutil/perfutil.h"
#include "teams/ad/3rdparty/hphp/concurrent_lru_cache.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
namespace ks {
namespace front_server {
class UniverseProfitOptimize {
 public:
  static UniverseProfitOptimize& Instance() {
    static UniverseProfitOptimize inst;
    return inst;
  }
  bool Put(const std::string& key, const std::pair<int64_t, int64_t>& val) {
    if (!cache_) {
      return false;
    }
    CacheItem item;
    item.value = val;
    item.timestamp = base::GetTimestamp();
    return cache_->insert_or_assign(key, item);
  }
  bool Get(const std::string& key, int64_t ttl_sec, std::pair<int64_t, int64_t>& val,  // NOLINT
           const char** err) {
    if (!cache_) {
      *err = kBadInit;
      return false;
    }
    ConstAccessor ac;
    if (!cache_->find(ac, key)) {
      *err = kUnFound;
      return false;
    }
    if (ac.empty() || ((base::GetTimestamp() - ac->timestamp) / 1000000 > ttl_sec)) {
      *err = kExpired;
      ac.release();
      cache_->erase(key);
      return false;
    }
    val.first = ac->value.first;
    val.second = ac->value.second;
    return true;
  }
  ~UniverseProfitOptimize() {}

 private:
  struct CacheItem {
    std::pair<int64_t, int64_t> value;
    int64_t timestamp;
  };
  using LRUCache = ad_3rd::ConcurrentLRUCache<std::string, CacheItem>;
  using ConstAccessor = LRUCache::ConstAccessor;
  static constexpr char kBadInit[] = "BadInit";
  static constexpr char kUnFound[] = "KeyNotFound";
  static constexpr char kExpired[] = "Expired";
  static constexpr char kParseErr[] = "ParseErr";

 private:
  std::unique_ptr<LRUCache> cache_;
  UniverseProfitOptimize() {
    auto capacity = FrontKconfUtil::UniverseAdnProfitCacheCapacity();
    if (capacity <= 0) {
      capacity = 200000;
    }
    cache_ = std::make_unique<LRUCache>(capacity);
  }
  DISALLOW_COPY_AND_ASSIGN(UniverseProfitOptimize);
};
}  // namespace front_server
}  // namespace ks
