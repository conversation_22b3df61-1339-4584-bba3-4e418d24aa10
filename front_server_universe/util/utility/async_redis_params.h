#pragma once
#include <string>
#include <algorithm>
#include <vector>
#include "teams/ad/ad_base/src/redis/kconf_redis.h"

namespace ks {
namespace front_server {
class UniverseData;
class ContextData;
class BaseParams {
 public:
  void SetDependDataParams(ad_base::DependDataParams dparams) { dparams_ = dparams; }
  void SetIsRedisClientGetSucc(bool succ) { succ_ = succ; }
  std::string GetName() { return name_; }
  std::string GetTable() { return table_; }
  std::string GetKey() { return key_; }
  std::vector<std::string> GetMultiKey() { return multi_key_; }
  int32_t GetTimeout() { return timeout_; }
  int64_t GetRange() { return range_; }
  bool GetRedisClientStatus() { return succ_; }
  bool IsAllow() { return is_allow_; }
  ad_base::DependDataParams GetDependDataParams() { return dparams_; }
  void Clear() {
    name_.clear();
    table_.clear();
    multi_key_.clear();
    key_.clear();
    universe_session_ = nullptr;
    dparams_ = ad_base::DependDataLevel::WEAK_DEPEND;
    timeout_ = 10;
    range_ = 0;
    is_allow_ = false;
    succ_ = true;
  }
  void Init(ContextData* session_data);

 protected:
  virtual void SetTable(ContextData* session_data) = 0;
  virtual void SetKey(ContextData* session_data) = 0;
  virtual void SetTimeout(ContextData* session_data) = 0;
  virtual void SetIsRedisAllow(ContextData* session_data) = 0;
  std::string name_;
  std::string table_;
  std::string key_;
  std::vector<std::string> multi_key_;
  UniverseData* universe_session_ = nullptr;
  ad_base::DependDataParams dparams_{ad_base::DependDataLevel::WEAK_DEPEND};
  int32_t timeout_{10};   // ms
  int64_t range_{0};      // 当使用 zset 时，需要 range
  bool is_allow_{false};  // 准入：用于判断当前 redis 是否要使用
  // 当异步框架获取该 param 的 redis client 失败时, 设置该值为 false, 后续 wait 不再操作, 默认 true
  bool succ_{true};
};

#define ASYNC_REDIS_PARAM_REGISTER(class_name)                \
  class class_name : public BaseParams {                      \
   private:                                                   \
    void SetTable(ContextData* session_data) override;        \
    void SetKey(ContextData* session_data) override;          \
    void SetTimeout(ContextData* session_data) override;      \
    void SetIsRedisAllow(ContextData* session_data) override; \
  };

ASYNC_REDIS_PARAM_REGISTER(ConvertedUnitParams);
ASYNC_REDIS_PARAM_REGISTER(UniverseClickRewardFrequency);
ASYNC_REDIS_PARAM_REGISTER(UniverseCarouselParams);
ASYNC_REDIS_PARAM_REGISTER(UniverseFirePokeParams);
ASYNC_REDIS_PARAM_REGISTER(UniverseLlmU2pParams);
ASYNC_REDIS_PARAM_REGISTER(UniverseLlmU2pV2Params);
// [xiaowentao] 主站用户价值分层 w_level 和联盟活跃分层
ASYNC_REDIS_PARAM_REGISTER(UniverseActiveWLevelParams);
ASYNC_REDIS_PARAM_REGISTER(UniverseCpmBoundParams);
ASYNC_REDIS_PARAM_REGISTER(UniverseRtbCpmBidCoffParams);
ASYNC_REDIS_PARAM_REGISTER(UniverseInteractiveStyleFrequency);
ASYNC_REDIS_PARAM_REGISTER(UniverseFeatureInterval);
ASYNC_REDIS_PARAM_REGISTER(AdBrowsedInfoParams);
ASYNC_REDIS_PARAM_REGISTER(AdBrowsedInfoParamsSimple);
ASYNC_REDIS_PARAM_REGISTER(DebugInfoParams);
ASYNC_REDIS_PARAM_REGISTER(UniverseInnerLoopUserTagParams);
ASYNC_REDIS_PARAM_REGISTER(UniverseDeliveredQponInfoParams);
ASYNC_REDIS_PARAM_REGISTER(UniverseDeliveredQponCntParams);
ASYNC_REDIS_PARAM_REGISTER(UniverseLiveReservationInfoParams);
ASYNC_REDIS_PARAM_REGISTER(InnerStrategyCrowdParams);  // 内循环人群数据
ASYNC_REDIS_PARAM_REGISTER(UniverseUserSceneFreqParams);  // 联盟用户在场景上的请求频次信息
ASYNC_REDIS_PARAM_REGISTER(UniverseTinyUserQueryRecalledProduct);  // 联盟小系统用户近期检索返回的 product id
ASYNC_REDIS_PARAM_REGISTER(UniverseTinyUserQueryRecalledProductLonger);  // 联盟小系统用户近 7 日 product id
ASYNC_REDIS_PARAM_REGISTER(AdCreditUserScoreParams);  // 用户信任分
ASYNC_REDIS_PARAM_REGISTER(InnerTriggerItemHetuTagParams);  // 内流 trigger item 对应的河图标签
ASYNC_REDIS_PARAM_REGISTER(UniverseComponentizedStyleFreq);  // 联盟样式组件化组件曝光频率
ASYNC_REDIS_PARAM_REGISTER(UniverseEcpmWinExploreParams);  // 用户高 rank 竞败数据
}  // namespace front_server
}  // namespace ks
