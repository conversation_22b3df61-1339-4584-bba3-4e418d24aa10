#pragma once

#include <string>

#include "teams/ad/ad_proto/kuaishou/ad/bid_server/auction_log.pb.h"
#include "teams/ad/front_server_universe/engine/context_data/ad_common.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"

namespace ks {
namespace front_server {

struct AuctionHelper {
 public:
  static bool BuildAuctionAdList(AdList* ad_list, ContextData* session_data);
  static void RebuildAdResponse(AdResponse* ad_response, AdList* ad_list, ContextData* session_data,
                                const std::string& type);
};

}  // namespace front_server
}  // namespace ks
