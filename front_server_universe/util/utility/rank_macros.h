#pragma once
#include "teams/ad/ad_base/src/perf/perf.h"

#define PERF_EVERY_N_VARNAME(base, line) PERF_EVERY_N_VARNAME_CONCAT(base, line)
#define PERF_EVERY_N_VARNAME_CONCAT(base, line) base ## line
#define PERF_OCCURRENCES PERF_EVERY_N_VARNAME(perf_, __LINE__)
#define RANK_PERF_STATS(Value, NameSpace, Subtag, ...) \
  static int PERF_OCCURRENCES; \
  ++PERF_OCCURRENCES; \
  if (PERF_OCCURRENCES > 10) \
  PERF_OCCURRENCES -= 10; \
  if (PERF_OCCURRENCES == 1) \
  ks::ad_base::AdPerf::IntervalLogStash(Value, NameSpace, Subtag, ##__VA_ARGS__);
#define RANK_DOT_COUNT(context, Value, Subtag, ...) \
  static int PERF_OCCURRENCES; \
  ++PERF_OCCURRENCES; \
  if (PERF_OCCURRENCES > 10) \
  PERF_OCCURRENCES -= 10; \
  if (PERF_OCCURRENCES == 1) \
  context->dot_perf->Count(Value, Subtag, ##__VA_ARGS__);
#define RANK_DOT_STATS(context, Value, Subtag, ...) \
  static int PERF_OCCURRENCES; \
  ++PERF_OCCURRENCES; \
  if (PERF_OCCURRENCES > 10) \
    PERF_OCCURRENCES -= 10; \
  if (PERF_OCCURRENCES == 1) \
    context->dot_perf->Interval(Value, Subtag, ##__VA_ARGS__);

#define DRAGON_PERF_STATS_EVERY_N(Mod, Value, NameSpace, Subtag, ...) \
    { \
    static_assert(Mod > 0); \
    static int PERF_OCCURRENCES; \
    ++PERF_OCCURRENCES; \
    if (PERF_OCCURRENCES > Mod) \
      PERF_OCCURRENCES -= Mod; \
    if (PERF_OCCURRENCES == 1) \
      ks::ad_base::AdPerf::IntervalLogStash(Value, NameSpace, Subtag, ##__VA_ARGS__); \
    }
