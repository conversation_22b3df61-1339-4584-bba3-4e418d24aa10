#pragma once

#include "glog/logging.h"

namespace ks {
namespace front_server {

extern thread_local int64_t local_llsid;

}  // namespace front_server
}  // namespace ks

#define TLOG(severity) LOG(severity) << local_llsid << "] "
#define TLOG_IF(severity, condition) LOG_IF(severity, condition) << local_llsid << "] "
#define TLOG_EVERY_N(severity, n) LOG_EVERY_N(severity, n) << ks::front_server::local_llsid << "] "
