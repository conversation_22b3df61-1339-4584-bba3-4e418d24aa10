#pragma once

#include <vector>
#include <string>
#include <utility>
#include <unordered_map>
#include <unordered_set>

#include "base/common/basic_types.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"

namespace ks {
namespace front_server {

// 负责 Tracking 链接的填写
class Tracking {
 public:
  static std::string AddCallbackParam(const std::string track_url);
  static kuaishou::ad::AdTrackInfo::TrackUrlOperationType GetTrackOperationType(const std::string track_url,
      int64_t account_id);
};

}  // namespace front_server
}  // namespace ks
