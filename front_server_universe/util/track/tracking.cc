#include "teams/ad/front_server_universe/util/track/tracking.h"

#include <vector>
#include <unordered_map>
#include <utility>

#include "base/strings/string_split.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "ks/serving_util/dynamic_config.h"
#include "ks/util/json.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"

namespace ks {
namespace front_server {

std::string Tracking::AddCallbackParam(const std::string track_url) {
  Json *callback_map = DynamicJsonConfig::GetConfig()->Get("callback_map");
  if (!callback_map) return track_url;
  std::string result_url = track_url;
  for (auto it = callback_map->object_begin(); it != callback_map->object_end(); ++it) {
    const std::string& domain = it->first;
    std::string callback_param = it->second->StringValue();

    if (track_url.find(domain) != std::string::npos) {
      bool need_insert_symbol = false;
      std::vector<std::string> params;
      if (callback_param.find("*") == 0 || callback_param.find("&") != std::string::npos) {
        need_insert_symbol = true;
      }
      if (callback_param.find("*") == 0) {
        callback_param = callback_param.substr(1, callback_param.length() - 1);
      }
      if (need_insert_symbol) {
        if (track_url.find("?") == std::string::npos && track_url.find("&") == std::string::npos) {
          result_url += "?";
        } else {
          result_url += "&";
        }
      }
      base::SplitString(callback_param, "&", &params);
      for (int i = 0; i < params.size(); i ++) {
        if (track_url.find(params[i]) == std::string::npos) {
          result_url += params[i] + "&";
        }
      }
      int size = result_url.size();
      if (size > 0 && result_url[size - 1] == '&') {
        result_url = result_url.substr(0, result_url.size() - 1);
      }
      break;
    }
  }

  return result_url;
}

kuaishou::ad::AdTrackInfo::TrackUrlOperationType Tracking::GetTrackOperationType(const std::string track_url,
    int64_t account_id) {
  auto operation_origin_account_set = ks::front_server::FrontKconfUtil::operationOriginAccountSet();
  if (operation_origin_account_set->find(account_id) != operation_origin_account_set->end()) {
    falcon::Inc(absl::Substitute("ad_server.operation_origin_account_$0", account_id).c_str());
    return kuaishou::ad::AdTrackInfo::OPERATION_ORIGIN;
  }
  Json *operation_type = DynamicJsonConfig::GetConfig()->Get("track_operation_type");
  if (!operation_type) return kuaishou::ad::AdTrackInfo::UNKNOWN_OPERATION_TYPE;
  for (auto it = operation_type->object_begin(); it != operation_type->object_end(); ++it) {
    std::string domain = it->first;
    int type = it->second->IntValue(1);

    if (track_url.find(domain) != std::string::npos) {
      switch (type) {
        case 1:
          return kuaishou::ad::AdTrackInfo::OPERATION_EMPTY;
        case 2:
          return kuaishou::ad::AdTrackInfo::OPERATION_ORIGIN;
        default:
          return kuaishou::ad::AdTrackInfo::OPERATION_EMPTY;
      }
    }
  }
  return kuaishou::ad::AdTrackInfo::OPERATION_EMPTY;
}

}  // namespace front_server
}  // namespace ks
