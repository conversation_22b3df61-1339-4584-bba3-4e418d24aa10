
#pragma once
#include <vector>
#include "teams/ad/ad_proto/kuaishou/ad/front_service.kess.grpc.pb.h"
namespace ks {
namespace front_server {
class UTCUtil {
 public:
  static uint64_t fnv_hash_two_uint64(uint64_t value1, uint64_t value2) {
    // http://isthe.com/chongo/tech/comp/fnv/
    const unsigned char* p1 = reinterpret_cast<unsigned char*>(&value1);
    const unsigned char* p2 = reinterpret_cast<unsigned char*>(&value2);
    // 2166136261 * 16777619
    uint64_t hash = 14695981039346656037ULL;
    for (size_t i = 0; i < sizeof(uint64_t); ++i) {
      hash = (hash * 1099511628211ULL) ^ p1[i];
    }
    for (size_t i = 0; i < sizeof(uint64_t); ++i) {
      hash = (hash * 1099511628211ULL) ^ p2[i];
    }
    return hash;
  }
  static uint64_t get_hash_key(const kuaishou::ad::FrontServerRequest& req) {
    switch (req.request_hash().key_type()) {
    case kuaishou::ad::AdReqHashKey::UNKNOWN_ID:
    case kuaishou::ad::AdReqHashKey::RANDOM_ID:
      return 0;
    default:
      break;
    }
    uint64_t other_id = 0;
    if (req.universe_request().imp_info().ad_pos_info_size() > 0) {
      other_id = req.universe_request().imp_info().ad_pos_info(0).pos_id();
    }
    if (other_id == 0) {
      return 0;
    }
    return fnv_hash_two_uint64(req.request_hash().key_id(), other_id);
  }
};
}  // namespace front_server
}  // namespace ks
