
#pragma once
#include <stdint.h>
#include <memory>
#include <string>
#include <vector>
#include "absl/container/flat_hash_map.h"
#include "teams/ad/ad_base/src/admit/admit.h"
#include "teams/ad/ad_base/src/better_enums/better_enums.h"
#include "teams/ad/ad_base/src/clock_cache/expired_clock_cache.h"
#include "teams/ad/ad_base/src/kess/macros.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_user_profile_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/front_service.pb.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "teams/ad/front_server_universe/util/universe_traffic_control/utc_util.h"
#include "teams/ad/ad_base/src/perf/perf.h"
namespace ks {
namespace front_server {
BETTER_ENUM(UniverseGenericFlowControlType,
            int32_t,
            UNKNOWN = 0,
            UNIVERSE_ANTISPAM = 1,
            UNIVERSE_REQUEST_QUALITY_RECKON = 2,
            UNIVERSE_USERPROFILE = 3)
static const size_t UGFC_QPS_MARK_SIZE{100};
static const size_t UGFC_QPS_MARK_STEP{5};  // 以 UGFC_QPS_MARK_STEP  秒为一个单位，方便做平滑
static const size_t ARENA_MARK_SIZE{4};     //  Arean 数组的 size
class PidRatioUpdate {
 public:
  static PidRatioUpdate& Instance() {
    static PidRatioUpdate tmp;
    return tmp;
  }
  ~PidRatioUpdate();

  void IncPV(const int32_t& type, int64_t current_second) {
    auto it = qps_mark_map_.find(type);
    if (it != qps_mark_map_.end()) {
      it->second[(current_second / UGFC_QPS_MARK_STEP) % UGFC_QPS_MARK_SIZE]++;
    } else {
      LOG_EVERY_N(ERROR, 10000) << "IncPV unsupport type:" << type;
    }
  }

  const double GetAdjustRatio(int32_t type) const {
    auto it = adjust_ratio_map_.find(type);
    if (it != adjust_ratio_map_.end()) {
      return it->second;
    } else {
      LOG_EVERY_N(ERROR, 10000) << "GetAdjustRatio unsupport type:" << type;
    }
    return 0.0;
  }

 private:
  PidRatioUpdate();
  void UpdateAdjustRatio();
  void UpdateSingleAdjustRatio(const UniverseGenericFlowControlType& type);  // NOLINT

 private:
  // 后台线程同步相关
  std::thread* update_ratio_thread_{nullptr};
  bool stop_{false};
  std::mutex mtx_;
  std::condition_variable cv_;

  // 在线决策相关
  absl::flat_hash_map<int32_t, std::vector<std::atomic<int64_t>>> qps_mark_map_;  //  保存近段时间的 QPS 数据
  absl::flat_hash_map<int32_t, volatile double> adjust_ratio_map_;                //  保存 Ratio 数据
};
template <class K> class UGFCKeyType {
 public:
  int64_t operator()(const K& k) const {
    LOG(FATAL) << "UGFCKeyType unsupport template call";
    return 0;
  }
};
template <> class UGFCKeyType<kuaishou::ad::FrontServerRequest> {
 public:
  int64_t operator()(const kuaishou::ad::FrontServerRequest& k) const {
    return UTCUtil::get_hash_key(k);
  }
};
template <> class UGFCKeyType<kuaishou::ad::AdUserProfileRequest> {
 public:
  int64_t operator()(const kuaishou::ad::AdUserProfileRequest& k) const {
    const auto& rh = k.request_hash();
    if ((rh.key_type() == kuaishou::ad::AdReqHashKey::UNKNOWN_ID ||
         rh.key_type() == kuaishou::ad::AdReqHashKey::RANDOM_ID)) {
      return 0;
    }
    return UTCUtil::fnv_hash_two_uint64(static_cast<int>(rh.key_type()), rh.key_id());
  }
};
template <class T, class K> class UniverseGenericFlowControl {
 public:
  static UniverseGenericFlowControl& Instance() {
    static UniverseGenericFlowControl tmp;
    return tmp;
  }
  bool IsTakeOver(const UniverseGenericFlowControlType& type);
  void Store(const UniverseGenericFlowControlType& type, const K& req, const T& t);
  bool Take(const UniverseGenericFlowControlType& type, const K& req,
            T& t);  // NOLINT

 private:
  struct CacheValue {
    T entity;
    int64_t store_time;
  };

 private:
  UniverseGenericFlowControl();
  ~UniverseGenericFlowControl();

 private:
  // Cache
  absl::flat_hash_map<int32_t, ks::ad_base::ExpiredClockCache<uint64_t, CacheValue>*> cache_map_;
  std::mutex cache_init_mtx_;

 private:
};
template <class T, class K> UniverseGenericFlowControl<T, K>::UniverseGenericFlowControl() {
  for (const auto& type : UniverseGenericFlowControlType::_values()) {
    cache_map_[type._to_integral()] = nullptr;
  }
}
template <class T, class K> UniverseGenericFlowControl<T, K>::~UniverseGenericFlowControl() {
}
template <class T, class K>
bool UniverseGenericFlowControl<T, K>::IsTakeOver(const UniverseGenericFlowControlType& type) {
  auto universe_generic_flow_control_config = FrontKconfUtil::universeGenericFlowControlConfig();
  const auto& config_map = universe_generic_flow_control_config->data().config_map();
  auto cm_it = config_map.find(type._to_string());
  return cm_it != config_map.end();
}
template <class T, class K>
void UniverseGenericFlowControl<T, K>::Store(const UniverseGenericFlowControlType& type,
                                             const K& req,
                                             const T& t) {
  auto universe_generic_flow_control_config = FrontKconfUtil::universeGenericFlowControlConfig();
  const auto& config_map = universe_generic_flow_control_config->data().config_map();
  auto cm_it = config_map.find(type._to_string());
  if (cm_it == config_map.end()) {  // kconf 没有配置的，不执行, 方便做降级
    return;
  }
  UGFCKeyType<K> key_type;
  auto key = key_type(req);
  if (!key) {
    return;
  }
  auto& cache_ptr = cache_map_[type._to_integral()];
  if (cache_ptr == nullptr) {
    std::unique_lock<std::mutex> lck(cache_init_mtx_);
    if (cache_ptr == nullptr) {
      cache_ptr = new ks::ad_base::ExpiredClockCache<uint64_t, CacheValue>();
      cache_ptr->Init(cm_it->second.max_cache_time() * cm_it->second.max_qps() /
                          ks::ad_base::AdKessClient::Instance().GetServiceInstanceNum(),
                      cm_it->second.max_cache_time());
    }
  }
  auto& cache = *cache_ptr;
  auto init_handler = [&](CacheValue* v) {
    if (v != nullptr) {
      v->entity = t;
      v->store_time = time(0);
    }
  };
  cache.Put(key, init_handler, init_handler);
}
template <class T, class K>
bool UniverseGenericFlowControl<T, K>::Take(const UniverseGenericFlowControlType& type,
                                            const K& req,
                                            T& t) {  // NOLINT
  auto current_second = time(0);
  const auto& type_integral = type._to_integral();
  const auto& type_string = type._to_string();
  auto universe_generic_flow_control_config = FrontKconfUtil::universeGenericFlowControlConfig();
  const auto& config_map = universe_generic_flow_control_config->data().config_map();
  auto cm_it = config_map.find(type._to_string());
  if (cm_it == config_map.end()) {  // kconf 没有配置的，不执行, 方便做降级
    return false;
  }
  bool is_get_cache_succeed = false;
  do {
    UGFCKeyType<K> key_type;
    auto key = key_type(req);
    if (!key) {
      continue;
    }
    auto& cache_ptr = cache_map_[type._to_integral()];
    if (cache_ptr == nullptr) {
      std::unique_lock<std::mutex> lck(cache_init_mtx_);
      if (cache_ptr == nullptr) {
        cache_ptr = new ks::ad_base::ExpiredClockCache<uint64_t, CacheValue>();
        cache_ptr->Init(cm_it->second.max_cache_time() * cm_it->second.max_qps() /
                            ks::ad_base::AdKessClient::Instance().GetServiceInstanceNum(),
                        cm_it->second.max_cache_time());
      }
    }
    auto& cache = *cache_ptr;

    auto adjust_ratio = PidRatioUpdate::Instance().GetAdjustRatio(type_integral);
    if (adjust_ratio < DBL_EPSILON) {
      continue;
    }
    auto value_call = [&](const CacheValue& v) {
      if (current_second <= v.store_time + cm_it->second.max_cache_time() * adjust_ratio) {
        t = v.entity;
        is_get_cache_succeed = true;
      }
    };
    cache.Get(key, value_call, false);
  } while (false);
  if (!is_get_cache_succeed) {
    PidRatioUpdate::Instance().IncPV(type_integral, current_second);
  }
  ks::ad_base::AdPerf::IntervalLogStash(1,
                                        "ad.ad_front",
                                        "universe_generic_flow_control",
                                        "cache_status",
                                        type_string,
                                        absl::StrCat(is_get_cache_succeed));
  return is_get_cache_succeed;
}
}  // namespace front_server
}  // namespace ks
