

#include <algorithm>
#include <memory>
#include <thread>

#include "teams/ad/front_server_universe/util/universe_traffic_control/universe_generic_flow_control.h"
#include "teams/ad/ad_base/src/perf/perf.h"
namespace ks {
namespace front_server {
PidRatioUpdate::PidRatioUpdate() {
  for (const auto& type : UniverseGenericFlowControlType::_values()) {
    qps_mark_map_[type._to_integral()] = std::vector<std::atomic<int64_t>>(UGFC_QPS_MARK_SIZE);
    for (int32_t i = 0; i < UGFC_QPS_MARK_SIZE; ++i) {
      qps_mark_map_[type._to_integral()][i] = 0;
    }
    adjust_ratio_map_[type._to_integral()] = 0;
  }

  update_ratio_thread_ = new std::thread(&PidRatioUpdate::UpdateAdjustRatio, this);
}
PidRatioUpdate::~PidRatioUpdate() {
  std::unique_lock<std::mutex> lck(mtx_);
  stop_ = true;
  cv_.notify_all();
  if (update_ratio_thread_) {
    update_ratio_thread_->join();
    delete update_ratio_thread_;
  }
}
void PidRatioUpdate::UpdateAdjustRatio() {
  while (!stop_) {
    std::unique_lock<std::mutex> lck(mtx_);
    cv_.wait_for(lck, std::chrono::seconds(UGFC_QPS_MARK_STEP));
    for (const auto& type : UniverseGenericFlowControlType::_values()) {
      UpdateSingleAdjustRatio(type);
    }
  }
}
void PidRatioUpdate::UpdateSingleAdjustRatio(const UniverseGenericFlowControlType& ugfc_type) {
  if (!ugfc_type) {
    return;
  }
  auto instance_num = ks::ad_base::AdKessClient::Instance().GetServiceInstanceNum();
  if (instance_num == 0) {
    return;
  }
  const std::string type_string = ugfc_type._to_string();
  const int type_integral = ugfc_type._to_integral();

  auto universe_generic_flow_control_config = FrontKconfUtil::universeGenericFlowControlConfig();
  auto config_it = universe_generic_flow_control_config->data().config_map().find(type_string);
  if (config_it == universe_generic_flow_control_config->data().config_map().end()) {
    return;
  }

  int64_t target_qps = config_it->second.target_qps() *
                       universe_generic_flow_control_config->data().exp_proportion() / instance_num;
  if (target_qps == 0) {
    return;
  }
  auto current_second = time(0);
  auto& qps_mark = qps_mark_map_[type_integral];
  auto& adjust_ratio = adjust_ratio_map_[type_integral];
  size_t last_index = (current_second / UGFC_QPS_MARK_STEP - 1) % UGFC_QPS_MARK_SIZE;
  size_t last_last_index = (current_second / UGFC_QPS_MARK_STEP - 2) % UGFC_QPS_MARK_SIZE;
  int64_t actual_qps = qps_mark[last_index].load() / UGFC_QPS_MARK_STEP;
  if (actual_qps == 0) {
    return;
  }
  // 计算 e(K)
  int64_t qps = actual_qps;
  double e_k = (qps - target_qps) * 1.0 / target_qps;
  // 计算 e(K-1)
  qps = qps_mark[last_last_index].load() / UGFC_QPS_MARK_STEP;
  double e_k_sub_1 = (qps - target_qps) * 1.0 / target_qps;
  double p_value = e_k;
  double i_value = e_k + e_k_sub_1;  // 累计误差，只算近两次的
  double d_value = e_k - e_k_sub_1;
  double error_adjust = config_it->second.k_p() * p_value + config_it->second.k_i() * i_value +
                        config_it->second.k_d() * d_value;
  double ratio = adjust_ratio + error_adjust;
  ratio = std::max(ratio, 0.0);
  ratio = std::min(ratio, 10.0);
  adjust_ratio = ratio;
  // 当前 index 前移 50 的 qps 置零, 方便后面使用
  qps_mark[(current_second / UGFC_QPS_MARK_STEP - 50) % UGFC_QPS_MARK_SIZE] = 0;
  ks::ad_base::AdPerf::IntervalLogStash(
      adjust_ratio * 1000000, "ad.ad_front", "universe_generic_flow_control", "ratio", type_string);
  ks::ad_base::AdPerf::IntervalLogStash(
      target_qps, "ad.ad_front", "universe_generic_flow_control", "target_qps", type_string);
  ks::ad_base::AdPerf::IntervalLogStash(
      actual_qps, "ad.ad_front", "universe_generic_flow_control", "actual_qps", type_string);
}
}  // namespace front_server
}  // namespace ks
