#pragma once

#include <atomic>
#include <cstdint>
#include <memory>
#include <mutex>
#include <string>

#include "base/thread/thread_pool.h"  // ThreadPool
#include "glog/logging.h"
#include "teams/ad/ad_base/src/container/concurrent_queue.h"
#include "teams/ad/ad_base/src/kafka/kafka_client.h"
#include "teams/ad/ad_proto/kuaishou/ad/universe_delivered_qpon_info.pb.h"

namespace ks {
namespace ad_base {
class AdKafkaProducer;
}  // namespace ad_base

namespace front_server {
using kuaishou::ad::UniverseQcpxDeliveryLogInfo;
class UniverseQcpxDeliveryDataLog {
 public:
  static UniverseQcpxDeliveryDataLog* GetInstance() {
    static UniverseQcpxDeliveryDataLog instance;
    return &instance;
  }

  void Record(UniverseQcpxDeliveryLogInfo &&log_info);

  void Start();

  void Stop();

  bool IsReady() {
    return started_;
  }
  int64_t GetInitLatency() const {
    return init_latency_;
  }

 private:
  UniverseQcpxDeliveryDataLog();
  ~UniverseQcpxDeliveryDataLog();
  void Update();

  void RecordDirect(const UniverseQcpxDeliveryLogInfo &log_info);

 private:
  // 生成者消费者队列
  ks::ad_base::AdConcurrentQueue<UniverseQcpxDeliveryLogInfo> queue_;

  std::unique_ptr<ks::ad_base::AdKafkaProducer> producer_;
  std::mutex mtx_;
  bool started_ = false;

  std::atomic_bool stop_;
  ::thread::ThreadPool thread_pool_;

  std::string host_name_;
  int64_t init_latency_ = 0;
};
}  // namespace front_server
}  // namespace ks
