#include "teams/ad/front_server_universe/util/universe_qcpx_delivery_data_log/universe_qcpx_delivery_data_log.h"

#include <atomic>
#include <mutex>
#include <ostream>
#include <string>
#include <utility>

#include "base/common/closure.h"
#include "base/common/sleep.h"  // base::SleepForMilliseconds
#include "base/time/timestamp.h"
#include "glog/logging.h"
#include "serving_base/utility/system_util.h"

#include "serving_base/jansson/json.h"
#include "teams/ad/ad_base/src/kafka/kafka_client.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"

namespace ks {
namespace front_server {

UniverseQcpxDeliveryDataLog::UniverseQcpxDeliveryDataLog()
    : started_(false), stop_(false), thread_pool_(1), host_name_(serving_base::GetHostName()) {
  if (!ks::ad_base::AdKessClient::Instance().IsTestEnv()) {
    producer_ = std::make_unique<ad_base::AdKafkaProducer>();
    if (producer_) {
      producer_->set_disable_compress(true);
    }
  }
}

UniverseQcpxDeliveryDataLog::~UniverseQcpxDeliveryDataLog() {
  Stop();
}

void UniverseQcpxDeliveryDataLog::Start() {
  auto start_ts = base::GetTimestamp();
  std::lock_guard<std::mutex> lck(mtx_);
  if (started_) {
    return;
  }

  if (producer_) {
    const std::string user_params = "compression.codec=lz4";
    producer_->Init("universe_qcpx_delivery_data_log", user_params, 0,
                    ks::engine_base::DependDataLevel::WEAK_DEPEND);
  }

  LOG(INFO) << "Start UniverseQcpxDeliveryDataLog.";
  thread_pool_.AddTask(NewCallback(this, &UniverseQcpxDeliveryDataLog::Update));
  started_ = true;
  init_latency_ = base::GetTimestamp() - start_ts;
}

void UniverseQcpxDeliveryDataLog::Stop() {
  if (stop_.load(std::memory_order_acquire)) {
    return;
  }

  stop_.store(true, std::memory_order_release);
  thread_pool_.JoinAll();

  LOG(INFO) << "Exit UniverseQcpxDeliveryDataLog.";
}

void UniverseQcpxDeliveryDataLog::Record(UniverseQcpxDeliveryLogInfo &&log_info) {
  queue_.put(log_info);
}

void UniverseQcpxDeliveryDataLog::Update() {
  while (!stop_.load(std::memory_order_acquire) || queue_.size_approx() > 0) {
    UniverseQcpxDeliveryLogInfo log_info;
    if (!queue_.get(log_info)) {
      base::SleepForMilliseconds(1);
      continue;
    }

    RecordDirect(log_info);
  }
}

void UniverseQcpxDeliveryDataLog::RecordDirect(const UniverseQcpxDeliveryLogInfo &log_info) {
  auto auction_log_pro_queue_size = queue_.size_approx();

  // 回写到 kafka
  if (producer_) {
    auto ret = producer_->Produce(log_info.SerializeAsString());
    if (ret != ks::ad_base::AdKafkaStatus::SUCCESS) {
      LOG_EVERY_N(WARNING, 3000) << "universe_distribut_volume send error, ret=" << ret;
    }
  }
}

}  // namespace front_server
}  // namespace ks
