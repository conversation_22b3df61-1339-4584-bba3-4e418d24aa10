#pragma once

#include <atomic>
#include <cstdint>
#include <memory>
#include <mutex>
#include <string>

#include "base/thread/thread_pool.h"  // ThreadPool
#include "glog/logging.h"
#include "teams/ad/ad_base/src/container/concurrent_queue.h"
#include "teams/ad/ad_base/src/kafka/kafka_client.h"
#include "teams/ad/ad_proto/kuaishou/ad/bid_server/auction_log.pb.h"

namespace ks {
namespace ad_base {
class AdKafkaProducer;
}  // namespace ad_base

namespace front_server {
class UniverseAuctionLogManager {
 public:
  UniverseAuctionLogManager();
  ~UniverseAuctionLogManager();

  void Record(kuaishou::ad::AuctionLog &&auction_log_info);

  void Start();

  void Stop();

  bool IsReady() {
    return started_;
  }
  int64_t GetInitLatency() const {
    return init_latency_;
  }

 private:
  void Update();

  void RecordDirect(const kuaishou::ad::AuctionLog &auction_log_info);

 private:
  // 生成者消费者队列
  ks::ad_base::AdConcurrentQueue<kuaishou::ad::AuctionLog> queue_;

  std::unique_ptr<ks::ad_base::AdKafkaProducer> producer_;
  std::mutex mtx_;
  bool started_ = false;

  std::atomic_bool stop_;
  ::thread::ThreadPool thread_pool_;

  std::string host_name_;
  int64_t init_latency_ = 0;
};
}  // namespace front_server
}  // namespace ks
