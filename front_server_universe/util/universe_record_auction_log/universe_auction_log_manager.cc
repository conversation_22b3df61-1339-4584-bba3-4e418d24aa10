#include "teams/ad/front_server_universe/util/universe_record_auction_log/universe_auction_log_manager.h"

#include <array>
#include <atomic>
#include <cstdint>
#include <mutex>
#include <ostream>
#include <string>
#include <utility>

#include "base/common/closure.h"
#include "base/common/sleep.h"  // base::SleepForMilliseconds
#include "base/time/timestamp.h"
#include "glog/logging.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "serving_base/utility/system_util.h"

#include "serving_base/jansson/json.h"
#include "teams/ad/ad_base/src/kafka/kafka_client.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"

namespace ks {
namespace front_server {

UniverseAuctionLogManager::UniverseAuctionLogManager()
    : started_(false), stop_(false), thread_pool_(1), host_name_(serving_base::GetHostName()) {
  if (!ks::ad_base::AdKessClient::Instance().IsTestEnv()) {
    producer_ = std::make_unique<ad_base::AdKafkaProducer>();
    if (producer_) {
      producer_->set_disable_compress(true);
    }
  }
}

UniverseAuctionLogManager::~UniverseAuctionLogManager() {
  Stop();
}

void UniverseAuctionLogManager::Start() {
  auto start_ts = base::GetTimestamp();
  std::lock_guard<std::mutex> lck(mtx_);
  if (started_) {
    return;
  }

  if (producer_) {
    const std::string user_params = "compression.codec=lz4";
    producer_->Init("universe_ad_server_auction_log", user_params, 0,
                    ks::engine_base::DependDataLevel::WEAK_DEPEND);
  }

  LOG(INFO) << "Start UniverseAuctionLogManager.";
  thread_pool_.AddTask(NewCallback(this, &UniverseAuctionLogManager::Update));
  started_ = true;
  init_latency_ = base::GetTimestamp() - start_ts;
}

void UniverseAuctionLogManager::Stop() {
  if (stop_.load(std::memory_order_acquire)) {
    return;
  }

  stop_.store(true, std::memory_order_release);
  thread_pool_.JoinAll();

  LOG(INFO) << "Exit UniverseAuctionLogManager.";
}

void UniverseAuctionLogManager::Record(::kuaishou::ad::AuctionLog &&auction_log_info) {
  queue_.put(auction_log_info);
}

void UniverseAuctionLogManager::Update() {
  while (!stop_.load(std::memory_order_acquire) || queue_.size_approx() > 0) {
    kuaishou::ad::AuctionLog auction_log_info;
    if (!queue_.get(auction_log_info)) {
      base::SleepForMilliseconds(1);
      continue;
    }

    RecordDirect(auction_log_info);
  }
}

void UniverseAuctionLogManager::RecordDirect(const ::kuaishou::ad::AuctionLog &auction_log_info) {
  auto start_ts = base::GetTimestamp();
  auto auction_log_pro_queue_size = queue_.size_approx();
  falcon::Set(
      "ad_server.universe_auction_log_pro_queue_size", auction_log_pro_queue_size, falcon::kNonAdditiveGauge);

  // 回写到 kafka
  if (producer_) {
    falcon::Inc("ad_server.universe_auction_log_record_num", 1);
    auto ret = producer_->Produce(auction_log_info.SerializeAsString());
    if (ret != ks::ad_base::AdKafkaStatus::SUCCESS) {
      falcon::Inc("ad_server.universe_auction_log_record_error", 1);
      LOG_EVERY_N(WARNING, 3000) << "universe_auction_log send error, ret=" << ret;
    }
  }

  auto end_ts = base::GetTimestamp();

  LOG_EVERY_N(INFO, 3000) << "&universe_auction_log_pro_queue_size=" << auction_log_pro_queue_size
                          << "&latency=" << end_ts - start_ts
                          << "&auction_log_info=" << auction_log_info.ShortDebugString();
  falcon::Stat("ad_server.universe_auction_log_record_latency", end_ts - start_ts);
}

}  // namespace front_server
}  // namespace ks
