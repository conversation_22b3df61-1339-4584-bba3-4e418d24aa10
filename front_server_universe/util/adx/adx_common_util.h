#pragma once

#include <map>
#include <string>

#include "base/common/basic_types.h"
#include "third_party/jsoncpp/include/json/json.h"
#include "teams/ad/ad_base/src/crypt/ad_aes_crypter.h"
#include "teams/ad/ad_base/src/crypt/bio_base64.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/front_service.pb.h"

namespace Json {
class Value;
}  // namespace Json
namespace kuaishou {
namespace ad {
class AdDataV2;
}  // namespace ad
}  // namespace kuaishou

namespace ks {
namespace front_server {

// 加密 adx 的价格
bool EncryptAdxPrice(kuaishou::ad::AdxSourceType adx_source_type,
    std::string *price_str);
bool EncryptAdxJingmeiPrice(uint64_t price, std::string *price_str);
int64_t GetAdxWinPrice(const kuaishou::ad::AdResult& item);

bool FormatADXPrice(const kuaishou::ad::AdResult& item,
    std::string *price_str);

bool IsAdxWhiteUser(const int64_t &user_id, const int64_t dsp_id = 0);

}  // namespace front_server
}  // namespace ks

