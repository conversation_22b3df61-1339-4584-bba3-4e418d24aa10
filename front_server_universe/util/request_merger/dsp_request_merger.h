#pragma once

#include <memory>
#include <sstream>
#include <vector>

#include "base/time/timestamp.h"
#include "libcuckoo/cuckoohash_map.hh"
#include "teams/ad/ad_base/src/hash/hash.h"
#include "teams/ad/ad_base/src/kess/macros.h"
#include "teams/ad/ad_proto/kuaishou/ad/front_service.pb.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"

namespace ks {
namespace front_server {

class DspRequestMergeInfo {
 public:
  std::atomic<bool> finished{false};
  kuaishou::ad::AdResponse ad_response;
  uint64_t llsid;
};

class DspRequestMerger {
 public:
  static DspRequestMerger &Instance() {
    static DspRequestMerger tmp;
    return tmp;
  }
  // 尝试去合并请求，合并成功直接返回，否则创建一个新的队列来给其他线程装请求
  bool TryMerge(const uint64_t key, const uint64_t llsid,
                std::shared_ptr<DspRequestMergeInfo> &dsp_request_merge_info) {  // NOLINT
    dsp_request_merge_info.reset(new DspRequestMergeInfo());
    dsp_request_merge_info->llsid = llsid;
    bool is_new = response_writer_container_map_.uprase_fn(
        key,
        [&dsp_request_merge_info](auto &val) {
          val->emplace_back(dsp_request_merge_info);
          return false;
        },
        std::make_shared<std::vector<std::shared_ptr<DspRequestMergeInfo>>>());

    if (is_new == true) {
      dsp_request_merge_info = nullptr;
    }

    return !is_new;
  }
  void AllocateAds(const uint64_t key, int64_t kess_status, kuaishou::ad::AdResponse *response) {
    std::shared_ptr<std::vector<std::shared_ptr<DspRequestMergeInfo>>> request_merge_info_ptr{nullptr};
    response_writer_container_map_.erase_fn(key, [&request_merge_info_ptr](const auto &val) {
      request_merge_info_ptr = val;
      return true;
    });
    if (request_merge_info_ptr == nullptr) {
      return;
    }

    if (request_merge_info_ptr->size() == 0) {
      return;
    }

    if (kess_status != KESS_STATUS_OK) {
      response->clear_result();
    }

    // 1. 将 ad_result swap
    ::google::protobuf::RepeatedPtrField<::kuaishou::ad::AdResult> swap_ad_result;
    response->mutable_result()->Swap(&swap_ad_result);

    // 2.  拷贝 response 到 request_merge_info_ptr 里面，并设置 llsid
    for (size_t i = 0; i < request_merge_info_ptr->size(); ++i) {
      (*request_merge_info_ptr)[i]->ad_response.CopyFrom(*response);
      (*request_merge_info_ptr)[i]->ad_response.set_llsid((*request_merge_info_ptr)[i]->llsid);
    }

    // 3.  将所有的 response 放到一个 vector 里面
    std::vector<kuaishou::ad::AdResponse *> response_vec(request_merge_info_ptr->size() + 1);
    response_vec[0] = response;
    for (size_t i = 0; i < request_merge_info_ptr->size(); ++i) {
      response_vec[i + 1] = &((*request_merge_info_ptr)[i]->ad_response);
    }

    // 4. 填充广告
    for (size_t i = 0; i < swap_ad_result.size(); ++i) {
      auto result = response_vec[i % response_vec.size()]->add_result();
      result->Swap(&swap_ad_result[i]);
    }

    // 5. 设置 finished 标志
    for (size_t i = 0; i < request_merge_info_ptr->size(); ++i) {
      (*request_merge_info_ptr)[i]->finished = true;
    }
  }

  bool GetAllocateAds(const std::shared_ptr<DspRequestMergeInfo> &dsp_request_merge_info,
                      kuaishou::ad::AdResponse *response) {
    auto start_ts = base::GetTimestamp();
    int64_t duration_us = FrontKconfUtil::sleepDurationMicroSeconds();
    while (!dsp_request_merge_info->finished && base::GetTimestamp() < start_ts + 500 * 1000 * 1000) {
      std::this_thread::sleep_for(std::chrono::microseconds(duration_us));
    }
    if (dsp_request_merge_info->finished) {
      response->CopyFrom(dsp_request_merge_info->ad_response);
      return true;
    }
    return false;
  }

  void EraseKey(const uint64_t key) { response_writer_container_map_.erase(key); }

 private:
  cuckoohash_map<uint64_t, std::shared_ptr<std::vector<std::shared_ptr<DspRequestMergeInfo>>>,
                 ks::ad_base::FNVHash>
      response_writer_container_map_;
  google::protobuf::Arena arena_;

 private:
};

}  // namespace front_server
}  // namespace ks
