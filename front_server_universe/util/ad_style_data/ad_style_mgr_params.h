#pragma once

#include <string>
#include <map>
#include "teams/ad/ad_base/src/spdm_lib/src/context.h"

namespace ks {
namespace front_server {

struct AdStyleMgrParams {
 public:
  void Reset(ks::spdm::Context* spdm);
  void SetAbtestForAdForms();

 public:
  std::string ab_test_for_ad_forms;  //  广告形态的 abtest 数据的 jsonstring
  std::string kuaishou_default_h5_url;

 private:
  ks::spdm::Context* spdm_ptr_ = nullptr;
};

}  // namespace front_server
}  // namespace ks
