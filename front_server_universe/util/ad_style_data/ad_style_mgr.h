#pragma once

#include <stdint.h>
#include <string>
#include <memory>
#include <vector>
#include <utility>
#include <map>
#include "serving_base/crypt/aes_crypter.h"
#include "serving_base/region/region_dict.h"
#include "third_party/jsoncpp/include/json/json.h"
#include "teams/ad/ad_base/src/util/check_reco_request_type.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/industry_opt/playlet_opt.pb.h"
#include "teams/ad/front_server_universe/util/utility/utils.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_forward_index.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_style_service.pb.h"
#include "teams/ad/front_server_universe/util/ad_style_data/ad_style_mgr_params.h"

using kuaishou::ad::forward_index::StyleInfoItem;
using kuaishou::ad::AdResult;
using serving_base::AesCrypter;
using kuaishou::ad::AdConversionInfoUniverse;
namespace ks {
namespace front_server {

struct MainColor {
  uint8_t R;
  uint8_t G;
  uint8_t B;
  float ratio;
  MainColor(uint8_t r, uint8_t g, uint8_t b, float ra)
      : R(r)
      , G(g)
      , B(b)
      , ratio(ra) {}
};

/*
 * 负责组装广告样式的类
 */
class AdStyleMgr {
 private:
  ContextData* session_data_;
  kuaishou::ad::AdRequest* ad_request_;
  AdResult* ad_result_;
  StyleInfoItem* forward_index_item_;

  int32_t actionbar_style_;
  int32_t animation_type_;
  std::string actionbar_color_;
  std::string display_info_;
  std::string expose_tag_;  // 推荐理由, 空格分割
  kuaishou::ad::AdDataV2 ad_data_v2;

  AesCrypter aes_crypter_;
  bool is_multi_screen_info_;
  base::Json h5_data_json_;
  AdStyleMgrParams* params_;

  enum WebCardEnum {
    APP_INSTALL_CARD = 1,  // app 下载类型卡片， 新增需要通知数据 lisen
    MERCHANT_CARD = 2,     // 电商类卡片
    OTHER_TYPE_CARD = 3,   // 除 1 和 2 之外的其它类别通用类型(特殊类型有需要可再单独新增)
    INSURE_CARD = 4,       // 保险行业卡片
    GAME_ORDER_CARD = 5,   // 游戏预约卡片
    MULTI_SUB_CARDS_STYLE1 = 6,  // 多利益点卡片，包含图片、主标题、副标题
    MULTI_SUB_CARDS_STYLE2 = 7,  // 多利益点卡片，包含主标题、副标题
    MULTI_SUB_CARDS_STYLE3 = 8,  // 多利益点卡片，包含图片、主标题
    GAME_INSTALL_CARD = 9,  // 游戏通用卡片
    FEED_HORIZONTAL_CARD = 10,  // 双 feed 横条 card
    INSURE_CARD_V2 = 11,  // 保险行业卡片二期
    COUPON_CARD = 12,  // 优惠券 card
    PIC_CARD = 100,  // 图片卡
    MERCHANT_V2_CARD = 103,  // 商品卡
  };
  enum PopCardEnum {
    INSURE_COMPONENT = 1,  // 保险行业卡片二期
    GAME_COMPONENT = 2,  //  游戏预约码
  };

  // wiki: https://wiki.corp.kuaishou.com/pages/viewpage.action?pageId=152992559
  enum ActionBarEnum {
    // 未知类型 kuaishou::ad::AdEnum_ActionBarStyle_UNKNOW_ACTIONBAR_STYLE
    UNNOKNOWN_STYLE = 0,
    // 兜底 action bar，会随着收拾上滑，平滑移动到作品下方 ActionBarStyle_TEST_ACTIONBAR_STYLE_A
    COMMON_STYLE = 1,
    // 动效卡片，固定在作品的下部分区域，不会滑到作品下方 ActionBarStyle_TEST_ACTIONBAR_STYLE_B
    ANIMATION_STYLE = 2,
    // 线索收集类卡片，固定在作品下部分区域，不会滑到作品下方 ActionBarStyle_TEST_ACTIONBAR_STYLE_C
    LEADS_COLLECTION_STYLE = 3,
    // 橱窗类，随着收拾上滑，平滑到作品下方
    // 如果有橱窗在，则滑到橱窗的下方区域 ActionBarStyle_TEST_ACTIONBAR_STYLE_D
    SHOW_WINDOW_STYLE = 4,
    // 条形，如果作品长度大于屏幕的高度，先挂在屏幕下方，随着手势上滑，
    // 接到作品下方 ActionBarStyle_TEST_ACTIONBAR_STYLE_E
    BANNER_STYLE = 5,
    // 视频+多图（图文）的形式联合推广,需和多图绑定使用 ActionBarStyle_TEST_ACTIONBAR_STYLE_F
    MULTI_PICTURES_STYLE = 6,
    // 另一种橱窗类型 ActionBarStyle_TEST_ACTIONBAR_STYLE_G
    NEW_SHOP_WINDOW_STYLE = 7,
    // 直营电商
    DIRECT_SALE_SHOP_STYLE = 10,
    // 滑滑版简单 actionbar，和通用卡片一起使用 ActionBarStyle_TEST_ACTIONBAR_STYLE_K
    CARD_SIMPLE_STYLE = 11,
    // 下载类信息前置实验样式，滑动到下载信息页面，从底部动画弹出，且文案有动画
    PRE_DOWNLOAD_STYLE = 12,
    // 无 action bar
    NO_ACTION_BAR_STYLE = 100,
  };

  void SetWebCardCommonInfo(WebCardEnum card_type);
  // wiki：https://wiki.corp.kuaishou.com/pages/viewpage.action?pageId=147622677

 public:
  AdStyleMgr();
  // 负责组装出各种样式
  void Process(
      ContextData* context_data,
      AdResult* ad_result,
      StyleInfoItem* style_item,
      AdStyleMgrParams* params);
  const kuaishou::ad::AdDataV2* GetResultPb() { return &ad_data_v2; }
  // 联盟延迟补充字段信息
  void ProcessUniverseFix(AdResult* ad_result);

  // 延迟序列化，来源 session_data_->cur_ad_data_v2
  void SetAdDataV2Later(AdResult* item, ContextData* session_data_);

  // ad_data_v2 中 url 宏替换
  void SetUrlReplace(AdResult* item, const std::map<std::string, std::string> &replace_macro_map);

  // 透传宏参，给聚星替换宏
  void SetTransMacros(std::pair<std::string, std::string> trans_macros_pair);

 private:
  bool Initialize(
      ContextData* context_data,
      AdResult* ad_result,
      StyleInfoItem* style_item,
      AdStyleMgrParams* params);
  bool MinimumAppVersion(const std::string& ios_version, const std::string& android_version) const;
  void SetFreqControlClientFlag();
  bool IsDspDownload() const;
  bool IsAdvanceDownload() const;
  bool IsAdxDownload() const;
  bool IsFansTopV2Live() const;

  // 准备数据
  bool Prepare();
  // 设置默认的 display_info
  bool PrepareDefaultDisplayInfo();
  // 设置默认的 action_bar_color
  bool PrepareDefaultActionbarColor();
  // 推荐理由
  bool PrepareExposeTag();
  // 设置 action_bar_style
  void SetActionbarTime(kuaishou::ad::AdDataV2_ActionBarInfo*);
  int SetActionbarStyle();
  // 设置评论区 action_bar_style
  void SetCommentActionbarInfo();
  // 设置落地页相关
  void SetLandingPageInfo();

  void SetPlayInfo();
  // 设置封面贴纸相关
  void SetCoverStickerInfo();

  void SetAdPageButtonControl();
  void SetDspAdPageButtonControl();
  void SetAdPageProfileButtonControl();
  void SetAdPageAttentionButtonControl();
  void SetDpaButtonControl();
  void SetAdCoverPageButtonControl();

  // 设置 feed 流封面转换开关
  void SetCoverActionBarInfo();

  // 设置 ios app id
  void SetIosAppId();

  // 设置 convert info
  void SetConvertInfo();
  // 设置 adtest ad forms
  void SetAdtestForAdForms();
  // 设置 adx 广告的卡片样式
  void SetAdxCardInfo();
  // 获得 config 中相应的字段 name 的 value
  std::string GetValueFromConfigString(const std::string& config, const std::string& name);
  // 金融保险行业卡片内容
  void SetInsureCardInfo();
  void SetAdFreqInfo();

  // 设置 负反馈
  void SetNegativeMenuInfo();
  // 设置 app 信息
  void SetAppDetailInfo();
  // 设置跳转应用商店的 uri
  void SetMarketUri();
  // 设置 下载信息前置 信息
  bool SetAppDetailInfoV2();
  // 应用商店命中判断
  bool MeetAppStoreRequirements();
  // 设置 webview 类型
  void SetTrolleyView();
  // 广告标签二期优化
  void SetAdLabelOptimization();
  void SetLiveComponent();
  void SetCommonControlInfo();
  bool UseCommentForDoubleFeed();
  // 是否是游戏广告
  bool IsGameAd();
  // 设置联盟信息流广告 APP 信息
  void SetUniverseAppDetailInfo();
  // 统一最后设置 h5_data
  void SetH5Data();
  void SetH5DataV2();
  void FillFakeStyleData(const int64_t style_type, const int64_t resource_type);
  // 设置落地页跳转策略
  void SetBridgeInfo();
  void AddPromotionConsultationClickUrl();
  // 设置透传给 admatrix 样式
  void SetOriginStyleInfo();
  void SetOriginStyleInfoUniverse();
  // originStyleInfo  中电商相关样式
  void SetMerchantOriginStyleInfo(kuaishou::ad::AdDataV2_OriginStyleInfo* origin_style_info);
  // originStyleInfo  中问卷相关样式
  void SetSurveyOriginStyleInfoOpt(kuaishou::ad::AdDataV2_OriginStyleInfo* origin_style_info);
  void FillAdStyleInfo();
  // 激励/全屏视频填充
  void FillPlayDetailInfo();
  // 激励视频 ad_style_info 填充
  void FillDeepInspireAdStyleInfo();
  void FillAdConversionInfo();
  // 重新设置 actionbar 时间
  void ResetActionbarTime();
  bool MatchPcLiveScene();
  // 设置客户端落地页地址
  void SetClickUrl();
  // 设置联盟轻互动样式
  void SetUniverseInteractiveStyle();
  // 设置联盟样式优选结果
  void SetUniversePreferStyle();

  void SetMerchantProductInfo();
  void SetPosInfo();

  // 引流直播接入激励视频所需字段
  void SetInsVideoLiveInfo();

  // 金牛视频播放 n 秒埋点字段
  void SetPhotoPlayedReportTime();

  // adx 填充隐私数据
  void SetAdxAppInfo();

  // 企微直跳设置
  void SetWechatDirectCall();

  // rta 宏参数, 给聚星替换宏
  void SetRtaMacros();

  // 设置通用透传字段
  void SetBizInfo();

  // c 补
  void FillSmartOffersInfo(kuaishou::ad::industry_opt::AdSmartOffersInfo* p_ad_smart_offers_info);
  void SetAdSmartOffersInfo();
  void SetKxySubsidyInfo();
  // 设置快聘相似职位信息
  void SetRecruitSimilarPositionInfo();
  void SetPhotoSource();
};

}  // namespace front_server
}  // namespace ks
