#include "teams/ad/front_server_universe/util/ad_style_data/ad_style_mgr_params.h"

#include <algorithm>
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/front_server_universe/util/kconf/kconf_data.pb.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "third_party/jsoncpp/include/json/json.h"

namespace ks {
namespace front_server {

void AdStyleMgrParams::Reset(ks::spdm::Context* spdm) {
  spdm_ptr_ = spdm;

  SetAbtestForAdForms();

  kuaishou_default_h5_url = "https://activity.e.kuaishou.com/ksDownloadPageV2?bizId=ad_fe_full_loading";
}

void AdStyleMgrParams::SetAbtestForAdForms() {
  auto p_kconf_map = FrontKconfUtil::abtestForAdForms();
  if (p_kconf_map == nullptr) {
    return;
  }
  auto& r_map = *p_kconf_map;
  JsonObject json;
  for (const auto& pr : r_map) {
    switch (pr.second) {
      case 1:
        json.set(pr.first, spdm_ptr_->TryGetBoolean(pr.first, false));
        break;
      case 2:
        json.set(pr.first, spdm_ptr_->TryGetInteger(pr.first, 0));
        break;
      case 3:
        json.set(pr.first, spdm_ptr_->TryGetString(pr.first, ""));
        break;
      default:
        break;
    }
  }
  if (json.get()) {
    ab_test_for_ad_forms = base::JsonToString(json.get());
  } else {
    ab_test_for_ad_forms.clear();
  }
}

}  // namespace front_server
}  // namespace ks
