#pragma once

#include <string>
#include <vector>
#include <utility>
#include <unordered_map>
#include <memory>

#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
#include "teams/ad/ad_proto/kuaishou/ad/ssp/ad_ssp.grpc.pb.h"
#include "teams/ad/front_server_universe/util/utility/front_logging.h"
#include "teams/ad/ad_base/src/perf/perf.h"
#include "teams/ad/ad_base/src/pb_helper/pb_helper.h"
#include "glog/logging.h"

namespace ks {
namespace front_server {
using kuaishou::ad::ssp::AdUniversePosition;
using kuaishou::ad::ssp::AdUniverseMedium;
using kuaishou::ad::ssp::AdUniverseAccount;
using kuaishou::ad::ssp::AdUniverseShield;
using kuaishou::ad::ssp::AdUniverseTraffic;
using kuaishou::ad::ssp::AdUniverseAutoBlackList;

struct NewAdPosInfo {
  std::unordered_map<int64_t, AdUniverseAccount> universe_account_map;
  std::unordered_map<std::string, AdUniverseMedium> universe_medium_map;
  std::unordered_map<int64_t, AdUniversePosition> universe_position_map;
  std::unordered_map<int64_t, AdUniverseShield> universe_shield_map;
  // std::unordered_map<int64_t, AdUniverseTraffic> universe_traffic_map;
  std::unordered_map<int64_t, AdUniverseAutoBlackList> universe_auto_black_list;
};

// case 2: 线上使用 pb
class UniversePosResourceContainer {
 public:
  using StructData = NewAdPosInfo;
  using ProtoData = kuaishou::ad::ssp::AdUniversePositionResponse;
  // note: 在这里进行在线使用的数据结构的切换
  using Container = std::unordered_map<int64_t, StructData>;   // 1.线上使用自定义数据结构
  // 文件中一行转换为 pb
  static bool line_to_proto(const std::string& line,
                            std::pair<int64_t, std::unique_ptr<google::protobuf::Message>>* pb) {
    std::unique_ptr<ProtoData> msg(new ProtoData());
    if (!ks::ad_base::pb_utils::ParseBase64BigPB(*msg, line)) {
      LOG(WARNING) << "UniversePosResource parse fail.";
      ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_front",
                                            "universe_pos_resource",
                                            "parse_line_failed");
      return false;
    }
    pb->first = 0;
    pb->second = std::move(msg);
    return true;
  }

  static Container::value_type pb_to_record(int64_t key, std::unique_ptr<google::protobuf::Message> pb) {
    StructData data;
    if (pb) {
      auto* msg = static_cast<ProtoData*>(pb.get());
      for (auto& account : msg->ad_universe_account()) {
        (data.universe_account_map)[account.uid()] = account;
      }
      for (auto& medium : msg->ad_universe_medium()) {
        if (data.universe_account_map.find(medium.uid()) == data.universe_account_map.end()) {
          TLOG_EVERY_N(WARNING, 100) << "Error medium, app_id=" << medium.app_id() << ", uid=" << medium.uid();   // NOLINT
          continue;
        }
        (data.universe_medium_map)[medium.app_id()] = medium;
      }
      // for (auto& traffic : msg->ad_universe_traffic()) {
      //   (data.universe_traffic_map)[traffic.traffic_id()] = traffic;
      // }
      for (auto& position : msg->ad_universe_position()) {
        if (data.universe_account_map.find(position.uid()) == data.universe_account_map.end()) {
          TLOG_EVERY_N(WARNING, 1000) << "Error position, position_id=" << position.position_id()
                                    << ", uid=" << position.uid();
          continue;
        }
        auto it = data.universe_medium_map.find(position.app_id());
        if (it == data.universe_medium_map.end()) {
          TLOG_EVERY_N(WARNING, 100000) << "Error position, position_id=" << position.position_id()
                                        << ", app_id=" << position.app_id();
          continue;
        }
        if (it->second.uid() != position.uid()) {
          TLOG_EVERY_N(WARNING, 100) << "Error position, position_id=" << position.position_id()
                                     << ", uid=" << position.uid() << ", app_id=" << position.app_id();
          continue;
        }
        (data.universe_position_map)[position.position_id()] = position;
      }
      for (auto& shield : msg->ad_universe_shield()) {
        if (data.universe_account_map.find(shield.uid()) == data.universe_account_map.end()) {
          LOG_EVERY_N(WARNING, 1000) << "Error shield, shield_id=" << shield.shield_id()
                                     << ",  uid=" << shield.uid();
          continue;
        }
        (data.universe_shield_map)[shield.shield_id()] = shield;
      }
      for (auto& black_list : msg->ad_universe_auto_black_list()) {
        if (data.universe_account_map.find(black_list.uid()) == data.universe_account_map.end()) {
          LOG_EVERY_N(WARNING, 1000) << "Error black_list, package_size=" << black_list.package_size()
                                     << ", uid=" << black_list.uid();
          continue;
        }
        (data.universe_auto_black_list)[black_list.uid()] = black_list;
      }
      TLOG(INFO) << "GetAdUniversePosition, account num=" << data.universe_account_map.size()
                << ", medium num=" << data.universe_medium_map.size()
                << ", position num=" << data.universe_position_map.size()
                << ", traffic num=" << data.universe_shield_map.size();
    }
    return std::make_pair(key, data);
  }
};

class UniversePosResourceV2
    : public ad_base::P2pCacheLoaderHelper<UniversePosResourceContainer> {
 public:
  static UniversePosResourceV2 *GetInstance() {
    static UniversePosResourceV2 instance;
    return &instance;
  }

 public:
  const AdUniversePosition& GetAdUniversePosition(const int64_t pos_id);

  const AdUniverseMedium& GetAdUniverseMedium(const std::string& app_id);

  const AdUniverseAccount& GetAdUniverseAccount(const int64_t uid);

  const AdUniverseAutoBlackList& GetAdUniverseAutoBlackList(const int64_t uid);

  bool GetShield(const int64_t shield_id, kuaishou::ad::AdUniverseShield* ad_universe_shield);

 private:
  UniversePosResourceV2()
      : ad_base::P2pCacheLoaderHelper<UniversePosResourceContainer>(
            10 * 60 * 1000,
            *::ad::data_push::DataPushKconfUtil::universePosResourceP2pName(),
            ks::engine_base::DependDataLevel::STRONG_DEPEND) {
  }
};
}  // namespace front_server
}  // namespace ks
