
#include "teams/ad/front_server_universe/util/ad_pos_manager/universe_pos_resource_v2.h"

#include "teams/ad/engine_base/data_stability/depend_data_level.h"
#include "teams/ad/data_push/utils/kconf/kconf.h"

namespace ks {
namespace front_server {
const AdUniversePosition& UniversePosResourceV2::GetAdUniversePosition(const int64_t pos_id) {
  static const AdUniversePosition kEmptyAdUniversePosition;
  auto scoped_ptr = this->GetData();
  if (scoped_ptr == nullptr) {
    return kEmptyAdUniversePosition;
  }
  auto iter = (*scoped_ptr)[0].universe_position_map.find(pos_id);
  if (iter != (*scoped_ptr)[0].universe_position_map.end()) {
    return iter->second;
  }
  return kEmptyAdUniversePosition;
}

const AdUniverseMedium& UniversePosResourceV2::GetAdUniverseMedium(const std::string& app_id) {
  static const AdUniverseMedium kEmptyAdUniverseMedium;
  auto scoped_ptr = this->GetData();
  if (scoped_ptr == nullptr) {
    return kEmptyAdUniverseMedium;
  }
  auto iter = (*scoped_ptr)[0].universe_medium_map.find(app_id);
  if (iter != (*scoped_ptr)[0].universe_medium_map.end()) {
    return iter->second;
  }
  return kEmptyAdUniverseMedium;
}

const AdUniverseAccount& UniversePosResourceV2::GetAdUniverseAccount(const int64_t uid) {
  static const AdUniverseAccount kEmptyAdUniverseAccount;
  auto scoped_ptr = this->GetData();
  if (scoped_ptr == nullptr) {
    return kEmptyAdUniverseAccount;
  }
  auto iter = (*scoped_ptr)[0].universe_account_map.find(uid);
  if (iter != (*scoped_ptr)[0].universe_account_map.end()) {
    return iter->second;
  }
  return kEmptyAdUniverseAccount;
}

const AdUniverseAutoBlackList& UniversePosResourceV2::GetAdUniverseAutoBlackList(const int64_t uid) {
  static const AdUniverseAutoBlackList kEmptyAdUniverseAutoBlackList;
  auto scoped_ptr = this->GetData();
  if (scoped_ptr == nullptr) {
    return kEmptyAdUniverseAutoBlackList;
  }
  auto iter = (*scoped_ptr)[0].universe_auto_black_list.find(uid);
  if (iter != (*scoped_ptr)[0].universe_auto_black_list.end()) {
    return iter->second;
  }
  return kEmptyAdUniverseAutoBlackList;
}

bool UniversePosResourceV2::GetShield(const int64_t shield_id,
    kuaishou::ad::AdUniverseShield* ad_universe_shield) {
  auto scoped_ptr = this->GetData();
  if (scoped_ptr == nullptr) {
    return false;
  }
  auto iter = (*scoped_ptr)[0].universe_shield_map.find(shield_id);
  if (iter != (*scoped_ptr)[0].universe_shield_map.end()) {
    ad_universe_shield->set_shield_id(iter->second.shield_id());
    ad_universe_shield->set_uid(iter->second.uid());
    for (int i = 0; i < iter->second.keyword_size(); ++i) {
      ad_universe_shield->add_keyword(iter->second.keyword(i));
    }
    for (int i = 0; i < iter->second.package_size(); ++i) {
      ad_universe_shield->add_package(iter->second.package(i));
    }
    for (int i = 0; i < iter->second.industry_size(); ++i) {
      int industry = iter->second.industry(i);
      ad_universe_shield->add_industry((kuaishou::ad::AdUniverseShield::ShieldIndustry)industry);
    }
    ad_universe_shield->set_test_status(iter->second.test_status());
    return true;
  }
  return false;
}

}  // namespace front_server
}  //  namespace ks
