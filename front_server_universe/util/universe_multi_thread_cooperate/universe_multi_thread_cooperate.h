#pragma once

#include <memory>
#include <string>
#include <mutex>
#include <condition_variable>

#include "ks/base/perfutil/perfutil.h"
#include "third_party/libcuckoo/cuckoohash_map.hh"
#include "teams/ad/ad_base/src/hash/hash.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"

namespace ks {
namespace front_server {

struct UniverseMultiThreadCooperateCacheItem {
  std::condition_variable cv {};
  std::mutex mtx {};
  bool is_cached {false};
  int64_t timestamp {0};
  int64_t target_cpm {0};
};

class UniverseMultiThreadCooperate {
 public:
  using Key = std::string;
  using Value = std::shared_ptr<UniverseMultiThreadCooperateCacheItem>;
  using CacheType = cuckoohash_map<Key, Value, ad_base::FNVHash>;

  static UniverseMultiThreadCooperate* Instance() {
    static UniverseMultiThreadCooperate ins;
    return &ins;
  }
  ~UniverseMultiThreadCooperate() {}

  bool Get(const Key& key, Value* value) {
    Value val {nullptr};
    cache_.find(key, val);

    if (!val || (base::GetTimestamp() - val->timestamp) / 1000 > kTtlMicSec) {
      cache_.erase(key);
      return false;
    }

    *value = val;
    return true;
  }

  bool Put(const Key& key, const int64_t target_cpm) {
    if (cache_.contains(key)) {
      Value val {nullptr};
      cache_.find(key, val);
      if (val && (base::GetTimestamp() - val->timestamp) / 1000 <= kTtlMicSec &&
          val->target_cpm >= target_cpm) {
        return false;
      }
    }

    Value new_val = std::make_shared<UniverseMultiThreadCooperateCacheItem>();
    new_val->timestamp = base::GetTimestamp();
    new_val->target_cpm = target_cpm;
    return cache_.insert_or_assign(key, new_val);
  }

  size_t Size() {
    return cache_.size();
  }

  bool Erase(const Key& key) {
    return cache_.erase(key);
  }

 private:
  static constexpr int32_t kTtlMicSec = 1000;

  CacheType cache_ {10000};
};

}  // namespace front_server
}  // namespace ks
