#pragma once

#include <memory>
#include <string>

#include "teams/ad/3rdparty/hphp/concurrent_lru_cache.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"

namespace ks {
namespace front_server {

class UniverseTopPosStat {
 public:
  static UniverseTopPosStat* Instance() {
    static UniverseTopPosStat inst;
    return &inst;
  }
  ~UniverseTopPosStat() {}
  bool Put(const int64_t physical_pos_id, const int64_t pos_id, const int64_t cpm_floor) {
    if (!cache_) {
      return false;
    }
    CacheItem item{pos_id, cpm_floor};
    return cache_->insert_or_assign(physical_pos_id, item);
  }
  bool Get(const int64_t physical_pos_id, int64_t* pos_id,
           int64_t* cpm_floor, std::string* err) {
    if (!cache_) {
      *err = kBadInit;
      return false;
    }
    ConstAccessor ac;
    if (!cache_->find(ac, physical_pos_id) || ac.empty()) {
      *err = kUnFound;
      return false;
    }
    if (ac->pos_id <= 0) {
      *err = kPosIdInvalid;
      return false;
    }

    *pos_id = ac->pos_id;
    *cpm_floor = ac->cpm_floor;
    return true;
  }
  bool UpdateAndGet(const int64_t physical_pos_id, const int64_t pos_id, const int64_t cpm_floor,
                    int64_t* top_pos_id, int64_t* top_pos_cpm_floor) {
    if (!cache_) {
      return false;
    }
    int64_t cache_pos_id{0};
    int64_t cache_cpm_floor{0};
    std::string err;
    Get(physical_pos_id, &cache_pos_id, &cache_cpm_floor, &err);
    if (pos_id > 0 && cpm_floor > 0 && cpm_floor > cache_cpm_floor) {
      *top_pos_id = pos_id;
      *top_pos_cpm_floor = cpm_floor;
      Put(physical_pos_id, pos_id, cpm_floor);
    } else if (cache_pos_id > 0 && cache_cpm_floor > 0) {
      *top_pos_id = cache_pos_id;
      *top_pos_cpm_floor = cache_cpm_floor;
    }
    return true;
  }

 private:
  struct CacheItem {
    int64_t pos_id{0};
    int64_t cpm_floor{0};
    CacheItem(const int64_t p, const int64_t f) : pos_id(p), cpm_floor(f) {}
  };
  using LRUCache = ad_3rd::ConcurrentLRUCache<int64_t, CacheItem>;
  using ConstAccessor = LRUCache::ConstAccessor;
  static constexpr char kBadInit[] = "BadInit";
  static constexpr char kUnFound[] = "KeyNotFound";
  static constexpr char kPosIdInvalid[] = "PosIdInvalid";

  UniverseTopPosStat() {
    auto capacity = FrontKconfUtil::UniverseTopPosCapacity();
    if (capacity <= 0) {
      capacity = 200000;
    }
    cache_ = std::make_unique<LRUCache>(capacity);
    LOG(INFO) << "create UniverseTopPosStat with capacity = " << capacity;
  }

  std::unique_ptr<LRUCache> cache_;
};

}  // namespace front_server
}  // namespace ks
