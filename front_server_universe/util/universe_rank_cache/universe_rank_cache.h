#pragma once

#include <memory>
#include <string>

#include "teams/ad/ad_base/src/perf/perf.h"
#include "teams/ad/3rdparty/hphp/concurrent_lru_cache.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/front_server_universe/engine/utils/universe/ranking_cache_util.h"


namespace ks {
namespace front_server {

class UniverseRankCache {
 public:
  static UniverseRankCache* Instance() {
    static UniverseRankCache inst;
    return &inst;
  }
  ~UniverseRankCache() {}
  bool Put(const std::string& key, RankingCacheData& val) {  // NOLINT
    if (!cache_) {
      return false;
    }
    CacheItem item;
    val.response().SerializeToString(&item.serialized_response);
    item.model_predict_cpm = val.model_predict_cpm();
    item.timestamp = base::GetTimestamp();
    return cache_->insert_or_assign(key, item);
  }
  bool Get(const std::string& key, int64_t ttl_sec, RankingCacheData* val, const char** err) {
    ks::ad_base::AdPerf::IntervalLogStash(cache_->size(), "ad.front", "universe_rank_cache_size");
    if (!cache_) {
      *err = kBadInit;
      return false;
    }
    ConstAccessor ac;
    if (!cache_->find(ac, key)) {
      *err = kUnFound;
      return false;
    }
    if (ac.empty() || ((base::GetTimestamp() - ac->timestamp)/1000000 > ttl_sec)) {
      *err = kExpired;
      ac.release();
      cache_->erase(key);
      return false;
    }
    if (val->mutable_response()->ParseFromString(ac->serialized_response)) {
      val->set_model_predict_cpm(ac->model_predict_cpm);
      return true;
    } else {
      *err = kParseErr;
      return false;
    }
  }

  bool Contains(const std::string& key) {
    if (!cache_) {
      return false;
    }
    ConstAccessor ac;
    return cache_->find(ac, key) && !ac.empty();
  }

 private:
  struct CacheItem {
    std::string serialized_response;
    double model_predict_cpm;
    int64_t timestamp;
  };
  using LRUCache = ad_3rd::ConcurrentLRUCache<std::string, CacheItem>;
  using ConstAccessor = LRUCache::ConstAccessor;
  static constexpr char kBadInit[] = "BadInit";
  static constexpr char kUnFound[] = "KeyNotFound";
  static constexpr char kExpired[] = "Expired";
  static constexpr char kParseErr[] = "ParseErr";

  UniverseRankCache() {
    auto capacity = FrontKconfUtil::universeRankCacheCapacity();
    if (capacity <= 0) {
      capacity = 1000000;
    }
    cache_ = std::make_unique<LRUCache>(capacity);
    LOG(INFO) << "create UniverseRankCache with capacity = " << capacity;
  }

  std::unique_ptr<LRUCache> cache_;
};


}  // namespace front_server
}  // namespace ks
