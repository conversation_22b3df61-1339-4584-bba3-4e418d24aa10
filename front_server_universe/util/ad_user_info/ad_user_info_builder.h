#pragma once

#include <string>
#include <set>
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/fanstop/fans_top_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/athena_universe_interface.pb.h"
#include "teams/ad/front_server_universe/util/common/common.h"
#include "third_party/boost/boost/any.hpp"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"


using kuaishou::ad::AdRequest;
using kuaishou::ad::AdUserInfo;
using kuaishou::fanstop::FansTopUserInfo;
using kuaishou::fanstop::FansTopRequest;
using kuaishou::newsmodel::UserInfo;
using kuaishou::ad::universe::AthenaAdRequest;
using kuaishou::ad::universe::AthenaAdResponse;
using kuaishou::ad::AdUserEmbed;

namespace ks {
namespace ad_user_profile {
class AdUserProfileService;
}

namespace front_server {

enum class AD_FLOW_TYPE {
  AD_UNKNOWN_FLOW_TYPE,
  AD_EXPLORE_FLOW,
  AD_NEARBY_FLOW,
  AD_FOLLOW_FLOW,
  AD_DETAIL_FLOW,
  AD_UNIVERSE_FLOW,
};

class AdUserInfoBuilder {
 public:
    static void BuildUserInfo(const ContextData& session_data,
                            AD_FLOW_TYPE ad_flow_type,
                            FansTopUserInfo* fanstop_user_info,
                            AdUserInfo* ad_user_info);
};

}  // namespace front_server
}  // namespace ks
