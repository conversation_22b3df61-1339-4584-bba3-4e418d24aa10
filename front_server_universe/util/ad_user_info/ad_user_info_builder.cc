#include "teams/ad/front_server_universe/util/ad_user_info/ad_user_info_builder.h"


#include <algorithm>
#include <memory>
#include <set>
#include "base/time/timestamp.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "third_party/abseil/absl/strings/numbers.h"
#include "teams/ad/picasso/sdk/picasso_client/picasso_client.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/ad_base/src/perf/perf.h"
#include "teams/ad/ad_base/src/pb_helper/pb_helper.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/front_server_universe/util/grpc/grpc_client.h"
#include "teams/ad/front_server_universe/util/universe_traffic_control/universe_generic_flow_control.h"
#include "teams/ad/front_server_universe/engine/utils/data_adapter/user_info_adapter.h"
#include "teams/ad/engine_base/user_profile_cache/user_profile_cache.h"


#define AD_USER_DEFAULT_AGE 20

using kuaishou::ad::kess::AdUserProfileService;
using google::protobuf::util::MessageDifferencer;
static const char kAdUserProfileService[] = "AdUserProfileService";
static const char kAdUserProfileServiceUniverse[] = "AdUserProfileServiceUniverse";
static const char kAdUserProfileServiceKnews[] = "AdUserProfileServiceKnews";

namespace ks {
namespace front_server {

static const int64 universe_fake_user_id_base = 400000000000000UL;
static const int64 universe_fake_user_id_interval = 100000000000000UL;

// called by universe
void AdUserInfoBuilder::BuildUserInfo(const ContextData& session_data,
                                      AD_FLOW_TYPE ad_flow_type,
                                      FansTopUserInfo* fanstop_user_info,
                                      AdUserInfo* ad_user_info) {
  if (session_data.get_user_id() % 100 >= FrontKconfUtil::userProfilePassRatio()) {
    return;
  }
  if (ad_user_info) {
    LOG_EVERY_N(INFO, FrontKconfUtil::logReqDetailFrequency())
      << "reco_ad_user_info = " << ad_user_info->ShortDebugString();
  }

  AdUserProfileRequest *req = AdUserProfileRequest::default_instance().New(ad_user_info->GetArena());
  req->set_ad_request_flow_type(session_data.get_front_server_request()->flow_type());
  AdUserProfileResponse *res = AdUserProfileResponse::default_instance().New(ad_user_info->GetArena());
  req->set_request_type(kuaishou::ad::RequestType::UNIVERSE);
  req->mutable_athena_ad_request()->CopyFrom(*(session_data.get_ud_p_athena_ad_request()));
  req->mutable_request_hash()->CopyFrom(session_data.get_ad_request()->request_hash());
  auto& ugfc = UniverseGenericFlowControl<std::shared_ptr<::kuaishou::ad::AdUserProfileResponse>,
                                          ::kuaishou::ad::AdUserProfileRequest>::Instance();
  bool is_ugfc_take_over = session_data.get_universe_traffic_control_context()->is_use_downstream_cache &&
                           ugfc.IsTakeOver(UniverseGenericFlowControlType::UNIVERSE_USERPROFILE);
  bool enable_universe_user_profile_cache =
      !is_ugfc_take_over && FrontKconfUtil::enableUniverseProfileFrontCache();

  bool get_cache_success = enable_universe_user_profile_cache
    && ks::engine_base::UserProfileCache::GetInstance()->GetResponse(*req, res);
  ::grpc::Status status;
  std::shared_ptr<::kuaishou::ad::AdUserProfileResponse> ugfc_res_ptr;
  if (is_ugfc_take_over &&
      ugfc.Take(UniverseGenericFlowControlType::UNIVERSE_USERPROFILE, *req, ugfc_res_ptr) &&
      ugfc_res_ptr != nullptr) {
    (*ad_user_info) = ugfc_res_ptr->user_profile();
    auto& athena_device_info = session_data.get_ud_p_athena_ad_request()->device_info();
    if (ad_user_info->device_info_size() > 0 && athena_device_info.app_package().size() > 0) {
      std::set<std::string> app_package_set(ad_user_info->device_info(0).app_package().begin(),
          ad_user_info->device_info(0).app_package().end());
      for (const auto &item : athena_device_info.app_package()) {
        if (app_package_set.count(item.pkg_name()) == 0) {  // 去重
          ad_user_info->mutable_device_info(0)->add_app_package(item.pkg_name());
          ad_user_info->mutable_device_info(0)->add_app_first_install_time(item.first_install_time());
          ad_user_info->mutable_device_info(0)->add_app_last_update_time(item.last_update_time());
          ad_user_info->mutable_device_info(0)->add_is_system_app(item.system_app());
        }
      }
    }
  } else if (get_cache_success) {
    ad_user_info->Swap(res->mutable_user_profile());
  } else {
    falcon::Inc("front_server.ad_user_profile_service_req_cnt");
    auto start_ts = base::GetTimestamp();
    status = GrpcClient::AdUserProfileService_GetProfile(kAdUserProfileServiceUniverse,
                                                                      req, res);
    if (!status.ok()) {
      falcon::Inc("front_server.ad_user_profile_service_req_Universe2_failed");
    } else {
      if (is_ugfc_take_over && res->status() == ::kuaishou::ad::ResponseStatus::OK_STATUS &&
          res->user_profile().id() > 0) {
        ugfc_res_ptr = std::shared_ptr<::kuaishou::ad::AdUserProfileResponse>(
            new ::kuaishou::ad::AdUserProfileResponse());
        (*ugfc_res_ptr) = *res;
        ugfc.Store(UniverseGenericFlowControlType::UNIVERSE_USERPROFILE, *req, ugfc_res_ptr);
      } else if (enable_universe_user_profile_cache &&
                 res->status() == ::kuaishou::ad::ResponseStatus::OK_STATUS && res->user_profile().id() > 0) {
        ks::engine_base::UserProfileCache::GetInstance()->Insert(*req, *res);
      }
      ad_user_info->Swap(res->mutable_user_profile());
    }
    auto end_ts = base::GetTimestamp();
    falcon::Stat("front_server.ad_user_profile_service_cost_us", end_ts - start_ts);
  }
  ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_front", "universe_profile_cache_uid",
                                      get_cache_success ? "cache" : "not_cache",
                                      ad_user_info->id() > 0 ? "valid" : "zero",
                                      absl::StrCat(res->status()),
                                      status.error_message());
  LOG_EVERY_N(INFO, FrontKconfUtil::logReqDetailFrequency())
    << "\n ad_flow_type = " << static_cast<int32_t>(ad_flow_type);
  LOG_IF_EVERY_N(INFO, ad_user_info, FrontKconfUtil::logReqDetailFrequency())
    << " ad_flow_type = " << static_cast<int32_t>(ad_flow_type);
  LOG_IF_EVERY_N(INFO, fanstop_user_info, FrontKconfUtil::logReqDetailFrequency())
    << " ad_flow_type = " << static_cast<int32_t>(ad_flow_type);
}

}  // namespace front_server
}  // namespace ks
