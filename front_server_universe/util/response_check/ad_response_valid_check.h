#pragma once

#include <string>
#include <map>

#include "teams/ad/front_server_universe/engine/context_data/context_data.h"

#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"

namespace ks {
namespace front_server {
class ContextData;

class AdResponseValidCheck {
 private:
  ContextData* session_data_ = nullptr;

 public:
  explicit AdResponseValidCheck(ContextData *context_data, bool is_fanstop = false)
      : session_data_(context_data), is_fanstop_(is_fanstop) {}
  // 检查 response 中必要字段的有效性，含有无效字段则打点，可通过 kconf 控制某一字段无效时是否过滤该广告
  bool IsVaildAdResponse(const kuaishou::ad::AdResult& result, std::string response_type);

 private:
  // 广告层级的字段检查
  bool CheckIsValidUniverseDeliverInfo(
          const kuaishou::ad::UniverseAdDeliverInfo& universe_deliver_info,
          const std::map<std::string, bool>& config,
          const std::string& ocpc_action_type_str);
  bool CheckIsValidBaseInfo(
          const kuaishou::ad::AdBaseInfo& base_info,
          const std::map<std::string, bool>& config,
          const std::string& ocpc_action_type_str);
  bool CheckIsValidCallbackPassback(
          const kuaishou::ad::AdBaseInfo_AdCallbackPassback& callback_passback,
          const std::map<std::string, bool>& config,
          const std::string& ocpc_action_type_str);
  std::map<std::string, bool> GetConfig(std::string request_type);

 private:
  std::string invalid_filed{""};
  std::string invalid_log_str{""};
  bool is_write_log = false;
  bool invalid_but_not_filter = false;
  bool is_fanstop_;
};

}  // namespace front_server
}  // namespace ks

