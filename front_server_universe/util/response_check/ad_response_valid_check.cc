#include "teams/ad/front_server_universe/util/response_check/ad_response_valid_check.h"

#include "teams/ad/ad_base/src/util/fill_service_version.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"

#define CHECK_IS_INVALID_DOT_LOG(parent_filed, filed_name, invaild_value, log_str)                        \
  {                                                                                                       \
    session_data_->dot_perf->Count(1, "front_response_check", log_str, ocpc_action_type_str,              \
                                   ad_base::util::GetServiceVersionName(), is_fanstop_ ? "fanstop" : ""); \
    auto log_iter = log_config->find(#filed_name);                                                        \
    iter = config.find(#filed_name);                                                                      \
    if (iter != config.end()) {                                                                           \
      is_valid = is_valid && (!iter->second);                                                             \
    }                                                                                                     \
    if (invalid_filed.empty()) {                                                                          \
      invalid_filed = absl::StrCat(#parent_filed, ".", #filed_name);                                      \
    }                                                                                                     \
    if (log_iter != log_config->end()) {                                                                  \
      invalid_but_not_filter = true;                                                                      \
    }                                                                                                     \
  }

#define CHECK_IS_FIELD_INVALID_EQUAL(parent_filed, filed_name, invaild_value)    \
  {                                                                              \
    if (parent_filed.filed_name() == invaild_value) {                            \
      auto log_str = absl::StrCat(#parent_filed, ".unknown_", #filed_name);      \
      CHECK_IS_INVALID_DOT_LOG(parent_filed, filed_name, invaild_value, log_str) \
    }                                                                            \
  }

#define CHECK_IS_FIELD_INVALID_LESS_THAN(parent_filed, filed_name, invaild_value) \
  {                                                                               \
    if (parent_filed.filed_name() <= invaild_value) {                             \
      auto log_str = absl::StrCat(#parent_filed, ".invalid_", #filed_name);       \
      CHECK_IS_INVALID_DOT_LOG(parent_filed, filed_name, invaild_value, log_str)  \
    }                                                                             \
  }

#define CHECK_IS_STRING_FIELD_INVALID_EMPTY(parent_filed, filed_name)            \
  {                                                                              \
    if (parent_filed.filed_name().empty()) {                                     \
      auto log_str = absl::StrCat(#parent_filed, ".empty_", #filed_name);        \
      CHECK_IS_INVALID_DOT_LOG(parent_filed, filed_name, invaild_value, log_str) \
    }                                                                            \
  }

namespace ks {
namespace front_server {

bool AdResponseValidCheck::IsVaildAdResponse(const kuaishou::ad::AdResult& result,
                                              std::string response_type) {
  if (session_data_ == nullptr || session_data_->dot_perf == nullptr) {
    return true;
  }

  auto config = GetConfig(response_type);
  // 广告层级字段检查
  auto universe_devery_info = result.ad_deliver_info().universe_ad_deliver_info();
  auto base_info = result.ad_deliver_info().ad_base_info();
  auto callback_passback = base_info.ad_callback_passback();
  const auto& ocpc_action_type_str = kuaishou::ad::AdActionType_Name(base_info.ocpc_action_type());
  auto is_valid =
      CheckIsValidUniverseDeliverInfo(universe_devery_info, config, ocpc_action_type_str);
  is_valid = is_valid && CheckIsValidBaseInfo(base_info, config, ocpc_action_type_str);
  is_valid = is_valid &&
             CheckIsValidCallbackPassback(callback_passback, config, ocpc_action_type_str);
  auto creative_id = result.ad_deliver_info().ad_base_info().creative_id();
  if (!is_valid) {
    session_data_->dot_perf->Count(1, "front_response_check", "filtered_invalid_num",
                                    invalid_filed);
  }
  if (!is_valid || invalid_but_not_filter) {
    // 非法 pv 写 log
    const auto& style_info = session_data_->get_style_info_resp()->style_info();
    auto style_iter = style_info.find(base_info.creative_id());
    std::string style_info_str{};
    if (style_iter != style_info.end()) {
      style_info_str = style_iter->second.ShortDebugString();
    }
    std::string ad_result_str = result.ShortDebugString();
    LOG_IF(WARNING, FrontKconfUtil::enableWriteResponseCheckLog())
        << "front_response_check: " << response_type << ", invalid_filed: " << invalid_filed
        << ", llsid: " << session_data_->get_llsid() << ", ad_result: " << ad_result_str
        << ", style_info: " << style_info_str;
  }
  return is_valid;
}

bool AdResponseValidCheck::CheckIsValidUniverseDeliverInfo(
            const kuaishou::ad::UniverseAdDeliverInfo& universe_deliver_info,
            const std::map<std::string, bool>& config,
            const std::string& ocpc_action_type_str) {
  // config 中的值为 true 表示当该字段不合法时过滤， false 则不过滤
  // 该函数返回 true 表示不过滤
  bool is_valid = true;
  auto iter = config.end();
  auto log_config = FrontKconfUtil::invalidFiledWriteLog();

  CHECK_IS_STRING_FIELD_INVALID_EMPTY(universe_deliver_info, app_id)
  CHECK_IS_FIELD_INVALID_LESS_THAN(universe_deliver_info, page_id, 0)
  CHECK_IS_FIELD_INVALID_LESS_THAN(universe_deliver_info, sub_page_id, 0)
  CHECK_IS_FIELD_INVALID_LESS_THAN(universe_deliver_info, pos_id, 0)
  LOG_IF(INFO, !is_valid && session_data_->get_preview_type() == PreviewType::kPreviewFanstop)
      << "[preview ad] llsid:" << session_data_->get_llsid() << " CheckIsValidUniverseDeliverInfo faild";
  return is_valid;
}

bool AdResponseValidCheck::CheckIsValidBaseInfo(
            const kuaishou::ad::AdBaseInfo& base_info,
            const std::map<std::string, bool>& config,
            const std::string& ocpc_action_type_str) {
  // config 中的值为 true 表示当该字段不合法时过滤， false 则不过滤
  // 该函数返回 true 表示不过滤
  bool is_valid = true;
  auto iter = config.end();
  auto log_config = FrontKconfUtil::invalidFiledWriteLog();

  CHECK_IS_FIELD_INVALID_LESS_THAN(base_info, account_id, 0)
  CHECK_IS_FIELD_INVALID_LESS_THAN(base_info, campaign_id, 0)
  CHECK_IS_FIELD_INVALID_LESS_THAN(base_info, unit_id, 0)
  CHECK_IS_FIELD_INVALID_LESS_THAN(base_info, creative_id, 0)
  CHECK_IS_FIELD_INVALID_EQUAL(base_info, campaign_type,
                                kuaishou::ad::AdEnum_CampaignType_UNKNOWN_CAMPAIGN_TYPE)
  CHECK_IS_FIELD_INVALID_EQUAL(base_info, ad_source_type,
                                kuaishou::ad::AdSourceType::UNKNOWN_SOURCE_TYPE)
  CHECK_IS_FIELD_INVALID_EQUAL(base_info, charge_action_type,
                                kuaishou::ad::AdActionType::UNKNOWN_ACTION_TYPE)
  // CHECK_IS_FIELD_INVALID_EQUAL(base_info, creative_material_type,
  //                               kuaishou::ad::AdEnum_CreativeMaterialType_UNKNOWN_TYPE)
  CHECK_IS_FIELD_INVALID_EQUAL(base_info, unit_type,
                                kuaishou::ad::AdEnum_UnitType_UNKNOWN_TYPE_UNIT)
  if (base_info.item_type() != kuaishou::ad::AdEnum_ItemType_ITEM_LIVE &&
      (!base_info.ad_photo()) &&
      base_info.creative_material_type() != kuaishou::ad::AdEnum::ITEM_CARD) {
    // 直播推广没有 photo_id
    CHECK_IS_FIELD_INVALID_LESS_THAN(base_info, photo_id, 0)
    CHECK_IS_FIELD_INVALID_LESS_THAN(base_info, cover_id, 0)
  }
  if (IS_OCPX(base_info.bid_type())) {
    CHECK_IS_FIELD_INVALID_EQUAL(base_info, ocpc_action_type,
                                      kuaishou::ad::UNKNOWN_ACTION_TYPE)
  }
  if (base_info.campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
      base_info.campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE ||
      base_info.campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
      ((base_info.campaign_type() == kuaishou::ad::AdEnum_CampaignType_SITE_PAGE ||
        base_info.campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_CID ||
        base_info.campaign_type() == kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_AD_WX_MINI_APP)
      && base_info.account_type() != AdEnum::ACCOUNT_SELF_SERVICE) ||
      (base_info.campaign_type() == kuaishou::ad::AdEnum_CampaignType_LANDING_PAGE &&
       base_info.ad_source_type() != kuaishou::ad::BRAND)) {
    if (base_info.account_type() != AdEnum::ACCOUNT_LSP &&
        base_info.account_type() != AdEnum::ACCOUNT_SOCIAL) {
      CHECK_IS_STRING_FIELD_INVALID_EMPTY(base_info, url)
    }
  }

  LOG_IF(INFO, !is_valid && session_data_->get_preview_type() == PreviewType::kPreviewFanstop)
      << "[preview ad] llsid:" << session_data_->get_llsid() << " CheckIsValidBaseInfo faild";
  return is_valid;
}

bool AdResponseValidCheck::CheckIsValidCallbackPassback(
            const kuaishou::ad::AdBaseInfo_AdCallbackPassback& callback_passback,
            const std::map<std::string, bool>& config,
            const std::string& ocpc_action_type_str) {
  // config 中的值为 true 表示当该字段不合法时过滤， false 则不过滤
  // 该函数返回 true 表示不过滤
  bool is_valid = true;
  auto iter = config.end();
  auto log_config = FrontKconfUtil::invalidFiledWriteLog();

  CHECK_IS_FIELD_INVALID_EQUAL(callback_passback, ad_scene,
                                kuaishou::ad::AdEnum_AdScene_UNKNOWN_SCENE)
  CHECK_IS_FIELD_INVALID_EQUAL(callback_passback, charge_action_type,
                                kuaishou::ad::AdActionType::UNKNOWN_ACTION_TYPE)
  CHECK_IS_FIELD_INVALID_EQUAL(callback_passback, ad_source_type,
                                kuaishou::ad::AdSourceType::UNKNOWN_SOURCE_TYPE)
  // CHECK_IS_FIELD_INVALID_EQUAL(callback_passback, creative_material_type,
  //                               kuaishou::ad::AdEnum_CreativeMaterialType_UNKNOWN_TYPE)
  CHECK_IS_FIELD_INVALID_EQUAL(callback_passback, unit_type,
                                kuaishou::ad::AdEnum_UnitType_UNKNOWN_TYPE_UNIT)
  CHECK_IS_FIELD_INVALID_EQUAL(callback_passback, placement_type,
                                kuaishou::ad::AdEnum_PlacementType_UNKNOWN_PT)
  if (IS_OCPX(callback_passback.bid_type())) {
    CHECK_IS_FIELD_INVALID_EQUAL(callback_passback, ocpc_action_type,
                                  kuaishou::ad::UNKNOWN_ACTION_TYPE)
  }
  CHECK_IS_FIELD_INVALID_LESS_THAN(callback_passback, pos_id, 0)
  CHECK_IS_FIELD_INVALID_LESS_THAN(callback_passback, cover_id, 0)
  LOG_IF(INFO, !is_valid && session_data_->get_preview_type() == PreviewType::kPreviewFanstop)
      << "[preview ad] llsid:" << session_data_->get_llsid() << " CheckIsValidCallbackPassback faild";
  return is_valid;
}

std::map<std::string, bool> AdResponseValidCheck::GetConfig(std::string request_type) {
  auto& config = is_fanstop_ ? FrontKconfUtil::enableFilterInvalidFanstopAdResponse()->data()
      : FrontKconfUtil::enableFilterInvalidAdResponse()->data();
  if (request_type.compare("explore") == 0) {
    return config.explore_config;
  }
  if (request_type.compare("nearby") == 0) {
    return config.nearby_config;
  }
  if (request_type.compare("galaxy") == 0) {
    return config.galaxy_config;
  }
  if (request_type.compare("universe") == 0) {
    return config.universe_config;
  }
  if (request_type.compare("follow") == 0) {
    return config.follow_config;
  }
  return std::map<std::string, bool>();
}

}  // namespace front_server
}  // namespace ks
