# -*- coding: UTF8 -*-
import json
import os

config = {
  "max_ack_rank_info": 10,
  "ack_rank_info_sample_rate": 0.2,
  "server_show_log_producer_config": {
    "topic" : "ad_server_show_log"
  },
  "server_show_log_producer_config_test": {
    "topic" : "ad_server_show_log_test1"
  },
  "server_show_log_ads_producer_config": {
    "topic" : "ad_server_show_log_ads"
  },
  "server_show_log_ads_producer_config_test": {
    "topic" : "ad_server_show_log_ads_test"
  },
  "ad_log_producer_config": {
    "topic" : "ad_log"
  },
  "ad_dsp_server_show_queue": {
    "topic": "ad_dsp_server_show",
    "zk_path": "/kuaishou/kafkaLogs/producers/ZL_COMMON1",
    "extra_consumer_params" : "kuaishou.set.offset.ms.ago=1"
  },
  "explore_max_fanstop_num" : 4,
  "explore_max_ad_num" : 1,

  "detail_user_predict_client": "grpc_ad_detail_cpm_detail_cpm_online_lxd_v0_predict_server",
  "detail_ios_min_app_version": "5.2.0.222",
  "pearl_adserver_timeout_ms": 300,
  "kuaishoudianwan_adserver_timeout_ms": 300,
  "android_min_app_version": "5.0.2.4577",
  # 基于 grpc 的 tcpcopy, 可以设置 dryrun 环境
  "grpc_tcp_copy" : {
    "default" : "online",
    # tcp copy 环境
    #"ad-rs171.idcgz1.hn1.kwaidc.com" : "dryrun_tcpcopy_receiver",   # dryrun
  },
  "ack_redis_config": {
    "zk_path":"config.zk.cluster.zw:2181:/ks2/redis/adUsedItem/_ZW",
    "max_connections_per_proxy": 5,
    "redis_timeout_mills": 10,
    "ttl_in_seconds": 30,
    "dsp_key_prefix": "ad_dsp_nn",
    "detail_key_prefix": "ad_detail_nn",
    "dsp_bid_prefix_key": "ad_dsp_bid",
  },
  "ack_kafka_config": {
    "kafka_topic" : "server_show_ack_predict_server",
    # Partition count of the topic.
    "partition_cnt" : 60,
    "dsp_key_prefix": "ad_dsp_nn",
    "detail_key_prefix": "ad_detail_nn",
    "dsp_bid_prefix_key": "ad_dsp_bid",
  },
  "adx_ack_kafka_config": {
    "kafka_topic" : "adx_server_show_ack_predict_server",
    "adx_key_prefix" : "ad_adx_nn",
  },
  "retrieval_ack_kafka_config": {
    "kafka_topic" : "ad_server_target_response",
    "dsp_key_prefix" : "ad_dsp_retrieval",
    "detail_key_prefix": "ad_detail_retrieval",
  },
  "detail_cpm_ack_kafka_config": {
    "kafka_topic" : "detail_cpm_server_show_ack_predict_server",
    "partition_cnt" : 120,
    "detail_key_prefix": "ad_detail_cpm_nn",
  },
  "universe_cpm_ack_kafka_config": {
    "kafka_topic" : "ad_pv_cpm_ack_ps_universe",
    "partition_cnt" : 40,
    "detail_key_prefix": "ad_universe_cpm_nn",
  },
  "new_universe_cpm_ack_kafka_config": {
    "kafka_topic" : "ad_pv_cpm_ack_ps_universe_new",
    "partition_cnt" : 40,
    "detail_key_prefix": "ad_universe_cpm_nn",
  },
  "delivery_candidate_kafka_config": {
    "kafka_topic" : "delivery_candidate",
    "candidate_key_prefix": "ad_candidate",
  },
  "detail_delivery_candidate_kafka_config": {
    "kafka_topic" : "universe_delivery_candidate",
    "candidate_key_prefix": "ad_detail_candidate",
  },
  "_by_ab_test_group_config": {
    "_default": {
      "max_old_user_id" : 1400000001,
    },
    "world_ads_legacy_hash": {
      "expansion_exp": {
        "more_expansion": {
          "request_flow_type": 3,
          "userinfo_cover_id_repeat_check_seconds": 86400,
          "userinfo_dup_cover_id_repeat_check_seconds": 86400,
          "cpm_protection_min_value": 30.0,
        },
      },
    }
  },
  "_user_experiment_allocation": {
    "world_ads_legacy_hash": {
      "expansion_exp": {
        "expansion_base": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89],
        "more_expansion": [90, 91, 92, 93, 94, 95],
      },
      "no_delivery": {
        "no_delivery_1": [99], #极速版放量
        "no_delivery_2": [98], #极速版放量
        "no_delivery_3": [97], #极速版放量
        "no_delivery_4": [96], #极速版放量
      }
    }
  },
  "grpc" : {
    "test" : False,
    "server" : {
      "kess_name" : "USE_KSN_AS_SERVICE",  # kess_name 可以自动扩展为 kess_name + "_" + ksp 分组, default 不扩展
      "port" : 20012,
      "kcs_grpc_port_key" : "AUTO_PORT1",  # 支持服务部署到容器上， 此时 port 参数失效
      "quit_wait_seconds" : 120, # 默认 60s
      "thread_num": 800  ## default cpu num
    },
    "client_map" : {
      ## key - > kess_name kess_name 可以自动扩展 KSP 分组, 如果满足以下条件
      #  1. 当前服务运行的环境 ksp 分组名字在 Kconf ad.engine.allowKspGroupNames 中
      #  2. kess_name + "_" + ksp 分组名字在 Kconf  ad.engine.kessGrpcClients 中
      "AdUserProfileService" : "ad-user-profile", #"grpc_adUserProfileService",
      "NewAdPosGrpcService" : "grpc_sspService",
      "forward_index_client": "ad-forward-index", #"grpc_adForwardIndexService",
      "forward_index_client_dpa": "grpc_adForwardIndexService_dpa",
      "AdUserProfileServiceUniverse": "ad-user-profile-universev2", #"grpc_adUserProfileService_universev2"
      "AdUserProfileServiceKnews": "ad-user-profile-knews", #"grpc_adUserProfileService_knews",
      "AdBrandGrpcService": "ad-brand", #"grpc_adBrandServer",
      "AdBrandGrpcServiceKnewsSeries": "ad-brand-universe", #"grpc_adBrandServer_universe",
      "AdBrandGrpcServiceContent": "ad-brand-content", #"grpc_adBrandServer_content",
      "AdBrandGrpcServiceSearch": "ad-brand-search", #"grpc_adBrandServer_search",
      # jk 服务
      "ad_adx_track_client":"grpc_adxTrackService",
      "new_ad_brand_client" : "ad-brand-pinpai", #"grpc_adBrandServer_pinpai",
      "merchant_user_cache_client" : "grpc_apiCoreUserCacheService",
      "merchant_user_count_client" : "grpc_apiCoreUserCountService",
      "ad_rta_client": "grpc_rtaService",
      "KwaishopProductCBaseinfoService":"kwaishop-product-cbaseinfo-center",
      "KwaishopProductDynamicAttrService":"kwaishop-product-cbaseinfo-center",
      "LiveStreamDisplayCountQueryService": "grpc_liveStreamDisplayCountQueryService",
      "KwaishopProductDetailService": "kwaishop-product-detail-service",
      "MerchantPhotoItemService": "grpc_merchantPhotoItemService",
      # 粉条白名单
      "FansTopWhiteListService": "grpc_fansTopWhiteListService",
      # 搜索热点词配置
      "MmuOperationHotSpotService": "grpc_mmuOperationHotSpotService",
      "FlowSearchHotBoardService": "operation-flow-digraph-special-runner.flow-search-hot-board-service",
      "ApiCorePhotoAuthorService": "grpc_apiCorePhotoAuthorService",
      # 品牌体验白名单
      "AdBrandQueryRpcServiceV2": "grpc_adBrandInfoQueryServiceV2",
      "grpc_EDS":"grpc_EDS",
      # 联盟流量预估服务配置
      "universe_pv_quality_router_client": "ad-predict-router", #"grpc_adPsRouterServer",
      "ad_style_server_splash" : "ad-style-server-splash", #"grpc_adStyleServer_splash",
      "ad_style_server_universe" : "ad-style-server-universe", #"grpc_adStyleServer_universe",
      "ad_style_server" : "ad-style-server", #"grpc_adStyleServer",
      "universe_debug_target" : "ad-target-server-universe", #"grpc_adTargetService_universe",
      "kwaishopSelectionUserService" : "kwaishop-selection-user-service",
      "userOrderShopService" : "grpc_adUserOrderedShopQueryRpcService",
      "kwaishopAddressService" : "grpc_kwaishopAddressService",
      "ad_innerloop_userdata_service" : "innerloop-userdata-service-proxy",
      "ad_session_client": "ad-session-server", #"grpc_adSessionService"
      "PromotionBaseDataRheaDataService" : "grpc_promotionBaseDataRheaDataService",
    },
    "not_modify_kess_client_map" : {
      "grpc_adUserProfileService_universev2": 1,
      "grpc_adServer_knews": 1
    },
    "client_level" : {
      "grpc_adSocialRecoFollowService" : "STRONG_DEPEND",
      "ad-brand-universe" : "STRONG_DEPEND", # "grpc_adBrandServer_universe" : "STRONG_DEPEND",
      "ad-query-retrieval" : "STRONG_DEPEND", # "grpc_queryRetrievalService" : "STRONG_DEPEND",
      "ad-user-profile-universev2" : "STRONG_DEPEND", # "grpc_adUserProfileService_universev2" : "STRONG_DEPEND",
      "grpc_adForwardIndexService_fanstop" : "STRONG_DEPEND",
      "ad-user-profile-knews" : "STRONG_DEPEND", # "grpc_adUserProfileService_knews" : "STRONG_DEPEND",
      "ad-brand" : "STRONG_DEPEND", # "grpc_adBrandServer" : "STRONG_DEPEND",
      "ad-user-profile" : "STRONG_DEPEND",# "grpc_adUserProfileService" : "STRONG_DEPEND",
      "grpc_rtaService" : "STRONG_DEPEND",
      "ad-user-data-server" : "STRONG_DEPEND",
      "grpc_adForwardIndexService_dpa" : "STRONG_DEPEND",
      "ad-pack-server-universe" : "STRONG_DEPEND",
      "ad-server" : "STRONG_DEPEND", # "grpc_adServer" : "STRONG_DEPEND",
      "ad-brand-pinpai" : "STRONG_DEPEND", # "grpc_adBrandServer_pinpai" : "STRONG_DEPEND",
      "ad-server-fanstop" : "STRONG_DEPEND", # "grpc_adServer_fanstop" : "STRONG_DEPEND",
      "ad-server-splash" : "STRONG_DEPEND",
      "ad-server-search" : "STRONG_DEPEND",
      "ad-server" : "STRONG_DEPEND",
      "ad-server-fanstop" : "STRONG_DEPEND",
      "ad-server-splash" : "STRONG_DEPEND",
      "ad-server-search" : "STRONG_DEPEND",
      "ad-server-universe" : "STRONG_DEPEND",
      "ad-server-universe-tiny" : "STRONG_DEPEND",
      "ad-forward-index" : "STRONG_DEPEND",# "grpc_adForwardIndexService" : "STRONG_DEPEND",
      "grpc_adForwardIndexService_stylematerial" : "STRONG_DEPEND",
      "ad-pack-server-long-tail-flow" : "STRONG_DEPEND",
      "ad-server-universe" : "STRONG_DEPEND", # "grpc_adServer_universe" : "STRONG_DEPEND",
      "ad-session-server" : "STRONG_DEPEND", # "grpc_adSessionService" : "STRONG_DEPEND",
      "grpc_adP2pArbiterService" : "STRONG_DEPEND",
      "ad-pack-server" : "STRONG_DEPEND",
      "grpc_antispamAdUnionRequestRpcService" : "STRONG_DEPEND",
      "grpc_adServer_galaxy" : "STRONG_DEPEND",
      "grpc_adUserProfileService_universe" : "STRONG_DEPEND",
      "ad-predict-router-dsp-retrieval" : "STRONG_DEPEND",
      "ad-predict-router" : "STRONG_DEPEND", # "grpc_adPsRouterServer" : "STRONG_DEPEND",
      "grpc_liveStreamDisplayCountQueryService" : "WEAK_DEPEND",
      "grpc_mmuOperationHotSpotService" : "WEAK_DEPEND",
      "ad-predict-router-dsp" : "STRONG_DEPEND",
      "ad-predict-router-dsp-ic" : "STRONG_DEPEND",
      "ad-twin-towers-router-universe" : "WEAK_DEPEND",
      "grpc_apiCoreUserCountService" : "WEAK_DEPEND",
      "grpc_EDS" : "WEAK_DEPEND",
      "grpc_adBrandInfoQueryServiceV2" : "WEAK_DEPEND",
      "grpc_fansTopWhiteListService" : "WEAK_DEPEND",
      "grpc_adPsFanstopRouterServer" : "WEAK_DEPEND",
      "grpc_apiCoreUserCacheService" : "WEAK_DEPEND",
      "grpc_apiCorePhotoAuthorService" : "WEAK_DEPEND",
      "grpc_kwaishopAddressService" : "STRONG_DEPEND",
      "innerloop-userdata-service-proxy": "STRONG_DEPEND",
      "kwaishop-selection-user-service" : "WEAK_DEPEND",
      "grpc_adUserOrderedShopQueryRpcService" : "WEAK_DEPEND",
      "grpc_win_rate_infer_v1" : "WEAK_DEPEND",
      "grpc_sspService" : "WEAK_DEPEND", # 有代码，无访问
      "ad-style-server" : "WEAK_DEPEND", # "grpc_adStyleServer" : "WEAK_DEPEND", # 有代码，无访问
      "ad-style-server-splash" : "STRONG_DEPEND", # "grpc_adStyleServer_splash" : "STRONG_DEPEND",
      "ad-brand-content" : "STRONG_DEPEND", # "grpc_adBrandServer_content" : "WEAK_DEPEND", # 有代码，无访问
      "grpc_adServer_thanos" : "WEAK_DEPEND", # 有代码，无访问
      "grpc_merchantPhotoItemService" : "WEAK_DEPEND", # 有代码，无访问
      "grpc_adxTrackService" : "WEAK_DEPEND",  # 有代码，无访问
      "grpc_adStyleServer_default" : "WEAK_DEPEND",  # 有代码，无访问
      "grpc_adDspBaseRpcService" : "WEAK_DEPEND",  # 有代码，无访问
      "grpc_adServer_knews" : "WEAK_DEPEND",  # 有代码，无访问
      "grpc_adEcomProductCacheService" : "WEAK_DEPEND",  # 有代码，无访问
      "grpc_adServer_merchant" : "WEAK_DEPEND",  # 有代码，无访问
      "grpc_antispamUnionAdRequestService" : "WEAK_DEPEND",  # 有代码，无访问
      "grpc_merchantMarketingResourceQueryService" : "WEAK_DEPEND", # 有配置，无访问
      "grpc_merchantMarketingResourceQueryServiceV2": "WEAK_DEPEND", # 有配置，无访问
      "grpc_ad_detail_cpm_detail_cpm_online_lxd_v0_predict_server": "WEAK_DEPEND", # 有配置，无访问
      "grpc_adForwardIndexService_stylematerial_offline": "WEAK_DEPEND", # 有配置，无访问
      "grpc_adBrandService": "WEAK_DEPEND", # 有配置，无访问
      "grpc_adLogSendRpcService": "WEAK_DEPEND", # 有配置，无访问
      "grpc_kwaishop-product-detail-service" : "WEAK_DEPEND", # 未找到
    },
    "custom_client_level": {
        "ad-front-server-archimedes": {  # 内粉部署用独立的定制配置
            "ad-forward-index" : "STRONG_DEPEND",
            "ad-user-profile" : "STRONG_DEPEND",
            "ad-user-data-server" : "STRONG_DEPEND",
            "grpc_adServer_archimedes": "STRONG_DEPEND",
            "ad-server-archimedes" : "STRONG_DEPEND",
            "ad-pack-server" : "STRONG_DEPEND",
            "grpc_adSocialRecoFollowService": "DEGRADE_TEST",
            "grpc_adBrandServer_universe": "DEGRADE_TEST",
            "grpc_queryRetrievalService": "DEGRADE_TEST",
            "grpc_adUserProfileService_universev2": "DEGRADE_TEST",
            "grpc_adForwardIndexService_fanstop": "STRONG_DEPEND",
            "grpc_adUserProfileService_knews": "DEGRADE_TEST",
            "grpc_adBrandServer": "DEGRADE_TEST",
            "grpc_adUserProfileService": "STRONG_DEPEND",
            "grpc_rtaService": "DEGRADE_TEST",
            "grpc_adForwardIndexService_dpa": "DEGRADE_TEST",
            "grpc_adPackService_universe": "DEGRADE_TEST",
            "grpc_adServer": "DEGRADE_TEST",
            "grpc_adBrandServer_pinpai": "DEGRADE_TEST",
            "grpc_adServer_fanstop": "DEGRADE_TEST",
            "grpc_adServer_splash": "DEGRADE_TEST",
            "grpc_adServer_search": "DEGRADE_TEST",
            "grpc_adForwardIndexService": "STRONG_DEPEND",
            "grpc_adForwardIndexService_stylematerial": "DEGRADE_TEST",
            "grpc_adPackService_long-tail-flow": "DEGRADE_TEST",
            "grpc_adServer_universe": "DEGRADE_TEST",
            "grpc_adSessionService": "DEGRADE_TEST",
            "grpc_adP2pArbiterService": "STRONG_DEPEND",
            "grpc_adPackService": "STRONG_DEPEND",
            "grpc_antispamAdUnionRequestRpcService": "DEGRADE_TEST",
            "grpc_adServer_galaxy": "DEGRADE_TEST",
            "grpc_adUserProfileService_universe": "DEGRADE_TEST",
            "grpc_adPsDspRetrievalRouterServer": "DEGRADE_TEST",
            "grpc_adPsRouterServer": "DEGRADE_TEST",
            "grpc_liveStreamDisplayCountQueryService": "DEGRADE_TEST",
            "grpc_adPsDspNNRouterServer": "DEGRADE_TEST",
            "grpc_apiCoreUserCountService": "DEGRADE_TEST",
            "grpc_EDS": "DEGRADE_TEST",
            "grpc_adBrandInfoQueryServiceV2": "DEGRADE_TEST",
            "grpc_fansTopWhiteListService": "WEAK_DEPEND",
            "grpc_mmuOperationHotSpotService": "WEAK_DEPEND",
            "grpc_apiCorePhotoAuthorService": "DEGRADE_TEST",
            "grpc_adPsFanstopRouterServer": "DEGRADE_TEST",
            "grpc_apiCoreUserCacheService": "DEGRADE_TEST",
            "grpc_sspService": "DEGRADE_TEST",  # 有代码，无访问
            "grpc_adStyleServer": "DEGRADE_TEST",  # 有代码，无访问
            "grpc_adBrandServer_content": "DEGRADE_TEST",  # 有代码，无访问
            "grpc_adServer_thanos": "DEGRADE_TEST",  # 有代码，无访问
            "grpc_merchantPhotoItemService": "DEGRADE_TEST",  # 有代码，无访问
            "grpc_adxTrackService": "DEGRADE_TEST",  # 有代码，无访问
            "grpc_adStyleServer_default": "DEGRADE_TEST",  # 有代码，无访问
            "grpc_adDspBaseRpcService": "DEGRADE_TEST",  # 有代码，无访问
            "grpc_adServer_knews": "DEGRADE_TEST",  # 有代码，无访问
            "grpc_adEcomProductCacheService": "DEGRADE_TEST",  # 有代码，无访问
            "grpc_adServer_merchant": "DEGRADE_TEST",  # 有代码，无访问
            "grpc_antispamUnionAdRequestService": "DEGRADE_TEST",  # 有代码，无访问
            "grpc_merchantMarketingResourceQueryService": "DEGRADE_TEST",  # 有配置，无访问
            "grpc_merchantMarketingResourceQueryServiceV2": "DEGRADE_TEST",  # 有配置，无访问
            "grpc_ad_detail_cpm_detail_cpm_online_lxd_v0_predict_server": "DEGRADE_TEST",  # 有配置，无访问
            "grpc_adForwardIndexService_stylematerial_offline": "DEGRADE_TEST",  # 有配置，无访问
            "grpc_adBrandService": "DEGRADE_TEST",  # 有配置，无访问
            "grpc_adLogSendRpcService": "DEGRADE_TEST",  # 有配置，无访问
            "grpc_kwaishop-product-detail-service": "DEGRADE_TEST",  # 未找到
        }
    }
  },
  "version_log" : {
    "user" : "jenkins",
    "client": "CLIENT",
    "ad_front_server" : "FRONT_SERVER",
    "ad_server" : "AD_SERVER",
    "ad_target_server" : "TARGET_SERVER",
    "ad_rank_server" : "RANK_SERVER"
  },
  "ad-front-server-universe" : {
    "ksn_flow_mapping" : {
      "accepted_flow_types" :["FLOW_UNIVERSE", "FLOW_UNIVERSE_TINY", "FLOW_UNIVERSE_SWIFT"],
      "deployment" : "DEPLOYMENT_DSP"
    }
  },
  "ad-front-server-universe-tiny" : {
    "ksn_flow_mapping" : {
      "accepted_flow_types" :["FLOW_UNIVERSE", "FLOW_UNIVERSE_TINY", "FLOW_UNIVERSE_SWIFT"],
      "deployment" : "DEPLOYMENT_DSP"
    }
  },
  "callback_map" : {
    "uri6.com" : "*callback=__CALLBACK__&idfa=__IDFA__&imei=__IMEI__",
    "lnk0.com" : "*callback_url=__CALLBACK__&mac_md5=__MAC2__&idfa=__IDFA__&imei=__IMEI__",
    "extads.gameloft.com" : "*callback=__CALLBACK_PARAM__&ip=__IP__&idfa=__IDFA__",
    "ac.o2.qq.com" : "*callback=__CALLBACK__&imei=__IMEI__&ifa=__IDFA__&cip=__IP__&time=__TS__",
    "gad.netease.com" : "*imei=__CALLBACK_PARAM__&idfa=__IDFA__",
    "at.pinduoduo.com" : "*imei=__IMEI__&callback=__CALLBACK__"
  },
  "track_operation_type" : {
    "miaozhen.com" : 2,
  },
  "ad_universe_server_show_queue": {
    "topic": "ad_universe_server_show",
    "zk_path": "/kuaishou/kafkaLogs/producers/LF_LOG1"
  }
}
