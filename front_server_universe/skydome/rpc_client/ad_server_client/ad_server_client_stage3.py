import json
from ..send_common_attrs import send_to_adserver_common_attrs as send_common_attrs
from .recv_common_attrs import recv_common_attrs as recv_common_attrs_
from ..recv_table_columns import recv_ad_server_table_columns as recv_table_columns

send_table_columns = [
]

recv_common_attrs = recv_common_attrs_ + [
  {"name": "ad_rank_request", "as": "ad_server_response.ad_rank_request"}
]

param = dict(
  name = "ad_server_client_stage3",
  kess_service = "{{ad_server_ksn}}",
  send_common_attrs = send_common_attrs,
  recv_common_attrs = recv_common_attrs,
  send_table_columns = send_table_columns,
  recv_table_columns = recv_table_columns
)
