#!/usr/bin/env python3
# coding=utf-8
import os
import sys
import json
sys.path.append(os.path.normpath(os.path.dirname(os.path.realpath(__file__))+ "/../../.."))  # teams/ad/
from ad_base.src.attrs.ad_item_attrs import get_item_attr_keys_for
from .send_common_attrs import send_to_rank_common_attrs as send_common_attrs
from .recv_table_columns import recv_ad_rank_table_columns as recv_table_columns
recv_common_attrs = [
  "ad_rank_response"
]

## 不发送 table attr, 发送一张空表
param = dict(
  name = "ad_rank_client",
  kess_service = "{{ad_rank_ksn}}",
  send_common_attrs = send_common_attrs,
  recv_common_attrs = recv_common_attrs,
  send_table_columns = [
    {
      "name" : "global_ad_table",  "attrs" :[]
    }
  ],
  recv_table_columns = recv_table_columns
)

# 发送 table attr
param_with_attr = param.copy()
param_with_attr.update(
  name = "ad_rank_client_with_attr",
  send_table_columns =  [
    {
      "name" : "global_ad_table",
      "attrs" : list(get_item_attr_keys_for("adserver", "rank"))
    }
  ]
)
# 发送 table attr 做 diff, 所有字段用别名
param_with_attr_diff = param.copy()
param_with_attr_diff.update(
  name = "ad_rank_client_with_attr_diff",
  send_table_columns =  [
    {
      "name" : "global_ad_table",
      "attrs" : [ {"name": req_item , "as": req_item + ".new"} for req_item in get_item_attr_keys_for("adserver", "rank")]
    }
  ]
)