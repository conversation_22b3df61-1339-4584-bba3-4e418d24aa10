# 用户数据
ud_common_attrs = [
  "ud_user_id",
  "ud_reservation_id",
  "ud_bigr_tag",
  "ud_photo_pay_order_price",
  "ud_live_pay_order_price",
  "ud_playlet_offer_info",
  "ud_product_interest_tags",
  "ud_user_promising_r3_brand_industry_set",
  "ud_user_group_lv_dym",
  "ud_game_experience_ts"
]

send_to_adserver_common_attrs = [
  # 默认传 "llsid", "user_id",
  {"name": "ad_server_request", "as": "request"},
  "rank_open_all_item_attr",
  "ad-rank-server.debug_host",
  "ad-rank-server.extra_time_out",
  "enable_move_build_ad_response_to_front_diff",
  "enable_move_build_ad_response_to_front",
  "enable_move_request_rank_to_front",
  "enable_move_request_jk_to_front"
]
send_to_adserver_common_attrs.extend(ud_common_attrs)


send_to_rank_common_attrs = [
  # 默认传 "llsid", "user_id",
  {"name": "ad_server_request", "as": "ad_server.raw_request"},  # 请求 adserver 前设置
  {"name": "ad_server_response.ad_rank_request", "as": "ad_rank_request"}, # adserver 返回
  "ad_server_to_rank_item_attr_flag"
]
send_to_rank_common_attrs.extend(ud_common_attrs)