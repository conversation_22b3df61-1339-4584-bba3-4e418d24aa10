#include "teams/ad/front_server_universe/engine/strategy/style_postproc/merchant_style_strategy.h"

#include <string>
#include <map>
#include <utility>
#include <unordered_set>

#include "teams/ad/ad_proto/kuaishou/ad/ad_forward_index.pb.h"
#include "teams/ad/front_server_universe/util/merchant_util/merchant_style_util.h"
#include "teams/ad/front_server_universe/engine/utils/merchant/merchant_logic.h"

namespace ks {
namespace front_server {

void MerchantStyleStrategy::SetDspBaseInfo(kuaishou::ad::AdBaseInfo* base_info,
                                           const StyleInfoItem& style_info) {
  if (base_info == nullptr) {
    return;
  }
  auto campaign_type = style_info.campaign().type();
  std::unordered_set<kuaishou::ad::AdEnum_CampaignType> campaign_type_set = {
    kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE, kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE,
    kuaishou::ad::AdEnum::TAOBAO,
    kuaishou::ad::AdEnum::APP, kuaishou::ad::AdEnum::LANDING_PAGE,
    kuaishou::ad::AdEnum::SITE_PAGE, kuaishou::ad::AdEnum::APP_ADVANCE,
    kuaishou::ad::AdEnum::AD_CID,
    kuaishou::ad::AdEnum::AD_WX_MINI_APP
  };
  if (campaign_type_set.count(campaign_type) <= 0) {
    return;
  }

  merchant_util::SetLiveReservationInfo(style_info, base_info);
}

}  // namespace front_server
}  // namespace ks
