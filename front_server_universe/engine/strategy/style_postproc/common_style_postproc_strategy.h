#pragma once

#include <string>

#include "base/common/basic_types.h"
#include "base/encoding/url_encode.h"
#include "teams/ad/front_server_universe/engine/strategy/strategy.h"
#include "teams/ad/ad_proto/kuaishou/ad/front_service.pb.h"

using kuaishou::ad::forward_index::StyleInfoItem;

namespace ks {
namespace front_server {
class ContextData;

class CommonStylePostStrategy
    : public Strategy
    , public StrategyBase<CommonStylePostStrategy> {
 public:
  explicit CommonStylePostStrategy(ContextData *context_data) : Strategy(context_data) {}
  static constexpr AdStrategyType class_name() {
    return AdStrategyType::CommonStylePostStrategy;
  }
  void FillCommonResult();

  std::string GetAccountMulExpValue(const StyleInfoItem& style_info);
};

}  // namespace front_server
}  // namespace ks

