#include "teams/ad/front_server_universe/engine/strategy/style_postproc/common_style_postproc_strategy.h"

#include <vector>
#include <string>

#include "glog/logging.h"
#include "teams/ad/engine_base/utils/utils.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"

namespace ks {
namespace front_server {


void CommonStylePostStrategy::FillCommonResult() {
  // 刷新方向提供给 server show
  session_data_->mutable_ad_response()->mutable_global_ext_data()->set_refresh_direction(
      ks::ad_base::GetRefreshDirection(*(session_data_->get_ad_request())));

  // 新的 location 库获取的 adcode 信息提供到 server_show log 中
  session_data_->mutable_ad_response()->mutable_global_ext_data()->set_adcode(
      session_data_->get_ad_request()->ad_user_info().adcode().origin_adcode());
}

std::string CommonStylePostStrategy::GetAccountMulExpValue(const StyleInfoItem& style_info) {
  std::string exp_name = "";
  auto account_exp_map = FrontKconfUtil::accountMulExpMap();
  auto account_exp_map_itr = account_exp_map->end();
  account_exp_map_itr = account_exp_map->find(
      std::to_string(style_info.account().id()));
  if (account_exp_map_itr != account_exp_map->end()) {
    exp_name = account_exp_map_itr->second;
  }
  std::vector<std::string> split_strs;
  base::SplitString(exp_name, ",", &split_strs);
  bool init_flag = false;
  std::string res = "";
  for (auto& splits : split_strs) {
    if (splits.empty()) {
      return "";
    }
    std::string exp_value = session_data_->get_spdm_ctx().
        TryGetString(splits, "");
    if (exp_value.empty()) {
      continue;
    }
    if (init_flag) {
      res += "-";
    }
    res += exp_value;
    init_flag = true;
  }
  return res;
}

}  // namespace front_server
}  // namespace ks
