#pragma once

#include "teams/ad/front_server_universe/engine/strategy/strategy.h"

namespace kuaishou {
namespace ad {
class AdBaseInfo;
class AdResult;
}
}

namespace ks {
namespace front_server {

class ContextData;

class MerchantStyleStrategy : public Strategy, public StrategyBase<MerchantStyleStrategy> {
 public:
  explicit MerchantStyleStrategy(ContextData *context_data)
      : Strategy(context_data) {}
  static constexpr AdStrategyType class_name() {
    return AdStrategyType::MerchantStyle;
  }

  void SetDspBaseInfo(kuaishou::ad::AdBaseInfo* base_info, const StyleInfoItem& style_info);
};


}  // namespace front_server
}  // namespace ks
