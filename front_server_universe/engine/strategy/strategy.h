#pragma once

#include "ks/base/abtest/session_context.h"
#include "teams/ad/ad_base/src/pattern/factory.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/front_server_universe/engine/strategy/enums.h"

namespace ks {
namespace front_server {
using ks::ad_base::pattern::FactorySignature;
using ks::ad_base::pattern::StaticFactory;
using ks::ad_base::pattern::FactoryItemBase;
class ContextData;

class Strategy {
  friend class StrategyManager;
 public:
  explicit Strategy(ContextData* context_data) : session_data_(context_data) {
  }
  virtual ~Strategy() = default;
  Strategy(const Strategy&) = delete;

 protected:
  virtual void Initialize() {
  }
  ContextData* session_data_;  // 请求上下文
};


using SigType = FactorySignature<Strategy, ContextData*>;
using AdStrategyFactory = StaticFactory<AdStrategyType, SigType>;
template<typename T>
using StrategyBase = FactoryItemBase<T, AdStrategyFactory, SigType>;

}  // namespace front_server
}  // namespace ks
