#pragma once

#include <map>
#include <utility>
#include <string>
#include <memory>

#include "base/common/logging.h"
#include "teams/ad/pslab/refactor/context.h"
#include "teams/ad/front_server_universe/engine/strategy/strategy.h"

namespace ks {
namespace front_server {

struct ConfigClass;
class StrategyManager : public ::ad::concept::refactor::ContextSystemBase<ConfigClass> {
 public:
  explicit StrategyManager(::ad::concept::refactor::Context<ConfigClass> *context);

  // pv 级初始化
  void Initialize() {
    for (auto &item : strategies_) {
      item.second->Initialize();
    }
  }

  // 获取策略接口
  template<typename T>
  std::shared_ptr<T> GetStrategy(const AdStrategyType &type) {
    auto iter = strategies_.find(type);
    if (iter != strategies_.end()) {
      return std::dynamic_pointer_cast<T>(iter->second);
    } else {
      return nullptr;
    }
  }

 private:
  std::map<AdStrategyType, std::shared_ptr<Strategy>> strategies_;
};

}  // namespace front_server
}  // namespace ks
