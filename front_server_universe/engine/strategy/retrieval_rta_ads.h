#pragma once

#include <utility>
#include <map>
#include <memory>
#include <string>
#include <vector>

#include "teams/ad/ad_base/src/kess/utils/half_async_base.h"
#include "teams/ad/ad_proto/kuaishou/ad/rta/rta_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"
#include "teams/ad/front_server_universe/engine/strategy/strategy.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"

using ::kuaishou::ad::rta::kess::RtaService;

using ks::ad_target::BaseHalfAsync;

namespace ks {
namespace front_server {

class RetrievalRtaAdsStrategy : public Strategy, public StrategyBase<RetrievalRtaAdsStrategy> {
 public:
  explicit RetrievalRtaAdsStrategy(ContextData* context_data)
      : Strategy(context_data) {}
  static constexpr AdStrategyType class_name() {
    return AdStrategyType::RetrievalRtaAdsStrategy;
  }
  ~RetrievalRtaAdsStrategy() {
  }

  void Process();
  void Start();

 protected:
  bool NeedReqRta(const kuaishou::ad::AdRequest *request, ContextData *session_data);
  bool FillIds(kuaishou::ad::AdRequest *request, ContextData *session_data);

 private:
  class AsyncRetrievalRta
      : public BaseHalfAsync<kuaishou::ad::rta::RtaRequest, kuaishou::ad::rta::RtaResponse> {
   public:
    AsyncRetrievalRta(kuaishou::ad::rta::RtaRequest* req, kuaishou::ad::rta::RtaResponse* resp)
        : BaseHalfAsync<kuaishou::ad::rta::RtaRequest,
                        kuaishou::ad::rta::RtaResponse>(req, resp) {}
    void Initialize(ContextData* session_data) { session_data_ = session_data; }
    bool SetRequest(kuaishou::ad::rta::RtaRequest* req) override;
    REGISTER_CLIENT_METHOD(RtaService, ad_rta_client, GetRtaResult)

   protected:
    bool DataProc(const ::grpc::Status& status, kuaishou::ad::rta::RtaResponse* resp) override {
      return true;
    }
    ContextData* session_data_{nullptr};
  };

  struct RtaParam {
    AsyncRetrievalRta *async_rta_retrieval = nullptr;
    bool need_request_rta = false;
    void Clear();
  };

  kuaishou::ad::rta::RtaRequest rta_request_;
  RtaParam rta_param_;
  std::string imei_;
  std::string oaid_;
  std::string idfa_;
};

}  // namespace front_server
}  // namespace ks
