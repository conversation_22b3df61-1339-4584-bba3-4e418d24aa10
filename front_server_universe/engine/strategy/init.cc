#include "teams/ad/front_server_universe/engine/strategy/init.h"
#include "teams/ad/front_server_universe/engine/strategy/admit/universe_elastic_control.h"
#include "teams/ad/front_server_universe/engine/strategy/style_postproc/common_style_postproc_strategy.h"
#include "teams/ad/front_server_universe/engine/strategy/style_postproc/merchant_style_strategy.h"
#include "teams/ad/front_server_universe/engine/strategy/retrieval_rta_ads.h"

namespace ks {

namespace front_server {
// 如果策略不再使用，从这里删除，避免不必要的操作

void RegisterStrategies() {
  CommonStylePostStrategy::RegisterFactory();
  UniverseElasticControl::RegisterFactory();
  MerchantStyleStrategy::RegisterFactory();
  RetrievalRtaAdsStrategy:: RegisterFactory();
}

}  // namespace front_server
}  // namespace ks
