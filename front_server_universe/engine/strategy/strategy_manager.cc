#include "teams/ad/front_server_universe/engine/strategy/strategy_manager.h"

#include <map>
#include <utility>
#include <string>
#include <memory>

#include "teams/ad/pslab/refactor/context.h"
#include "teams/ad/front_server_universe/engine/strategy/strategy.h"
#include "teams/ad/front_server_universe/engine/server/init.h"

namespace ks {
namespace front_server {

StrategyManager::StrategyManager(::ad::concept::refactor::Context<ConfigClass> *context) :
    ::ad::concept::refactor::ContextSystemBase<ConfigClass>(context) {
  // 创建所有的 strategy
  auto types = AdStrategyFactory::Instance().GetAllValidKey();
  ContextData *context_data = context_->GetMutableContextData<ContextData>();
  for (auto &type : types) {
    strategies_[type] =
        AdStrategyFactory::Instance().Create(std::move(type), context_data);
    // LOG(INFO) << "Create Strategy, strategy type:" << static_cast<int>(type)
    //          << " context_data:" << context_data;
  }
}

}  // namespace front_server
}  // namespace ks
