#pragma once
#include <tuple>
#include <string>
#include "teams/ad/front_server_universe/engine/utils/ad_libretrieval/base_retrieval_handler.h"
#include "teams/ad/front_server_universe/engine/utils/ad_libretrieval/base_retrieval_func.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.kess.grpc.pb.h"

namespace ks {
namespace front_server {

class DspRetrievalHandler: public BaseRetrievalHandler {
 public:
  DspRetrievalHandler(): BaseRetrievalHandler("async_rpc_call_tag_dsp_retrieval") {}
  void Init(AdContext* context, ContextData* session_data, BaseRetrievalFunc* retrieval_func);
  void BuildAndSend();
  bool ReceiveParse();
  void ParsePostProc(const std::string& tag = "");
  bool IsRetrievalValid(AdContext* context);
  void Clear() {
    context_ = nullptr;
    is_downgrade_archimedes_ = false;
    invalid_flag = 0;
    ClearBase();
  }

 private:
  bool FillAdServerRequestAttr();
  // 联盟 ps 预取，放在此处是为了减少请求
  void UniversePsPreapreBuildAndSend();
  void AsyncSend(const std::string& kess_name, uint64_t hash, const std::string& async_rpc_call_tag);
  void FillRequest();
  bool WaitUniversePsPreapre();

  AdContext* context_;
  kuaishou::ad::algorithm::PreparePredictRequest prepare_predict_request_;

  ks::kess::rpc::Waiter<std::tuple<::grpc::Status, ::kuaishou::ad::AdResponse *>> waiter_;
  bool is_downgrade_archimedes_ = false;
  int64_t invalid_flag = 0;
  int64_t ad_server_timeout_ms_ = 0;
  int64_t ad_server_start_time_stamp_ = 0;
};

}   // namespace front_server
}   // namespace ks
