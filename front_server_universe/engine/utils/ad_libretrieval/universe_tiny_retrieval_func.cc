#include "teams/ad/front_server_universe/engine/utils/ad_libretrieval/universe_tiny_retrieval_func.h"

#include "teams/ad/ad_base/src/log_record/ad_browse_set.h"
#include "teams/ad/front_server_universe/engine/strategy/admit/universe_elastic_control.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "teams/ad/front_server_universe/util/universe_client_limiter_controller/universe_client_limiter_controller.h"

namespace ks {
namespace front_server {

bool UniverseTinyRetrievalFunc::IsDspRetrievalValid(ContextData* session_data, AdContext* context) {
  session_data->dot_perf->Count(1, "universe_request_ads_use_tiny");

  // 执行弹性队列策略
  StrategyManager* p_strategy_manager = context->GetContextSystem<StrategyManager>();
  if (p_strategy_manager) {
    auto universe_elastic_stra = p_strategy_manager->GetStrategy<UniverseElasticControl>(
        AdStrategyType::UniverseElasticControl);
    universe_elastic_stra->ProcessElasticControl();
  }

  // 令牌桶限流
  bool is_client_limit_allow =
    UniverseClientLimiterController::Instance()->Allow("universe_adserver_protect_tiny");
  if (!is_client_limit_allow) {
    session_data->SetAdInvalidFlag(
        static_cast<int>(AdmitInvalidType::UNIVERSE_AD_SERVER_QPS_LIMITER_FILTER));
    return false;
  }
  session_data->dot_perf->Count(1, "universe_adserver_protect_tiny", absl::StrCat(is_client_limit_allow));
  session_data->mutable_ad_request()->set_sub_page_id(19000002);
  session_data->set_sub_page_id(19000002);
  for (auto& imp_info :
      (*session_data->mutable_ad_request()->mutable_universe_ad_request_info()->mutable_imp_info())) {
    imp_info.set_sub_page_id(19000002);
  }
  ASyncGetPrepareData(session_data, context);
  return true;
}

const std::string UniverseTinyRetrievalFunc::GetDspServerName(ContextData* session_data) {
  return GetAdServerKessName(session_data, "ad-server-universe-tiny", "ad_server_universe_tiny");
}

}  // namespace front_server
}  // namespace ks
