#pragma once

#include <string>
#include <tuple>

#include "teams/ad/front_server_universe/engine/server/init.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/front_server_universe/engine/utils/ad_libretrieval/base_retrieval_func.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"
namespace ks {
namespace front_server {

using AdResultWaiter = ks::kess::rpc::Waiter<std::tuple<::grpc::Status, kuaishou::ad::AdResponse*>>;
// RetrievalHandler 基类示例
class BaseRetrievalHandler {
 public:
  explicit BaseRetrievalHandler(const char* async_rpc_call_tag)
      : session_data_(nullptr), valid_(true), retrieval_func_(nullptr), ad_request_(nullptr),
      ad_response_(nullptr), async_rpc_call_tag_(async_rpc_call_tag),
      kess_status_(0), start_ts_(0), time_cost_ts_(0) {}
  virtual ~BaseRetrievalHandler() {}

  virtual void Init(AdContext* context, ContextData* session_data, BaseRetrievalFunc* retrieval_func) = 0;
  virtual void BuildAndSend() = 0;
  virtual bool ReceiveParse() = 0;
  virtual void ParsePostProc(const std::string& tag = "") = 0;
  virtual void Clear() = 0;

  void ClearBase() {
    session_data_ = nullptr;
    valid_ = true;
    retrieval_func_ = nullptr;
    ad_request_ = nullptr;
    ad_response_ = nullptr;
    kess_status_ = 0;
    start_ts_ = 0;
    time_cost_ts_ = 0;
    need_parse_proc = false;
  }
  inline void Reset(ContextData* session_data, BaseRetrievalFunc* retrieval_func) {
    Clear();
    session_data_ = session_data;
    retrieval_func_ = retrieval_func;
  }

 protected:
  ContextData* session_data_;
  bool valid_;
  BaseRetrievalFunc* retrieval_func_;
  kuaishou::ad::AdRequest* ad_request_;
  kuaishou::ad::AdResponse* ad_response_;
  const std::string async_rpc_call_tag_;
  int kess_status_;
  int start_ts_;
  int time_cost_ts_;
  bool need_parse_proc = false;
  void FillExtraCommonAttrFromKconf(const std::string &ad_server_ksn);
};

}   // namespace front_server
}   // namespace ks
