#include "dragon/src/interop/json_value.h"   // NOLINT
#include "kconf/kconf_flags.h"  // NOLINT
#include "teams/ad/front_server_universe/engine/utils/ad_libretrieval/base_retrieval_handler.h"
namespace ks {
namespace front_server {
DEFINE_INF_KCONF_json("ad.frontserver.fillCommonAttrForAdServer",
                      fillCommonAttrForAdServer,
                      ::Json::Value(),
                      "fillCommonAttrForAdServer");
namespace {

}  // namespace
void BaseRetrievalHandler::FillExtraCommonAttrFromKconf(const std::string &ad_server_ksn) {
  auto *dragon = session_data_->common_w_;
  if (!dragon)
    return;
  auto kconf = kconf_fillCommonAttrForAdServer();
  if (!kconf || kconf->isNull())
    return;
  auto deal_json_value = [&] (const std::string &name) {
    if (!kconf->isMember(name))
      return;
    const ::Json::Value &json_value = (*kconf)[name];
    for (auto it = json_value.begin(); it != json_value.end(); ++it) {
      try {
        ks::platform::interop::SaveJsonValueToCommonAttr(dragon, it.memberName(), *it, false);
      } catch (const std::exception &e) {
        LOG(ERROR) << "kconf ad.frontserver.fillCommonAttrForAdServer "
            << name << "." << it.memberName() << " json_exception:" << e.what();
      }
    }
  };
  // 从 kconf 中取 common 节点
  deal_json_value("common");
  // 从 kconf 中取 ad_server_ksn 节点
  deal_json_value(ad_server_ksn);
}

}   // namespace front_server
}   // namespace ks
