#pragma once

#include <string>
#include <set>

#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/ad_proto/kuaishou/ad/athena_universe_interface.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/athena_service.pb.h"
#include "teams/ad/front_server_universe/engine/utils/ad_libretrieval/universe_retrieval_func.h"

namespace ks {
namespace front_server {
using kuaishou::ad::universe::AthenaAdRequest;

// 不同流量接口处理
class UniverseTinyRetrievalFunc : public UniverseRetrievalFunc {
 public:
  bool IsDspRetrievalValid(ContextData* session_data, AdContext* context) override;
  const std::string GetDspServerName(ContextData* session_data) override;
};

}   // namespace front_server
}   // namespace ks
