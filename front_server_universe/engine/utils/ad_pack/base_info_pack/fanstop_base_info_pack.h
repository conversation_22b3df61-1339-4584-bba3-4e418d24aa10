#pragma once

#include <string>
#include <unordered_map>

#include "base/common/basic_types.h"
#include "teams/ad/front_server_universe/engine/strategy/strategy.h"
#include "teams/ad/front_server_universe/engine/context_data/base_data.h"
#include "teams/ad/front_server_universe/util/creative_parser/creative_parser.h"

namespace ks {
namespace front_server {

class FanstopBaseInfoPack {
 public:
  void Process(StyleInfoItem *style_info, ContextData *session_data, kuaishou::ad::AdResult *ad_result,
               const std::unordered_map<std::string, bool> *authors_delivery = nullptr);
  void Clear();

 private:
  bool Initialize(StyleInfoItem *style_info, ContextData *session_data, kuaishou::ad::AdResult *ad_result,
                  const std::unordered_map<std::string, bool> *authors_delivery);
  void SetFanstopExtData();
  void SetChargeActionType();
  void SetFanstopBaseInfo();
  void SetFanstopBaseInfoForEspOrder();
  void UpdateDasExtData(const uint64_t author_id, kuaishou::ad::FanstopExtInfo* fanstop_ext);

 private:
  bool inner_authors_delivery_{false};
  bool is_esp_order_in_specialty_{false};
  StyleInfoItem* style_info_;
  ContextData* session_data_;
  kuaishou::ad::AdBaseInfo* ad_base_info_;
  kuaishou::ad::AdEnum::AdQueueType ad_queue_type_;
};

}  // namespace front_server
}  // namespace ks
