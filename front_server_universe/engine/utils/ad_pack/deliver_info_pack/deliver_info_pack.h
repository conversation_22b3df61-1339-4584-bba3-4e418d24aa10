#pragma once

#include <string>
#include <vector>
#include <algorithm>
#include <set>
#include <memory>
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/front_server_universe/engine/utils/ad_pack/online_join_pack.h"
#include "teams/ad/front_server_universe/util/creative_parser/creative_parser.h"

using kuaishou::ad::AdDeliverInfo;
using kuaishou::ad::RankResult;
using kuaishou::ad::SessionRankInfo;
using kuaishou::ad::forward_index::StyleInfoItem;
using kuaishou::ad::AdResult;
using kuaishou::ad::AdEnum_BudgetReuseType;

namespace ks {
namespace front_server {
struct DeliverPackerParams;
class CommonAdDeliverInfoPack {
 public:
  void Process(const StyleInfoItem* style_info, const CreativeParser* creative_parser, AdResult* ad_result,
               DeliverPackerParams* params, RankAdCommon* ad_common);

 private:
  bool Initialize(const StyleInfoItem* style_info, const CreativeParser* creative_parser, AdResult* ad_result,
                  DeliverPackerParams* params, RankAdCommon* ad_common);
  void SetPredictInfo();
  void SetReason();
  void SetReserveInfo();
  void FillChargeTag();
  void SetRecoRequestInfo();
  void SetHetuTagInfo();
  void SetBidTransInfo();
  void SetAdDeliverInfo();
  // 填充 universe ad deliver 信息
  void SetUniverseAdDeliverInfo();
  bool NoLimitVirtualGoldUnit();
  void SetTargetSearch();
  void SetHostingInfo();
  void SetEnvInfo();
  void SetLpComponent();
  void SetGuidedFeedback(uint64 style_id = 0);

 private:
  ContextData* session_data_{nullptr};
  AdResult* ad_result_{nullptr};
  const CreativeParser* creative_parser_{nullptr};
  const StyleInfoItem* style_info_{nullptr};
  AdDeliverInfo* ad_deliver_info_{nullptr};    // 内部都通过这个指针修改
  DeliverPackerParams* params_{nullptr};
  const RankResult* rank_result_{nullptr};
  const RankAdCommon* ad_common_{nullptr};
  std::vector<ks::ad_base::RequestImpInfo>* req_imp_info_vec_{nullptr};
  kuaishou::ad::OnlineJoinParams online_join_pb_;
  std::shared_ptr<absl::flat_hash_set<std::string>> valid_deliver_bid_trans_attr_name_list_;
  OnlineJoinPack online_join_pack_;
};

}   // namespace front_server
}   // namespace ks
