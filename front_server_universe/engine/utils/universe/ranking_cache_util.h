#pragma once

#include <map>
#include <set>
#include <string>
#include <vector>

#include "teams/ad/ad_base/src/better_enums/better_enums.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"

namespace ks {
namespace front_server {
class ContextData;

BETTER_ENUM(AdmitReadCachePluginType, int32_t,
            UNKNOWN = 0,
            MODEL_PREDICT_CPM_COMPARE = 1,
            MODEL_PREDICT_CPM_COMPARE_WITH_RATIO = 2,
            HIGH_QUALITY_PV_SKIP_READ_CACHE = 3,
            POS_ID_BLACKLIST_SKIP_READ_CACHE = 4)
BETTER_ENUM(AdmitWriteCachePluginType, int32_t,
            UNKNOWN = 0,
            MODEL_PREDICT_CPM_COMPARE = 1,
            REAL_CPM_COMPARE = 2)

struct RankingCacheData {
  RankingCacheData() {}
  RankingCacheData(const kuaishou::ad::AdResponse res, double model_predict_cpm)
      : response_(res), model_predict_cpm_(model_predict_cpm) {}

  void Swap(RankingCacheData& other) {
    this->response_.Swap(&other.response_);
    this->model_predict_cpm_ = other.model_predict_cpm_;
  }

  kuaishou::ad::AdResponse* mutable_response() { return &response_; }
  const kuaishou::ad::AdResponse& response() const { return response_; }

  const double& model_predict_cpm() const { return model_predict_cpm_; }
  void set_model_predict_cpm(double model_predict_cpm) { model_predict_cpm_ = model_predict_cpm; }

 private:
  kuaishou::ad::AdResponse response_;
  double model_predict_cpm_;
};

typedef std::function<bool(const RankingCacheData*, ContextData*)> RankCachePluginFuncPtr;

class RankCachePluginManager {
 public:
  static RankCachePluginManager* Instance() {
    static RankCachePluginManager instance_;
    return &instance_;
  }

  bool AdmitWriteCache(const RankingCacheData*, ContextData*);
  bool AdmitReadCache(const RankingCacheData*, ContextData*);

 private:
  RankCachePluginManager();
  RankCachePluginManager(const RankCachePluginManager& single) = delete;
  const RankCachePluginManager& operator=(const RankCachePluginManager& single) = delete;

 private:
  std::map<AdmitReadCachePluginType, RankCachePluginFuncPtr> read_plugin_map_;
  std::map<AdmitWriteCachePluginType, RankCachePluginFuncPtr> write_plugin_map_;
};

}  // namespace front_server
}  // namespace ks
