#include "teams/ad/front_server_universe/engine/utils/data_adapter/user_info_adapter.h"
#include "absl/time/time.h"
#include "base/common/basic_types.h"
#include "glog/logging.h"
#include "google/protobuf/arena.h"
#include "ks/base/abtest/session_context.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_base/src/dot/dot.h"
#include "teams/ad/ad_base/src/redis/redis_util.h"
#include "teams/ad/ad_base/src/util/check_reco_request_type.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/front_service.pb.h"
#include "teams/ad/engine_base/kconf/special_regions.h"
#include "teams/ad/front_server_universe/engine/context_data/base_data.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/front_server_universe/util/utility/utils.h"
#include "teams/ad/front_server_universe/util/ad_user_info/ad_user_info_builder.h"
#include "teams/ad/ad_base/src/perf/perf.h"

namespace ks {
namespace front_server {

void UserInfoAdapter::FillInstalledAppInfoFromAthenaRequest(
    const kuaishou::ad::universe::AthenaAdRequest &athena_request, kuaishou::ad::AdRequest *ad_request) {
  kuaishou::ad::AdUserInfo *ad_user_info = ad_request->mutable_ad_user_info();
  // const kuaishou::ad::universe::UserInfo &athena_user_info = athena_request.user_info();
  const kuaishou::ad::universe::DeviceInfo &athena_device_info = athena_request.device_info();
  if (ad_user_info->device_info_size() == 0) {
    ad_user_info->add_device_info();
  }
  kuaishou::ad::DeviceInfo *device_info = ad_user_info->mutable_device_info(0);
  for (auto &item : athena_device_info.app_package()) {
    auto installed = device_info->add_installed_app_info();
    installed->set_app_name(item.app_name());
    installed->set_app_version(item.app_version());
    installed->set_pkg_name(item.pkg_name());
  }
  // 透传客户端实时获取的部分 app 版本信息
  kuaishou::ad::RealTimeInstalledInfo *real_time_installed_info =
                                      device_info->mutable_real_time_installed_info();
  real_time_installed_info->set_kwai_version_name(
                            athena_device_info.kwai_version_name());
  real_time_installed_info->set_kwai_nebula_verison_name(
                            athena_device_info.kwai_nebula_verison_name());
  real_time_installed_info->set_wechat_version_name(
                            athena_device_info.wechat_version_name());
}

void UserInfoAdapter::FillRecoUserInfoFromAthenaRequest(
    const kuaishou::ad::universe::AthenaAdRequest &athena_request, kuaishou::ad::AdRequest *ad_request,
    const ks::spdm::Context& spdm_ctx) {
  const kuaishou::ad::AdUserInfo &ad_user_info = ad_request->ad_user_info();
  kuaishou::newsmodel::UserInfo *reco_user_info = ad_request->mutable_reco_user_info();

  reco_user_info->set_id(ad_user_info.id());

  const kuaishou::ad::universe::GeoInfo &geo_info = athena_request.geo_info();
  kuaishou::newsmodel::Location *location = reco_user_info->mutable_location();
  location->set_lat(geo_info.latitude());
  location->set_lon(geo_info.longitude());

  const kuaishou::ad::universe::NetworkInfo &network_info = athena_request.network_info();
  if (SPDM_enable_reco_user_info_ip_upgrade_v2(spdm_ctx)) {
    if (!network_info.ip_v4().empty()) {
      location->set_ip(network_info.ip_v4());
    }
    if (!network_info.ipv6().empty()) {
      location->set_ipv6(network_info.ipv6());
    }
  } else {
    if (!network_info.ip_v4().empty()) {
      location->set_ip(network_info.ip_v4());
    } else if (!network_info.ipv6().empty()) {
      location->set_ipv6(network_info.ipv6());
    }
  }
}

}  // namespace front_server
}  // namespace ks
