#pragma once

#include <string>
#include <unordered_set>
#include <vector>
#include<algorithm>
#include "base/common/basic_types.h"

#include "ks/base/abtest/session_context.h"
#include "teams/ad/ad_base/src/common/basis_enum_types.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/engine_base/common/enum.h"
#include "teams/ad/front_server_universe/util/common/common.h"
#include "teams/ad/front_server_universe/engine/context_data/base_data.h"

namespace kuaishou {
namespace ad {
class FrontServerRequest;
class AdUserInfo;
class AdRequest;
namespace universe {
class AthenaAdRequest;
}  // namespace universe
}  // namespace ad
namespace newsmodel {
class UserInfo;
}
}  // namespace kuaishou
namespace ks {
namespace front_server {
class UserInfoAdapter final {
 public:
  static void FillRecoUserInfoFromAthenaRequest(const kuaishou::ad::universe::AthenaAdRequest &athena_request,
                                                kuaishou::ad::AdRequest *ad_request,
                                                const ks::spdm::Context& spdm_ctx);
  static void FillInstalledAppInfoFromAthenaRequest(const kuaishou::ad::universe::AthenaAdRequest &athena_request,  // NOLINT
                                                kuaishou::ad::AdRequest *ad_request);
};
}  // namespace front_server
}  // namespace ks
