#pragma once

#include <string>
#include <unordered_map>

#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/athena_universe_interface.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/front_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/fanstop/fans_top_service.pb.h"
#include "teams/ad/front_server_universe/util/common/common.h"

#include "google/protobuf/arena.h"

namespace ks {
namespace front_server {

class RequestAdapter final {
 private:
  static std::unordered_map<kuaishou::ad::FrontRequestType, std::string> type_to_name;

 public:
  RequestAdapter() = delete;
  static ReqAdapterCode BuildAdRequest(kuaishou::ad::FrontServerRequest *front_server_request,
                                       kuaishou::ad::AdRequest **ad_req,
                                       google::protobuf::Arena *arena,
                                       FrontServerScene scene);

 private:
  static void BuildExtCommonData(kuaishou::ad::FrontServerRequest *front_server_request,
                                 kuaishou::ad::AdRequest *ad_request,
                                 FrontServerScene scene);

  static ReqAdapterCode DefaultBuildAdRequest(kuaishou::ad::AdRequest **ad_req,
                                              google::protobuf::Arena *arena);
};

}  // namespace front_server
}  // namespace ks
