#include "teams/ad/front_server_universe/engine/utils/data_adapter/request_adapter.h"

#include <vector>
#include "absl/strings/substitute.h"
#include "glog/logging.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/front_server_universe/engine/utils/data_adapter/user_info_adapter.h"
#include "teams/ad/front_server_universe/util/ad_user_info/ad_user_info_builder.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "ks/base/abtest/session_context_factory.h"
#include "ks/base/abtest/abtest_instance.h"
#include "ks/base/abtest/abtest_globals.h"
#include "teams/ad/front_server_universe/engine/context_data/utility.h"
#include "teams/ad/ad_base/src/perf/perf.h"

using kuaishou::ad::AdRequest;
using kuaishou::ad::AdUserInfo;
using kuaishou::ad::AthenaExploreRequest;
using kuaishou::ad::AthenaFollowRequest;
using kuaishou::ad::AthenaNearbyRequest;
using kuaishou::ad::AthenaRequest;
using kuaishou::ad::FrontServerRequest;
using kuaishou::ad::UniverseAdRequestInfo;
using kuaishou::ad::universe::AthenaAdRequest;
using kuaishou::newsmodel::RecoClientRequestInfo;

using google::protobuf::Arena;

namespace ks {
namespace front_server {


ReqAdapterCode RequestAdapter::BuildAdRequest(FrontServerRequest *front_server_request, AdRequest **ad_req,
                                              Arena *arena, FrontServerScene scene) {
  ReqAdapterCode req_adapter_code = ReqAdapterCode::kAdapterSuccess;
  if (front_server_request == nullptr) {
    LOG(ERROR) << "front_server_request is nullptr";
    req_adapter_code = ReqAdapterCode::kNullPointerError;
    return req_adapter_code;
  }

  req_adapter_code = DefaultBuildAdRequest(ad_req, arena);
  // 一些公共数据添充
  BuildExtCommonData(front_server_request, *ad_req, scene);
  return req_adapter_code;
}

void RequestAdapter::BuildExtCommonData(kuaishou::ad::FrontServerRequest *front_server_request,
                                   kuaishou::ad::AdRequest *ad_request,
                                   FrontServerScene scene) {
  const ks::infra::kenv::RpcTraceContext &rpc_trace_ctx = ks::infra::kenv::ServiceMeta::GetRpcTraceContext();
  const ks::infra::kenv::RpcStressTestContext &stress_test = rpc_trace_ctx.GetRpcStressTestCtx();
  const std::string &biz_name = stress_test.GetBizName();

  // 广告个性化推荐开关
  if (engine_base::AdKconfUtil::universeEnableCloseAdPersonal() &&
      front_server_request->universe_request().status_info().disable_personal_ad()) {
    ad_request->set_ad_personal_switch_status(kuaishou::ad::AdPersonalSwitchStatus::UNIVERSE_CLOSED);
    ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_front", "universe_ad_personal_switch",
        front_server_request->universe_request().app_info().app_id());
  }

  // 将无 diff 测试流量的 fake type 进行转换
  if (front_server_request->fake_type() != ::kuaishou::ad::UserFakeType::NORMAL_USER) {
    if (ad_request != nullptr) {
      ad_request->set_fake_type(front_server_request->fake_type());
    }
  }
  // 将商业化压力测试流量的 fake type 进行转换
  if (biz_name.length() > 0) {
    if (ad_request != nullptr) {
      ad_request->set_fake_type(::kuaishou::ad::UserFakeType::PRESS_FROM_LANE);
      ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_front", "biz_name_request_num", biz_name,
                                         kuaishou::ad::FrontRequestType_Name(front_server_request->type()));
    }
    LOG_EVERY_N(INFO, 1000) << "debug flow biz name is: " << biz_name;
  }
  // 识别搜索评测平台流量
  if (front_server_request->type() == kuaishou::ad::SEARCH_REQUEST) {
    const std::string &session_id = ad_request->search_info().session_id();
    const std::string prefix = *FrontKconfUtil::openSearchSessionIdPrefix();
    if (!prefix.empty() && session_id.substr(0, prefix.size()) == prefix) {
      ad_request->set_fake_type(::kuaishou::ad::UserFakeType::PRESS_FROM_LANE);
      ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_front", "seweb_request_num");
    }
  }
  if (ad_request && front_server_request->has_request_hash()) {
    ad_request->mutable_request_hash()->CopyFrom(front_server_request->request_hash());
  }
}

// Begin of inner class AthenaAdRequest2AdReqeust implemenation
ReqAdapterCode RequestAdapter::DefaultBuildAdRequest(AdRequest **ad_req, Arena *arena) {
  auto req_adapter_code = ReqAdapterCode::kAdapterSuccess;
  *ad_req = Arena::CreateMessage<AdRequest>(arena);

  return req_adapter_code;
}
// End of inner class AthenaAdRequest2AdReqeust implemenation
// End of inner class AthenaAdRequest2AdReqeust implemenation
// End of inner class AthenaAdRequest2AdReqeust implemenation

}  // namespace front_server
}  // namespace ks
