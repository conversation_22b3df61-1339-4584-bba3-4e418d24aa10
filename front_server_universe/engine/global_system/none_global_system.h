#pragma once
#pragma once

#include <array>
#include <atomic>
#include <vector>
#include "base/common/basic_types.h"

namespace ks {
namespace front_server {
// 统一引擎框架 ConfigClass 必须要声明 GlobalSystemType, 这里写一个空类
class NoneGlobalSystem final : public ::ad::concept::refactor::GlobalSystemBase<NoneGlobalSystem> {
 public:
  NoneGlobalSystem() = default;
  ~NoneGlobalSystem() = default;

 private:
  DISALLOW_COPY_AND_ASSIGN(NoneGlobalSystem);
};

}  // namespace front_server
}  // namespace ks
