#pragma once
#include <tuple>
#include <map>
#include <memory>
#include <string>

#include "teams/ad/engine_base/dragon_node/skydome_node.h"
#include "teams/ad/pslab/refactor/context_pool.h"
#include "teams/ad/pslab/refactor/core/macro.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/front_server_universe/engine/strategy/strategy_manager.h"
#include "teams/ad/front_server_universe/engine/global_system/none_global_system.h"

namespace ks {
namespace front_server {

enum class AdNodeType {
  PretreatmentType,
  ForwardHandlerType,
  DataPostProcType,
  PostProcType,
  StylePostProcType,
  AcFetcherType,
  FollowDataPostProcType,
  FollowFanstopHandlerType,
  FollowAdSocialHandlerType,
  NearbyDataPostProcType,
  UniverseDataPostProcType,
  UniverseStylePostProcType,
  SplashDataPostProcType,
  KwaiGalaxyStylePostProcType,
  KwaiGalaxyDataPostProcType,
  AdPosSelectType,
  UniverseAcFetcherType,
  SearchQueryRetrievalType,
  SearchAuthorPhotoType,
  SearchMergeType,
  SearchStylePostProcType,
  SearchSelectPostProcType,
  SearchDataPostProcType,
  MerchantStylePostProcType,
  MerchantDataPostProcType,
  MerchantRpcHandlerType,
  JingKuaiRpcHandlerType,
  TraceApiDataPostProcType,
  UserInfoCollectionType,
  ExploreResultPkType,
  NearbyResultPkType,
  MerchantResultPkType,
  UniverseUserInfoCollectionType,
  UniverseAntispamType,
  UniverseQueryRewriteType,
  UniverseRequestRtaType,
  SplashForwardHandlerType,
  SplashStylePostProcType,
  FanstopAdmitType,
  CommonAdmitType,
  AdLibRetrievalNodeType,
  AdLibRetrievalWaitType,
  AdLibRetrievalParsePostType,
  RebuildAdserverResponseType,
  UniverseRequestQualityReckonType,
  AdStyleForwardHandlerType,
  DefaultStyleForwardNodeType,
  AuctionType,
  PsPrepareNodeType,
  SplashStyleForwardHandlerType,
  ContentDataPostProcType,
  AdFilterNodeType,
  UniverseDealAggreTaskType,
  UniverseTaskPostProcType,
  UniverseDebugNodeType,
  UniverseAuctionType,
  UniverseCpmThresholdType,
  UniverseBiddingNodeType,
  ForwardHandlerWaitType,
  InspireResultPkType,
  AfterRetrievalInitType,
  UniverseQueryMockType,
  UniverseAggSceneAdjustType,
  UniverseCarouselSceneAdjustType
};

struct ContextInfo;

struct ConfigClass {
  using ContextDataType = std::tuple<ContextData>;
  using SessionDataType = ContextData;
  using NodeKeyType = AdNodeType;
  using ContextInfoType = ContextInfo;
  using ContextSystemType = std::tuple<StrategyManager>;
  using GlobalSystemType = std::tuple<NoneGlobalSystem>;
};

struct ContextInfo {
  using AdGraphDef = ::ad::concept::refactor::GraphDef<ConfigClass>;
  static std::map<std::string, std::shared_ptr<rapidjson::Document>> GetGraphDefs() {
    std::map<std::string, std::shared_ptr<rapidjson::Document>> graph;
    return graph;
  }

  static std::string GetServiceName() {
    return "front_server";
  }
};

using ContextPoolTypeMore = ::ad::concept::refactor::ContextPool<ConfigClass, 1600, std::thread::id>;

using AdContext = ::ad::concept::refactor::Context<ConfigClass>;
using AdSyncNode = ::ks::engine_base::dragon::SkydomeSyncNode<AdContext>;
using AdAsyncNode = ::ks::engine_base::dragon::SkydomeAsyncNode<AdContext>;
using AdGraph = ::ad::concept::refactor::Graph<ConfigClass>;
template<typename T>
using AdNodeItemBase = ::ad::concept::refactor::NodeTypeCollection<ConfigClass>::NodeItemBase<T>;
using AdStrategyBase = ::ad::concept::refactor::Strategy<ConfigClass>;


}  // namespace front_server
}  // namespace ks
