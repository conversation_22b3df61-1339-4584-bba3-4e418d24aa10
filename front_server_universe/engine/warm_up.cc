#include "teams/ad/front_server_universe/engine/warm_up.h"
#include <string>
#include <future>
#include "teams/ad/front_server_universe/engine/server/init.h"
#include "teams/ad/front_server_universe/engine/server/node_register.h"
#include "teams/ad/front_server_universe/engine/strategy/init.h"
#include "teams/ad/picasso/sdk/picasso_client/picasso_client.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "ks/serving_util/grpc/grpc_tcpcopy_util.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/ad_base/src/container/singleton.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/front_server_universe/util/utility/ksn_util.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/ad_base/src/auto_param/auto_param.h"

using ks::ad_base::Singleton;

DEFINE_string(ip_data_file, "", "ip data file");


extern bool ks_flag_grpc_force_use_epollsig;
extern bool ks_flag_grpc_enable_tcp_user_timeout;
extern int ks_flag_grpc_tcp_user_timeout_ms;

namespace ks {
namespace front_server {

void ad_warm_up(bool enable_redis_warmup_async) {
  LOG(INFO) << "start to warm up";
  // 修改一些全局参数
  ks_flag_grpc_force_use_epollsig = FrontKconfUtil::universeKsFlagGrpcForceUseEpollsig();
  LOG(INFO) << "ks_flag_grpc_force_use_epollsig = " << ks_flag_grpc_force_use_epollsig;
  ks_flag_grpc_enable_tcp_user_timeout = true;
  ks_flag_grpc_tcp_user_timeout_ms = 500;
  falcon::SetMaxCounterNumber(40960);
  // 显示声明注册 节点/策略
  RegisterStrategies();

  // enable grpc copy
  ks::serving_util::grpc::GrpcTcpcopyUtil::Singleton()->SetFlagsForTcpcopy();
  // 注册依赖服务
  AD_KESS_CLIENT_INIT_CHECK("init error");

  // warmup auto_param
  ks::ad_base::auto_param::AutoParam::warm_up();

  // 初始化引擎框架
  ContextPoolTypeMore::Instance();

  ks::ad_picasso::sdk::PicassoOption opt;
  opt.client_tag = "ad_front_server";
  opt.self_kess_server_name = "grpc_adFrontServer";
  opt.use_hash = true;
  if (!ks::ad_picasso::sdk::PicassoClient::GetInstance()->Init(opt)) {
    LOG(INFO) << "init picasso failed";
  }

  if (!enable_redis_warmup_async) {
    ks::ad_base::KconfRedis::Instance().Warmup();
  }
  // 启动
  LOG(INFO) << "warm up finished";
  ::google::FlushLogFiles(::google::INFO);
}
}  // namespace front_server
}  // namespace ks
