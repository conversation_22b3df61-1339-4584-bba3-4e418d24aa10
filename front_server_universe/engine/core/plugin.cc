#include "teams/ad/front_server_universe/engine/core/plugin.h"

#include <iostream>

#include "glog/logging.h"

namespace ks {
namespace front_server {
void StrategyPluginManager::RegPluginFunc(const std::string &plugin_name, CommonFuncPtr func) {
  auto iter = common_func_map_.find(plugin_name);
  if (iter != common_func_map_.end()) {
    return;
  }

  common_func_map_[plugin_name] = func;
}

void StrategyPluginManager::RegPluginFunc(const std::string &plugin_name, CommonAdFuncPtr func) {
  auto iter = common_ad_func_map_.find(plugin_name);
  if (iter != common_ad_func_map_.end()) {
    return;
  }

  common_ad_func_map_[plugin_name] = func;
}

CommonFuncPtr StrategyPluginManager::GetCommonFuncPtr(const std::string &plugin_name) {
  auto iter = common_func_map_.find(plugin_name);
  if (iter == common_func_map_.end()) {
    return nullptr;
  }
  return iter->second;
}

CommonAdFuncPtr StrategyPluginManager::GetCommonAdFuncPtr(const std::string &plugin_name) {
  auto iter = common_ad_func_map_.find(plugin_name);
  if (iter == common_ad_func_map_.end()) {
    return nullptr;
  }
  return iter->second;
}

}  // namespace front_server
}  // namespace ks
