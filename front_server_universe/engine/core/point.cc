#include "teams/ad/front_server_universe/engine/core/point.h"

#include "teams/ad/front_server_universe/util/common/micros.h"

namespace ks {
namespace front_server {

void GetPlugins(FrontServerScene scene,
                const ServerStrategyConf::CallPoint* scene_conf,
                std::vector<ServerStrategyConf::PluginConf>* plugins) {
#define FILL_SENCE_PLUGIN(sence, plugins) \
  for (int i = 0; i < scene_conf->sence##_size(); i++) plugins->push_back(scene_conf->sence(i));  // NOLINT

  switch (scene) {
    case FrontServerScene::FOLLOW:
      FILL_SENCE_PLUGIN(follow_scene, plugins);
      break;
    case FrontServerScene::NEARBY:
      FILL_SENCE_PLUGIN(nearby_scene, plugins);
      break;
    case FrontServerScene::EXPLORE:
      FILL_SENCE_PLUGIN(explore_scene, plugins);
      break;
    case FrontServerScene::SPLASH:
      FILL_SENCE_PLUGIN(splash_scene, plugins);
      break;
    case FrontServerScene::GALAXY:
      FILL_SENCE_PLUGIN(galaxy_scene, plugins);
      break;
    case FrontServerScene::UNIVERSE:
      FILL_SENCE_PLUGIN(universe_scene, plugins);
      break;
    case FrontServerScene::SEARCH:
      FILL_SENCE_PLUGIN(search_scene, plugins);
      break;
    case FrontServerScene::MERCHANT:
      FILL_SENCE_PLUGIN(merchant_scene, plugins);
      break;
    case FrontServerScene::TRACEAPI:
      FILL_SENCE_PLUGIN(traceapi_scene, plugins);
      break;
    default:
      FILL_SENCE_PLUGIN(default_scene, plugins);
      break;
  }
}

void SeqPoint::AddPlugin(AdlistPluginBase* plugin) {
  if (plugin == nullptr) {
    PERF_COUNT(1, "seq_point_add_plugin_nullptr", name_);
    return;
  }
  new_plugin_list_.emplace_back(plugin);
  plugin_name_set_.insert(plugin->Name());
  return;
}

void SeqPoint::AddPlugin(std::string func_name) {
  auto func = StrategyPluginManager::Instance()->GetCommonFuncPtr(func_name);
  if (func != nullptr) {
    common_plugin_list_.push_back(func);
  } else {
    PERF_INTERVAL(1, "seq_point_plugin_func_nullptr", func_name);
  }
}

void SeqPoint::Initialize(ContextData* context) {
  session_data_ = context;
  scene_ = session_data_->GetUnifyScene();
}

void SeqPoint::Initialize(ContextData* context,
                          const google::protobuf::Map<std::string, ServerStrategyConf::CallPoint>* conf) {
  if (conf == nullptr) {
    LOG_EVERY_N(INFO, 10000) << "SeqPoint Server Conf Is NULL";
    return;
  }

  session_data_ = context;
  scene_ = session_data_->GetUnifyScene();
  auto scenes = conf->find(name_);
  if (scenes == conf->end()) {
    LOG_EVERY_N(INFO, 1000) << "SeqPoint Point Conf Not Found, " << name_
                            <<", conf size: "<< conf->size();
    return;
  }

  std::vector<ServerStrategyConf::PluginConf> plugins;
  GetPlugins(scene_, &scenes->second, &plugins);
  for (int i = 0; i < plugins.size(); i++) {
    auto& plugin = plugins[i];
    if (plugin_name_set_.count(plugin.name())) {
      // 配置注册和代码注册切换时使用, 代码中注册了该插件, 配置中不再加载
      continue;
    }
    bool enable_plugin = session_data_->get_spdm_ctx().TryGetBoolean(plugin.enable_tag(), true);
    if (!enable_plugin) {
      PERF_INTERVAL(1, "seq_point_plugin_ab_switch_disabled", plugin.name());
      continue;
    }
    if (!IsValidPluginName(plugin.name())) {
      PERF_INTERVAL(1, "seq_point_plugin_custom_disabled", plugin.name());
      continue;
    }
    auto func = StrategyPluginManager::Instance()->GetCommonFuncPtr(plugin.name());
    if (func != nullptr) {
      common_plugin_list_.push_back(func);
    } else {
      PERF_INTERVAL(1, "seq_point_plugin_func_nullptr", plugin.name());
    }
  }
}

bool SeqPoint::IsValidPluginName(const std::string& plugin_name) {
  return true;
}

StraRetCode SeqPoint::Process(Params* params, AdList* ad_list) {
  StraRetCode ret_code = StraRetCode::SUCC;
  if (nullptr != ad_list) {
    for (auto* plugin : new_plugin_list_) {
      if (nullptr == plugin) {
        continue;
      }
      if (!plugin->IsRun(session_data_, params, scene_, ad_list)) {
        continue;
      }
      ret_code = plugin->Process(session_data_, params, scene_, ad_list);
      if (ret_code != StraRetCode::SUCC) {
        return ret_code;
      }
    }
  }
  for (auto func : common_plugin_list_) {
    if (func == nullptr) {
      continue;
    }
    ret_code = func(session_data_, scene_, params);
    if (ret_code != StraRetCode::SUCC) {
      return ret_code;
    }
  }
  return ret_code;
}

void SeqPoint::Clear() {
  session_data_ = nullptr;
  common_plugin_list_.clear();
  // 清理 class 类型的 plugin, 防止保存了状态
  for (auto* plugin_class : new_plugin_list_) {
    if (nullptr == plugin_class) {
      continue;
    }
    plugin_class->Clear();
  }
}

StraRetCode SeqAdPoint::Process(kuaishou::ad::AdResult* ad_result, Params* params) {
  StraRetCode ret_code = StraRetCode::SUCC;
  for (auto func : common_plugin_list_) {
    if (func == nullptr) {
      continue;
    }
    ret_code = func(session_data_, ad_result, scene_, params);
    if (ret_code != StraRetCode::SUCC) {
      return ret_code;
    }
  }
  return ret_code;
}

void SeqAdPoint::Clear() {
  session_data_ = nullptr;
  common_plugin_list_.clear();
}

void SeqAdPoint::AddPlugin(std::string func_name) {
  auto func = StrategyPluginManager::Instance()->GetCommonAdFuncPtr(func_name);
  if (func != nullptr) {
    common_plugin_list_.push_back(func);
  } else {
    PERF_INTERVAL(1, "seq_point_plugin_func_nullptr", func_name);
  }
}

void SeqAdPoint::Initialize(ContextData* context) {
  session_data_ = context;
  scene_ = session_data_->GetUnifyScene();
}

void SeqAdPoint::Initialize(ContextData* context,
                            const google::protobuf::Map<std::string, ServerStrategyConf::CallPoint>* conf) {
  if (conf == nullptr) {
    LOG_EVERY_N(INFO, 10000) << "SeqAdPoint Server Conf Is NULL";
    return;
  }

  session_data_ = context;
  scene_ = session_data_->GetUnifyScene();
  auto scenes = conf->find(name_);
  if (scenes == conf->end()) {
    LOG_EVERY_N(INFO, 1000) << "SeqAdPoint Point Conf Not Found, " << name_
                            <<", conf size: "<< conf->size();
    return;
  }

  std::vector<ServerStrategyConf::PluginConf> plugins;
  GetPlugins(scene_, &scenes->second, &plugins);
  for (int i = 0; i < plugins.size(); i++) {
    auto& plugin = plugins[i];
    bool enable_plugin = session_data_->get_spdm_ctx().TryGetBoolean(plugin.enable_tag(), true);
    if (!enable_plugin) {
      PERF_INTERVAL(1, "seq_ad_point_plugin_ab_switch_disabled", plugin.name());
      continue;
    }
    auto func = StrategyPluginManager::Instance()->GetCommonAdFuncPtr(plugin.name());
    if (func != nullptr) {
      common_plugin_list_.push_back(func);
    } else {
      PERF_INTERVAL(1, "seq_ad_point_plugin_func_nullptr", plugin.name());
    }
  }
}

}  // namespace front_server
}  // namespace ks
