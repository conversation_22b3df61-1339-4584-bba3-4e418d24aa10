#pragma once

#include <map>
#include <string>
#include <vector>
#include <set>

#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/front_server_universe/engine/core/plugin.h"
#include "teams/ad/front_server_universe/util/kconf/kconf_data.pb.h"

namespace ks {
namespace front_server {
using namespace ks::front_server::kconf;  //  NOLINT
class Point {
 public:
  Point() {}
  Point(const std::string& node_name, const std::string& name) : node_name_(node_name), name_(name) {}
  void SetName(const std::string& node_name, const std::string& name) {
    node_name_ = node_name;
    name_ = name;
  }
 public:
  std::string name_;
  std::string node_name_;
  FrontServerScene scene_;
};

// 该回调点将顺序执行每种策略的相关配置
class SeqPoint : public Point {
 public:
  SeqPoint() {}
  SeqPoint(const std::string& node_name, const std::string& name) : Point(node_name, name) {}

  // 根据 kconf&AB 平台配置，生成本次执行所需的 plugin_list
  // kconf 配置：pos+_name-> 策略插件 list<plugin_name, enable-tag>
  // AB 平台配置：通过 enable-tag 控制插件起停，获取不到 enable-tag 默认为 false
  void Initialize(ContextData* context,
                  const google::protobuf::Map<std::string, ServerStrategyConf::CallPoint>*);
  void Initialize(ContextData* context);
  void AddPlugin(AdlistPluginBase* plugin);
  void AddPlugin(std::string func_name);
  // 遍历 plugin_list，执行具体业务逻辑
  StraRetCode Process(Params* params, AdList* ad_list = nullptr);
  // 清理函数，清理 plugin_list
  void Clear();

 private:
  // 判断当前 plugin name 是否可以执行
  virtual bool IsValidPluginName(const std::string& plugin_name);

 protected:
  ContextData* session_data_;
  std::vector<CommonFuncPtr> common_plugin_list_;
  std::vector<AdlistPluginBase*> new_plugin_list_;
  std::set<std::string> plugin_name_set_;   // 配置注册改为代码注册切换中使用
};

// 该回调点将顺序执行每种策略的相关配置
class SeqAdPoint : public Point {
 public:
  SeqAdPoint(const std::string& node_name, const std::string& name) : Point(node_name, name) {}
  void Initialize(ContextData* context,
                  const google::protobuf::Map<std::string, ServerStrategyConf::CallPoint>* conf);
  void Initialize(ContextData* context);
  // 遍历 plugin_list，执行具体业务逻辑
  StraRetCode Process(kuaishou::ad::AdResult* ad_result, Params* params);
  void AddPlugin(std::string func_name);
  void Clear();

 protected:
  ContextData* session_data_;
  std::vector<CommonAdFuncPtr> common_plugin_list_;
};

}  // namespace front_server
}  // namespace ks
