#pragma once

#include <string>
#include <unordered_set>
#include <set>
#include <vector>
#include <memory>

#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_trace_log.pb.h"
#include "teams/ad/front_server_universe/engine/core/plugin.h"

namespace ks {
namespace front_server {

class AdFilterParams : public Params {
 public:
  kuaishou::log::ad::AdTraceFilterCondition filter_reason{
      kuaishou::log::ad::AdTraceFilterCondition::UNKNOWN_AD_TRACE_FILTER_CONDITION};
  kuaishou::ad::forward_index::StyleInfoItem* style_info{nullptr};    // 插件中有对该变量修改的操作
  std::string app_id{""};
  std::string app_version{""};
  std::string platform{""};
  std::string plugin_name{""};
  kuaishou::ad::AdEnum_AdRequestFlowType ad_request_flow_type =
      kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_UNKNOWN;
  kuaishou::ad::AdEnum_InteractiveForm interactive_form =
      kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_UNKNOWN_FROM;
  std::shared_ptr<absl::flat_hash_set<int64>> live_punish_filter_white_list;
  std::string live_promotion_min_main_version;
  std::string live_promotion_min_nebula_version;
  bool search_live_stream_only = false;
  bool enable_universe_material_info_filter = false;
  bool skip_local_store = false;
  std::unordered_set<std::string> universe_package_dedup_set;
  std::unordered_set<std::string> universe_agg_scene_product_set;
  std::unordered_set<int64_t> universe_agg_scene_account_set;
  std::unordered_set<int64_t> universe_agg_scene_author_set;
  bool enable_universe_live_audience_filter = false;
  bool is_live_audience_deliver_rate_allow = true;

 public:
  void InitializeRequestLevelParams(ContextData* session_data);
  void UpdateAdLevelParams(ContextData* session_data, const kuaishou::ad::AdResult& ad_result);
  bool MinimumAppVersion(const std::string& ios_version, const std::string& android_version) const;
  void CalcPreDiversity(ContextData* session_data);
  void Clear();
};

}  // namespace front_server
}  // namespace ks
