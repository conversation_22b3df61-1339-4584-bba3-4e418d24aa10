#include "teams/ad/front_server_universe/engine/node/plugins/ad_filter/ad_filter_param.h"

#include "teams/ad/engine_base/cache_loader/ad_traffic_creative_rule_v2.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "teams/ad/front_server_universe/util/universe_client_limiter_controller/universe_client_limiter_controller.h"
#include "teams/ad/front_server_universe/util/utility/utils.h"

namespace ks {
namespace front_server {

void AdFilterParams::Clear() {
  filter_reason = kuaishou::log::ad::AdTraceFilterCondition::UNKNOWN_AD_TRACE_FILTER_CONDITION;
  style_info = nullptr;
  app_id.clear();
  app_version.clear();
  plugin_name.clear();
  ad_request_flow_type = kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_UNKNOWN;
  interactive_form = kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_UNKNOWN_FROM;
  live_punish_filter_white_list.reset();
  live_promotion_min_main_version.clear();
  live_promotion_min_nebula_version.clear();
  search_live_stream_only = false;
  universe_package_dedup_set.clear();
  universe_agg_scene_product_set.clear();
  universe_agg_scene_account_set.clear();
  universe_agg_scene_author_set.clear();
  enable_universe_live_audience_filter = false;
  is_live_audience_deliver_rate_allow = true;
}

void AdFilterParams::InitializeRequestLevelParams(ContextData* session_data) {
  const int64_t& uid = session_data->get_ad_request()->ad_user_info().id();
  const std::string& device_id = session_data->get_ad_request()->ad_user_info().device_id();
  app_id = ks::ad_base::GetRequestAppId(*(session_data->get_ad_request()));
  app_version = session_data->get_ad_request()->ad_user_info().platform_version();
  platform = session_data->get_ad_request()->ad_user_info().platform();
  ad_request_flow_type = ks::ad_base::GetAdRequestType(*(session_data->get_ad_request()));
  interactive_form = ks::ad_base::GetInteractiveForm(*(session_data->get_ad_request()));
  live_punish_filter_white_list = FrontKconfUtil::livePunishFilterWhiteList();
  live_promotion_min_main_version = *(FrontKconfUtil::livePromotionMinMainVersion());
  live_promotion_min_nebula_version = *(FrontKconfUtil::livePromotionMinNebulaVersion());
  search_live_stream_only = (session_data->get_ad_request()->search_info().search_source() ==
                                 kuaishou::ad::SearchInfo::LIVE_STREAM_SEARCH);
  enable_universe_live_audience_filter = FrontKconfUtil::enableUniverseLiveAudienceFilter();
  for (RankAdCommon *ad : session_data->mutable_ad_list()->Ads()) {
    auto *result = ad->GetResult();
    if (result && FrontKconfUtil::universeLiveAudienceRateLimitOcpxTypes()->count(
                      result->ad_deliver_info().ad_base_info().ocpc_action_type()) > 0) {
      is_live_audience_deliver_rate_allow = UniverseClientLimiterController::Instance()->Allow(
                                                              "live_audience_deliver_rate_limit");
      break;
    }
  }
  auto config = FrontKconfUtil::localStoreVersion();
  skip_local_store = true;
  if (config != nullptr) {
    if (app_id == "kuaishou") {
      skip_local_store =
          MinimumAppVersion(config->data().main_app_ios_ver(), config->data().main_app_android_ver());
    } else if (app_id == "kuaishou_nebula") {
      skip_local_store =
          MinimumAppVersion(config->data().nebula_app_ios_ver(), config->data().nebula_app_android_ver());
    }
  }
}

void AdFilterParams::UpdateAdLevelParams(ContextData* session_data, const kuaishou::ad::AdResult& ad_result) {
  filter_reason = kuaishou::log::ad::AdTraceFilterCondition::UNKNOWN_AD_TRACE_FILTER_CONDITION;
  const auto& creative_id = ad_result.ad_deliver_info().ad_base_info().creative_id();
  auto* mutable_style_info_map = session_data->mutable_style_info_resp()->mutable_style_info();
  auto& style = (*mutable_style_info_map)[creative_id];
  style_info = &(style);
}

bool AdFilterParams::MinimumAppVersion(const std::string& ios_version,
                                       const std::string& android_version) const {
  if (platform == "ios") {
    return engine_base::CompareAppVersion(app_version, ios_version) >= 0;
  }
  return engine_base::CompareAppVersion(app_version, android_version) >= 0;
}

}  // namespace front_server
}  // namespace ks
