#pragma once

#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/front_server_universe/engine/core/plugin.h"

namespace ks {
namespace front_server {

// 声明插件，进行初始化判断，如果有问题直接返回 node_error
DECLARE_AD_PLUGIN_FUNCTION(StyleInfoInvalidFilter)                      // style info invalid 过滤
DECLARE_AD_PLUGIN_FUNCTION(LivePunishFilter)
DECLARE_AD_PLUGIN_FUNCTION(UniverseKeyWordFilter)
DECLARE_AD_PLUGIN_FUNCTION(UniverseBiddingCpmFloorFilter)
DECLARE_AD_PLUGIN_FUNCTION(UniverseMaterialInfoFilter)
DECLARE_AD_PLUGIN_FUNCTION(UniverseNonDspShieldFilter)
DECLARE_AD_PLUGIN_FUNCTION(UniverseVivoAdxFilter)  // 联盟 vivo-adx 过滤
DECLARE_AD_PLUGIN_FUNCTION(UniverseOppoAdxFilter)  // 联盟 oppo-adx 过滤
DECLARE_AD_PLUGIN_FUNCTION(UniverseHuaWeiAdxFilter)   // 联盟 huawei-adx 过滤
DECLARE_AD_PLUGIN_FUNCTION(SmallGameAdBlackFlowFilter)  // 黑名单流量广告屏蔽
DECLARE_AD_PLUGIN_FUNCTION(AggregatePageWhiteFilter)    // 聚合中间页白名单过滤
DECLARE_AD_PLUGIN_FUNCTION(UniversePackageDedupFilter)  // 联盟特定流量重复包名过滤
DECLARE_AD_PLUGIN_FUNCTION(LiveAudienceDeliverRateFilter)  // 直播进人下发速率控制
DECLARE_AD_PLUGIN_FUNCTION(UniverseAggSceneFilter)  // 联盟激励聚合页过滤
DECLARE_AD_PLUGIN_FUNCTION(UniverseMediaPackageFilter)  // 联盟媒体包屏蔽
DECLARE_AD_PLUGIN_FUNCTION(UniverseBrowsedInfoFreqFilter)  // 联盟频控过滤
}  // namespace front_server
}  // namespace ks
