#pragma once

#include "teams/ad/front_server_universe/engine/core/plugin.h"
#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_cpm_threshold_params.h"

namespace ks {
namespace front_server {

class BonusExplorePlugin : public AdlistPluginBase {
 public:
  bool IsRun(
      const ContextData* session_data, const Params* params, FrontServerScene pos, const AdList* ad_list);
  StraRetCode Process(ContextData* session_data, Params* params, FrontServerScene pos, AdList* ad_list);

 protected:
  ContextData* session_data;
  UniverseCpmThresholdParams* params;
  FrontServerScene pos;
  AdList* ad_list;
  RankAdCommon* p_target_ad{nullptr};
  int64_t aim_cpm;
  int64_t origin_rank_benefit;
  double bonus_rate;
  double real_roi;

 private:
  bool Initailize(
      ContextData* session_data, UniverseCpmThresholdParams* params, FrontServerScene pos, AdList* ad_list);
  virtual bool InitInner() = 0;
  // 策略流量准入
  virtual bool Admit() = 0;
  // 获得目标广告
  virtual bool GetTargetAd() = 0;
  // 补贴约束，预算控制
  virtual bool BonusControl() = 0;
  // 补贴操作
  virtual bool BonusProcess() = 0;
};

}  // namespace front_server
}  // namespace ks
