#include <algorithm>
#include "teams/ad/front_server_universe/engine/node/plugins/plugin_base.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"

namespace ks {
namespace front_server {
bool BonusExplorePlugin::Initailize(ContextData* session_data,
                               UniverseCpmThresholdParams* params,
                               FrontServerScene pos,
                               AdList* ad_list) {
  this->session_data = session_data;
  this->params = params;
  this->pos = pos;
  this->ad_list = ad_list;
  aim_cpm = std::max(session_data->get_ud_universe_pos_cpm_bound(),
                             session_data->get_ud_universe_pos_target_cpm());
  origin_rank_benefit = 0;
  bonus_rate = 0.0;
  real_roi = 0.0;
  p_target_ad = nullptr;
  return InitInner();
}

bool BonusExplorePlugin::IsRun(const ContextData * session_data,
                          const Params* params,
                          FrontServerScene pos,
                          const AdList* ad_list) {
  if (pos != FrontServerScene::UNIVERSE) {
    return false;
  }
  return true;
}

StraRetCode BonusExplorePlugin::Process(ContextData * context_data,
                                   Params* params,
                                   FrontServerScene pos,
                                   AdList* ad_list) {
  if (ad_list->Size() <= 0) {
    return StraRetCode::SUCC;
  }
  auto *params_ = dynamic_cast<UniverseCpmThresholdParams *>(params);
  if (!Initailize(context_data, params_, pos, ad_list)) {
    return StraRetCode::SUCC;
  }
  session_data->dot_perf->Count(1, "ad_front_bouns_explore", Name());
  if (!Admit()) {
    return StraRetCode::SUCC;
  }
  session_data->dot_perf->Count(1, "ad_front_bouns_explore_flow_admit", Name());
  if (!GetTargetAd()) {
    return StraRetCode::SUCC;
  }
  session_data->dot_perf->Count(1, "ad_front_bouns_explore_gettargetad", Name());
  if (!BonusControl()) {
    return StraRetCode::SUCC;
  }
  session_data->dot_perf->Count(1, "ad_front_bouns_explore_bonus_admit", Name());
  if (!BonusProcess()) {
    return StraRetCode::SUCC;
  }
  session_data->dot_perf->Count(1, "ad_front_bouns_explore_complete", Name());
  session_data->dot_perf->Interval(origin_rank_benefit, "ad_front_bonus_explore_origin_rank_benefit", Name());
  session_data->dot_perf->Interval(aim_cpm, "ad_front_bonus_explore_aim_cpm", Name());
  session_data->dot_perf->Interval(bonus_rate, "ad_front_bonus_explore_bonus_rate", Name());
  session_data->dot_perf->Interval(real_roi, "ad_front_bonus_explore_real_roi", Name());
  return StraRetCode::SUCC;
}
}   // namespace front_server
}   // namespace ks
