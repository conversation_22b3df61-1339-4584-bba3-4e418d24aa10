#include "teams/ad/front_server_universe/engine/node/plugins/universe_bidding/universe_bidding_params.h"

#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"

namespace ks {
namespace front_server {

void UniverseBiddingParams::SetAdnPriceAdjustParams(ContextData *session_data) {
}

}   // namespace front_server
}   // namespace ks
