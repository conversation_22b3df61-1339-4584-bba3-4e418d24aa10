#pragma once

#include <vector>
#include <algorithm>
#include "absl/container/flat_hash_set.h"
#include "teams/ad/front_server_universe/engine/core/plugin.h"
#include "teams/ad/front_server_universe/engine/node/plugins/plugin_base.h"
#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_cpm_threshold_params.h"

namespace ks {
namespace front_server {

class UniverseWLevelCrowdExplorePlugin : public AdlistPluginBase {
 public:
  const char* Name() { return "UniverseWLevelCrowdExplore"; }
  void Clear() {}
  bool IsRun(const ContextData* session_data, const Params* params,
             FrontServerScene pos, const AdList* ad_list);
  StraRetCode Process(ContextData* session_data, Params* params,
                      FrontServerScene pos, AdList* ad_list);

 private:
  // 初始化 参数重置
  void Initialize();
  // 准入 是否进行顶价
  bool PvAdmit(const ContextData *session_data, const UniverseCpmThresholdParams *params, AdList *ad_list);
  // 寻找匹配的配置
  bool AdMatch(const UniverseCpmThresholdParams *params, const RankAdCommon& ad);
  // 执行顶价
  void AdCalc(ContextData *session_data, const UniverseCpmThresholdParams *params, RankAdCommon* p_ad);

 private:
  int64_t aim_cpm_ = 0;
  int64_t aim_cpm_threshold_ = 0;
  double max_threshold_ratio_ = 1.0;
  int64_t max_threshold_delta_cpm_ = 0;
  double explore_ratio_ = 1.0;
};

}  // namespace front_server
}  // namespace ks
