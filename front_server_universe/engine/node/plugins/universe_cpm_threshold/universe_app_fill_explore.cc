#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_app_fill_explore.h"

#include <algorithm>
#include <vector>
#include <string>
#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_cpm_threshold_params.h"

namespace ks {
namespace front_server {
StraRetCode UniverseAppFillExplorePlugin::Process(ContextData* session_data, Params* params,
                                                        FrontServerScene pos, AdList* ad_list) {
  auto *params_ = dynamic_cast<UniverseCpmThresholdParams*>(params);
  Initialize();
  auto comparator = [](const RankAdCommon *left, const RankAdCommon *right) {
    int64_t left_rank_benefit = left->rank_benifit();
    int64_t right_rank_benefit = right->rank_benifit();
    return std::tie(left_rank_benefit) > std::tie(right_rank_benefit);
  };
  if (PvAdmit(session_data, params_, ad_list)) {
    // 基于 rank_benefit 降序排序
    ad_list->StableSort(comparator);
    // 首位探索
    for (auto *p_ad : ad_list->Ads()) {
      if (AdMatch(*p_ad)) {
        AdCalc(session_data, p_ad);
        return StraRetCode::SUCC;
      }
      break;
    }
  }
  return StraRetCode::SUCC;
}

bool UniverseAppFillExplorePlugin::IsRun(const ContextData *session_data, const Params *params,
                                          FrontServerScene pos, const AdList *ad_list) {
  if (pos != FrontServerScene::UNIVERSE) {
    return false;
  }
  return true;
}

// 成员变量清理
void UniverseAppFillExplorePlugin::Initialize() {
  aim_cpm_threshold_ = 0.0;
  cpm_upper_ratio_ = 1.0;
}

/*
 * 流量维度准入 准入条件:
 * 1. ab 参数
 * 2. 当前底价超过底价下限
 * 3. 当前 pv 是内循环高价值人群
 */
bool UniverseAppFillExplorePlugin::PvAdmit(const ContextData *session_data,
                                         const UniverseCpmThresholdParams *params, AdList *ad_list) {
  if (ad_list->Size() <= 0) {
    return false;
  }
  // 非 bidding 流量过滤
  if (!session_data->get_is_universe_rtb_flow()) {
    return false;
  }
  // ab 参数
  if (!params->enable_universe_app_fill_explore) {
    return false;
  }
  // pos_id 准入
  auto pos_id_white_list = engine_base::AdKconfUtil::universeAppFillExplorePosWhiteList();
  if (pos_id_white_list == nullptr
      || pos_id_white_list->count(session_data->get_ud_pos_id()) == 0) {
    return false;
  }
  // 底价过滤
  if (session_data->get_ud_cpm_bid_floor() <
      params->inner_explore_bound_limit * kBenifitFactor) {
    return false;
  }
  // 更新填充门槛的目标 cpm
  aim_cpm_threshold_ = session_data->get_ud_cpm_bid_floor();
  cpm_upper_ratio_ = params->universe_app_fill_explore_upper_ratio;
  return true;
}

/*
 * 广告维度准入 准入条件:
 * 联盟内循环深度广告
 */
bool UniverseAppFillExplorePlugin::AdMatch(const RankAdCommon& ad) {
  // 转化目标准入
  auto ocpx_white_list = engine_base::AdKconfUtil::universeAppFillExploreOcpxWhiteList();
  if (ocpx_white_list == nullptr
     || ocpx_white_list->count(ad.ocpx_action_type()) == 0) {
    return false;
  }
  // 产品名过滤
  auto product_name_black_list = engine_base::AdKconfUtil::universeAppFillExploreProductBlackList();
  if (product_name_black_list == nullptr
      || product_name_black_list->count(ad.product_name()) != 0) {
    return false;
  }
  return true;
}

void UniverseAppFillExplorePlugin::AdCalc(ContextData *session_data, RankAdCommon* p_ad) {
  if (p_ad->bid_info.rtb_ecpm > 0 && aim_cpm_threshold_ > 0
      && p_ad->bid_info.rtb_ecpm < (aim_cpm_threshold_ * 100)) {
    double real_diff_ratio = (aim_cpm_threshold_ * 100.0 / p_ad->bid_info.rtb_ecpm);
    if (real_diff_ratio <= cpm_upper_ratio_) {
      p_ad->set_rank_benifit(aim_cpm_threshold_ * kBenifitFactor);
      p_ad->set_cpm(aim_cpm_threshold_ * kBenifitFactor);
      p_ad->set_auction_bid(aim_cpm_threshold_);
      p_ad->set_universe_rtb_ecpm(aim_cpm_threshold_ * 100);
    }
  }
}
}  // namespace front_server
}  // namespace ks
