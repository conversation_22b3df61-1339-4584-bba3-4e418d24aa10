#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_inner_cpm_bound_explore.h"

#include <algorithm>
#include <vector>
#include <string>
#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_cpm_threshold_params.h"

namespace ks {
namespace front_server {
StraRetCode UniverseInnerCpmBoundExplorePlugin::Process(ContextData* session_data, Params* params,
                                                        FrontServerScene pos, AdList* ad_list) {
  auto *params_ = dynamic_cast<UniverseCpmThresholdParams*>(params);
  Initialize(session_data);
  auto comparator = [](const RankAdCommon *left, const RankAdCommon *right) {
    int64_t left_rank_benefit = left->rank_benifit();
    int64_t right_rank_benefit = right->rank_benifit();
    return std::tie(left_rank_benefit) > std::tie(right_rank_benefit);
  };
  if (PvAdmit(session_data, params_, ad_list)) {
    // 基于 rank_benefit 排序
    ad_list->StableSort(comparator);
    // 遍历队列 找第一个符合条件的配置 rank_benefit 越大 配置越新 优先级越高
    for (auto *p_ad : ad_list->Ads()) {
      if (AdMatch(*p_ad)) {
        AdCalc(session_data, p_ad);
        return StraRetCode::SUCC;
      }
      if (is_skip_) {
        break;
      }
    }
  }
  return StraRetCode::SUCC;
}

bool UniverseInnerCpmBoundExplorePlugin::IsRun(const ContextData *session_data, const Params *params,
                                          FrontServerScene pos, const AdList *ad_list) {
  if (pos != FrontServerScene::UNIVERSE) {
    return false;
  }
  return true;
}

// 成员变量清理
void UniverseInnerCpmBoundExplorePlugin::Initialize(const ContextData *session_data) {
  bound_explore_config_ = nullptr;
  bound_explore_valid_config_.clear();
  target_config_ = nullptr;
  aim_cpm_ = 0;
  user_tags.clear();
  is_skip_ = false;
  enable_bidding_ = SPDM_enable_bidding_cpm_bound_explore(session_data->get_spdm_ctx());
}

/*
 * 流量维度准入 准入条件:
 * 1. ab 开关打开
 * 2. 当前底价超过底价下限
 * 3. 当前 pv 无广告过门槛
 * 4. 当前 pv 的 uid/appid/posid 可以匹配到满足媒体条件的配置 若配置指定了人群包 还需要满足人群包条件
 */
bool UniverseInnerCpmBoundExplorePlugin::PvAdmit(const ContextData *session_data,
                                         const UniverseCpmThresholdParams *params, AdList *ad_list) {
  if (ad_list->Size() <= 0) {
    return false;
  }
  // ab 开关
  if (!params->enable_universe_inner_cpm_bound_explore) {
    return false;
  }
  // 底价过滤
  if (session_data->get_ud_universe_pos_cpm_bound() <
      params->inner_explore_bound_limit * kBenifitFactor) {
    return false;
  }

  // 确保无广告过门槛
  for (auto *p_ad : ad_list->Ads()) {
    if (p_ad->ad_price.rank_benifit >= session_data->get_ud_universe_pos_cpm_bound() &&
        p_ad->ad_price.cpm >= (session_data->get_ud_cpm_bid_floor() * kBenifitFactor)) {
      return false;
    }
  }

  // 获取人群标签
  for (auto orientation : session_data->get_ad_request()->ad_user_info().universe_tag_info()) {
    if (orientation.id() == 3) {
      user_tags.insert(orientation.arr_long().begin(), orientation.arr_long().end());
      break;
    }
  }

  // 获取有效配置
  GetValidConfig(session_data);
  if (bound_explore_valid_config_.empty()) {
    return false;
  }
  // 更新目标 cpm
  if (enable_bidding_ && session_data->get_is_universe_rtb_flow()) {
    // bidding max(cpm_bound, cpm_floor)
    aim_cpm_ = std::max(params->universe_unify_cpm_bound * kBenifitFactor,
                      session_data->get_ud_cpm_bid_floor() * kBenifitFactor);
  } else {
    aim_cpm_ = std::max(session_data->get_ud_universe_pos_cpm_bound(),
                      session_data->get_ud_universe_pos_target_cpm());
  }
  return true;
}

bool UniverseInnerCpmBoundExplorePlugin::GetValidConfig(const ContextData* session_data) {
  auto* bound_explore_p2p = GetUniverseOperationBoundExploreContainer();
  if (!bound_explore_p2p) {
    return false;
  }
  auto bound_explore_p2p_ptr = bound_explore_p2p->GetData();
  if (!bound_explore_p2p_ptr || bound_explore_p2p_ptr->size() != 1) {
    return false;
  }
  auto iter = bound_explore_p2p_ptr->find(0);
  if (iter == bound_explore_p2p_ptr->end()) {
    return false;
  }
  bound_explore_config_ = &(iter->second);
  // 获取有效配置
  bound_explore_config_->GetValidConfigs(
      session_data->get_ad_request()->universe_ad_request_info().medium_uid(),
      session_data->copy_ud_app_id(), session_data->get_ud_pos_id(), user_tags,
      &bound_explore_valid_config_);
  session_data->dot_perf->Interval(bound_explore_valid_config_.size(), "valid_bound_explore_config");
  return !bound_explore_valid_config_.empty();
}

/*
 * 广告维度准入 准入条件:
 * 当前 ad 的 authorid/accountid/unitid 可以匹配从满足媒体条件的配置中匹配到到满足广告条件的配置
 * 若配置中的 campaign_type pt_type ocpx_action_type item_type 非空 则也要满足
 * 如果某广告距离 aim_cpm 的倍数大于配置上限 提前返回
 */
bool UniverseInnerCpmBoundExplorePlugin::AdMatch(const RankAdCommon& ad) {
  // 只处理内循环广告
  if (!ad.is_universe_inner_ad()) {
    return false;
  }

  if (ad.rank_benifit() <= 0 || aim_cpm_ <= 0) {
    return false;
  }
  static ad_base::TargetKeyConvertor str_2_int64;
  int64_t unit_id = ad.unit_id();
  int64_t account_id = ad.account_id();
  int64_t author_id = ad.author_id();
  int32_t campaign_type = ad.campaign_type();
  int64_t second_industry_id = ad.second_industry_id();
  int32_t item_type = ad.item_type();
  int64_t ocpx_action_type = ad.ocpx_action_type();
  int64_t pt_type_int64 = str_2_int64(ad.GetUniverseAdPlacementType());
  int64_t rank_benifit = ad.rank_benifit();
  double real_diff_ratio = aim_cpm_ * 1.0 / rank_benifit;
  if (real_diff_ratio > FrontKconfUtil::universeInnerBoundExploreMaxRatio()) {
    is_skip_ = true;
  }
  bound_explore_config_->AdMatch(unit_id, account_id, author_id, campaign_type,
                                 second_industry_id, item_type, ocpx_action_type,
                                 pt_type_int64, real_diff_ratio, bound_explore_valid_config_,
                                 &target_config_);
  return target_config_ != nullptr;
}

void UniverseInnerCpmBoundExplorePlugin::AdCalc(const ContextData *session_data, RankAdCommon* p_ad) {
  if (target_config_ == nullptr) {
    return;
  }
  double real_diff_ratio = aim_cpm_ * 1.0 / p_ad->rank_benifit();
  double ratio = std::max(std::min((double)real_diff_ratio, 10.0), 1.0);
  LOG_EVERY_N(INFO, 10000) << "UniverseInnerCpmBoundExplorePlugin AdCalc, unit_id: " << p_ad->unit_id()
            << ", account_id: " << p_ad->account_id()
            << ", author_id: " << p_ad->author_id()
            << ", campaign_type: " << p_ad->campaign_type()
            << ", second_industry_id: " << p_ad->second_industry_id()
            << ", item_type: " << p_ad->item_type()
            << ", ocpx_action_type: " << p_ad->ocpx_action_type()
            << ", pt_type: " << p_ad->GetUniverseAdPlacementType()
            << ", ratio: " << ratio
            << ", config id: " << target_config_->id;
  session_data->dot_perf->Interval(ratio * 100, "universe_operation_config_explore_rate",
                                     absl::StrCat(target_config_->id));
  session_data->dot_perf->Interval(ratio * 100, "universe_operation_config_explore_rate_divide",
                                     absl::StrCat(p_ad->ocpx_action_type()),
                                     absl::StrCat(p_ad->campaign_type()));
  p_ad->set_rank_benifit(aim_cpm_);
  p_ad->set_cpm(p_ad->ad_price.cpm * ratio);
  p_ad->set_auction_bid(p_ad->bid_info.auction_bid * ratio);
  p_ad->set_universe_inner_bound_explore_ratio(ratio);
}
}  // namespace front_server
}  // namespace ks
