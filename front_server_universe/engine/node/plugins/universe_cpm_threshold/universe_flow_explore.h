#pragma once

#include <string>
#include "teams/ad/front_server_universe/engine/node/plugins/plugin_base.h"

namespace ks {
namespace front_server {

class UniverseFlowExplorePlugin : public BonusExplorePlugin {
 public:
  const char* Name() { return "UniverseFlowExplore"; }
  void Clear() {}

 private:
  bool InitInner();
  bool Admit();
  bool GetTargetAd();
  bool BonusControl();
  bool BonusProcess();
  bool FlowAdmit();
  bool IsBudgetExceeded(const std::string strategy_tag_str, int64_t bonus_total_threshold);
  bool IsTargetAdUnion(RankAdCommon* p_ad);
  bool ExploreAdmitUnion(RankAdCommon* p_ad, double* roi);

 private:
  RankAdCommon* p_target_ad_union{nullptr};
  RankAdCommon* p_target_ad_kwai{nullptr};
};
}  // namespace front_server
}  // namespace ks

