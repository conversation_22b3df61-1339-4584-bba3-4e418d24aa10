#pragma once

#include "teams/ad/front_server_universe/engine/core/plugin.h"
#include "teams/ad/front_server_universe/engine/node/plugins/plugin_base.h"
#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_cpm_threshold_params.h"

namespace ks {
namespace front_server {

class UniverseTargetAudienceExplorePlugin : public BonusExplorePlugin {
 public:
  const char* Name() { return "UniverseTargetAudienceExplore"; }
  void Clear() {}

 private:
  bool InitInner();
  bool Admit();
  bool GetTargetAd();
  bool BonusControl();
  bool BonusProcess();

  bool IsTarget(const RankAdCommon* p_ad, int64_t* p_strategy_id);

 private:
  int64_t strategy_id{0};
  int64_t pos_id{0};
  static constexpr double kMinChargeValue = 1.0;
};
}  // namespace front_server
}  // namespace ks
