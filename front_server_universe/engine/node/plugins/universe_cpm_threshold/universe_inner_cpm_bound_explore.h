#pragma once

#include <vector>
#include <algorithm>
#include "absl/container/flat_hash_set.h"
#include "teams/ad/front_server_universe/engine/core/plugin.h"
#include "teams/ad/front_server_universe/engine/node/plugins/plugin_base.h"
#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_cpm_threshold_params.h"
#include "teams/ad/front_server_universe/bg_task/universe_operation_bound_explore/universe_operation_bound_explore.h"

namespace ks {
namespace front_server {

class UniverseInnerCpmBoundExplorePlugin : public AdlistPluginBase {
 public:
  const char* Name() { return "UniverseInnerCpmBoundExplore"; }
  void Clear() {}
  bool IsRun(const ContextData* session_data, const Params* params,
             FrontServerScene pos, const AdList* ad_list);
  StraRetCode Process(ContextData* session_data, Params* params,
                      FrontServerScene pos, AdList* ad_list);

 private:
  // 初始化 参数重置
  void Initialize(const ContextData *session_data);
  // 准入 是否进行顶价
  bool PvAdmit(const ContextData *session_data, const UniverseCpmThresholdParams *params, AdList *ad_list);
  // 寻找匹配的配置
  bool AdMatch(const RankAdCommon& ad);
  // 执行顶价
  void AdCalc(const ContextData *session_data, RankAdCommon* p_ad);
  bool GetValidConfig(const ContextData* session_data);

 private:
  BoundExploreConfigGroup* bound_explore_config_ = nullptr;
  std::vector<const engine_base::OperationConfigBoundExplore*> bound_explore_valid_config_;
  const engine_base::OperationConfigBoundExplore* target_config_ = nullptr;
  int64_t aim_cpm_ = 0;
  absl::flat_hash_set<int64_t> user_tags;
  bool is_skip_ = false;
  bool enable_bidding_ = false;
};

}  // namespace front_server
}  // namespace ks
