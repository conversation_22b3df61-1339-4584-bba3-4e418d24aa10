#pragma once

#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_cpm_threshold_params.h"

namespace ks {
namespace front_server {

class UniverseTinyForceCpmPlugin : public AdlistPluginBase {
 public:
  const char* Name() { return "UniverseTinyForceCpm"; }
  void Clear() {}
  bool IsRun(const ContextData* session_data, const Params* params,
             FrontServerScene pos, const AdList* ad_list);
  StraRetCode Process(ContextData* session_data, Params* params,
                      FrontServerScene pos, AdList* ad_list);
};

}  // namespace front_server
}  // namespace ks
