#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_inner_pv_allocation.h"

#include <algorithm>
#include <vector>
#include <string>
#include "absl/strings/str_cat.h"
#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_cpm_threshold_params.h"

namespace ks {
namespace front_server {
StraRetCode UniverseInnerPvAllocationPlugin::Process(ContextData* session_data, Params* params,
                                                        FrontServerScene pos, AdList* ad_list) {
  auto *params_info = dynamic_cast<UniverseCpmThresholdParams*>(params);
  Initialize();
  auto comparator = [](const RankAdCommon *left, const RankAdCommon *right) {
    int64_t left_rank_benefit = left->rank_benifit();
    int64_t right_rank_benefit = right->rank_benifit();
    return std::tie(left_rank_benefit) > std::tie(right_rank_benefit);
  };
  if (PvAdmit(session_data, params_info, ad_list)) {
    // 基于 rank_benefit 降序排序
    ad_list->StableSort(comparator);
    // 首位探索
    for (auto *p_ad : ad_list->Ads()) {
      if (p_ad == nullptr) {
        continue;
      }
      ++idx_all;
      if (idx_all == 1) {
        // 全局首位广告记录下来
        first_all_ad_ = p_ad;
      }
      if ((p_ad->campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE
            ||p_ad->campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE)
          && (p_ad->bid_info.ocpx_action_type == kuaishou::ad::EVENT_ORDER_PAIED
            || p_ad->bid_info.ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS
            || p_ad->bid_info.ocpx_action_type == kuaishou::ad::AD_STOREWIDE_ROAS
            || p_ad->bid_info.ocpx_action_type == kuaishou::ad::AD_MERCHANT_T7_ROI)) {
        // 判断是否内循环深度预算 是则记录下来
        ++idx_inner_deep;
        if (idx_inner_deep == 1) {
          // 内循环深度首位广告记录下来
          first_all_inner_deep_ad_ = p_ad;
          if (idx_all == 2) {
            // 内循环深度预算非全局首位，但是全局 Top2
            inner_deep_top1_all_top2 = p_ad;
          }
        }
      }
      if (AdMatch(p_ad)) {
        AdCalc(session_data, params_info, p_ad);
        return StraRetCode::SUCC;
      }
      continue;
    }
  }
  return StraRetCode::SUCC;
}

bool UniverseInnerPvAllocationPlugin::IsRun(const ContextData *session_data, const Params *params,
                                          FrontServerScene pos, const AdList *ad_list) {
  if (pos != FrontServerScene::UNIVERSE) {
    return false;
  }
  return true;
}

// 成员变量清理
void UniverseInnerPvAllocationPlugin::Initialize() {
  idx_all = 0;
  idx_inner_deep = 0;
  aim_cpm = 0;
  ecpm_delta = 0;
  ecpm_delta_ratio = 1.0;
  is_inner_deep_top1_not_all_top1 = false;
  is_inner_deep_top1_all_top2 = false;
  first_all_ad_ = nullptr;
  first_all_inner_deep_ad_ = nullptr;
  inner_deep_top1_all_top2 = nullptr;
}

/*
 * 流量维度准入 准入条件:
 * 1. ab 参数
 * 2. 当前底价超过底价下限
 * 3. 当前 pv 有内循环深度预算的
 */
bool UniverseInnerPvAllocationPlugin::PvAdmit(const ContextData *session_data,
                                         const UniverseCpmThresholdParams *params, AdList *ad_list) {
  if (ad_list->Size() <= 0) {
    return false;
  }

  // ab 参数
  if (!params->enable_universe_inner_pv_allocation) {
    return false;
  }

  // 底价过滤
  if (session_data->get_ud_universe_pos_cpm_bound() <
      params->inner_explore_bound_limit * kBenifitFactor) {
    return false;
  }

  return true;
}

/*
 * 广告维度准入 准入条件:
 */
bool UniverseInnerPvAllocationPlugin::AdMatch(RankAdCommon* ad) {
  if (ad != first_all_ad_
      && ad == first_all_inner_deep_ad_) {
    // 联盟内循环深度广告 不是全局首位 & 内循环深度首位
    aim_cpm = first_all_ad_->ad_price.cpm;
    is_inner_deep_top1_not_all_top1 = true;
    return true;
  }
  return false;
}

void UniverseInnerPvAllocationPlugin::AdCalc(ContextData *session_data,
                              const UniverseCpmThresholdParams *params, RankAdCommon* p_ad) {
  if (is_inner_deep_top1_not_all_top1
      && p_ad->ad_price.cpm > 0) {
    double add_ratio = params->universe_inner_pv_allocation_ecpm_add_ratio;
    ecpm_delta_ratio = fmax(aim_cpm * 1.0 / p_ad->ad_price.cpm + add_ratio, 1.0);
    ecpm_delta = fmax(aim_cpm - p_ad->ad_price.cpm, 0);
    if (ecpm_delta_ratio > 1.0 && ecpm_delta > 0
        && ecpm_delta_ratio <= params->universe_inner_pv_allocation_ecpm_ratio_upbound
        && ecpm_delta <= params->universe_inner_pv_allocation_ecpm_delta_threshold) {
      p_ad->set_rank_benifit(p_ad->ad_price.rank_benifit * ecpm_delta_ratio);
      p_ad->set_cpm(p_ad->ad_price.cpm * ecpm_delta_ratio);
      p_ad->set_auction_bid(p_ad->bid_info.auction_bid * ecpm_delta_ratio);
      // ratio 字段落表
      session_data->set_ud_inner_pv_allocation_strategy_ratio(ecpm_delta_ratio);
      // perf 监控
      std::string perf_key = absl::StrCat(",", p_ad->campaign_type(), ",", p_ad->bid_info.ocpx_action_type);
      std::string perf_key_hit = absl::StrCat("front_server_universe.inner_pv_allocation",
                                              "_hit",
                                              perf_key);
      std::string perf_key_ratio = absl::StrCat("front_server_universe.inner_pv_allocation",
                                                "_ratio",
                                                perf_key);
      session_data->dot_perf->Interval(ecpm_delta_ratio * 100, perf_key_ratio);
      session_data->dot_perf->Count(1, perf_key_hit);
    }
  }
}
}  // namespace front_server
}  // namespace ks
