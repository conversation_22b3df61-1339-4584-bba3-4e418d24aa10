#pragma once

#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/front_server_universe/engine/core/plugin.h"
#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_cpm_threshold_params.h"

namespace ks {
namespace front_server {

class UniverseCpmBoundFilterPlugin : public AdlistPluginBase {
 public:
  const char* Name() { return "UniverseCpmBoundFilter"; }
  void Clear() {}
  bool IsRun(const ContextData* session_data, const Params* params,
             FrontServerScene pos, const AdList* ad_list);
  StraRetCode Process(ContextData* session_data, Params* params,
                      FrontServerScene pos, AdList* ad_list);
 private:
  void MonitorRemoveMediumUidCount(ContextData* session_data_,
                                   bool is_hit_exp,
                                   bool is_flow_in_white_list,
                                   bool is_rtb_flow);
  void MonitorRankingPriorValues(const RankAdCommon &ad, double cpm_bound,
                                 double target_cpm,
                                 const int64_t max_rank_cpm,
                                 const int64_t max_rank_benefit,
                                 ContextData *session_data,
                                 UniverseCpmThresholdParams* params);
  void CpmBoundFilter(ContextData* session_data, AdList* ad_list, UniverseCpmThresholdParams* params);
  bool IsFlowInWhiteList(ContextData* session_data_);
  bool IsRtbFlow(ContextData* session_data_);
};

}  // namespace front_server
}  // namespace ks
