#pragma once

#include <vector>
#include <algorithm>
#include "absl/container/flat_hash_set.h"
#include "teams/ad/front_server_universe/engine/core/plugin.h"
#include "teams/ad/front_server_universe/engine/node/plugins/plugin_base.h"
#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_cpm_threshold_params.h"

namespace ks {
namespace front_server {

class UniverseInnerPvAllocationPlugin : public AdlistPluginBase {
 public:
  const char* Name() { return "UniverseInnerPvAllocation"; }
  void Clear() {}
  bool IsRun(const ContextData* session_data, const Params* params,
             FrontServerScene pos, const AdList* ad_list);
  StraRetCode Process(ContextData* session_data, Params* params,
                      FrontServerScene pos, AdList* ad_list);

 private:
  // 初始化 参数重置
  void Initialize();
  // 准入
  bool PvAdmit(const ContextData *session_data, const UniverseCpmThresholdParams *params, AdList *ad_list);
  // 寻找匹配的配置
  bool AdMatch(RankAdCommon* p_ad);
  // 执行
  void AdCalc(ContextData *session_data, const UniverseCpmThresholdParams *params, RankAdCommon* p_ad);

  // private:
  int64_t idx_all = 0;
  int64_t idx_inner_deep = 0;
  int64_t aim_cpm = 0;
  int64_t ecpm_delta = 0;
  double ecpm_delta_ratio = 1.0;
  bool is_inner_deep_top1_not_all_top1 = false;
  bool is_inner_deep_top1_all_top2 = false;
  RankAdCommon* first_all_ad_ = nullptr;
  RankAdCommon* first_all_inner_deep_ad_ = nullptr;
  RankAdCommon* inner_deep_top1_all_top2 = nullptr;
};

}  // namespace front_server
}  // namespace ks
