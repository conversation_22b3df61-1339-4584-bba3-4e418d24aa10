#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_sort_by_rank_benefit.h"

namespace ks {
namespace front_server {

bool SortByRankBenifitPlugin::IsRun(const ContextData* session_data,
                                    const Params* params,
                                    FrontServerScene pos,
                                    const AdList* ad_list) {
  if (pos != FrontServerScene::UNIVERSE) {
    return false;
  }
  return true;
}

StraRetCode SortByRankBenifitPlugin::Process(ContextData* session_data,
                                             Params* params,
                                             FrontServerScene pos,
                                             AdList* ad_list) {
  if (ad_list->Ads().size() <= 0) {
    return StraRetCode::SUCC;
  }
  auto *params_ = dynamic_cast<UniverseCpmThresholdParams*>(params);

  auto comparator = [](const RankAdCommon *left, const RankAdCommon *right) {
      int64_t left_rank_benefit = left->ad_price.GetRankBenefit();
      int64_t right_rank_benefit = right->ad_price.GetRankBenefit();
      return std::tie(left_rank_benefit) > std::tie(right_rank_benefit);
    };

  ad_list->StableSort(comparator);

  int num = 0;
  for (auto& p_ad : ad_list->Ads()) {
    // 记录每个元素在 rank 中的顺位
    p_ad->set_pos_in_rank(num);
    num += 1;
  }
  return StraRetCode::SUCC;
}

}  // namespace front_server
}  // namespace ks
