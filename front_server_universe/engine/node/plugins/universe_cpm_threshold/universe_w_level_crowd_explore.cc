#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_w_level_crowd_explore.h"

#include <algorithm>
#include <vector>
#include <string>
#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_cpm_threshold_params.h"

namespace ks {
namespace front_server {
StraRetCode UniverseWLevelCrowdExplorePlugin::Process(ContextData* session_data, Params* params,
                                                        FrontServerScene pos, AdList* ad_list) {
  auto *params_ = dynamic_cast<UniverseCpmThresholdParams*>(params);
  Initialize();
  auto comparator = [](const RankAdCommon *left, const RankAdCommon *right) {
    int64_t left_rank_benefit = left->rank_benifit();
    int64_t right_rank_benefit = right->rank_benifit();
    return std::tie(left_rank_benefit) > std::tie(right_rank_benefit);
  };
  if (PvAdmit(session_data, params_, ad_list)) {
    // 基于 rank_benefit 降序排序
    ad_list->StableSort(comparator);
    // 首位探索
    for (auto *p_ad : ad_list->Ads()) {
      if (AdMatch(params_, *p_ad)) {
        AdCalc(session_data, params_, p_ad);
        return StraRetCode::SUCC;
      }
      break;
    }
  }
  return StraRetCode::SUCC;
}

bool UniverseWLevelCrowdExplorePlugin::IsRun(const ContextData *session_data, const Params *params,
                                          FrontServerScene pos, const AdList *ad_list) {
  if (pos != FrontServerScene::UNIVERSE) {
    return false;
  }
  return true;
}

// 成员变量清理
void UniverseWLevelCrowdExplorePlugin::Initialize() {
  aim_cpm_ = 0;
  aim_cpm_threshold_ = 0;
  max_threshold_ratio_ = 1.0;
  max_threshold_delta_cpm_ = 0;
  explore_ratio_ = 1.0;
}

/*
 * 流量维度准入 准入条件:
 * 1. ab 参数
 * 2. 当前 pv 是 w-level 人群
 */
bool UniverseWLevelCrowdExplorePlugin::PvAdmit(const ContextData *session_data,
                                         const UniverseCpmThresholdParams *params, AdList *ad_list) {
  if (ad_list->Size() <= 0) {
    return false;
  }
/*
 * enable_universe_w_level_crowd_adn_fill_high adn 填充（高于 bound 也置为 bound）
 * enable_universe_w_level_crowd_adn_fill adn 填充
 * enable_universe_w_level_crowd_adn_explore adn 顶价
 * enable_universe_w_level_crowd_bidding_explore bidding 顶价
 */
  // ab 参数
  if (!params->enable_universe_w_level_crowd_bidding_explore
      && !params->enable_universe_w_level_crowd_adn_fill) {
    return false;
  }

  // w-level 人群 pv
  auto* universe_ad_request_info = session_data->mutable_ad_request()->mutable_universe_ad_request_info();
  auto w_level = universe_ad_request_info->universe_user_w_level();
  bool is_w_pv = w_level.size() > 1 && params->universe_valid_w_level != 0
                && w_level[1] >= params->universe_valid_w_level + '0';
  if (!is_w_pv) {
    return false;
  }

  // 更新填充门槛的目标 cpm
  aim_cpm_threshold_ = session_data->get_ud_universe_pos_cpm_bound();
  // 更新目标 cpm
  aim_cpm_ = std::max(session_data->get_ud_universe_pos_cpm_bound(),
                      session_data->get_ud_universe_pos_target_cpm());
  // 更新阈值
  max_threshold_ratio_ = params->universe_w_level_crowd_adn_max_ratio;
  max_threshold_delta_cpm_ = params->universe_w_level_crowd_adn_max_delta_cpm;
  // 探索系数
  explore_ratio_ = params->universe_w_level_crowd_explore_ratio;
  return true;
}

/*
 * 广告维度准入 准入条件:
 * 联盟付费广告
 */
bool UniverseWLevelCrowdExplorePlugin::AdMatch(const UniverseCpmThresholdParams *params,
                                      const RankAdCommon& ad) {
  // 联盟付费广告
  if (ad.ocpx_action_type() != kuaishou::ad::AD_PURCHASE
    && params->enable_universe_w_level_purchase_ad) {
    return false;
  }
  return true;
}

void UniverseWLevelCrowdExplorePlugin::AdCalc(ContextData *session_data,
                                      const UniverseCpmThresholdParams *params, RankAdCommon* p_ad) {
  if (p_ad->ad_price.rank_benifit > aim_cpm_threshold_) {
    // 1. 竞胜实验（adn 顶价）
    if (false && session_data->get_is_universe_adn_flow()) {
        p_ad->set_rank_benifit(p_ad->ad_price.rank_benifit * explore_ratio_);
        p_ad->set_cpm(p_ad->ad_price.cpm * explore_ratio_);
        p_ad->set_auction_bid(p_ad->bid_info.auction_bid * explore_ratio_);
        p_ad->set_universe_w_level_crowd_ratio(explore_ratio_);
    }
    // 2. 竞胜实验（bidding 顶价）
    if (params->enable_universe_w_level_crowd_bidding_explore && !session_data->get_is_universe_adn_flow()) {
        p_ad->set_rank_benifit(p_ad->ad_price.rank_benifit * explore_ratio_);
        p_ad->set_cpm(p_ad->ad_price.cpm * explore_ratio_);
        p_ad->set_auction_bid(p_ad->bid_info.auction_bid * explore_ratio_);
        p_ad->set_universe_w_level_crowd_ratio(explore_ratio_);
    }
  }
  if (params->enable_universe_w_level_crowd_adn_fill && session_data->get_is_universe_adn_flow()) {
    double real_diff_ratio = aim_cpm_threshold_ * 1.0 / p_ad->ad_price.rank_benifit;
    int64_t real_threshold_delta_cpm = aim_cpm_threshold_ - p_ad->ad_price.rank_benifit;
    // 3. 填充实验（高于 bound 也置为 bound）
    if (false) {
      if (real_diff_ratio <= max_threshold_ratio_
          && real_threshold_delta_cpm <= max_threshold_delta_cpm_) {
        p_ad->set_rank_benifit(aim_cpm_threshold_);
        p_ad->set_cpm(aim_cpm_threshold_);
        p_ad->set_auction_bid(aim_cpm_threshold_ / 1e6);
        p_ad->set_universe_w_level_crowd_ratio(real_diff_ratio);
      }
    } else {
    // 4. 填充实验（高于 bound 不置为 bound）
      if (p_ad->ad_price.rank_benifit < aim_cpm_threshold_
          && real_diff_ratio <= max_threshold_ratio_
          && real_threshold_delta_cpm <= max_threshold_delta_cpm_) {
        p_ad->set_rank_benifit(aim_cpm_threshold_);
        p_ad->set_cpm(aim_cpm_threshold_);
        p_ad->set_auction_bid(aim_cpm_threshold_ / 1e6);
        p_ad->set_universe_w_level_crowd_ratio(real_diff_ratio);
      }
    }
  }
}
}  // namespace front_server
}  // namespace ks
