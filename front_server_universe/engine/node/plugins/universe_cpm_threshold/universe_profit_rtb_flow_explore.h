#pragma once
#include <unordered_map>
#include <unordered_set>

#include "teams/ad/front_server_universe/engine/core/plugin.h"
#include "teams/ad/front_server_universe/engine/node/plugins/plugin_base.h"
#include "teams/ad/engine_base/cache_loader/universe_rtb_win_rate.h"
#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_cpm_threshold_params.h"

namespace ks {
namespace front_server {

class UniverseProfitRtbFlowExplorePlugin : public BonusExplorePlugin {
 public:
  const char* Name() { return "UniverseProfitRtbFlowExplore"; }
  void Clear() {}

 private:
  bool InitInner();
  bool Admit();
  bool GetTargetAd();
  bool BonusControl();
  bool BonusProcess();

  void AdjustRoiLimitByUg(const RankAdCommon* p_ad, double* roi_limit);
  bool SetMediaTags();
  bool IsTarget(const RankAdCommon* p_ad, double* p_roi,
      int64_t* p_strategy_id, int64_t* p_target_rtb_ecpm, int64_t* p_base_rtb_ecpm);
  bool IsTargetRtbAd(const RankAdCommon* p_ad);
  bool CalRtbRoi(const RankAdCommon* p_ad, double roi_limit,
                double *max_roi, int64_t *max_target_rtb_ecpm, int64_t* tmp_base_rtb_ecpm);

 private:
  int64_t strategy_id{0};
  std::unordered_map<int64_t, double> win_rate_map;
  int64_t target_rtb_ecpm = 0;
  int64_t base_rtb_ecpm = 0;
  std::unordered_set<int64_t> union_bouns_tag;
};
}  // namespace front_server
}  // namespace ks
