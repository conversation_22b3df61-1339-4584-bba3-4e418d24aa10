#include "teams/ad/front_server_universe/engine/node/plugins/universe_cpm_threshold/universe_tiny_force_cpm.h"

namespace ks {
namespace front_server {

StraRetCode UniverseTinyForceCpmPlugin::Process(ContextData* session_data, Params* params,
                                                FrontServerScene pos, AdList* ad_list) {
  auto *params_ = dynamic_cast<UniverseCpmThresholdParams*>(params);
  const auto& config = FrontKconfUtil::universeTinyForceCpm()->data();
  auto iter = config.pos_2_cpm.find(session_data->get_ud_pos_id());
  if (iter == config.pos_2_cpm.end()) return StraRetCode::SUCC;

  int64_t aim_cpm = iter->second;
  for (auto *p_ad : ad_list->Ads()) {
    // 首位广告按配置出价
    if ((config.pb().close_product_names() || config.product_names.count(p_ad->base_np.product_name)) &&
        p_ad->universe_tiny_query_type() == 10) {
      p_ad->set_rank_benifit(aim_cpm);
      p_ad->set_cpm(aim_cpm);
      p_ad->set_auction_bid(aim_cpm / 1e6);
      p_ad->set_universe_rtb_ecpm(p_ad->bid_info.auction_bid * session_data->get_universe_rtb_coff() * 100);
      session_data->dot_perf->Interval(aim_cpm, "universe_tiny_force_cpm",
                                       absl::StrCat(session_data->get_ud_pos_id()),
                                       p_ad->base_np.product_name);
      LOG_EVERY_N(INFO, 1000) << "UniverseTinyForceCpm pos_id:" << session_data->get_ud_pos_id()
                              << " product_name:" << p_ad->base_np.product_name << " aim_cpm:" << aim_cpm
                              << " rank_benifit:" << p_ad->rank_benifit() << " cpm:" << p_ad->ad_price.cpm
                              << " auction_bid:" << p_ad->bid_info.auction_bid
                              << " rtb_ecpm:" << p_ad->bid_info.rtb_ecpm
                              << " rtb_coff:" << session_data->get_universe_rtb_coff();
    }
  }
  return StraRetCode::SUCC;
}

bool UniverseTinyForceCpmPlugin::IsRun(const ContextData *session_data, const Params *params,
                                       FrontServerScene pos, const AdList *ad_list) {
  if (pos != FrontServerScene::UNIVERSE) {
    return false;
  }
  if (!session_data->get_ud_is_universe_tiny_flow()) {
    return false;
  }
  if (FrontKconfUtil::universeTinyForceCpm()->data().pos_ids.count(session_data->get_ud_pos_id()) ==
      0) {
    return false;
  }
  return true;
}

}  // namespace front_server
}  // namespace ks
