#pragma once

#include <string>
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/front_server_universe/engine/core/plugin.h"

namespace ks {
namespace front_server {

// 声明插件
DECLARE_PLUGIN_FUNCTION(LlsidFilter)
DECLARE_PLUGIN_FUNCTION(UserCpmPredictFilter)  // 用户 cpm
DECLARE_PLUGIN_FUNCTION(UniverseNewMdediaFilter)  // 联盟新媒体保护
DECLARE_PLUGIN_FUNCTION(PreHandleFilter)
DECLARE_PLUGIN_FUNCTION(UniverseCreativeAudit)
}  // namespace front_server
}  // namespace ks
