#include "teams/ad/front_server_universe/engine/node/plugins/universe_auction/universe_auction_params.h"

#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"

namespace ks {
namespace front_server {
void UniverseAuctionParams::SetPriceAdjustParams(ContextData *session_data) {
  inner_nobid_charge_ratio_upper = SPDM_inner_nobid_charge_ratio_upper(session_data->get_spdm_ctx());
  inner_cost_cap_charge_ratio_upper = SPDM_inner_cost_cap_charge_ratio_upper(session_data->get_spdm_ctx());
  inner_live_nobid_charge_ratio_upper =
      SPDM_inner_live_nobid_charge_ratio_upper(session_data->get_spdm_ctx());
  universe_inner_loop_indcap_map = engine_base::AdKconfUtil::universeInnerLoopIndCapAdjust();
  universe_inner_loop_account_map = engine_base::AdKconfUtil::universeInnerLoopAccountAdjust();
  universe_inner_loop_indcapocpc_map = engine_base::AdKconfUtil::universeInnerLoopIndCapOcpAdjust();
  merchant_video_pay_charge_ratio_upper =
      SPDM_merchant_video_pay_charge_ratio_upper(session_data->get_spdm_ctx());
}


}   // namespace front_server
}   // namespace ks
