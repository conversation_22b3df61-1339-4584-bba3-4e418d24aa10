#pragma once

#include "teams/ad/front_server_universe/engine/core/plugin.h"
#include "teams/ad/front_server_universe/engine/node/plugins/universe_auction/universe_auction_params.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"

namespace ks {
namespace front_server {

class RankingPriceUniversePlugin : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS
 private:
  double GetPriceRatio(ContextData* session_data);
  double GetInnerLoopRatio(ContextData* session_data, RankAdCommon* p_ad, UniverseAuctionParams* params);
  void PriceAdjust(ContextData* session_data, AdList* ad_list, UniverseAuctionParams* params);
  int64 RankdomSetPriceDouble2Int64(double price);
};

}  // namespace front_server
}  // namespace ks
