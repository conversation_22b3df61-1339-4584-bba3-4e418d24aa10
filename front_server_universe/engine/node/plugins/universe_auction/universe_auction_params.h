#pragma once
#include <map>
#include <memory>
#include <string>

#include "teams/ad/front_server_universe/engine/core/plugin.h"

namespace ks {
namespace front_server {
class ContextData;
class UniverseAuctionParams final : public Params {
 public:
  bool retarget_sort_succ{false};
  std::shared_ptr<std::map<std::string, double>> universe_inner_loop_indcap_map;
  std::shared_ptr<std::map<int64_t, double>> universe_inner_loop_account_map;
  std::shared_ptr<std::map<std::string, double>> universe_inner_loop_indcapocpc_map;
  bool enable_plugin_process{true};
  double inner_nobid_charge_ratio_upper;
  double inner_cost_cap_charge_ratio_upper;
  double inner_live_nobid_charge_ratio_upper;
  double merchant_video_pay_charge_ratio_upper;
 public:
  void SetPriceAdjustParams(ContextData *session_data);
};
}   // namespace front_server
}   // namespace ks
