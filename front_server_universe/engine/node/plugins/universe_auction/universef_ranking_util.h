#pragma once

#include <string>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <vector>

#include "teams/ad/engine_base/kconf/universe_cold_start_support.h"

namespace kuaishou {
namespace ad {
class AdRequest;
}
}

namespace ks {
namespace front_server {

struct RankAdCommon;
struct ContextData;
class AdList;

// 这个函数要移动到通用函数中
bool TargetColdStartSupport(
    const ContextData &session_context, RankAdCommon *ad, double *p_bonus_rate = nullptr);

}  // namespace front_server
}  // namespace ks

