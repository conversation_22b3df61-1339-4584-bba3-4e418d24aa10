#include "teams/ad/front_server_universe/engine/node/plugins/universe_auction/ranking_universe_retarget_strategy.h"

#include <algorithm>
#include <memory>
#include <string>
#include <unordered_map>

#include "teams/ad/front_server_universe/engine/node/plugins/universe_auction/universe_auction_params.h"

namespace ks {
namespace front_server {

const char* RetargetAssignPricePlugin::Name() { return "RetargetAssignPrice"; }

void RetargetAssignPricePlugin::Clear() {}

bool RetargetAssignPricePlugin::IsRun(const ContextData *session_data_, const Params *auction_params,
                                      FrontServerScene scene, const AdList *ad_list) {
  if (scene != FrontServerScene::UNIVERSE) {
    return false;
  }
  if (ad_list->Size() <= 0) {
    return false;
  }
  return true;
}

StraRetCode RetargetAssignPricePlugin::Process(ContextData *session_data_, Params *auction_params,
                                               FrontServerScene scene, AdList *ad_list) {
  auto *params_ = dynamic_cast<UniverseAuctionParams *>(auction_params);
  std::unordered_map<int64_t, int64_t> tag_adjust_counter;
  for (auto *p_ad : ad_list->Ads()) {
    // 注意，这里的条件和重定向补贴策略的准入条件一致
    if (!p_ad->GetRankResult()->rank_base_info().is_retarget_ad()) {
      continue;
    }
    auto total_price = p_ad->price();
    p_ad->set_virtual_price(p_ad->retarget_dsp_price_ratio() * total_price);
    p_ad->set_price(total_price - p_ad->virtual_price());
    tag_adjust_counter[p_ad->base.retarget_tool_tag] += 1;
    session_data_->dot_perf->Interval(p_ad->price(), "front_server.retarget_strategy.price");
  }
  for (auto &kv : tag_adjust_counter) {
    session_data_->dot_perf->Interval(kv.second, "engine_base.retarget_assign_price",
                                     std::to_string(kv.first));
  }
  return StraRetCode::SUCC;
}

}   // namespace front_server
}   // namespace ks
