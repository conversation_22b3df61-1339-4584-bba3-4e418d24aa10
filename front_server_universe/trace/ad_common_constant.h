#pragma once

#include <string>
#include "teams/ad/ad_base/src/trace_filter/trace_filter.h"

namespace ks {
namespace front_server {
/// 通用常量

/// 通用宏定义
#define CONTINUE_WITH_REASON(event_id, filter_name)  \
  {                                                  \
    session_data_->RecordHardLastCondition(event_id); \
    char falcon_str[30 + MAX_FILTER_NAME_LENGTH] = {0}; \
    strncpy(falcon_str, "front_server.filter_by_", strlen("front_server.filter_by_")); \
    session_data_->dot_perf->Count(1, falcon_str, filter_name); \
    strncat(falcon_str, filter_name, MAX_FILTER_NAME_LENGTH); \
    falcon::Inc(falcon_str, 1); \
    continue;                                              \
  }

#define CONTINUE_WITH_REASON_R(is_hard, event_id, node_type)  \
{ \
  session_data_->mutable_ad_select_stage_infos()->SetAdResponseFilterInfo(is_hard, creative_id,   \
                          event_id, node_type);  \
  CONTINUE_WITH_REASON(event_id, \
    ks::ad_base::TraceFilterName::Instance()->NamePost(event_id).c_str()) \
}

#define TRACE_WITH_REASON(event_id, filter_name)                                                    \
  {                                                                                                 \
    session_data_->RecordHardLastCondition(event_id); \
    char falcon_str[30 + MAX_FILTER_NAME_LENGTH] = {0};                                             \
    strncpy(falcon_str, "front_server.filter_by_", strlen("front_server.filter_by_"));              \
    session_data_->dot_perf->Count(1, falcon_str, filter_name);                                     \
    strncat(falcon_str, filter_name, MAX_FILTER_NAME_LENGTH);                                       \
    falcon::Inc(falcon_str, 1);                                                                     \
  }

#define TRACE_WITH_REASON_R(is_hard, event_id, node_type)                                                   \
  {                                                                                                         \
    session_data_->mutable_ad_select_stage_infos()->SetAdResponseFilterInfo(is_hard, creative_id, event_id, \
                                                                            node_type);                     \
    TRACE_WITH_REASON(event_id, ks::ad_base::TraceFilterName::Instance()->NamePost(event_id).c_str())       \
  }

#define LATENCY_RECORD(name)       \
  ad_base::LatencyRecord lr(name); \
  ignore_result(lr)

}  // namespace front_server
}  // namespace ks
