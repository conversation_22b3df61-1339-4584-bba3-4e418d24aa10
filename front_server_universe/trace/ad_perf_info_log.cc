#include "teams/ad/front_server_universe/trace/ad_perf_info_log.h"

namespace ks {
namespace front_server {
void AdPerfInfoLog::Clear() {
  perf_info.Clear();
}

void AdPerfInfoLog::SetDownStreamServiceStatus(kuaishou::ad::ServiceDeployName server_name,
                                                    int64_t time_cost_us, int32_t status) {
  auto *down_stream_service_status = perf_info.add_down_stream_service_status();
  down_stream_service_status->set_server_name(server_name);
  down_stream_service_status->set_time_cost_ms(time_cost_us/1000);
  down_stream_service_status->set_status(status);
}

}  // namespace front_server
}  // namespace ks
