#pragma once

#include "teams/ad/ad_proto/kuaishou/ad/engine_trace_log/ad_simplify_always_log.pb.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_trace_log.pb.h"

namespace ks {
namespace front_server {

using kuaishou::ad::AdTraceFunnel;

class AdFrontSimplifyAlwaysLogManager {
 public:
  AdFrontSimplifyAlwaysLogManager();
  ~AdFrontSimplifyAlwaysLogManager() {}
  static AdFrontSimplifyAlwaysLogManager* GetInstance();

  void SetUserInfo(ContextData* context, const kuaishou::ad::AdRequest& ad_request);
  void SetUniverseRequestInfo(ContextData* context, const kuaishou::ad::AdRequest& ad_request);
  void TraceFrontPkFunnel(kuaishou::ad::FrontServerResponse* front_resp,
             kuaishou::ad::AdResponse* ad_resp, int* ad_cnt_after_pk, int* ad_cnt_after_pk_soft);
  void CountAdNum(int* soft, int* hard, const kuaishou::ad::AdResult& result);


  void AlwaysLogConvert(ContextData* context, kuaishou::log::ad::AdDspTraceAlwaysLog* log);
  void SetUniverseRequestInfo(::kuaishou::ad::UniverseAdRequestInfo* mutable_universe_request_info,
      const ::kuaishou::ad::UniverseAdRequestInfo& src);
  void SetUserInfo(::kuaishou::log::ad::AdTraceUserInfo* mutable_user_info,
      const ContextData& context, const kuaishou::ad::AdRequest& ad_request);

 private:
  void ConvertFunnel(const google::protobuf::RepeatedPtrField<kuaishou::log::ad::AdServingFunnel>&,
                     kuaishou::log::ad::AdDspTraceAlwaysLogDetailInfo*);
  void SetResourceWhiteBoxInfo(ContextData* context);
};

#define ALWAYS_LOG_TRACER AdFrontSimplifyAlwaysLogManager::GetInstance()

}  // namespace front_server
}  // namespace ks
