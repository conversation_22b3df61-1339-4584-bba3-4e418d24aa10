#pragma once
#include <cstdint>
#include "teams/ad/ad_proto/kuaishou/ad/engine_trace_log/ad_simplify_trace_log.pb.h"

namespace ks {
namespace front_server {

class AdPerfInfoLog {
 public:
  AdPerfInfoLog() {}
  ~AdPerfInfoLog() {}
  void Clear();
  void SetServerTime(int64_t server_time) {
    perf_info.set_server_time_cost_ms(server_time);
  }
  void SetDownStreamServiceStatus(kuaishou::ad::ServiceDeployName server_name, int64_t time_cost_us, int32_t status);  // NOLINT

 public:
  kuaishou::ad::PerfInfo perf_info;
};

}  // namespace front_server
}  // namespace ks
