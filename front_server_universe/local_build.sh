NO_STRIP=false
for arg in "$@"; do
  if [ "$arg" == "--no-strip" ]; then
    NO_STRIP=true
    break
  fi
done

export ENABLE_LLD=true
kbuild build //teams/ad/front_server_universe
if [ "$NO_STRIP" = false ]; then
  echo "去除符号信息(--no-strip禁用)..."
  mv .build/clang_opt/targets/teams/ad/front_server_universe/ad_front_server .build/clang_opt/targets/teams/ad/front_server_universe/ad_front_server.debug
  strip -o .build/clang_opt/targets/teams/ad/front_server_universe/ad_front_server .build/clang_opt/targets/teams/ad/front_server_universe/ad_front_server.debug
fi