#pragma once

#include <string>

#include "third_party/jsoncpp/include/json/json.h"
#include "json/value.h"

namespace ks {
namespace front_server {

class FillApiExtData {
 public:
  FillApiExtData() {
    final_json_.clear();
    final_string_ = "";
  }
  ::Json::Value AddJson(const std::string json_key, ::Json::Value json_value);
  std::string GetExtDataString();
 private:
  ::Json::Value final_json_;
  std::string final_string_;
};

}  // namespace front_server
}  // namespace ks
