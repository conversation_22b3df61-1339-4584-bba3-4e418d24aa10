#include "teams/ad/front_server_universe/common/fill_ext_data.h"

#include "json/writer.h"

namespace ks {
namespace front_server {

::Json::Value FillApiExtData::AddJson(const std::string json_key, ::Json::Value json_value) {
  final_json_[json_key] = json_value;
  return final_json_;
}
std::string FillApiExtData::GetExtDataString() {
  ::Json::FastWriter writer;
  final_string_ = writer.write(final_json_);
  return final_string_;
}

}  // namespace front_server
}  // namespace ks
