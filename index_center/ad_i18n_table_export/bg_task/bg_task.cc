#include "teams/ad/index_center/ad_i18n_table_export/bg_task/bg_task.h"

#include <signal.h>
#include <unistd.h>

#include <fstream>
#include <string>

#include "base/common/sleep.h"
#include "base/time/time.h"
#include "base/time/timestamp.h"
#include "glog/logging.h"
#include "perfutil/perfutil.h"
#include "teams/ad/ad_base/src/common/scoped_guard.h"
#include "teams/ad/ad_i18n/engine_base/ad_counter/ad_post_data_loader.h"

constexpr int32_t kWaitSleepInterval = 100;

namespace ks {
namespace ad {

void LoadAdPostData(ks::ad_base::ScopeGuard& sg) {  // NOLINT
  LOG(INFO) << "Start ad post data loader start.";
  auto start_ts = base::GetTimestamp();

  ad_i18n::engine_base::ad_counter::AdPostDataLoader::GetInstance()->Start();

  sg += [&] {
    while (!ad_i18n::engine_base::ad_counter::AdPostDataLoader::GetInstance()->IsReady()) {
      base::SleepForMilliseconds(kWaitSleepInterval);
      LOG_EVERY_N(INFO, 100) << "AdPostData is not ready...";
    }

    auto ad_post_data_latency = base::GetTimestamp() - start_ts;

    LOG(INFO) << "Finish load ad_post_data. latency: " << ad_post_data_latency;
  };
}

bool StartBgTask() {
  LOG(INFO) << "Start bg task.";
  auto start_ts = base::GetTimestamp();

  ks::ad_base::ScopeGuard sg;

  // 加载后验数据
  LoadAdPostData(sg);
  sg.Refresh();

  auto bg_task_latency = (base::GetTimestamp() - start_ts);
  LOG(INFO) << "Finish bg task. latency " << bg_task_latency;

  return true;
}

void StopBgTask() {
  LOG(INFO) << "Stop bg task";

  // 停止后验数据更新
  ad_i18n::engine_base::ad_counter::AdPostDataLoader::GetInstance()->Stop();
}

}  // namespace ad
}  // namespace ks
