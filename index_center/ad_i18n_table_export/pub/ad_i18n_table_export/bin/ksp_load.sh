#!/bin/sh

cd `dirname $0` || exit
absolute_path=`readlink -f .`
service_dir=`dirname $absolute_path`
service_name=`basename $service_dir`

trap 'kill -TERM $PID' QUIT INT TERM KILL

# 摘除zookeeper节点
python $absolute_path/zk_op/ZKHandler.py $service_dir $service_name pre &
PID=$!
wait $PID
if [ $? != 0 ]
then 
    echo "摘除zookeeper节点失败"
    exit 1;
fi

bash $absolute_path/load.sh $1 &
PID=$!
wait $PID
if [ $? != 0 ]
then 
    echo "服务重启失败"
    exit 2;
fi

if [ "$1" != "stop" ]
then
    # 检查节点是否已添加
    python $absolute_path/zk_op/ZKHandler.py $service_dir $service_name post &
    PID=$!
    wait $PID
    if [ $? != 0 ]
    then 
        echo "zookeeper节点挂载失败"
        exit 3;
    fi
fi
