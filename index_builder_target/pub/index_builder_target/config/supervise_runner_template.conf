[program:{program}]
command={directory}/bin/run
directory={directory}
numprocs=1
umask=022
priority=999
autostart=true
autorestart=unexpected
startsecs=10
startretries=3
exitcodes=0
stopsignal=TERM
stopwaitsecs=100
user=web_server
environment=USER="web_server"
stopasgroup=true
killasgroup=true
redirect_stderr=false
stdout_logfile={directory}/log/stdout
stdout_logfile_maxbytes=100MB
stdout_logfile_backups=3
stdout_capture_maxbytes=10MB
stdout_events_enabled=false
stderr_logfile={directory}/log/stderr
stderr_logfile_maxbytes=100MB
stderr_logfile_backups=3
stderr_capture_maxbytes=10MB
stderr_events_enabled=false
