syntax = "proto3";

package ks.index_builder_target;
import "teams/ad/ad_proto/kuaishou/ad/ad_inc.proto";
import "teams/ad/index_builder/multiform_filter/multiform_kconf.proto";
import "teams/ad/index_builder/admit_filters/admit_filter.proto";
import "teams/ad/index_adapter/utils/kconf/kconf.proto";

message BuilderTargetConfig {
  string src = 1;
  string dst = 2;
  repeated string table_names = 3;                         // 支持导出的表
  ks.index_builder.AdmitFilterEnum.Type filter_param = 4;  // 业务过滤规则
  string pb_wb_list_key = 5;
  bool enable_dump_on_kfs = 6;
  repeated string p2p_dirs = 7;  // p2p推送目录
  string hot_data_path = 8;
  ks.index_adapter.ShardParam.Param shard_param = 9;
  repeated int32 shard_no_vec = 10;
  string kafka2hive_key = 11;  // kafka2hive config key
  bool force_clean_default_value = 12;
  bool force_clean_recursive = 13;
}
