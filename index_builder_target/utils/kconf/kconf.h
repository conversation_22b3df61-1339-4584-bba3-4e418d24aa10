#pragma once

#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/index_builder_target/utils/kconf/kconf.pb.h"

namespace ks {
namespace index_builder_target {
using namespace ks::ad_base::kconf; //NOLINT
class Kconf {
 public:
  DEFINE_PROTOBUF_NODE_KCONF(BuilderTargetConfig, ad.index_builder,
                             builderTargetConfig);
};
}  // namespace index_builder_target
}  // namespace ks
