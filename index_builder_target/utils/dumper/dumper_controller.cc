#include "teams/ad/index_builder_target/utils/dumper/dumper_controller.h"

#include <algorithm>
#include <set>
#include <map>
#include "absl/strings/substitute.h"
#include "absl/time/clock.h"
#include "base/common/logging.h"
#include "base/common/stl_logging.h"
#include "base/encoding/base64.h"
#include "base/file/file_util.h"
#include "base/thread/thread_pool.h"
#include "ks/serving_util/dynamic_config.h"
#include "perfutil/perfutil.h"
#include "pub/src/base/time/timestamp.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/index_adapter/utils/consumer/message_consumer.h"
#include "teams/ad/index_adapter/utils/kconf/kconf.h"
#include "teams/ad/index_adapter/utils/stream_map/stream_map_maker.h"
#include "teams/ad/index_builder/utils/table_config.h"
#include "teams/ad/index_adapter/utils/perf_manager/perf_manager.h"
#include "teams/ad/index_builder/utils/kafka2hive/kafka2hive_manager.h"
#include "teams/ad/index_builder/utils/cache_loader/rtb_white_list_no_smr.h"

using ks::index_adapter::PerfManager;
using ks::infra::PerfUtil;
using ks::index_builder::TableConfigManager;

namespace ks {
namespace index_builder_target {

DataDumper::DataDumper() {}

bool DataDumper::Start() {
  int64_t start_ts = base::GetTimestamp();
  LOG(INFO) << "DataDumper start...";
  bool once_start = running_.exchange(true);
  if (once_start) {
    LOG_ASSERT(false) << "DataDumper start twice please check!!!";
  }
  auto* rtb_white_list = ks::index_builder::RtbWhiteList::GetInstance();
  rtb_white_list->Start();
  while (!rtb_white_list->IsReady()) {
    LOG(INFO) << "rtb_white_list not ready sleep for 1s";
    sleep(1);
  }
  LOG(INFO) << "rtb_white_list ready!";


  ks::index_builder::DumpConfig config;
  ks::index_builder::TableConfigManager::GetInstance()->Init(config);
  PerfManager::GetInstance()->Start();
  config_ = Kconf::builderTargetConfig()->data();
  ks::index_builder::Kafka2HiveManager::GetInstance()->Init();
  auto kafka2hive_key = config_.kafka2hive_key();
  if (!kafka2hive_key.empty()) {
    ks::index_builder::Kafka2HiveManager::GetInstance()->Init(kafka2hive_key);
  }
  if (!config_.hot_data_path().empty()) {
    ks::index_adapter::HotDataCenter::GetInstance()->Init(
        config_.hot_data_path());
    ks::index_adapter::HotDataCenter::GetInstance()->Start();
  }
  dump_thread_ = std::thread([this]() { DumpThread(); });
  if (!config_.enable_dump_on_kfs()) {
    mkdir("../ad_index", 0777);
  }
  return true;
}

void DataDumper::DumpThread() {
  LOG(INFO) << "DumpThread start...";
  while (running_) {
    LOG(INFO) << "Try to dump data!";
    thread::ThreadPool dump_pool{2};
    if (config_.shard_no_vec().empty()) {
      LOG(INFO) << "Try to dump normal data";
      dump_pool.AddTask(::NewCallback(this, &DataDumper::BuilderRun));
    } else {
      LOG(INFO) << "Try to dump shard data";
      dump_pool.AddTask(::NewCallback(this, &DataDumper::BuildersRun));
    }
    dump_pool.JoinAll();
    std::this_thread::sleep_for(std::chrono::seconds{60});
  }
  LOG(INFO) << "DumpThread end...";
}

void DataDumper::BuilderRun() {
  std::shared_ptr<BuilderTarget> builder =
      std::make_shared<BuilderTarget>(config_, -1);
  ks::index_builder::InstanceStreamMap map;
  std::string version;
  std::vector<std::shared_ptr<BuilderTarget>> v_b{builder};
  if (builder->CheckStatus(&version, &map)) {
    thread::ThreadPool dump_pool(200);
    LOG(INFO) << "Start dump " << version;
    for (auto item : map) {
      for (auto stream_ptr : item.second) {
        LOG(INFO) << stream_ptr->DebugInfo();
        if (v_b.back()->IsTableMatch(item.first)) {
          dump_pool.AddTask(::NewCallback(this, &DataDumper::BuildImpl,
                                          stream_ptr, v_b));
        }
      }
    }
    dump_pool.JoinAll();
    builder->Finish();
    LOG(INFO) << "End Dump" << version;
  }
}

void DataDumper::BuildersRun() {
  std::vector<std::shared_ptr<BuilderTarget>> builders;
  std::map<std::string, std::vector<std::shared_ptr<BuilderTarget>>> builder_versions;
  std::map<std::string, ks::index_builder::InstanceStreamMap> builder_streams;
  for (auto seq : config_.shard_no_vec()) {
    builders.push_back(std::make_shared<BuilderTarget>(config_, seq));
    auto builder = builders.back();
    ks::index_builder::InstanceStreamMap map;
    std::string version;
    if (builder->CheckStatus(&version, &map)) {
      builder_versions[version].push_back(builder);
      builder_streams[version] = map;
    }
  }
  for (auto version_pro : builder_streams) {
    auto stream_map = version_pro.second;
    auto v_b = builder_versions[version_pro.first];
    LOG(INFO) << "Start dump " << version_pro.first << "with " << v_b.size();
    if (v_b.empty()) {
      LOG(FATAL) << "Matched builder not found" << version_pro.first;
    }
    thread::ThreadPool dump_pool(200);
    for (auto item : stream_map) {
      for (auto stream_ptr : item.second) {
        LOG(INFO) << stream_ptr->DebugInfo();
        if (v_b.back()->IsTableMatch(item.first)) {
          dump_pool.AddTask(::NewCallback(this, &DataDumper::BuildImpl,
                                          stream_ptr, v_b));
        }
      }
    }
    dump_pool.JoinAll();
    for (auto builder : v_b) {
      builder->Finish();
    }
    LOG(INFO) << "End dump " << version_pro.first;
  }
}

void DataDumper::BuildImpl(ks::index_builder::InstanceStreamPtr stream_ptr,
                           std::vector<std::shared_ptr<BuilderTarget>> builders) {
  while (stream_ptr->Valid()) {
    const auto* ad_inst = stream_ptr->NextAdInstance();
    auto ad_tmp = *ad_inst;
    for (auto builder : builders) {
      builder->Consume(&ad_tmp);
    }
  }
  for (auto builder : builders) {
    builder->TableFinish(stream_ptr->GetTableName());
  }
}

void DataDumper::Stop() {
  running_.store(false);
  dump_thread_.join();
  PerfManager::GetInstance()->Stop();
}

}  // namespace index_builder_target
}  // namespace ks
