#pragma once

#include <third_party/abseil/absl/time/time.h>
#include <atomic>
#include <memory>
#include <string>
#include <thread>
#include <utility>
#include <vector>
#include "absl/container/flat_hash_map.h"
#include "absl/container/flat_hash_set.h"
#include "absl/strings/str_cat.h"
#include "falcon/counter.h"
#include "perfutil/perfutil.h"
#include "teams/ad/index_builder_target/utils/builder/builder.h"
#include "teams/ad/index_adapter/utils/kconf/kconf.h"

namespace ks {
namespace index_builder_target {

class DataDumper {
 public:
  DataDumper();
  bool Start();
  void Stop();

 private:
  void DumpThread();
  void BuilderRun();
  void BuildersRun();
  void BuildImpl(ks::index_builder::InstanceStreamPtr stream_ptr,
                 std::vector<std::shared_ptr<BuilderTarget>> builders);
  std::atomic<bool> running_{false};
  std::thread dump_thread_;
  absl::Time t{absl::FromTimeT(0)};
  BuilderTargetConfig config_;
};
}  // namespace index_builder_target
}  // namespace ks
