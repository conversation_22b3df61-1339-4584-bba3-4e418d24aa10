#pragma once

#include <algorithm>
#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <vector>
#include "base/file/file_path.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/index_manager.h"
#include "teams/ad/engine_base/utils/deploy_variable.h"
#include "teams/ad/index_adapter/strategy/strategy_common.h"
#include "teams/ad/index_adapter/utils/consumer/consumer_manager.h"
#include "teams/ad/index_adapter/utils/stream_map/stream_map_maker.h"
#include "teams/ad/index_builder_target/utils/kconf/kconf.h"

namespace ks {
namespace index_builder_target {

/*
  每个 builder 负责一个分片的制作
*/

using ks::index_builder::InstanceStreamMap;
using ks::index_builder::InstanceStreamPtr;
using ks::engine_base::DeployVariable;

class BuilderTarget {
 public:
  explicit BuilderTarget(const BuilderTargetConfig& config, int64_t seq);
  ~BuilderTarget();

  bool CheckStatus(std::string* version,
                   ks::index_builder::InstanceStreamMap* map);
  std::string GetPrefix() {
    if (seq_ == -1) {
      return absl::Substitute("$0_$1", DeployVariable::GetKwsName(),
                              DeployVariable::GetStage());
    } else {
      return absl::Substitute("$0_$1_$2", DeployVariable::GetKwsName(),
                              DeployVariable::GetStage(), seq_);
    }
  }

  bool IsTableMatch(const std::string& table_name) {
    const auto& table_names = config_.table_names();
    if (table_names.size() != 0) {
      return std::find(table_names.cbegin(), table_names.cend(), table_name) !=
             table_names.cend();
    }
    return true;
  }
  void Finish();
  void Consume(kuaishou::ad::AdInstance* ad_inst);
  void TableFinish(const std::string& table_name);

 private:
  void initSrcPath();
  void initDstPath();
  void CleanDefaultValue(kuaishou::ad::AdInstance* ad_inst);
  void PBClear(google::protobuf::Message* msg);

 private:
  static inline base::FilePath kfs_root_{"/home/<USER>/kuaishou-worker/project/kfs-ad-index/"};
  bool kfs_ready_{false};

  std::string source_path_;
  std::string local_path_;
  std::string hdfs_path_;
  InstanceStreamMap stream_map_;
  BuilderTargetConfig config_;
  int64_t seq_{-1};

  kuaishou::ad::tables::DumpInfo dump_info_;
  std::shared_ptr<ks::index_adapter::FieldFilterStrategy> field_strategy_;
  std::shared_ptr<ks::index_adapter::StrategyBase> admit_strategy_;
  std::shared_ptr<ks::index_adapter::StrategyBase> shard_strategy_;
  std::shared_ptr<ks::index_adapter::ConsumerManagerBase> consumer_manager_;

  bool enable_dump_on_kfs_{false};
};

}  // namespace index_builder_target
}  // namespace ks
