#include "teams/ad/index_builder_target/utils/builder/builder.h"

#include <limits>
#include <memory>
#include <string>
#include <utility>

#include "absl/strings/match.h"
#include "absl/strings/numbers.h"
#include "absl/time/time.h"
#include "base/file/file_path.h"
#include "base/file/file_util.h"
#include "kenv/kenv.h"
#include "perfutil/perfutil.h"
#include "teams/ad/engine_base/utils/deploy_variable.h"
#include "teams/ad/index_adapter/utils/consumer/consumer_manager.h"
#include "teams/ad/index_adapter/utils/hdfs_uploader.h"
#include "teams/ad/index_adapter/utils/perf_manager/perf_manager.h"
#include "teams/ad/index_builder/utils/kafka2hive/kafka2hive_manager.h"
#include "teams/ad/index_builder_target/utils/kconf/kconf.h"

namespace ks {
namespace index_builder_target {

using ks::index_adapter::StreamMapBase;
using ks::index_adapter::StreamMapMakerFactory;
using base::file_util::FileEnumerator;
using ks::index_adapter::HdfsUploader;
using ks::index_adapter::PerfManager;
using ks::engine_base::DeployVariable;
using ks::index_adapter::ConsumerManager;
using ks::index_builder::Kafka2HiveManager;

BuilderTarget::BuilderTarget(const BuilderTargetConfig& config, int64_t seq)
    : config_(config), seq_(seq) {
  kfs_ready_ = base::file_util::DirectoryExists(kfs_root_);
  if (!kfs_ready_) {
    LOG(INFO) << "kfs root not exists, kfs is not ready, kfs=" << kfs_root_.ToString();
  }

  initSrcPath();
  initDstPath();
  LOG(INFO) << "src_path=" << source_path_;
  LOG(INFO) << "dst_path=" << hdfs_path_;

  enable_dump_on_kfs_ = config_.enable_dump_on_kfs() && kfs_ready_;
  if (enable_dump_on_kfs_) {
    mkdir(
        "/home/<USER>/kuaishou-worker/project/kfs-ad-index/"
        "ad-index-target-kfs",
        0777);
    local_path_ = base::FilePath(hdfs_path_).BaseName().value();
    local_path_ = base::FilePath(
                      "/home/<USER>/kuaishou-worker/project/kfs-ad-index/"
                      "ad-index-target-kfs")
                      .Append(local_path_)
                      .value();
    mkdir(local_path_.c_str(), 0777);
  } else {
    local_path_ =
        base::FilePath("../ad_index/").ToString();
    mkdir(local_path_.c_str(), 0777);
  }
  LOG(INFO) << "local_path=" << local_path_;
  ks::index_adapter::CleanLocalIndex(local_path_, 4);
  ks::index_adapter::ClearHistoryFile(hdfs_path_);
  field_strategy_ = std::make_unique<ks::index_adapter::FieldFilterStrategy>(
      "field_strategy", config_.pb_wb_list_key());
  admit_strategy_.reset(
      ks::index_adapter::StrategyFactory::GetInstance()->GetStrategy(
          config_.filter_param()));
  shard_strategy_.reset(
     ks::index_adapter::StrategyFactory::GetInstance()->GetStrategy(config_.shard_param()));
  if (shard_strategy_) {
    shard_strategy_->SetShardNo(seq_);
  }

  PerfManager::GetInstance()->RegisterIndexMonitorTask(GetPrefix(), hdfs_path_);
}

BuilderTarget::~BuilderTarget() {
  PerfManager::GetInstance()->UpdateBuildEndTime(GetPrefix());
}
bool BuilderTarget::CheckStatus(std::string* version,
                                ks::index_builder::InstanceStreamMap* map) {
  std::unique_ptr<StreamMapBase> stream_map_maker_ptr;
  std::string source_version, dest_version;
  if (source_path_.find("kfs-ad-index") != std::string::npos) {
    stream_map_maker_ptr.reset(
        StreamMapMakerFactory::GetInstance()->GetStreamMapTableLiteOnKfs());
    stream_map_maker_ptr->CheckLastetSuccessVersionOfAdInstanceOnKfs(
        source_path_, &source_version);
  } else {
    stream_map_maker_ptr.reset(
        StreamMapMakerFactory::GetInstance()->GetStreamMapTableLite());
    stream_map_maker_ptr->CheckLastetSuccessVersionOfAdInstance(
        source_path_, &source_version);
  }
  if (enable_dump_on_kfs_) {
    stream_map_maker_ptr.reset(
        StreamMapMakerFactory::GetInstance()->GetStreamMapTableLiteOnKfs());
    stream_map_maker_ptr->CheckLastetSuccessVersionOfAdInstanceOnKfs(
        local_path_, &dest_version);
  } else {
    stream_map_maker_ptr.reset(
        StreamMapMakerFactory::GetInstance()->GetStreamMapTableLite());
    stream_map_maker_ptr->CheckLastetSuccessVersionOfAdInstance(hdfs_path_,
                                                                &dest_version);
  }
  if (source_version.empty() || source_version <= dest_version) {
    LOG(INFO) << "source_version: " << source_version
              << " dest_version: " << dest_version << " not match! Skip";
    return false;
  }
  source_path_ =
      base::FilePath(source_path_).Append(source_version).ToString();
  hdfs_path_ =
      base::FilePath(hdfs_path_).Append(source_version).ToString();
  local_path_ = base::FilePath(local_path_).Append(source_version).ToString();
  stream_map_maker_ptr->InitAdInstanceMessageStreamMap(source_path_, &stream_map_);

  *version = source_version;
  *map = stream_map_;
  // prepare local dir
  if (base::file_util::PathExists(base::FilePath(local_path_))) {
    if (!base::file_util::Delete(base::FilePath(local_path_), true)) {
      LOG(FATAL) << "Delete(" << local_path_ << ") failed";
    }
  }
  if (hadoop::HDFSExists(hdfs_path_.c_str())) {
    if (hadoop::HDFSRmr(hdfs_path_.c_str())) {
      LOG(INFO) << "Delete Existed Dir " << hdfs_path_ << " Success";
    } else {
      LOG(FATAL) << "Delete Existed Dir " << hdfs_path_ << " Fail";
    }
  }
  mkdir(local_path_.c_str(), 0777);
  PerfManager::GetInstance()->UpdateBuildBeginTime(GetPrefix());
  if (!config_.hot_data_path().empty()) {
    std::string trick_version = source_version.append("00");
    ks::index_adapter::HotDataCenter::GetInstance()->CheckStatus(trick_version);
  }
  consumer_manager_ = std::make_shared<ks::index_adapter::ConsumerManager>();
  std::vector<std::string> tables;
  for (auto item : stream_map_) {
    LOG(INFO) << "table_name: " << item.first;
    auto last_stream_ptr = item.second.back();
    if (IsTableMatch(item.first)) {
      tables.push_back(item.first);
    }
  }
  // ks::index_adapter::AdapterConfig config;
  // (*config.mutable_p2p_dirs()) = config_.p2p_dirs();
  // (*config.mutable_hot_data_path()) = config_.hot_data_path();
  index_adapter::ExtraConsumerDataInfo extra_info;
  extra_info.p2p_dirs.insert(extra_info.p2p_dirs.end(),
                             config_.p2p_dirs().begin(),
                             config_.p2p_dirs().end());
  extra_info.hot_data_path = config_.hot_data_path();
  consumer_manager_->Init(tables, local_path_, hdfs_path_, extra_info);
  for (auto item : stream_map_) {
    if (!IsTableMatch(item.first)) {
      continue;
    }
    for (auto& shard : item.second) {
      consumer_manager_->RegisterTable(item.first);
      LOG(INFO) << "RegisterTable table_name: " << item.first;
    }
  }
  return true;
}

void BuilderTarget::Finish() {
  consumer_manager_->Finish();

  ks::index_builder::InstanceStreamMap map;
  std::string version;
  if (CheckStatus(&version, &map)) {
    absl::Time t;
    if (absl::ParseTime("%Y-%m-%d_%H%M", version, absl::LocalTimeZone(), &t, nullptr)) {
      infra::PerfUtil::SetLogStash(absl::ToUnixSeconds(t), "ad.index_adapter", "version_end_dump",
                                   GetPrefix());
    }
  }
}

void BuilderTarget::TableFinish(const std::string& table_name) {
  consumer_manager_->UnRegisterTable(table_name);
}
void BuilderTarget::CleanDefaultValue(kuaishou::ad::AdInstance* ad_inst) {
  if (!config_.force_clean_default_value() || !ad_inst) {
    return;
  }
  auto* extens = index_adapter::GetExtensionField(ad_inst);
  PBClear(extens);
}
void BuilderTarget::PBClear(google::protobuf::Message* msg) {
  auto* ref = msg->GetReflection();
  auto* desc = msg->GetDescriptor();
  for (int i = 0; i < desc->field_count(); ++i) {
    auto* field = desc->field(i);
    if (!field->is_optional()) {
      continue;
    }
    // 有的话递归清理
    if (field->cpp_type() == google::protobuf::FieldDescriptor::CppType::CPPTYPE_MESSAGE) {
      if (config_.force_clean_recursive() && ref->HasField(*msg, field)) {
        auto* sub_msg = ref->MutableMessage(msg, field);
        PBClear(sub_msg);
        continue;
      } else {
        continue;
      }
    }
    if (field->cpp_type() == google::protobuf::FieldDescriptor::CppType::CPPTYPE_STRING &&
        ref->GetString(*msg, field).empty()) {
      ref->ClearField(msg, field);
      continue;
    }
    if (field->cpp_type() == google::protobuf::FieldDescriptor::CppType::CPPTYPE_INT32 &&
        ref->GetInt32(*msg, field) == 0) {
      ref->ClearField(msg, field);
      continue;
    }
    if (field->cpp_type() == google::protobuf::FieldDescriptor::CppType::CPPTYPE_INT64 &&
        ref->GetInt64(*msg, field) == 0) {
      ref->ClearField(msg, field);
      continue;
    }
    if (field->cpp_type() == google::protobuf::FieldDescriptor::CppType::CPPTYPE_UINT64 &&
        ref->GetUInt64(*msg, field) == 0) {
      ref->ClearField(msg, field);
      continue;
    }
    if (field->cpp_type() == google::protobuf::FieldDescriptor::CppType::CPPTYPE_UINT32 &&
        ref->GetUInt32(*msg, field) == 0) {
      ref->ClearField(msg, field);
      continue;
    }
    if (field->cpp_type() == google::protobuf::FieldDescriptor::CppType::CPPTYPE_BOOL &&
        ref->GetBool(*msg, field) == false) {
      ref->ClearField(msg, field);
      continue;
    }
    if (field->cpp_type() == google::protobuf::FieldDescriptor::CppType::CPPTYPE_DOUBLE &&
        ref->GetDouble(*msg, field) == 0) {
      ref->ClearField(msg, field);
      continue;
    }
    if (field->cpp_type() == google::protobuf::FieldDescriptor::CppType::CPPTYPE_FLOAT &&
        ref->GetFloat(*msg, field) == 0) {
      ref->ClearField(msg, field);
      continue;
    }
    if (field->cpp_type() == google::protobuf::FieldDescriptor::CppType::CPPTYPE_ENUM &&
        ref->GetEnumValue(*msg, field) == 0) {
      ref->ClearField(msg, field);
      continue;
    }
  }
  return;
}

void BuilderTarget::Consume(kuaishou::ad::AdInstance* ad_inst) {
  if (!admit_strategy_->Process(ad_inst)) {
    Kafka2HiveManager::GetInstance()->KafkaFilterReason(
        ad_inst, ks::ad::index_message_proxy::AD_FILTER);
    LOG_EVERY_N(INFO, 100000) << admit_strategy_->Name();
    return;
  }
  // 过滤全店虚拟创意
  if (ks::index_builder::IsStoreWideVirtualCreative(ad_inst)) {
    return;
  }
  if (shard_strategy_ && !shard_strategy_->Process(ad_inst)) {
    LOG_EVERY_N(INFO, 100000) << shard_strategy_->Name();
    return;
  }
  if (field_strategy_) {
    LOG_EVERY_N(INFO, 100000) << field_strategy_->Name();
    field_strategy_->Process(ad_inst);
  }
  LOG_EVERY_N(INFO, 100000) << ad_inst->ShortDebugString();
  CleanDefaultValue(ad_inst);
  consumer_manager_->Consume(*ad_inst);
}

void BuilderTarget::initSrcPath() {
  const auto& az = ks::infra::KEnv::GetKWSInfo()->GetAZ();
  source_path_ = config_.src();
  if (absl::StartsWith(source_path_, "/home/<USER>") && !kfs_ready_) {
    auto real_source_path = base::FilePath("/home/<USER>/index_adapter/");
    source_path_ = real_source_path.Append(base::FilePath(source_path_).BaseName()).value();
    LOG(INFO) << "kfs not usable, use hdfs_src as source, hdfs_src=" << source_path_;
  }
  if (!source_path_.empty() && source_path_.back() == '/') {
    source_path_.substr(0, source_path_.size() - 1);
  }
  if (!absl::StartsWith(source_path_, "/home/<USER>") && az == "YZ") {
    source_path_ += "_YZ";
  }
  // YZ kfs 集群
  if (absl::StartsWith(source_path_, "/home/<USER>") && az == "YZ" && kfs_ready_) {
    source_path_ += "_YZ";
  }
}

void BuilderTarget::initDstPath() {
  const auto& az = ks::infra::KEnv::GetKWSInfo()->GetAZ();
  hdfs_path_ = config_.dst();
  if (!hdfs_path_.empty() && hdfs_path_.back() == '/') {
    hdfs_path_.substr(0, hdfs_path_.size() - 1);
  }
  if (seq_ != -1) {
    hdfs_path_ += absl::Substitute("_shard$0", seq_);
  }
  if (az == "YZ") {
    hdfs_path_ += "_YZ";
  }
}

}  // namespace index_builder_target
}  // namespace ks
