#pragma once

#include <atomic>
#include <string>
#include <utility>
#include <memory>
#include <optional>
#include "glog/logging.h"
#include "folly/concurrency/ConcurrentHashMap.h"
#include "teams/ad/grid/cache/cache_instance.h"
#include "teams/ad/grid/config/config.h"
#include "teams/ad/grid/proto/config.pb.h"

namespace ks {
namespace grid {
template <typename CacheRow>
class CacheList {
 public:
  static CacheList& Instance() {
    static CacheList s_cache_list_;
    auto initialize = [&] () {
      auto scan = [&] () {
        pthread_setname_np(pthread_self(), "CacheListScanner");
        while (s_cache_list_.running_scanner_) {
          auto& caches = s_cache_list_.caches_;
          for (auto iter = caches.begin(); iter != caches.end(); ++iter) {
            auto& pr = *iter;
            LOG_EVERY_N(INFO, 1000) << "CacheListScanner: scan("
                                    << pr.first
                                    << "), instance("
                                    << reinterpret_cast<int*>(pr.second.get())
                                    << ")";

            if (!pr.second) { continue; }

            bool need_init = pr.second->NeedInit();
            bool exceed_max_idle = pr.second->ExceedMaxIdle();

            LOG_EVERY_N(INFO, 1000) << "CacheListScanner: scan("
                                    << pr.first
                                    << "), instance("
                                    << reinterpret_cast<int*>(pr.second.get())
                                    << "), need_init="
                                    << need_init
                                    << ", exceed_max_idle="
                                    << exceed_max_idle;
            if (pr.second->data_ &&
                pr.second->data_->capacity()) {
              infra::PerfUtil::IntervalLogStash(pr.second->data_->size() * 10000 /
                                                pr.second->data_->capacity(),
                                                "ad.grid",
                                                "cache.load_factor",
                                                pr.second->TableName(),
                                                pr.second->signature_md5_);
              infra::PerfUtil::IntervalLogStash(pr.second->data_->capacity(),
                                                "ad.grid",
                                                "cache.capacity",
                                                pr.second->TableName(),
                                                pr.second->signature_md5_);
              infra::PerfUtil::IntervalLogStash(pr.second->data_->size(),
                                                "ad.grid",
                                                "cache.size",
                                                pr.second->TableName(),
                                                pr.second->signature_md5_);
            }
            infra::PerfUtil::IntervalLogStash((!!need_init),
                                                "ad.grid",
                                                "cache.uninitialized",
                                                pr.second->TableName(),
                                                pr.second->signature_md5_);
            infra::PerfUtil::IntervalLogStash((!!exceed_max_idle),
                                                "ad.grid",
                                                "cache.exceed_max_idle",
                                                pr.second->TableName(),
                                                pr.second->signature_md5_);
            if (need_init) { continue; }
            if (exceed_max_idle) { pr.second->Clear(); }

            proto::CacheOptions cache_option;
            if (GridKconf::GridCacheConfigs()->data().HasTableCache(pr.second->TableName())) {
              cache_option = GridKconf::GridCacheConfigs()->data().GetCacheOptions(pr.second->TableName());
            } else {
              cache_option = GridKconf::GridCacheConfigs()->data().GetCacheOptions("default");
            }
            pr.second->UpdateCacheOptions(std::move(cache_option));
            auto& cur_cache_options = pr.second->GetCacheOptions();

            infra::PerfUtil::GaugeLogStash(cur_cache_options.ttl(),
                                                "ad.grid",
                                                "cache.ttl",
                                                pr.second->TableName(),
                                                pr.second->signature_md5_);
            infra::PerfUtil::GaugeLogStash(cur_cache_options.max_idle(),
                                                "ad.grid",
                                                "cache.max_idle",
                                                pr.second->TableName(),
                                                pr.second->signature_md5_);
            infra::PerfUtil::GaugeLogStash(cur_cache_options.enable_cache_null(),
                                                "ad.grid",
                                                "cache.enable_cache_null",
                                                pr.second->TableName(),
                                                pr.second->signature_md5_);
            infra::PerfUtil::GaugeLogStash(cur_cache_options.init_cache_size(),
                                                "ad.grid",
                                                "cache.init_cache_size",
                                                pr.second->TableName(),
                                                pr.second->signature_md5_);
          }
          std::this_thread::sleep_for(std::chrono::seconds(10));
        }
        LOG(INFO) << "Scanner Exit";
      };
      s_cache_list_.running_scanner_ = true;

      std::thread tmp(scan);
      s_cache_list_.scanner_ = std::move(tmp);
      s_cache_list_.scanner_.detach();
    };
    std::call_once(s_cache_list_.init_flag_, initialize);
    return s_cache_list_;
  }

  std::optional<CacheInstance<CacheRow>*> Cache(const std::string& table_name,
                                      const std::string& signature_md5) {
    std::string key = CacheList::GenCacheKey(table_name, signature_md5);
    proto::CacheOptions cache_option;
    if (GridKconf::GridCacheConfigs()->data().HasTableCache(table_name)) {
      cache_option = GridKconf::GridCacheConfigs()->data().GetCacheOptions(table_name);
    } else {
      cache_option = GridKconf::GridCacheConfigs()->data().GetCacheOptions("default");
    }
    if (!cache_option.use()) {
      return std::nullopt;
    }

    auto iter = caches_.try_emplace(
      key,
      std::make_unique<CacheInstance<CacheRow>>(
          table_name, signature_md5, cache_option));
    if (iter.first != caches_.end() && iter.first->second) {
      return iter.first->second.get();
    }
    return std::nullopt;
  }

  static std::string GenCacheKey(const std::string& table_name,
                                 const std::string& signature_md5) {
    static const std::string s_combiner = "-";
    std::string key;
    key.reserve(table_name.size() + signature_md5.size() + s_combiner.size());
    key.append(table_name).append(s_combiner).append(signature_md5);
    return key;
  }

 private:
  CacheList() = default;
  ~CacheList() {
    running_scanner_.store(false);
  }

  template <typename T>
  using NamedHashMap =  folly::ConcurrentHashMap<std::string, T>;

  using NamedCacheMap = NamedHashMap<std::unique_ptr<CacheInstance<CacheRow>>>;

  NamedCacheMap caches_;
  std::atomic<bool> running_scanner_{false};
  std::thread scanner_;
  std::once_flag init_flag_;

  DISALLOW_COPY_AND_ASSIGN(CacheList);
};

}  // namespace grid
}  // namespace ks
