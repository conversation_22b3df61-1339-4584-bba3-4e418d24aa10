#pragma once

#include <atomic>
#include <string>
#include <memory>
#include <vector>
#include "perfutil/perfutil.h"
#include "ks/base/container/common.h"
#include "teams/ad/grid/config/config.h"
#include "teams/ad/grid/cache/time_limit_cache.h"
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/proto/config.pb.h"
#include "teams/ad/grid/proto/grid_generated.h"

namespace ks {
namespace grid {
struct ColumnDesc {
  std::string col_name;
  ad_index_meta::proto::ColumnType type;
};

template <typename TRowSP>
class CacheInstance {
 public:
  using TKey = uint64_t;
  using ConcurrentHash = TimeLimitCache<TRowSP>;

  CacheInstance(const std::string& table_name, const std::string& signatrue_md5,
                const proto::CacheOptions& options)
    : table_name_(table_name), signature_md5_(signatrue_md5), cache_option_(options) {
    last_visit_time_.store(base::GetTimestamp());
  }

  void UpdateCacheOptions(proto::CacheOptions&& options) {
    cache_option_ = options;
  }

  proto::CacheOptions& GetCacheOptions() {
    return cache_option_;
  }

  // 原初始化接口，使用 pb 协议，待迁移
  void Init(const std::vector<std::string>& select_fields,
            const std::vector<::ks::grid::proto::Column>& raw_columns) {
    auto impl = [&] () {
      columns_ = select_fields;
      raw_columns_ = raw_columns;
      data_.reset(new ConcurrentHash(cache_option_.init_cache_size()));
      LOG(INFO) << "reset successfully, container addr="
        << reinterpret_cast<int*>(&data_)
        << ", max_size=" << cache_option_.init_cache_size()
        << ", column size=" << columns_.size();
      return static_cast<size_t>(true);
    };
    std::call_once(init_flag_, impl);
  }

  // 新初始化接口，使用新协议
  void Init(const flatbuffers::Vector<flatbuffers::Offset<flat::Column>> *columns) {
    if (KS_UNLIKELY(columns == 0)) {
      LOG(ERROR) << "input error, columns is nullptr";
      return;
    }
    auto impl = [&] () {
      column_desc_ = std::make_shared<std::vector<ColumnDesc>>(columns->size());
      for (size_t i = 0; i < columns->size(); ++i) {
        const auto& column = columns->Get(i);
        (*column_desc_)[i].col_name = column->name()->str();
        (*column_desc_)[i].type = ad_index_meta::proto::ColumnType(static_cast<int>(column->type()));
      }
      data_.reset(new ConcurrentHash(cache_option_.init_cache_size()));
      LOG(INFO) << "reset successfully, container addr="
                  << reinterpret_cast<int*>(&data_)
                  << ", max_size=" << cache_option_.init_cache_size()
                  << ", column size=" << column_desc_->size()
                  << ", table_name="  << table_name_
                  << ": md5=" << signature_md5_;
      return static_cast<size_t>(true);
    };
    std::call_once(init_flag_, impl);
  }

  bool IsValidSchema(const std::vector<std::string>& select_fields) {
    return  columns_.empty() || columns_ == select_fields;
  }


  size_t BatchGet(const std::vector<TKey>& keys,
                               std::vector<TRowSP>* rows) {
    if (!rows) {
      return 0;
    }

    if (KS_UNLIKELY(keys.empty())) { return 0; }

    if (KS_UNLIKELY(!data_)) {
      LOG_EVERY_N(INFO, 10000) << "inner error, `container.data_' is null, maybe not init";
      return 0;
    }

    rows->resize(keys.size());
    size_t n_hit = 0;
    last_visit_time_.store(
      static_cast<uint64_t>(base::GetTimestamp()),
      std::memory_order::memory_order_relaxed);

    auto scoped_guard = folly::makeGuard(
    [&] () {
      double hit_rate = 0.0;
      if (KS_LIKELY(keys.size() > 0)) {
        hit_rate = static_cast<double>(n_hit)/keys.size();
      }
      infra::PerfUtil::IntervalLogStash((hit_rate*10000),
                                              "ad.grid",
                                              "cache.rate.hit",
                                              TableName(),
                                              SignatureMd5());
    });

    for (size_t i = 0; i < keys.size(); ++i) {
      // rows->emplace_back(nullptr);
      auto& current_row = (*rows)[i];
      const TKey& k = keys[i];
      auto ret = data_->find(k, &current_row);
      if (ret) {
        ++n_hit;
      }
    }
    return n_hit;
  }

  size_t BatchSet(const std::vector<TKey>& keys, const std::vector<TRowSP>& rows) {
    if (KS_UNLIKELY(keys.size() != rows.size())) {
      LOG(ERROR) << "[BatchSet] input error  keys.size() != rows.size()";
      return 0;
    }
    last_visit_time_.store(
      static_cast<uint64_t>(base::GetTimestamp()),
      std::memory_order::memory_order_relaxed);

    if (keys.empty()) {
      return 0;
    }

    if (!data_) {
      LOG_EVERY_N(INFO, 10000) << "inner error, `container.data_' is null, not init";
      return 0;
    }

    uint64_t current_ts = static_cast<uint64_t>(base::GetTimestamp());
    int32_t evict_count = 0;
    if (!cache_option_.disable_evict()) {
      uint64_t evict_time = current_ts - cache_option_.ttl();
      evict_count = data_->evict(evict_time);
    }

    auto scoped_guard = folly::makeGuard(
    [&] () {
      if (evict_count > 0) {
        infra::PerfUtil::IntervalLogStash(evict_count,
                                              "ad.grid",
                                              "cache.evict.count",
                                              TableName(),
                                              SignatureMd5());
      }
    });

    for (size_t i = 0; i < keys.size(); ++i) {
      auto& k = keys[i];
      if (!cache_option_.enable_cache_null() && !(rows[i])) {
        LOG_EVERY_N(INFO, 10000) << "row is Empty, StoreKey " << k
                                  << " table_name = " << table_name_;
        continue;
      }

      bool ok = data_->insert_or_assign(k, rows[i], current_ts);
      if (!ok) {
        LOG_EVERY_N(INFO, 10000) << "cache row insert: error-"
                                    << ", i-"
                                    << i
                                    << ", id-"
                                    << k
                                    << ", timestamp-"
                                    << current_ts;
      }
    }
    return keys.size();
  }

  bool NeedInit() const {
    return data_ == nullptr;
  }

  bool ExceedMaxIdle() const {
    uint64_t last = last_visit_time_.load(std::memory_order::memory_order_relaxed);
    uint64_t diff = static_cast<uint64_t>(base::GetTimestamp()) - last;
    return diff > cache_option_.max_idle();
  }

  const std::string& TableName() const { return table_name_; }
  const std::vector<::ks::grid::proto::Column>& TableSchema() const { return raw_columns_; }
  const std::shared_ptr<std::vector<ColumnDesc>>& GetTableSchema() const { return column_desc_; }
  const std::string& SignatureMd5() const { return signature_md5_; }

 private:
  void Clear() {
    if (data_) {
      data_->clear();
    }
  }

  std::atomic<uint64_t> last_visit_time_{0};

  std::unique_ptr<ConcurrentHash> data_;
  proto::CacheOptions cache_option_;

  std::once_flag init_flag_;

  std::string table_name_;
  std::string signature_md5_;

  std::vector<std::string> columns_;
  std::vector<::ks::grid::proto::Column> raw_columns_;
  std::shared_ptr<std::vector<ColumnDesc>> column_desc_;

  template<typename T>
  friend class CacheList;

  DISALLOW_COPY_AND_ASSIGN(CacheInstance);
};

}  // namespace grid
}  // namespace ks
