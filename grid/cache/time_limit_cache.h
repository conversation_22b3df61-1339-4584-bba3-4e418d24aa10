#pragma once

#include <atomic>
#include <utility>
#include <memory>
#include <string>
#include "base/time/timestamp.h"
#include "teams/ad/grid/config/config.h"
#include "folly/concurrency/ConcurrentHashMap.h"
#include "folly/MPMCQueue.h"

namespace ks {
namespace grid {
// 定义 ReclaimNode 结构体
template <typename CacheRow>
struct ReclaimNode {
  int64_t key = 0;
  int64_t timestamp = 0;
  std::weak_ptr<folly::ConcurrentHashMap<int64_t, CacheRow>> cached_rows;

  ReclaimNode() = default;
  ReclaimNode(int64_t key, int64_t timestamp,
    std::shared_ptr<folly::ConcurrentHashMap<int64_t, CacheRow>> cache_map)
    : key(key), timestamp(timestamp), cached_rows(cache_map) {}

  // 析构函数，在销毁时删除 cached_rows_ 中的键
  void Erase() {
    if (auto cache_map = cached_rows.lock()) {
      cache_map->erase(key);
    }
  }
};

template <typename CacheRow>
class TimeLimitCache {
 public:
  explicit TimeLimitCache(int64_t capacity) : capacity_(capacity),
      last_evict_time_(base::GetTimestamp()),
      reclaim_queue_(capacity) {
    cached_rows_ = std::make_shared<folly::ConcurrentHashMap<int64_t, CacheRow>>(capacity);
  }

  bool insert_or_assign(int64_t key, const CacheRow& row, int64_t timestamp) {
    ReclaimNode<CacheRow> node{key, timestamp, cached_rows_};
    if (!reclaim_queue_.writeIfNotFull(std::move(node))) {
      return false;
    }
    return cached_rows_->insert_or_assign(key, row).second;
  }

  bool find(int64_t key, CacheRow* row) {
    auto ret = cached_rows_->find(key);
    if (ret == cached_rows_->end()) {
      return 0;
    }
    *row = ret->second;
    return 1;
  }


  int32_t evict(int64_t evict_time) {
    thread_local std::shared_ptr<absl::flat_hash_map<void*, ReclaimNode<CacheRow>>>
      front_node(new absl::flat_hash_map<void*, ReclaimNode<CacheRow>>(),
        [](absl::flat_hash_map<void*, ReclaimNode<CacheRow>>* ptr) {
            // 自定义删除器，遍历 map 并调用每个 ReclaimNode 的 Erase 函数
            for (auto& entry : *ptr) {
              entry.second.Erase();
            }
            delete ptr;
        });
    int32_t evict_count = 0;
    if (last_evict_time_.load(std::memory_order_relaxed) > evict_time) {
      return 0;
    }
    last_evict_time_.store(evict_time, std::memory_order_release);
    auto itr = front_node->find(this);
    if (KS_LIKELY(itr != front_node->end())) {
      if (itr->second.timestamp > evict_time) {
        return 0;
      } else {
        cached_rows_->erase(itr->second.key);
      }
    } else {
      auto ret = front_node->try_emplace(this, ReclaimNode<CacheRow>{0, 0, cached_rows_});
      if (KS_UNLIKELY(!ret.second)) {
        return 0;
      }
      itr = ret.first;
    }
    ReclaimNode<CacheRow>& node = itr->second;
    while (reclaim_queue_.readIfNotEmpty(node)) {
      if (node.timestamp > evict_time) {
        return evict_count;
      }
      ++evict_count;
      cached_rows_->erase(node.key);
    }
    return evict_count;
  }

  void clear() {
    cached_rows_->clear();
    ReclaimNode<CacheRow> node;
    while (reclaim_queue_.readIfNotEmpty(node)) {}
  }

  int64_t size() const {
    return cached_rows_->size();
  }
  int64_t capacity() const {
    return capacity_;
  }

 private:
  int64_t capacity_ = 0;
  std::atomic_int64_t last_evict_time_{0};
  std::shared_ptr<folly::ConcurrentHashMap<int64_t, CacheRow>> cached_rows_;
  folly::MPMCQueue<ReclaimNode<CacheRow>> reclaim_queue_;
};
}  // namespace grid
}  // namespace ks
