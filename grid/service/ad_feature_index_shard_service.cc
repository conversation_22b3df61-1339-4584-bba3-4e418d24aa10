#include "teams/ad/grid/service/ad_feature_index_shard_service.h"

#include <memory>
#include <string>
#include <vector>
#include <utility>

#include "absl/strings/str_cat.h"
#include "base/time/timestamp.h"
#include "glog/logging.h"
#include "kess/rpc/grpc/server_context_helper.h"
#include "perfutil/perfutil.h"
#include "teams/ad/grid/utils/perf.h"
#include "teams/ad/grid/utils/utils.h"
#include "teams/ad/grid/handler/feature_index_shard_handler.h"

namespace ks {
namespace grid {

AdFeatureIndexShardService::AdFeatureIndexShardService() {
  shard_id_ = GetShardId();
}

::grpc::Status AdFeatureIndexShardService::GetBatchSchemaFreeData(
    ::grpc::ServerContext* context,
    const ::ks::ad_feature_index::proto::GetBatchSchemaFreeDataReq* request,
    ::ks::ad_feature_index::proto::GetBatchSchemaFreeDataResp* response) {
  auto start_time = base::GetTimestamp();
  auto caller = ks::kess::rpc::grpc::ServerContextHelper::GetCallerService(context);
  if (request == nullptr || request->reqs_size() == 0) {
    ::ks::infra::PerfUtil::CountLogStash(
        1, "ad.grid", "shardv2.warning.invalid_reqest", caller);
    return grpc::Status::OK;
  }

  LOG_FIRST_N(INFO, 8) << "enter request";
  GetBatchSchemaFreeDataHandler(caller, request, response);

  GRID_RATIO_PERF_INTERVAL_LOG_STASH(base::GetTimestamp() - start_time,
                                     "shardv2.rpc.pv",
                                     caller,
                                     std::to_string(shard_id_));

  return grpc::Status::OK;
}

}  // namespace grid
}  // namespace ks
