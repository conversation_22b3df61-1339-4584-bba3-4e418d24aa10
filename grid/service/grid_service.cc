#include <fcntl.h>
#include <memory>
#include <string>
#include <vector>

#include "absl/strings/str_cat.h"
#include "base/time/timestamp.h"
#include "glog/logging.h"
#include "folly/ScopeGuard.h"
#include "kess/rpc/grpc/server_context_helper.h"
#include "teams/ad/grid/service/grid_service.h"
#include "teams/ad/grid/handler/grid_handler.h"
#include "teams/ad/grid/index/index_mannger.h"
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/utils/metric.h"
#include "teams/ad/grid/config/config.h"


namespace ks {
namespace grid {

#define GS_HANDLE_ERROR(RET)                                            \
  do {                                                                  \
    switch ((RET)) {                                                    \
      case 0: break;                                                    \
      case -1: return grpc::Status(grpc::StatusCode::NOT_FOUND,         \
                                   "table not found");                  \
      default: return grpc::Status(grpc::StatusCode::UNKNOWN, "error"); \
    }                                                                   \
  } while (0)

GridService::GridService() {}

::grpc::Status GridService::BatchGetTable(::grpc::ServerContext* context,
                                          const GridRequest* request,
                                          GridResponse* response) {
  auto caller = ks::kess::rpc::grpc::ServerContextHelper::GetCallerService(context);
  if (!request->caller().empty()) {
    caller = request->caller();
  }
  auto& stat = Mertric::GetThreadStatInfo(caller);

  auto guard = folly::makeGuard([&] { stat.AddInvalidPv().Report(caller); });

  if (KS_UNLIKELY(request == nullptr)) {
    return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "request is null");
  }

  int ret = -1;

  if (request->version() == proto::Version::V1) {
    // only 正排查询链路
    // 未来会迁移到正倒排一体化查询链路
    if (request->kvquerys_size() == 0 &&
          request->querys_size() == 0) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "empty query");
    }

    switch (request->type()) {
      case proto::ProtoType::FB: {
        ret = BatchGetTableHandlerFB(caller, request, response);
        GS_HANDLE_ERROR(ret);
        break;
      }
      case proto::ProtoType::PB: {
        ret = BatchGetTableHandler(caller, request, response);
        GS_HANDLE_ERROR(ret);
        break;
      }
      default: {
        return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT,
                            "invalid request type");
      }
    }

  } else {
    // 正倒排一体化查询链路
    if (request->grid_querys_size() == 0) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "empty query");
    }
    ret = SerialSearchHandler(caller, request, response);
    GS_HANDLE_ERROR(ret);
  }

  guard.dismiss();
  stat.AddSuccessPv().Report(caller);
  return grpc::Status::OK;
}

#undef GS_HANDLE_ERROR

}  // namespace grid
}  // namespace ks
