#pragma once

#include <vector>
#include "taskflow/taskflow.hpp"
#include "teams/ad/ad_feature_index/proto/msg.kess.grpc.pb.h"
#include "teams/ad/ad_feature_index/proto/msg.pb.h"

namespace ks {
namespace grid {

class AdFeatureIndexShardService final
    : public ks::ad_feature_index::proto::kess::AdFeatureIndexService::Service {
 public:
  AdFeatureIndexShardService();

  ::grpc::Status GetBatchSchemaFreeData(::grpc::ServerContext* context,
                                        const ::ks::ad_feature_index::proto::GetBatchSchemaFreeDataReq* request,
                                        ::ks::ad_feature_index::proto::GetBatchSchemaFreeDataResp* response) override;

 private:
  int32_t shard_id_{0};
};

}  // namespace grid
}  // namespace ks
