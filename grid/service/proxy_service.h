#pragma once

#include "teams/ad/grid/proto/msg.kess.grpc.pb.h"
#include "teams/ad/grid/proto/msg.pb.h"
namespace ks {
namespace grid {

using ::ks::grid::proto::GridRequest;
using ::ks::grid::proto::GridResponse;

class ProxyService final
    : public ks::grid::proto::kess::GridService::Service {
 public:
  ProxyService();

  ::grpc::Status BatchGetTable(::grpc::ServerContext* context,
                                        const GridRequest* request,
                                        GridResponse* response) override;
};
}  // namespace grid
}  // namespace ks
