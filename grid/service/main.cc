#include <memory>
#include <vector>
#include "base/common/base.h"
#include "gflags/gflags.h"
#include "ks/serving_util/grpc/grpc_tcpcopy_util.h"
#include "serving_base/thp_component/kthp.h"
#include "teams/ad/ad_base/src/common/gracefull_exit.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/ksp/ad_web_request_handler.h"
#include "teams/ad/ad_base/src/ksp/dynamic_port.h"
#include "teams/ad/grid/web/web_server.h"
#include "teams/ad/grid/service/grid_service.h"
#include "teams/ad/grid/service/ad_feature_index_shard_service.h"
#include "teams/ad/grid/service/proxy_service.h"
#include "teams/ad/grid/index/index_mannger.h"


DEFINE_int32(web_thread_num, 4, "server http thread_num");
DEFINE_int32(web_server_port, 10080, "server http port");
DEFINE_bool(enable_kthp_process, false, "enable kthp process");
namespace ks {
namespace grid {
bool warm_up() {
  LOG(INFO) << "Try to warm up";
  ks::serving_util::grpc::GrpcTcpcopyUtil::Singleton()->SetFlagsForTcpcopy();
  // fix grpc timeout not under control problem
  // ks_flag_grpc_enable_tcp_user_timeout = true;
  // ks_flag_grpc_tcp_user_timeout_ms = 500;
  AD_KESS_CLIENT_INIT_CHECK("init error");

  return true;
}


class AdWebServiceHandlerDict {
 public:
  static serving_base::WebRequestHandlerDict* CreateHandler() {
    ks::ad_base::DefaultAdWebServiceDict::CallDict handler;
    handler["/adtable_kv"] = new AdTableIndexKVHandler();
    return new ks::ad_base::DefaultAdWebServiceDict(handler);
  }
};
}  // namespace grid
}  // namespace ks

int main(int argc, char** argv) {
  base::InitApp(&argc, &argv, "grid server");
  if (FLAGS_enable_kthp_process) {
    LOG(INFO) << "enable kthp for target splash.";
    CHECK(serving_base::KTHP::Init()) << "The preparation for large-sized pages is failed.";
  }
  // 先进行 warm up
  CHECK(ks::grid::warm_up()) << "warn up failed!";

  // ks::ad_base::DefaultAdWebService web_service(FLAGS_web_thread_num);
  ks::ad_base::DefaultAdWebService web_service(
      FLAGS_web_thread_num, []() -> serving_base::WebRequestHandlerDict * {
        return ks::grid::AdWebServiceHandlerDict::CreateHandler();
      });
  net::WebServer::Options web_server_option;
  web_server_option.port = ks::ad_base::DynamicPort::Instance().Port("AUTO_PORT0");
  web_server_option.backlog = 1024;
  if (web_server_option.port <= 0) {
    web_server_option.port =
        ks::DynamicJsonConfig::GetConfig()->GetInt("web_server_port", FLAGS_web_server_port);
    LOG(INFO) << "may be not running in kcs mode! can't get port AUTO_PORT0 "
              << "use dynamic_json port or gflag " << web_server_option.port;
  }
  net::WebServer web_server(web_server_option, &web_service);
  web_server.Start();
  LOG(INFO) << "web server started port " << web_server_option.port;

  ks::ad_base::AdKessClient& kess_instance = ks::ad_base::AdKessClient::Instance();
  std::vector<ks::kess::rpc::grpc::Service*> grpc_service_list;

  bool is_proxy = ks::DynamicJsonConfig::GetConfig()->GetBoolean("is_proxy", false);
  if (is_proxy) {
    grpc_service_list.push_back(new ks::grid::ProxyService);
  } else {
    ks::grid::DBManager::GetInstance().Init();
    grpc_service_list.reserve(2);
    grpc_service_list.push_back(new ks::grid::GridService);
    grpc_service_list.push_back(new ks::grid::AdFeatureIndexShardService);
  }

  ::google::FlushLogFiles(::google::INFO);
  // grpc
  kess_instance.StartService(grpc_service_list, web_server_option.port);
  kess_instance.StopService();

  // 停止 WEB 服务
  web_server.Stop();

  LOG(INFO) << "grid server safety";
  ::google::FlushLogFiles(::google::INFO);
  ::google::ShutdownGoogleLogging();
  ks::ad_base::GracefulShutdown();
}
