import requests
import json
import sys
import math

def request_kconf(key):
    r = requests.get("https://kconf.corp.kuaishou.com/api/config/get", params={
        'key': f'{key}',
    }, headers={
        'Authorization': 'Token 718f5d8524ed8c8a7f8d3174ca2949a8'
    })
    for env_json in r.json()['data']['subConfigs']:
        if env_json['stage'] == 'production':
            return env_json['data']
    exit(1)
    return "error"
"""
python3 sync_build.py table1 field1,filed2,...,field10 table2 field1,filed2,...,field10
"""
if __name__ == '__main__':
    table2field = {}
    l = len(sys.argv)
    num = math.floor((l -1)/2)
    for index in range(num):
        table2field[sys.argv[2*index + 1]] = {}
        fields = sys.argv[2*index + 2].split(",")
        table2field[sys.argv[2*index + 1]] = fields
    build_str = request_kconf('ad.adTable.buildConfig_feature_index_schemafree_prod_backup')
    build = json.loads(build_str)
    for table,field in table2field.items():
        hub_str = request_kconf(f'ad.adEngineDataTableSchema.TableSchemaHub_{table}')
        hub = json.loads(hub_str)
        field_map = {}
        for t in hub['column_list']:
            field_map[t['column_name']] = t
        if table in build['tables']:
            for f in field:
                build['tables'][table]['fields'][f] = {}
                build['tables'][table]['fields'][f]['id'] = len(build['tables'][table]['fields'])
                build['tables'][table]['fields'][f]['name'] = f
                build['tables'][table]['fields'][f]['type'] = field_map[f]['column_type']
                build['tables'][table]['fields'][f]['constraint'] = "NONE_CONSTRAINT"
                build['tables'][table]['fields'][f]['referenceTableName'] = ""
        else:
            # 新增表格
            build['tables'][table] = {
                'capacity': 2000000,
                'disableDataframe': True,
                'fields': {},
            }
            count = 0
            for f in field:
                count = count + 1
                build['tables'][table]['fields'].update({
                    f : {
                        'id': count,
                        'name': f,
                        'type': field_map[f]['column_type'],
                        'constraint': 'NONE_CONSTRAINT' if count > 1 else 'PRIMARY_KEY',
                        'referenceTableName': ''
                    }
                })
    with open("./result.json", '+w') as f:
        json.dump(obj=build, fp=f, indent=2)
