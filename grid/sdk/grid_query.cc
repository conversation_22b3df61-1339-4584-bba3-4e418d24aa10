#include <cstdint>
#include <sstream>
#include "teams/ad/grid/sdk/grid_query.h"
#include "base/hash_function/md5.h"

namespace ks {
namespace grid {
void GridQuery::CalculateMd5() {
  if (select_fields.empty()) {
    md5.clear();
    return;
  }
  std::string tmp;
  int64_t length = 0;
  for (auto& field : select_fields) {
    length += field.length();
  }
  tmp.reserve(length);
  for (auto& field : select_fields) {
    tmp.append(field);
  }
  md5 = MD5String(tmp);
}


std::string GridQuery::DebugString() const {
  std::stringstream ss;
  ss << "table_name: " << table_name
      << ", index_name:" << index_name
      << ", select_fields: [";
  for (auto& field : select_fields) {
    ss << field << ", ";
  }
  ss << "], ids: "<< ids.size();
  ss << "[";
  int count = 0;
  for (auto& id : ids) {
    ss << id.i64 << ", ";
    if (++count >= 10) {
      break;
    }
  }
  ss << "], index: " << index_name
      << ", limit: " << limit
      << ", md5: " << md5;
  return ss.str();
}
}  // namespace grid
}  // namespace ks
