#pragma once
#include <cstddef>
#include <cstdint>
#include <string>
#include <vector>
#include <memory>
#include <utility>
#include "absl/container/flat_hash_map.h"
#include "ks/base/container/common.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_index_meta/table_schema.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_index_meta/transport_schema.pb.h"
#include "teams/ad/ad_table/table/key128.h"
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/proto/msg.pb.h"
#include "teams/ad/grid/proto/grid_generated.h"
#include "teams/ad/grid/cache/cache_instance.h"
#include "teams/ad/grid/sdk/grid_type_trait.h"


using ::ks::grid::proto::Table;
using ::ks::grid::proto::GridResponse;
using ::ks::grid::proto::Row;
using CacheRow = std::shared_ptr<Row>;

DECLARE_bool(enable_grid_sdk_cache);

namespace ks {
namespace grid {
struct FbRow {
  std::shared_ptr<std::string> guard = nullptr;
  flatbuffers::Vector<flatbuffers::Offset<flat::Value>>* row = nullptr;

  operator bool() const {
    return !(guard == nullptr || row == nullptr || row == 0);
  }
};

class GridData {
 public:
  std::shared_ptr<std::vector<ColumnDesc>> TableSchema() {
    return guard_col_;
  }

  std::string TableName() {
    return table_name;
  }

  // 左闭右开区间
  std::pair<int32_t, int32_t> GetIndexRange(uint32_t index_id);

  bool IsValid() const;  // 表是否存在
  bool IsValid(uint32_t i) const;  // 第 i 行是否存在
  size_t RowSize() const;
  size_t ColSize() const;
  //  获取列类型,不存在则为 UNKNOWN_COLUMN
  ad_index_meta::proto::ColumnType GetColumnType(uint32_t col_index) const;
  ad_index_meta::proto::ColumnType GetColumnType(const std::string& col_name) const;

  bool IsBool(uint32_t row_index, uint32_t col_index) const;
  bool GetBool(uint32_t row_index, uint32_t col_index) const;
  bool GetBool(uint32_t row_index, const std::string& col_name) const;

  bool IsInt8(uint32_t row_index, uint32_t col_index) const;
  int8_t GetInt8(uint32_t row_index, uint32_t col_index) const;
  int8_t GetInt8(uint32_t row_index, const std::string& col_name) const;

  bool IsInt16(uint32_t row_index, uint32_t col_index) const;
  int16_t GetInt16(uint32_t row_index, uint32_t col_index) const;
  int16_t GetInt16(uint32_t row_index, const std::string& col_name) const;

  bool IsInt32(uint32_t row_index, uint32_t col_index) const;
  int32_t GetInt32(uint32_t row_index, uint32_t col_index) const;
  int32_t GetInt32(uint32_t row_index, const std::string& col_name) const;

  bool IsInt64(uint32_t row_index, uint32_t col_index) const;
  int64_t GetInt64(uint32_t row_index, uint32_t col_index) const;
  int64_t GetInt64(uint32_t row_index, const std::string& col_name) const;

  bool IsFloat(uint32_t row_index, uint32_t col_index) const;
  float GetFloat(uint32_t row_index, uint32_t col_index) const;
  float GetFloat(uint32_t row_index, const std::string& col_name) const;

  bool IsDouble(uint32_t row_index, uint32_t col_index) const;
  double GetDouble(uint32_t row_index, uint32_t col_index) const;
  double GetDouble(uint32_t row_index, const std::string& col_name) const;

  //  String & LIST 类型数据的生命周期由用户管理，不要传入空指针
  bool IsString(uint32_t row_index, uint32_t col_index) const;
  bool GetString(uint32_t row_index, uint32_t col_index, std::string* data) const;
  bool GetString(uint32_t row_index, const std::string& col_name, std::string* data) const;

  bool IsInt8List(uint32_t row_index, uint32_t col_index) const;
  bool GetInt8List(uint32_t row_index, uint32_t col_index, std::vector<int8_t>* data) const;
  bool GetInt8List(uint32_t row_index, const std::string& col_name, std::vector<int8_t>* data) const;

  bool IsInt16List(uint32_t row_index, uint32_t col_index) const;
  bool GetInt16List(uint32_t row_index, uint32_t col_index, std::vector<int16_t>* data) const;
  bool GetInt16List(uint32_t row_index, const std::string& col_name, std::vector<int16_t>* data) const;

  bool IsInt32List(uint32_t row_index, uint32_t col_index) const;
  bool GetInt32List(uint32_t row_index, uint32_t col_index, std::vector<int32_t>* data) const;
  bool GetInt32List(uint32_t row_index, const std::string& col_name, std::vector<int32_t>* data) const;

  bool IsInt64List(uint32_t row_index, uint32_t col_index) const;
  bool GetInt64List(uint32_t row_index, uint32_t col_index, std::vector<int64_t>* data) const;
  bool GetInt64List(uint32_t row_index, const std::string& col_name, std::vector<int64_t>* data) const;

  bool IsFloatList(uint32_t row_index, uint32_t col_index) const;
  bool GetFloatList(uint32_t row_index, uint32_t col_index, std::vector<float>* data) const;
  bool GetFloatList(uint32_t row_index, const std::string& col_name, std::vector<float>* data) const;

  bool IsDoubleList(uint32_t row_index, uint32_t col_index) const;
  bool GetDoubleList(uint32_t row_index, uint32_t col_index, std::vector<double>* data) const;
  bool GetDoubleList(uint32_t row_index, const std::string& col_name, std::vector<double>* data) const;

  bool IsStringList(uint32_t row_index, uint32_t col_index) const;
  bool GetStringList(uint32_t row_index, uint32_t col_index, std::vector<std::string>* data) const;
  bool GetStringList(uint32_t row_index, const std::string& col_name, std::vector<std::string>* data) const;

  // unsafe 接口，不做边界判定和类型判定，使用请确保安全
  flatbuffers::Vector<flatbuffers::Offset<flat::Value>>* UnsafeGetFBRow(uint32_t i) {
    return (*data_fb_)[i].row;
  }

  bool UnSafeGetBool(uint32_t row_index, uint32_t col_index) const {
    return UnSafeGet<bool>(row_index, col_index);
  }
  int8_t UnSafeGetInt8(uint32_t row_index, uint32_t col_index) const {
    return UnSafeGet<int8_t>(row_index, col_index);
  }
  int16_t UnSafeGetInt16(uint32_t row_index, uint32_t col_index) const {
    return UnSafeGet<int16_t>(row_index, col_index);
  }
  int32_t UnSafeGetInt32(uint32_t row_index, uint32_t col_index) const {
    return UnSafeGet<int32_t>(row_index, col_index);
  }
  int64_t UnSafeGetInt64(uint32_t row_index, uint32_t col_index) const {
    return UnSafeGet<int64_t>(row_index, col_index);
  }
  float UnSafeGetFloat(uint32_t row_index, uint32_t col_index) const {
    return UnSafeGet<float>(row_index, col_index);
  }
  double UnSafeGetDouble(uint32_t row_index, uint32_t col_index) const {
    return UnSafeGet<double>(row_index, col_index);
  }
  void UnSafeGetInt8List(uint32_t row_index, uint32_t col_index, std::vector<int8_t>* data) const {
    UnSafeGetList(row_index, col_index, data);
  }
  void UnSafeGetInt16List(uint32_t row_index, uint32_t col_index, std::vector<int16_t>* data) const {
    UnSafeGetList(row_index, col_index, data);
  }
  void UnSafeGetInt32List(uint32_t row_index, uint32_t col_index, std::vector<int32_t>* data) const {
    UnSafeGetList(row_index, col_index, data);
  }
  void UnSafeGetInt64List(uint32_t row_index, uint32_t col_index, std::vector<int64_t>* data) const {
    UnSafeGetList(row_index, col_index, data);
  }
  void UnSafeGetFloatList(uint32_t row_index, uint32_t col_index, std::vector<float>* data) const {
    UnSafeGetList(row_index, col_index, data);
  }
  void UnSafeGetDoubleList(uint32_t row_index, uint32_t col_index, std::vector<double>* data) const {
    UnSafeGetList(row_index, col_index, data);
  }
  void UnSafeGetString(uint32_t row_index, uint32_t col_index, std::string* data) const;
  void UnSafeGetStringList(uint32_t row_index, uint32_t col_index, std::vector<std::string>* data) const;

  // 定制逻辑， bool/int8/int32/int64 直接填充到 int64 数组中
  template <typename T>
  inline void UnSafeGetInt64ListTrans(
    uint32_t row_index,
    uint32_t col_index,
    std::vector<int64_t>* data) const {
    if (enable_fb_) {
      auto raw_data = GridTypeTrait<T>::FbAccessor((*data_fb_)[row_index].row->Get(col_index));
      if (raw_data == nullptr) {
        data->clear();
        return;
      }
      data->assign(raw_data->begin(), raw_data->end());
    } else {
      auto& raw_data = GridTypeTrait<T>::PbAccessor(data_->rows(row_index).data(col_index));
      data->assign(raw_data.begin(), raw_data.end());
    }
  }
  // 定制逻辑， float/double 直接填充到 double 数组中
  template <typename T>
  inline void UnSafeGetDoubleListTrans(
    uint32_t row_index,
    uint32_t col_index,
    std::vector<double>* data) const {
    if (enable_fb_) {
      auto raw_data = GridTypeTrait<T>::FbAccessor((*data_fb_)[row_index].row->Get(col_index));
      if (raw_data == nullptr) {
        data->clear();
        return;
      }
      data->assign(raw_data->begin(), raw_data->end());
    } else {
      auto& raw_data = GridTypeTrait<T>::PbAccessor(data_->rows(row_index).data(col_index));
      data->assign(raw_data.begin(), raw_data.end());
    }
  }

  // debug 接口 不做边界检查 注意使用
  std::string DebugString(int32_t i) const;

 private:
  struct NoCheckType {};
  struct CheckFBType {};
  struct CheckPBType {};
  template <typename T>
  bool CheckBoundary(uint32_t row_index, uint32_t col_index, CheckPBType) const {
    if (!CheckBoundary(row_index, col_index, NoCheckType())) {
      return false;
    }
    if (KS_UNLIKELY(data_->columns(col_index).type() != GridTypeTrait<T>::type)) {
      ++(stat_->invalid_type[col_index]);
      return false;
    }
    return true;
  }

  template <typename T>
  bool CheckBoundary(uint32_t row_index, uint32_t col_index, CheckFBType) const {
    if (!CheckBoundary(row_index, col_index, NoCheckType())) {
      return false;
    }
    if (KS_UNLIKELY((*col_)[col_index].type != GridTypeTrait<T>::type)) {
      ++(stat_->invalid_type[col_index]);
      return false;
    }
    return true;
  }

  bool CheckBoundary(uint32_t row_index, uint32_t col_index, NoCheckType) const {
    if (KS_LIKELY(row_index >= row_size_ || col_index >= col_size_)) {
      ++(stat_->invalid_index);
      return false;
    }
    return true;
  }

  template <typename T>
  inline bool IsType(uint32_t row_index, uint32_t col_index) const {
    if (enable_fb_) {
      return CheckBoundary<T>(row_index, col_index, CheckFBType());
    } else {
      return CheckBoundary<T>(row_index, col_index, CheckPBType());
    }
  }

  template <typename T>
  inline T GetPB(uint32_t row_index, uint32_t col_index) const {
    if (KS_UNLIKELY(data_ == nullptr)) return GridTypeTrait<T>::default_value;
    if (!CheckBoundary<T>(row_index, col_index, CheckPBType())) {
      return GridTypeTrait<T>::default_value;
    }
    if (data_->rows(row_index).data_size() > col_index) {
      return GridTypeTrait<T>::PbAccessor(data_->rows(row_index).data(col_index));
    }
    return GridTypeTrait<T>::default_value;
  }

  template <typename T>
  inline T GetFB(uint32_t row_index, uint32_t col_index) const {
    if (KS_UNLIKELY(data_fb_ == nullptr)) return GridTypeTrait<T>::default_value;
    if (!CheckBoundary<T>(row_index, col_index, CheckFBType())) {
      return GridTypeTrait<T>::default_value;
    }
    if ((*data_fb_)[row_index].row == nullptr) {
      return GridTypeTrait<T>::default_value;
    }
    if ((*data_fb_)[row_index].row->size() > col_index) {
      return GridTypeTrait<T>::FbAccessor((*data_fb_)[row_index].row->Get(col_index));
    }
    return GridTypeTrait<T>::default_value;
  }

  template <typename T>
  inline T Get(uint32_t row_index, uint32_t col_index) const {
    if (enable_fb_) {
      return GetFB<T>(row_index, col_index);
    } else {
      return GetPB<T>(row_index, col_index);
    }
  }

  template <typename T>
  inline T UnSafeGet(uint32_t row_index, uint32_t col_index) const {
    if (enable_fb_) {
      return GridTypeTrait<T>::FbAccessor((*data_fb_)[row_index].row->Get(col_index));
    } else {
      return GridTypeTrait<T>::PbAccessor(data_->rows(row_index).data(col_index));
    }
  }

  template <typename T>
  inline T Get(uint32_t row_index, const std::string& col_name) const {
    if (KS_UNLIKELY(col_index_ == nullptr)) {
      return GridTypeTrait<T>::default_value;
    }
    auto col_index = col_index_->find(col_name);
    if (col_index == col_index_->end()) {
      ++(stat_->invalid_col[col_name]);
      return GridTypeTrait<T>::default_value;
    }
    return Get<T>(row_index, col_index->second);
  }

  template <typename T>
  inline bool GetListPB(uint32_t row_index, uint32_t col_index, T* data) const {
    if (KS_UNLIKELY(data_ == nullptr)) {
      return false;
    }
    if (!CheckBoundary<T>(row_index, col_index, CheckPBType())) {
      return false;
    }
    if (KS_LIKELY(data_->rows(row_index).data_size() > col_index)) {
      auto& raw_data = GridTypeTrait<T>::PbAccessor(data_->rows(row_index).data(col_index));
      data->assign(raw_data.begin(), raw_data.end());
      return true;
    }
    return false;
  }

  template <typename T>
  inline bool GetListFB(uint32_t row_index, uint32_t col_index, T* data) const {
    if (KS_UNLIKELY(data_fb_ == nullptr)) {
      return false;
    }
    if (!CheckBoundary<T>(row_index, col_index, CheckFBType())) {
      return false;
    }
    if ((*data_fb_)[row_index].row == nullptr) {
      data->clear();
      return true;
    }
    if (KS_LIKELY((*data_fb_)[row_index].row->size() > col_index)) {
      auto raw_data = GridTypeTrait<T>::FbAccessor((*data_fb_)[row_index].row->Get(col_index));
      if (raw_data == nullptr) {
        data->clear();
        return true;
      }
      data->assign(raw_data->begin(), raw_data->end());
      return true;
    }
    return false;
  }

  template <typename T>
  inline bool GetList(uint32_t row_index, uint32_t col_index, T* data) const {
    if (enable_fb_) {
      return GetListFB<T>(row_index, col_index, data);
    } else {
      return GetListPB<T>(row_index, col_index, data);
    }
  }

  template <typename T>
  inline void UnSafeGetList(uint32_t row_index, uint32_t col_index, T* data) const {
    if (enable_fb_) {
      auto raw_data = GridTypeTrait<T>::FbAccessor((*data_fb_)[row_index].row->Get(col_index));
      if (raw_data == nullptr) {
        data->clear();
        return;
      }
      data->assign(raw_data->begin(), raw_data->end());
    } else {
      auto& raw_data = GridTypeTrait<T>::PbAccessor(data_->rows(row_index).data(col_index));
      data->assign(raw_data.begin(), raw_data.end());
    }
  }

  template <typename T>
  inline bool GetList(uint32_t row_index, const std::string& col_name, T* data) const {
    if (KS_UNLIKELY(col_index_ == nullptr)) {
      return false;
    }
    auto col_index = col_index_->find(col_name);
    if (col_index == col_index_->end()) {
      ++(stat_->invalid_col[col_name]);
      return false;
    }
    return GetList<T>(row_index, col_index->second, data);
  }

 public:
  ~GridData() {
    Perf();
  }

 private:
  friend class GridClient;
  friend class GridClientHandler;
  friend class SDKHandler;
  friend class SDKClient;

  void Reset(Table* table, std::shared_ptr<GridResponse> rep);
  bool ResetFB(std::shared_ptr<std::vector<FbRow>> rows,
                std::shared_ptr<std::vector<ColumnDesc>> columns,
                const std::string& name);
  void SetOffset(std::vector<int32_t>&& offset);
  void Perf();

  struct Stat {
    uint32_t invalid_index = 0;
    std::vector<uint64_t> invalid_type;
    absl::flat_hash_map<std::string, uint64_t> invalid_col;
  };
  std::shared_ptr<Stat> guard_stat_;
  Stat* stat_{nullptr};

  Table* data_{nullptr};
  std::shared_ptr<GridResponse> rep_;  // 负责管理生命周期
  std::shared_ptr<absl::flat_hash_map<std::string, uint32_t>> guard_col_index_;
  absl::flat_hash_map<std::string, uint32_t>* col_index_{nullptr};

  std::shared_ptr<std::vector<std::string>> guard_index2col_;
  std::vector<std::string>* index2col_{nullptr};

  uint32_t row_size_ = 0;
  uint32_t col_size_ = 0;
  std::string table_name;

  // 新版 SDK 存储结构
  bool enable_fb_ = false;
  std::shared_ptr<std::vector<FbRow>> guard_data_fb_{nullptr};  // 负责管理生命周期
  std::vector<FbRow>* data_fb_{nullptr};

  std::shared_ptr<std::vector<ColumnDesc>> guard_col_;
  std::vector<ColumnDesc>* col_{nullptr};

  // 倒排范围
  std::vector<int32_t> offset_;
};


template <>
inline bool GridData::GetListFB<std::string>(
    uint32_t row_index, uint32_t col_index, std::string* data) const {
  if (KS_UNLIKELY(data_fb_ == nullptr)) {
    return false;
  }
  if (!CheckBoundary<std::string>(row_index, col_index, CheckFBType())) {
    return false;
  }
  if ((*data_fb_)[row_index].row == nullptr) {
    data->clear();
    return true;
  }
  if (KS_LIKELY((*data_fb_)[row_index].row->size() > col_index)) {
    auto raw_data = GridTypeTrait<std::string>::FbAccessor((*data_fb_)[row_index].row->Get(col_index));
    if (raw_data == nullptr) {
      data->clear();
      return true;
    }
    data->assign(raw_data->begin(), raw_data->end());
    return true;
  }
  return false;
}

template <>
inline bool GridData::GetListFB<std::vector<std::string>>(
  uint32_t row_index, uint32_t col_index, std::vector<std::string>* data) const {
  if (KS_UNLIKELY(data_fb_ == nullptr)) {
    return false;
  }
  if (!CheckBoundary<std::vector<std::string>>(row_index, col_index, CheckFBType())) {
    return false;
  }
  data->clear();
  if ((*data_fb_)[row_index].row == nullptr) {
    return true;
  }
  if (KS_LIKELY((*data_fb_)[row_index].row->size() > col_index)) {
    auto raw_data =
      GridTypeTrait<std::vector<std::string>>::FbAccessor(
          (*data_fb_)[row_index].row->Get(col_index));
    if (raw_data == nullptr) {
      return true;
    }
    data->reserve(raw_data->size());
    for (int i = 0; i < raw_data->size(); ++i) {
      auto str = raw_data->GetAsString(i);
      data->emplace_back(str->c_str(), str->Length());
    }
    return true;
  }
  return false;
}

inline void GridData::UnSafeGetString(uint32_t row_index, uint32_t col_index, std::string* data) const {
  if (enable_fb_) {
    auto raw_data = GridTypeTrait<std::string>::FbAccessor((*data_fb_)[row_index].row->Get(col_index));
    if (raw_data == nullptr) {
      data->clear();
      return;
    }
    data->assign(raw_data->begin(), raw_data->end());
  } else {
    auto& raw_data = GridTypeTrait<std::string>::PbAccessor(data_->rows(row_index).data(col_index));
    data->assign(raw_data.begin(), raw_data.end());
  }
}

inline void GridData::UnSafeGetStringList(
  uint32_t row_index,
  uint32_t col_index,
  std::vector<std::string>* data
) const {
  if (enable_fb_) {
    auto raw_data = GridTypeTrait<std::vector<std::string>>::FbAccessor(
                      (*data_fb_)[row_index].row->Get(col_index));
    if (raw_data == nullptr) {
      return;
    }
    data->reserve(raw_data->size());
    for (int i = 0; i < raw_data->size(); ++i) {
      auto str = raw_data->GetAsString(i);
      data->emplace_back(str->c_str(), str->Length());
    }
  } else {
    auto& raw_data =
      GridTypeTrait<std::vector<std::string>>::PbAccessor(
        data_->rows(row_index).data(col_index));
    data->assign(raw_data.begin(), raw_data.end());
  }
}
}  // namespace grid
}  // namespace ks
