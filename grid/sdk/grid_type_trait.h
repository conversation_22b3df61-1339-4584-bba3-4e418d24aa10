#pragma once
#include <string>
#include <vector>
#include "teams/ad/ad_proto/kuaishou/ad/ad_index_meta/table_schema.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_index_meta/transport_schema.pb.h"

namespace ks {
namespace grid {

template<class T> struct GridTypeTrait {
};

template<> struct GridTypeTrait<bool> {
  static constexpr uint32_t type = ad_index_meta::proto::ColumnType::BOOL;
  static constexpr bool default_value{false};
  static bool PbAccessor(const ks::grid::proto::Value& value) {
    return value.bool_value();
  }
  static bool FbAccessor(const ks::grid::flat::Value* value) {
    return value->bool_value();
  }
};

template<> struct GridTypeTrait<int8_t> {
  static constexpr uint32_t type = ad_index_meta::proto::ColumnType::INT8;
  static constexpr int8_t default_value{0};
  static int8_t PbAccessor(const ks::grid::proto::Value& value) {
    return value.i8();
  }
  static int8_t FbAccessor(const ks::grid::flat::Value* value) {
    return value->i8();
  }
};

template<> struct GridTypeTrait<int16_t> {
  static constexpr uint32_t type = ad_index_meta::proto::ColumnType::INT16;
  static constexpr int16_t default_value{0};
  static int16_t PbAccessor(const ks::grid::proto::Value& value) {
    return value.i16();
  }
  static int16_t FbAccessor(const ks::grid::flat::Value* value) {
    return value->i16();
  }
};

template<> struct GridTypeTrait<int32_t> {
  static constexpr uint32_t type = ad_index_meta::proto::ColumnType::INT32;
  static constexpr int32_t default_value{0};
  static int32_t PbAccessor(const ks::grid::proto::Value& value) {
    return value.i32();
  }
  static int32_t FbAccessor(const ks::grid::flat::Value* value) {
    return value->i32();
  }
};

template<> struct GridTypeTrait<int64_t> {
  static constexpr uint32_t type = ad_index_meta::proto::ColumnType::INT64;
  static constexpr int64_t default_value{0};
  static int64_t PbAccessor(const ks::grid::proto::Value& value) {
    return value.i64();
  }
  static int64_t FbAccessor(const ks::grid::flat::Value* value) {
    return value->i64();
  }
};

template<> struct GridTypeTrait<float> {
  static constexpr uint32_t type = ad_index_meta::proto::ColumnType::FLOAT;
  static constexpr float default_value{0.0};
  static float PbAccessor(const ks::grid::proto::Value& value) {
    return value.float_value();
  }
  static float FbAccessor(const ks::grid::flat::Value* value) {
    return value->float_value();
  }
};

template<> struct GridTypeTrait<double> {
  static constexpr uint32_t type = ad_index_meta::proto::ColumnType::DOUBLE;
  static constexpr double default_value{0.0};
  static double PbAccessor(const ks::grid::proto::Value& value) {
    return value.double_value();
  }
  static double FbAccessor(const ks::grid::flat::Value* value) {
    return value->double_value();
  }
};

template<> struct GridTypeTrait<std::string> {
  static constexpr uint32_t type = ad_index_meta::proto::ColumnType::STRING;
  static const std::string& PbAccessor(const ks::grid::proto::Value& value) {
    return value.string_value();
  }
  static const flatbuffers::String* FbAccessor(const ks::grid::flat::Value* value) {
    return value->string_value();
  }
};

template<> struct GridTypeTrait<std::vector<int8_t>> {
  static constexpr uint32_t type = ad_index_meta::proto::ColumnType::INT8_LIST;
  static const ::google::protobuf::RepeatedField<int32_t>& PbAccessor(const ks::grid::proto::Value& value) {
    return value.int8_list_value();
  }
  static const flatbuffers::Vector<int8_t>* FbAccessor(const ks::grid::flat::Value* value) {
    return value->int8_list_value();
  }
};

template<> struct GridTypeTrait<std::vector<int16_t>> {
  static constexpr uint32_t type = ad_index_meta::proto::ColumnType::INT16_LIST;
  static const ::google::protobuf::RepeatedField<int32_t>& PbAccessor(const ks::grid::proto::Value& value) {
    return value.int16_list_value();
  }
  static const flatbuffers::Vector<int16_t>* FbAccessor(const ks::grid::flat::Value* value) {
    return value->int16_list_value();
  }
};


template<> struct GridTypeTrait<std::vector<int32_t>> {
  static constexpr uint32_t type = ad_index_meta::proto::ColumnType::INT32_LIST;
  static const ::google::protobuf::RepeatedField<int32_t>& PbAccessor(const ks::grid::proto::Value& value) {
    return value.int32_list_value();
  }
  static const flatbuffers::Vector<int32_t>* FbAccessor(const ks::grid::flat::Value* value) {
    return value->int32_list_value();
  }
};

template<> struct GridTypeTrait<std::vector<int64_t>> {
  static constexpr uint32_t type = ad_index_meta::proto::ColumnType::INT64_LIST;
  static const ::google::protobuf::RepeatedField<int64_t>& PbAccessor(const ks::grid::proto::Value& value) {
    return value.int64_list_value();
  }
  static const flatbuffers::Vector<int64_t>* FbAccessor(const ks::grid::flat::Value* value) {
    return value->int64_list_value();
  }
};

template<> struct GridTypeTrait<std::vector<float>> {
  static constexpr uint32_t type = ad_index_meta::proto::ColumnType::FLOAT_LIST;
  static const ::google::protobuf::RepeatedField<float>& PbAccessor(const ks::grid::proto::Value& value) {
    return value.float_list_value();
  }
  static const flatbuffers::Vector<float>* FbAccessor(const ks::grid::flat::Value* value) {
    return value->float_list_value();
  }
};

template<> struct GridTypeTrait<std::vector<double>> {
  static constexpr uint32_t type = ad_index_meta::proto::ColumnType::DOUBLE_LIST;
  static const ::google::protobuf::RepeatedField<double>& PbAccessor(const ks::grid::proto::Value& value) {
    return value.double_list_value();
  }
  static const flatbuffers::Vector<double>* FbAccessor(const ks::grid::flat::Value* value) {
    return value->double_list_value();
  }
};

template<> struct GridTypeTrait<std::vector<std::string>> {
  static constexpr uint32_t type = ad_index_meta::proto::ColumnType::STRING_LIST;
  static const ::google::protobuf::RepeatedPtrField<std::string>&
    PbAccessor(const ks::grid::proto::Value& value) {
    return value.string_list_value();
  }
  static const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>*
    FbAccessor(const ks::grid::flat::Value* value) {
    return value->string_list_value();
  }
};
}  // namespace grid
}  // namespace ks
