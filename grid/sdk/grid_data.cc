#include <cstddef>
#include <cstdint>
#include <memory>
#include <utility>
#include <vector>
#include "glog/logging.h"
#include "ks/base/container/common.h"
#include "teams/ad/grid/sdk/grid_data.h"
#include "teams/ad/grid/utils/utils.h"
#include "teams/ad/grid/utils/perf.h"

namespace ks {
namespace grid {
const bool default_bool = false;
const int8_t default_i8 = 0;
const int16_t  default_i16 = 0;
const int32_t default_i32 = 0;
const int64_t default_i64 = 0;
const float default_f32 = 0.0f;
const double default_f64 = 0.0;

size_t GridData::RowSize() const {
  return row_size_;
}

size_t GridData::ColSize() const {
  return col_size_;
}

bool GridData::IsValid() const {
  if (enable_fb_) {
    return data_fb_ != nullptr;
  } else {
    return data_ != nullptr;
  }
  return true;
}

bool GridData::IsValid(uint32_t i) const {
  if (KS_UNLIKELY(i >= row_size_)) {
    return false;
  } else {
    if (enable_fb_) {
      return (*data_fb_)[i].row != nullptr && (*data_fb_)[i].row->size() != 0;
    }
    return !(data_->rows(i).data().empty());
  }
}

std::pair<int32_t, int32_t> GridData::GetIndexRange(uint32_t index_id) {
  if (UNLIKELY(index_id  >= offset_.size() - 1)) {
    return std::make_pair(-1, -1);
  }
  if (offset_[index_id] == offset_[index_id + 1]) {
    return std::make_pair(-1, -1);
  } else {
    return std::make_pair(offset_[index_id], offset_[index_id + 1]);
  }
}

//  获取列类型,不存在则为 UNKNOWN_COLUMN
ad_index_meta::proto::ColumnType GridData::GetColumnType(uint32_t col_index) const {
  if (KS_UNLIKELY(col_index >= col_size_)) {
    return ad_index_meta::proto::ColumnType::UNKNOWN_COLUMN;
  } else {
    return data_->columns(col_index).type();
  }
}

ad_index_meta::proto::ColumnType GridData::GetColumnType(const std::string& col_name) const {
  if (KS_UNLIKELY(col_index_ == nullptr)) {
    return ad_index_meta::proto::ColumnType::UNKNOWN_COLUMN;
  }
  auto col_index = col_index_->find(col_name);
  if (col_index == col_index_->end()) {
    return ad_index_meta::proto::ColumnType::UNKNOWN_COLUMN;
  } else {
    return GetColumnType(col_index->second);
  }
}

bool GridData::IsBool(uint32_t row_index, uint32_t col_index) const {
  return IsType<bool>(row_index, col_index);
}
bool GridData::GetBool(uint32_t row_index, uint32_t col_index) const {
  return Get<bool>(row_index, col_index);
}

bool GridData::GetBool(uint32_t row_index, const std::string& col_name) const {
  return Get<bool>(row_index, col_name);
}

bool GridData::IsInt8(uint32_t row_index, uint32_t col_index) const {
  return IsType<int8_t>(row_index, col_index);
}
int8_t GridData::GetInt8(uint32_t row_index, uint32_t col_index) const {
  return Get<int8_t>(row_index, col_index);
}
int8_t GridData::GetInt8(uint32_t row_index, const std::string& col_name) const {
  return Get<int8_t>(row_index, col_name);
}

bool GridData::IsInt16(uint32_t row_index, uint32_t col_index) const {
  return IsType<int16_t>(row_index, col_index);
}
int16_t GridData::GetInt16(uint32_t row_index, uint32_t col_index) const {
  return Get<int16_t>(row_index, col_index);
}
int16_t GridData::GetInt16(uint32_t row_index, const std::string& col_name) const {
  return Get<int16_t>(row_index, col_name);
}

bool GridData::IsInt32(uint32_t row_index, uint32_t col_index) const {
  return IsType<int32_t>(row_index, col_index);
}
int32_t GridData::GetInt32(uint32_t row_index, uint32_t col_index) const {
  return Get<int32_t>(row_index, col_index);
}
int32_t GridData::GetInt32(uint32_t row_index, const std::string& col_name) const {
  return Get<int32_t>(row_index, col_name);
}

bool GridData::IsInt64(uint32_t row_index, uint32_t col_index) const {
  return IsType<int64_t>(row_index, col_index);
}
int64_t GridData::GetInt64(uint32_t row_index, uint32_t col_index) const {
  return Get<int64_t>(row_index, col_index);
}
int64_t GridData::GetInt64(uint32_t row_index, const std::string& col_name) const {
  return Get<int64_t>(row_index, col_name);
}

bool GridData::IsFloat(uint32_t row_index, uint32_t col_index) const {
  return IsType<float>(row_index, col_index);
}
float GridData::GetFloat(uint32_t row_index, uint32_t col_index) const {
  return Get<float>(row_index, col_index);
}
float GridData::GetFloat(uint32_t row_index, const std::string& col_name) const {
  return Get<float>(row_index, col_name);
}

bool GridData::IsDouble(uint32_t row_index, uint32_t col_index) const {
  return IsType<double>(row_index, col_index);
}
double GridData::GetDouble(uint32_t row_index, uint32_t col_index) const {
  return Get<double>(row_index, col_index);
}
double GridData::GetDouble(uint32_t row_index, const std::string& col_name) const {
  return Get<double>(row_index, col_name);
}

bool GridData::IsInt8List(uint32_t row_index, uint32_t col_index) const {
  return IsType<std::vector<int8_t>>(row_index, col_index);
}
bool GridData::GetInt8List(uint32_t row_index, uint32_t col_index,
                            std::vector<int8_t>* data) const {
  return GetList<std::vector<int8_t>>(row_index, col_index, data);
}
bool GridData::GetInt8List(uint32_t row_index, const std::string& col_name,
                            std::vector<int8_t>* data) const {
  return GetList<std::vector<int8_t>>(row_index, col_name, data);
}

bool GridData::IsInt16List(uint32_t row_index, uint32_t col_index) const {
  return IsType<std::vector<int16_t>>(row_index, col_index);
}
bool GridData::GetInt16List(uint32_t row_index, uint32_t col_index, std::vector<int16_t>* data) const {
  return GetList<std::vector<int16_t>>(row_index, col_index, data);
}
bool GridData::GetInt16List(uint32_t row_index, const std::string& col_name,
                             std::vector<int16_t>* data) const {
  return GetList<std::vector<int16_t>>(row_index, col_name, data);
}

bool GridData::IsInt32List(uint32_t row_index, uint32_t col_index) const {
  return IsType<std::vector<int32_t>>(row_index, col_index);
}
bool GridData::GetInt32List(uint32_t row_index, uint32_t col_index, std::vector<int32_t>* data) const {
  return GetList<std::vector<int32_t>>(row_index, col_index, data);
}
bool GridData::GetInt32List(uint32_t row_index, const std::string& col_name,
                             std::vector<int32_t>* data) const {
  return GetList<std::vector<int32_t>>(row_index, col_name, data);
}


bool GridData::IsInt64List(uint32_t row_index, uint32_t col_index) const {
  return IsType<std::vector<int64_t>>(row_index, col_index);
}
bool GridData::GetInt64List(uint32_t row_index, uint32_t col_index, std::vector<int64_t>* data) const {
  return GetList<std::vector<int64_t>>(row_index, col_index, data);
}
bool GridData::GetInt64List(uint32_t row_index, const std::string& col_name,
                             std::vector<int64_t>* data) const {
  return GetList<std::vector<int64_t>>(row_index, col_name, data);
}

bool GridData::IsFloatList(uint32_t row_index, uint32_t col_index) const {
  return IsType<std::vector<float>>(row_index, col_index);
}
bool GridData::GetFloatList(uint32_t row_index, uint32_t col_index, std::vector<float>* data) const {
  return GetList<std::vector<float>>(row_index, col_index, data);
}
bool GridData::GetFloatList(uint32_t row_index, const std::string& col_name,
                             std::vector<float>* data) const {
  return GetList<std::vector<float>>(row_index, col_name, data);
}

bool GridData::IsDoubleList(uint32_t row_index, uint32_t col_index) const {
  return IsType<std::vector<double>>(row_index, col_index);
}
bool GridData::GetDoubleList(uint32_t row_index, uint32_t col_index, std::vector<double>* data) const {
  return GetList<std::vector<double>>(row_index, col_index, data);
}
bool GridData::GetDoubleList(uint32_t row_index, const std::string& col_name,
                              std::vector<double>* data) const {
  return GetList<std::vector<double>>(row_index, col_name, data);
}


bool GridData::IsStringList(uint32_t row_index, uint32_t col_index) const {
  return IsType<std::vector<std::string>>(row_index, col_index);
}
bool GridData::GetStringList(uint32_t row_index, uint32_t col_index, std::vector<std::string>* data) const {
  return GetList<std::vector<std::string>>(row_index, col_index, data);
}
bool GridData::GetStringList(uint32_t row_index, const std::string& col_name,
                              std::vector<std::string>* data) const {
  return GetList<std::vector<std::string>>(row_index, col_name, data);
}

bool GridData::IsString(uint32_t row_index, uint32_t col_index) const {
  return IsType<std::string>(row_index, col_index);
}
bool GridData::GetString(uint32_t row_index, uint32_t col_index, std::string* data) const {
  return GetList<std::string>(row_index, col_index, data);
}
bool GridData::GetString(uint32_t row_index, const std::string& col_name, std::string* data) const {
  return GetList<std::string>(row_index, col_name, data);
}

void GridData::Perf() {
  if (stat_ == nullptr) return;
  if (stat_->invalid_index) {
     GRID_RATIO_PERF_INTERVAL_LOG_STASH(stat_->invalid_index, "sdk.get", "invalid_index", table_name);
     stat_->invalid_index = 0;
  }
  for (int i = 0; i < col_size_; ++i) {
    if (stat_->invalid_type[i]) {
      GRID_RATIO_PERF_INTERVAL_LOG_STASH(stat_->invalid_type[i], "sdk.get",
                              "invalid_type", table_name, (*index2col_)[i]);
      stat_->invalid_type[i] = 0;
    }
  }
  for (auto it : stat_->invalid_col) {
    GRID_RATIO_PERF_INTERVAL_LOG_STASH(it.second, "sdk.get", "invalid_col", table_name, it.first);
  }
  stat_->invalid_col.clear();
}

void GridData::Reset(Table* table, std::shared_ptr<GridResponse> rep) {
  if (table == nullptr || table == data_) {
    return;
  }
  data_ = table;
  rep_ = rep;
  guard_col_index_ = std::make_shared<absl::flat_hash_map<std::string, uint32_t>>();
  col_index_ = guard_col_index_.get();

  guard_index2col_ = std::make_shared<std::vector<std::string>>(table->columns_size());
  index2col_ = guard_index2col_.get();

  guard_stat_ = std::make_shared<Stat>();
  stat_ = guard_stat_.get();
  stat_->invalid_type.resize(table->columns_size(), 0);

  table_name = data_->name();

  auto columns = table->columns();
  for (int i = 0; i < columns.size(); ++i) {
    (*col_index_)[columns[i].name()] = i;
    (*index2col_)[i] = columns[i].name();
  }
  row_size_ = table->rows_size();
  col_size_ = table->columns_size();
}

bool GridData::ResetFB(std::shared_ptr<std::vector<FbRow>> rows,
            std::shared_ptr<std::vector<ColumnDesc>> columns,
            const std::string& name) {
  if (rows == nullptr || columns == nullptr) {
    return false;
  }

  enable_fb_ = true;
  guard_data_fb_ = rows;
  data_fb_ = rows.get();
  guard_col_ = columns;
  col_ = columns.get();

  guard_col_index_ = std::make_shared<absl::flat_hash_map<std::string, uint32_t>>();
  col_index_ = guard_col_index_.get();

  guard_index2col_ = std::make_shared<std::vector<std::string>>(columns->size());
  index2col_ = guard_index2col_.get();

  guard_stat_ = std::make_shared<Stat>();
  stat_ = guard_stat_.get();
  stat_->invalid_type.resize(columns->size(), 0);

  table_name = name;

  for (int i = 0; i < columns->size(); ++i) {
    (*col_index_)[(*columns)[i].col_name] = i;
    (*index2col_)[i] = (*columns)[i].col_name;
  }
  row_size_ = rows->size();
  col_size_ = columns->size();
  return true;
}

void GridData::SetOffset(std::vector<int32_t>&& offset) {
  offset_ = std::move(offset);
}

std::string GridData::DebugString(int32_t row_index) const {
  if (!IsValid(row_index)) {
    return "";
  }
  std::stringstream ss;
  for (int i = 0; i < ColSize(); ++i) {
    std::string& col_name = (*col_)[i].col_name;
    switch ((*col_)[i].type) {
      case ad_index_meta::proto::ColumnType::INT32: {
        ss << col_name << " : " << GetInt32(row_index, i) << "\t";
        break;
      }
      case ad_index_meta::proto::ColumnType::INT64: {
        ss << col_name << " : " << GetInt64(row_index, i) << "\t";
        break;
      }
      case ad_index_meta::proto::ColumnType::FLOAT: {
        ss << col_name << " : " << GetFloat(row_index, i) << "\t";
        break;
      }
      case ad_index_meta::proto::ColumnType::DOUBLE: {
        ss << col_name << " : " << GetDouble(row_index, i) << "\t";
        break;
      }
      case ad_index_meta::proto::ColumnType::STRING: {
        std::string str;
        GetString(row_index, i, &str);
        ss << col_name << " : " << str << "\t";
        break;
      }
      case ad_index_meta::proto::ColumnType::INT32_LIST: {
        std::vector<int32_t> v;
        GetInt32List(row_index, i, &v);
        ss << col_name << " : " << Vec2Str(v) << "\t";
        break;
      }
      case ad_index_meta::proto::ColumnType::INT64_LIST: {
        std::vector<int64_t> v;
        GetInt64List(row_index, i, &v);
        ss << col_name << " : " << Vec2Str(v) << "\t";
        break;
      }
      case ad_index_meta::proto::ColumnType::FLOAT_LIST: {
        std::vector<float> v;
        GetFloatList(row_index, i, &v);
        ss << col_name << " : " << Vec2Str(v) << "\t";
        break;
      }
      case ad_index_meta::proto::ColumnType::DOUBLE_LIST: {
        std::vector<double> v;
        GetDoubleList(row_index, i, &v);
        ss << col_name << " : " << Vec2Str(v) << "\t";
        break;
      }
      case ad_index_meta::proto::ColumnType::STRING_LIST: {
        std::vector<std::string> v;
        GetStringList(row_index, i, &v);
        ss << col_name << " : " << Vec2Str(v) << "\t";
        break;
      }
      default: {
        ss << col_name << " : " << "unsupport type" << "\t";
      }
    }
  }
  return ss.str();
}
}  // namespace grid
}  // namespace ks
