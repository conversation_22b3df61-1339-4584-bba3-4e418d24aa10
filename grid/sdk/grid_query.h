#pragma once
#include <vector>
#include <string>
#include "teams/ad/grid/proto/common.pb.h"

namespace ks {
namespace grid {

struct key128 {
  int64_t high;
  int64_t low;
};

//  数据请求主键类型
union Querykey {
  int32_t i32;
  int64_t i64;

  uint32_t u32;
  uint64_t u64;
  key128 i128;
};

//  数据请求数据结构
struct GridQuery {
  void CalculateMd5();
  std::string DebugString() const;

  std::string table_name;
  std::vector<std::string> select_fields;
  std::vector<Querykey> ids;
  std::string md5;
  std::string index_name;
  proto::IndexOP op = proto::IndexOP::UNION;
  int32_t limit = 0;
};
}  // namespace grid
}  // namespace ks
