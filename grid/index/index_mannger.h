#pragma once

#include <memory>
#include <string>
#include <vector>
#include "absl/container/flat_hash_map.h"
#include "teams/ad/ad_feature_index/proto/msg.pb.h"
#include "teams/ad/grid/proto/msg.pb.h"
#include "teams/ad/grid/utils/metric.h"
#include "teams/ad/ad_table/table_env/ad_table_env.h"
#include "teams/ad/ad_table/table_env/loader.h"
#include "teams/ad/ad_table/table_env/ad_table_env_options.h"
#include "teams/ad/ad_table/table_env/loader_options.h"
#include "teams/ad/grid/proto/grid_generated.h"

using ks::ad_table::AdTableEnv;
using ks::ad_table::DataLoader;
using ks::ad_table::LoadOptions;
using ks::ad_table::AdTableEnvOptions;

namespace ks {
namespace grid {

class DB;
using DBPtr = std::shared_ptr<DB>;

class DB {
 public:
  explicit DB(AdTableEnvOptions env_opt, const LoadOptions& config = LoadOptions());
  ~DB() = default;

  std::shared_ptr<proto::Table>
    BatchGetTable(const std::string& table_name,
                  const proto::KVQuery* request,
                  Mertric::StatInfo* stat,
                  const std::string& caller);

  flatbuffers::Offset<flat::GridTable>
    BatchGetTableFB(const std::string& table_name,
                    const proto::KVQuery* request,
                    Mertric::StatInfo* stat,
                    flatbuffers::FlatBufferBuilder* builder,
                    const std::string& caller);   // NOLINT

  // 兼容 ad-feature-index 接口
  // TODO(tanweibin) 迁移完毕后代码删除
  bool GetSchemaFreeData(const std::string& table_name,
                         const ::ks::ad_feature_index::proto::GetSchemaFreeDataReq& req,
                         ::ks::ad_feature_index::proto::GetSchemaFreeDataResp* resp);

  const AdTableEnv& Env() const { return *table_env_; }

 private:
  friend class DBManager;
  friend class AdTableIndexKVHandler;

  std::shared_ptr<AdTableEnv> table_env_;
  std::shared_ptr<DataLoader> loader_;

  // 兼容 ad-feature-index 接口
  // TODO(tanweibin) 迁移完毕后代码删除
  static const absl::flat_hash_map<std::string, std::vector<std::string>> kLogicTableMap;
};

class DBManager {
 public:
  static DBManager& GetInstance();
  bool Init();

  std::shared_ptr<DB> GetDB(const std::string& table_name) const;
  DBManager(const DBManager&) = delete;
  DBManager& operator=(const DBManager&) = delete;

 private:
  DBManager() = default;
  ~DBManager() = default;

  absl::flat_hash_map<std::string, DBPtr> db_map_;
};
}  // namespace grid
}  // namespace ks
