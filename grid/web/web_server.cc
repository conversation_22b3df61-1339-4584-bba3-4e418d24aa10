#include "absl/strings/numbers.h"
#include "teams/ad/ad_table/utils/json_util.h"
#include "teams/ad/grid/web/web_server.h"
#include "teams/ad/grid/index/index_mannger.h"

namespace ks {
namespace grid {

void AdTableIndexKVHandler::ProcessRequest(net::HTTPSession* session) {
  nlohmann::json json;
  json["status"] = "failed";
  do {
    if (!params_.count("table") || !params_.count("key")) {
      json["error_info"] = "params need: table, key";
      continue;
    }
    int64_t key = 0;
    if (!absl::SimpleAtoi(params_["key"], &key)) {
      json["error_info"] = "key parse failed";
      continue;
    }

    std::string table_name{params_["table"]};
    auto db = ks::grid::DBManager::GetInstance().GetDB(table_name);
    if (db == nullptr) {
      json["error_info"] = "db not find";
      continue;
    }
    auto* table = db->table_env_->Lookup(params_["table"]);

    if (!table) {
      json["error_info"] = "table not find";
      continue;
    }
    auto row_wrapper = table->Find(key);
    if (!row_wrapper) {
      json["error_info"] = "key not find";
      continue;
    }
    json["status"] = "success";
    json["info"] = RowToJson(table->GetTableSchema(), row_wrapper);
  } while (false);
  session->response.data = nlohmann::to_string(json);
}

}  // namespace grid
}  // namespace ks
