#pragma once
#include <sstream>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/common/basic_types.h"
#include "serving_base/utility/web_request_handler.h"

namespace ks {
namespace grid {

class AdTableIndexKVHandler : public serving_base::WebRequestHandler {
 public:
  AdTableIndexKVHandler() {}
  void ProcessRequest(net::HTTPSession* session) override;

 private:
  DISALLOW_COPY_AND_ASSIGN(AdTableIndexKVHandler);
};

}  // namespace grid
}  // namespace ks
