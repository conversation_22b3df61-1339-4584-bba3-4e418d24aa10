syntax = "proto3";
package ks.grid.proto;

import "teams/ad/grid/proto/common.proto";

enum ProtoType {
  PB = 0;
  FB = 1;
};

enum Version {
  V1 = 0;  // 默认值
  V2 = 1;
}

message GridRequest {
  repeated KVQuery kvquerys = 1;
  ProtoType type = 2;
  map<string, KVQuery> querys = 3;
  // 正倒排一体化查询请求
  repeated GridQuery grid_querys = 4;
  Version version = 5;
  string caller = 6;
}

message GridResponse {
  repeated Table tables = 1;
  ProtoType type = 2;
  repeated bytes blocks = 3; // 待迁移 sdk 后可废弃

  // 正倒排一体化查询结果
  repeated GridResult results = 4;
}

service GridService {
  rpc BatchGetTable(GridRequest) returns (GridResponse);
}
