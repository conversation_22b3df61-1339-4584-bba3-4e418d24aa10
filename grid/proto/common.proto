syntax = "proto3";
package ks.grid.proto;
option cc_enable_arenas = true;

import "teams/ad/ad_proto/kuaishou/ad/ad_index_meta/table_schema.proto";

message Value {
	bool bool_value = 1;
	int32 i8 = 2;
  int32 i16 = 3;
	int32 i32 = 4;
  int64 i64 = 5;
   
  uint32 u8 = 6;
  uint32 u16 = 7;
  uint32 u32 = 8;
  uint64 u64 = 9;
  
  float float_value = 10;
  double double_value = 11;
  
  bytes string_value = 12;
  repeated int32 int8_list_value = 13;
  repeated int32 int16_list_value = 14;
  repeated int32 int32_list_value = 15;
  repeated int64 int64_list_value = 16;
  repeated float float_list_value = 17;
  repeated double double_list_value = 18;
  repeated bytes string_list_value = 19;
}

message Key {
  int32 i32 = 1;
  int64 i64 = 2;
}

message Column {
  string name = 1;
	ks.ad_index_meta.proto.ColumnType type = 2;
}

message Row {
	repeated Value data = 3;
}

message KVQuery {
  string table_name = 1;
  repeated string select_fields = 2;
  repeated Key keys = 3; // 待迁移
  string field = 4; // 废弃
  string md5 = 5;
}

message Table {
	string name = 1;
  repeated Column columns = 2;
  repeated Row rows = 3;
}

enum IndexOP {
  UNION = 0;  // 不支持缓存
  UNION_ALL = 1;  // 支持缓存
};

// 正倒排一体化查询 Query & Result
message GridQuery {
  message KeyArray {
    uint32 width = 1;
    bytes buf = 2;
  }

  string table_name = 1;
  string index_name = 2;
  repeated string select_fields = 3;
  KeyArray keys = 4;
  uint32 limit = 5;
  IndexOP op = 6;
  string md5 = 10;
}

message GridResult {
  repeated bytes blocks = 1;
  repeated uint32 offsets = 2;
}

