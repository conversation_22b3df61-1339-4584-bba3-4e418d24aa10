// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_GRID_KS_GRID_FLAT_H_
#define FLATBUFFERS_GENERATED_GRID_KS_GRID_FLAT_H_

#include "flatbuffers/flatbuffers.h"

namespace ks {
namespace grid {
namespace flat {

struct Value;
struct ValueT;

struct Column;
struct ColumnT;

struct Row;
struct RowT;

struct GridTable;
struct GridTableT;

struct Resp;
struct RespT;

inline const flatbuffers::TypeTable *ValueTypeTable();

inline const flatbuffers::TypeTable *ColumnTypeTable();

inline const flatbuffers::TypeTable *RowTypeTable();

inline const flatbuffers::TypeTable *GridTableTypeTable();

inline const flatbuffers::TypeTable *RespTypeTable();

enum ColumnType {
  ColumnType_UNKNOWN_COLUMN = 0,
  ColumnType_BOOL = 1,
  ColumnType_INT32 = 2,
  ColumnType_INT64 = 3,
  ColumnType_FLOAT = 4,
  ColumnType_DOUBLE = 5,
  ColumnType_STRING = 6,
  ColumnType_INT32_LIST = 7,
  ColumnType_INT64_LIST = 8,
  ColumnType_FLOAT_LIST = 9,
  ColumnType_DOUBLE_LIST = 10,
  ColumnType_STRING_LIST = 11,
  ColumnType_TS_LIST = 12,
  ColumnType_INT8 = 13,
  ColumnType_INT16 = 14,
  ColumnType_INT8_LIST = 15,
  ColumnType_INT16_LIST = 16,
  ColumnType_MIN = ColumnType_UNKNOWN_COLUMN,
  ColumnType_MAX = ColumnType_INT16_LIST
};

inline const ColumnType (&EnumValuesColumnType())[17] {
  static const ColumnType values[] = {
    ColumnType_UNKNOWN_COLUMN,
    ColumnType_BOOL,
    ColumnType_INT32,
    ColumnType_INT64,
    ColumnType_FLOAT,
    ColumnType_DOUBLE,
    ColumnType_STRING,
    ColumnType_INT32_LIST,
    ColumnType_INT64_LIST,
    ColumnType_FLOAT_LIST,
    ColumnType_DOUBLE_LIST,
    ColumnType_STRING_LIST,
    ColumnType_TS_LIST,
    ColumnType_INT8,
    ColumnType_INT16,
    ColumnType_INT8_LIST,
    ColumnType_INT16_LIST
  };
  return values;
}

inline const char * const *EnumNamesColumnType() {
  static const char * const names[] = {
    "UNKNOWN_COLUMN",
    "BOOL",
    "INT32",
    "INT64",
    "FLOAT",
    "DOUBLE",
    "STRING",
    "INT32_LIST",
    "INT64_LIST",
    "FLOAT_LIST",
    "DOUBLE_LIST",
    "STRING_LIST",
    "TS_LIST",
    "INT8",
    "INT16",
    "INT8_LIST",
    "INT16_LIST",
    nullptr
  };
  return names;
}

inline const char *EnumNameColumnType(ColumnType e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesColumnType()[index];
}

struct ValueT : public flatbuffers::NativeTable {
  typedef Value TableType;
  bool bool_value;
  int8_t i8;
  int16_t i16;
  int32_t i32;
  int64_t i64;
  float float_value;
  double double_value;
  std::string string_value;
  std::vector<int8_t> int8_list_value;
  std::vector<int16_t> int16_list_value;
  std::vector<int32_t> int32_list_value;
  std::vector<int64_t> int64_list_value;
  std::vector<float> float_list_value;
  std::vector<double> double_list_value;
  std::vector<std::string> string_list_value;
  ValueT()
      : bool_value(false),
        i8(0),
        i16(0),
        i32(0),
        i64(0),
        float_value(0.0f),
        double_value(0.0) {
  }
};

struct Value FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef ValueT NativeTableType;
  static const flatbuffers::TypeTable *MiniReflectTypeTable() {
    return ValueTypeTable();
  }
  enum {
    VT_BOOL_VALUE = 4,
    VT_I8 = 6,
    VT_I16 = 8,
    VT_I32 = 10,
    VT_I64 = 12,
    VT_FLOAT_VALUE = 14,
    VT_DOUBLE_VALUE = 16,
    VT_STRING_VALUE = 18,
    VT_INT8_LIST_VALUE = 20,
    VT_INT16_LIST_VALUE = 22,
    VT_INT32_LIST_VALUE = 24,
    VT_INT64_LIST_VALUE = 26,
    VT_FLOAT_LIST_VALUE = 28,
    VT_DOUBLE_LIST_VALUE = 30,
    VT_STRING_LIST_VALUE = 32
  };
  bool bool_value() const {
    return GetField<uint8_t>(VT_BOOL_VALUE, 0) != 0;
  }
  bool mutate_bool_value(bool _bool_value) {
    return SetField<uint8_t>(VT_BOOL_VALUE, static_cast<uint8_t>(_bool_value), 0);
  }
  int8_t i8() const {
    return GetField<int8_t>(VT_I8, 0);
  }
  bool mutate_i8(int8_t _i8) {
    return SetField<int8_t>(VT_I8, _i8, 0);
  }
  int16_t i16() const {
    return GetField<int16_t>(VT_I16, 0);
  }
  bool mutate_i16(int16_t _i16) {
    return SetField<int16_t>(VT_I16, _i16, 0);
  }
  int32_t i32() const {
    return GetField<int32_t>(VT_I32, 0);
  }
  bool mutate_i32(int32_t _i32) {
    return SetField<int32_t>(VT_I32, _i32, 0);
  }
  int64_t i64() const {
    return GetField<int64_t>(VT_I64, 0);
  }
  bool mutate_i64(int64_t _i64) {
    return SetField<int64_t>(VT_I64, _i64, 0);
  }
  float float_value() const {
    return GetField<float>(VT_FLOAT_VALUE, 0.0f);
  }
  bool mutate_float_value(float _float_value) {
    return SetField<float>(VT_FLOAT_VALUE, _float_value, 0.0f);
  }
  double double_value() const {
    return GetField<double>(VT_DOUBLE_VALUE, 0.0);
  }
  bool mutate_double_value(double _double_value) {
    return SetField<double>(VT_DOUBLE_VALUE, _double_value, 0.0);
  }
  const flatbuffers::String *string_value() const {
    return GetPointer<const flatbuffers::String *>(VT_STRING_VALUE);
  }
  flatbuffers::String *mutable_string_value() {
    return GetPointer<flatbuffers::String *>(VT_STRING_VALUE);
  }
  const flatbuffers::Vector<int8_t> *int8_list_value() const {
    return GetPointer<const flatbuffers::Vector<int8_t> *>(VT_INT8_LIST_VALUE);
  }
  flatbuffers::Vector<int8_t> *mutable_int8_list_value() {
    return GetPointer<flatbuffers::Vector<int8_t> *>(VT_INT8_LIST_VALUE);
  }
  const flatbuffers::Vector<int16_t> *int16_list_value() const {
    return GetPointer<const flatbuffers::Vector<int16_t> *>(VT_INT16_LIST_VALUE);
  }
  flatbuffers::Vector<int16_t> *mutable_int16_list_value() {
    return GetPointer<flatbuffers::Vector<int16_t> *>(VT_INT16_LIST_VALUE);
  }
  const flatbuffers::Vector<int32_t> *int32_list_value() const {
    return GetPointer<const flatbuffers::Vector<int32_t> *>(VT_INT32_LIST_VALUE);
  }
  flatbuffers::Vector<int32_t> *mutable_int32_list_value() {
    return GetPointer<flatbuffers::Vector<int32_t> *>(VT_INT32_LIST_VALUE);
  }
  const flatbuffers::Vector<int64_t> *int64_list_value() const {
    return GetPointer<const flatbuffers::Vector<int64_t> *>(VT_INT64_LIST_VALUE);
  }
  flatbuffers::Vector<int64_t> *mutable_int64_list_value() {
    return GetPointer<flatbuffers::Vector<int64_t> *>(VT_INT64_LIST_VALUE);
  }
  const flatbuffers::Vector<float> *float_list_value() const {
    return GetPointer<const flatbuffers::Vector<float> *>(VT_FLOAT_LIST_VALUE);
  }
  flatbuffers::Vector<float> *mutable_float_list_value() {
    return GetPointer<flatbuffers::Vector<float> *>(VT_FLOAT_LIST_VALUE);
  }
  const flatbuffers::Vector<double> *double_list_value() const {
    return GetPointer<const flatbuffers::Vector<double> *>(VT_DOUBLE_LIST_VALUE);
  }
  flatbuffers::Vector<double> *mutable_double_list_value() {
    return GetPointer<flatbuffers::Vector<double> *>(VT_DOUBLE_LIST_VALUE);
  }
  const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *string_list_value() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_STRING_LIST_VALUE);
  }
  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *mutable_string_list_value() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_STRING_LIST_VALUE);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_BOOL_VALUE) &&
           VerifyField<int8_t>(verifier, VT_I8) &&
           VerifyField<int16_t>(verifier, VT_I16) &&
           VerifyField<int32_t>(verifier, VT_I32) &&
           VerifyField<int64_t>(verifier, VT_I64) &&
           VerifyField<float>(verifier, VT_FLOAT_VALUE) &&
           VerifyField<double>(verifier, VT_DOUBLE_VALUE) &&
           VerifyOffset(verifier, VT_STRING_VALUE) &&
           verifier.Verify(string_value()) &&
           VerifyOffset(verifier, VT_INT8_LIST_VALUE) &&
           verifier.Verify(int8_list_value()) &&
           VerifyOffset(verifier, VT_INT16_LIST_VALUE) &&
           verifier.Verify(int16_list_value()) &&
           VerifyOffset(verifier, VT_INT32_LIST_VALUE) &&
           verifier.Verify(int32_list_value()) &&
           VerifyOffset(verifier, VT_INT64_LIST_VALUE) &&
           verifier.Verify(int64_list_value()) &&
           VerifyOffset(verifier, VT_FLOAT_LIST_VALUE) &&
           verifier.Verify(float_list_value()) &&
           VerifyOffset(verifier, VT_DOUBLE_LIST_VALUE) &&
           verifier.Verify(double_list_value()) &&
           VerifyOffset(verifier, VT_STRING_LIST_VALUE) &&
           verifier.Verify(string_list_value()) &&
           verifier.VerifyVectorOfStrings(string_list_value()) &&
           verifier.EndTable();
  }
  ValueT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(ValueT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<Value> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ValueT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct ValueBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_bool_value(bool bool_value) {
    fbb_.AddElement<uint8_t>(Value::VT_BOOL_VALUE, static_cast<uint8_t>(bool_value), 0);
  }
  void add_i8(int8_t i8) {
    fbb_.AddElement<int8_t>(Value::VT_I8, i8, 0);
  }
  void add_i16(int16_t i16) {
    fbb_.AddElement<int16_t>(Value::VT_I16, i16, 0);
  }
  void add_i32(int32_t i32) {
    fbb_.AddElement<int32_t>(Value::VT_I32, i32, 0);
  }
  void add_i64(int64_t i64) {
    fbb_.AddElement<int64_t>(Value::VT_I64, i64, 0);
  }
  void add_float_value(float float_value) {
    fbb_.AddElement<float>(Value::VT_FLOAT_VALUE, float_value, 0.0f);
  }
  void add_double_value(double double_value) {
    fbb_.AddElement<double>(Value::VT_DOUBLE_VALUE, double_value, 0.0);
  }
  void add_string_value(flatbuffers::Offset<flatbuffers::String> string_value) {
    fbb_.AddOffset(Value::VT_STRING_VALUE, string_value);
  }
  void add_int8_list_value(flatbuffers::Offset<flatbuffers::Vector<int8_t>> int8_list_value) {
    fbb_.AddOffset(Value::VT_INT8_LIST_VALUE, int8_list_value);
  }
  void add_int16_list_value(flatbuffers::Offset<flatbuffers::Vector<int16_t>> int16_list_value) {
    fbb_.AddOffset(Value::VT_INT16_LIST_VALUE, int16_list_value);
  }
  void add_int32_list_value(flatbuffers::Offset<flatbuffers::Vector<int32_t>> int32_list_value) {
    fbb_.AddOffset(Value::VT_INT32_LIST_VALUE, int32_list_value);
  }
  void add_int64_list_value(flatbuffers::Offset<flatbuffers::Vector<int64_t>> int64_list_value) {
    fbb_.AddOffset(Value::VT_INT64_LIST_VALUE, int64_list_value);
  }
  void add_float_list_value(flatbuffers::Offset<flatbuffers::Vector<float>> float_list_value) {
    fbb_.AddOffset(Value::VT_FLOAT_LIST_VALUE, float_list_value);
  }
  void add_double_list_value(flatbuffers::Offset<flatbuffers::Vector<double>> double_list_value) {
    fbb_.AddOffset(Value::VT_DOUBLE_LIST_VALUE, double_list_value);
  }
  void add_string_list_value(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> string_list_value) {
    fbb_.AddOffset(Value::VT_STRING_LIST_VALUE, string_list_value);
  }
  explicit ValueBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ValueBuilder &operator=(const ValueBuilder &);
  flatbuffers::Offset<Value> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Value>(end);
    return o;
  }
};

inline flatbuffers::Offset<Value> CreateValue(
    flatbuffers::FlatBufferBuilder &_fbb,
    bool bool_value = false,
    int8_t i8 = 0,
    int16_t i16 = 0,
    int32_t i32 = 0,
    int64_t i64 = 0,
    float float_value = 0.0f,
    double double_value = 0.0,
    flatbuffers::Offset<flatbuffers::String> string_value = 0,
    flatbuffers::Offset<flatbuffers::Vector<int8_t>> int8_list_value = 0,
    flatbuffers::Offset<flatbuffers::Vector<int16_t>> int16_list_value = 0,
    flatbuffers::Offset<flatbuffers::Vector<int32_t>> int32_list_value = 0,
    flatbuffers::Offset<flatbuffers::Vector<int64_t>> int64_list_value = 0,
    flatbuffers::Offset<flatbuffers::Vector<float>> float_list_value = 0,
    flatbuffers::Offset<flatbuffers::Vector<double>> double_list_value = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> string_list_value = 0) {
  ValueBuilder builder_(_fbb);
  builder_.add_double_value(double_value);
  builder_.add_i64(i64);
  builder_.add_string_list_value(string_list_value);
  builder_.add_double_list_value(double_list_value);
  builder_.add_float_list_value(float_list_value);
  builder_.add_int64_list_value(int64_list_value);
  builder_.add_int32_list_value(int32_list_value);
  builder_.add_int16_list_value(int16_list_value);
  builder_.add_int8_list_value(int8_list_value);
  builder_.add_string_value(string_value);
  builder_.add_float_value(float_value);
  builder_.add_i32(i32);
  builder_.add_i16(i16);
  builder_.add_i8(i8);
  builder_.add_bool_value(bool_value);
  return builder_.Finish();
}

inline flatbuffers::Offset<Value> CreateValueDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    bool bool_value = false,
    int8_t i8 = 0,
    int16_t i16 = 0,
    int32_t i32 = 0,
    int64_t i64 = 0,
    float float_value = 0.0f,
    double double_value = 0.0,
    const char *string_value = nullptr,
    const std::vector<int8_t> *int8_list_value = nullptr,
    const std::vector<int16_t> *int16_list_value = nullptr,
    const std::vector<int32_t> *int32_list_value = nullptr,
    const std::vector<int64_t> *int64_list_value = nullptr,
    const std::vector<float> *float_list_value = nullptr,
    const std::vector<double> *double_list_value = nullptr,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *string_list_value = nullptr) {
  return ks::grid::flat::CreateValue(
      _fbb,
      bool_value,
      i8,
      i16,
      i32,
      i64,
      float_value,
      double_value,
      string_value ? _fbb.CreateString(string_value) : 0,
      int8_list_value ? _fbb.CreateVector<int8_t>(*int8_list_value) : 0,
      int16_list_value ? _fbb.CreateVector<int16_t>(*int16_list_value) : 0,
      int32_list_value ? _fbb.CreateVector<int32_t>(*int32_list_value) : 0,
      int64_list_value ? _fbb.CreateVector<int64_t>(*int64_list_value) : 0,
      float_list_value ? _fbb.CreateVector<float>(*float_list_value) : 0,
      double_list_value ? _fbb.CreateVector<double>(*double_list_value) : 0,
      string_list_value ? _fbb.CreateVector<flatbuffers::Offset<flatbuffers::String>>(*string_list_value) : 0);
}

flatbuffers::Offset<Value> CreateValue(flatbuffers::FlatBufferBuilder &_fbb, const ValueT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ColumnT : public flatbuffers::NativeTable {
  typedef Column TableType;
  std::string name;
  ColumnType type;
  ColumnT()
      : type(ColumnType_UNKNOWN_COLUMN) {
  }
};

struct Column FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef ColumnT NativeTableType;
  static const flatbuffers::TypeTable *MiniReflectTypeTable() {
    return ColumnTypeTable();
  }
  enum {
    VT_NAME = 4,
    VT_TYPE = 6
  };
  const flatbuffers::String *name() const {
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String *mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
  ColumnType type() const {
    return static_cast<ColumnType>(GetField<int8_t>(VT_TYPE, 0));
  }
  bool mutate_type(ColumnType _type) {
    return SetField<int8_t>(VT_TYPE, static_cast<int8_t>(_type), 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.Verify(name()) &&
           VerifyField<int8_t>(verifier, VT_TYPE) &&
           verifier.EndTable();
  }
  ColumnT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(ColumnT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<Column> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ColumnT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct ColumnBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(Column::VT_NAME, name);
  }
  void add_type(ColumnType type) {
    fbb_.AddElement<int8_t>(Column::VT_TYPE, static_cast<int8_t>(type), 0);
  }
  explicit ColumnBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ColumnBuilder &operator=(const ColumnBuilder &);
  flatbuffers::Offset<Column> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Column>(end);
    return o;
  }
};

inline flatbuffers::Offset<Column> CreateColumn(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name = 0,
    ColumnType type = ColumnType_UNKNOWN_COLUMN) {
  ColumnBuilder builder_(_fbb);
  builder_.add_name(name);
  builder_.add_type(type);
  return builder_.Finish();
}

inline flatbuffers::Offset<Column> CreateColumnDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    ColumnType type = ColumnType_UNKNOWN_COLUMN) {
  return ks::grid::flat::CreateColumn(
      _fbb,
      name ? _fbb.CreateString(name) : 0,
      type);
}

flatbuffers::Offset<Column> CreateColumn(flatbuffers::FlatBufferBuilder &_fbb, const ColumnT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct RowT : public flatbuffers::NativeTable {
  typedef Row TableType;
  int64_t id;
  std::vector<std::unique_ptr<ValueT>> data;
  RowT()
      : id(0) {
  }
};

struct Row FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef RowT NativeTableType;
  static const flatbuffers::TypeTable *MiniReflectTypeTable() {
    return RowTypeTable();
  }
  enum {
    VT_ID = 4,
    VT_DATA = 6
  };
  int64_t id() const {
    return GetField<int64_t>(VT_ID, 0);
  }
  bool mutate_id(int64_t _id) {
    return SetField<int64_t>(VT_ID, _id, 0);
  }
  const flatbuffers::Vector<flatbuffers::Offset<Value>> *data() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<Value>> *>(VT_DATA);
  }
  flatbuffers::Vector<flatbuffers::Offset<Value>> *mutable_data() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<Value>> *>(VT_DATA);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int64_t>(verifier, VT_ID) &&
           VerifyOffset(verifier, VT_DATA) &&
           verifier.Verify(data()) &&
           verifier.VerifyVectorOfTables(data()) &&
           verifier.EndTable();
  }
  RowT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(RowT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<Row> Pack(flatbuffers::FlatBufferBuilder &_fbb, const RowT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct RowBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_id(int64_t id) {
    fbb_.AddElement<int64_t>(Row::VT_ID, id, 0);
  }
  void add_data(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Value>>> data) {
    fbb_.AddOffset(Row::VT_DATA, data);
  }
  explicit RowBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  RowBuilder &operator=(const RowBuilder &);
  flatbuffers::Offset<Row> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Row>(end);
    return o;
  }
};

inline flatbuffers::Offset<Row> CreateRow(
    flatbuffers::FlatBufferBuilder &_fbb,
    int64_t id = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Value>>> data = 0) {
  RowBuilder builder_(_fbb);
  builder_.add_id(id);
  builder_.add_data(data);
  return builder_.Finish();
}

inline flatbuffers::Offset<Row> CreateRowDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    int64_t id = 0,
    const std::vector<flatbuffers::Offset<Value>> *data = nullptr) {
  return ks::grid::flat::CreateRow(
      _fbb,
      id,
      data ? _fbb.CreateVector<flatbuffers::Offset<Value>>(*data) : 0);
}

flatbuffers::Offset<Row> CreateRow(flatbuffers::FlatBufferBuilder &_fbb, const RowT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct GridTableT : public flatbuffers::NativeTable {
  typedef GridTable TableType;
  std::string name;
  std::vector<std::unique_ptr<ColumnT>> columns;
  std::vector<std::unique_ptr<RowT>> rows;
  GridTableT() {
  }
};

struct GridTable FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef GridTableT NativeTableType;
  static const flatbuffers::TypeTable *MiniReflectTypeTable() {
    return GridTableTypeTable();
  }
  enum {
    VT_NAME = 4,
    VT_COLUMNS = 6,
    VT_ROWS = 8
  };
  const flatbuffers::String *name() const {
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String *mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
  const flatbuffers::Vector<flatbuffers::Offset<Column>> *columns() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<Column>> *>(VT_COLUMNS);
  }
  flatbuffers::Vector<flatbuffers::Offset<Column>> *mutable_columns() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<Column>> *>(VT_COLUMNS);
  }
  const flatbuffers::Vector<flatbuffers::Offset<Row>> *rows() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<Row>> *>(VT_ROWS);
  }
  flatbuffers::Vector<flatbuffers::Offset<Row>> *mutable_rows() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<Row>> *>(VT_ROWS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.Verify(name()) &&
           VerifyOffset(verifier, VT_COLUMNS) &&
           verifier.Verify(columns()) &&
           verifier.VerifyVectorOfTables(columns()) &&
           VerifyOffset(verifier, VT_ROWS) &&
           verifier.Verify(rows()) &&
           verifier.VerifyVectorOfTables(rows()) &&
           verifier.EndTable();
  }
  GridTableT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(GridTableT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<GridTable> Pack(flatbuffers::FlatBufferBuilder &_fbb, const GridTableT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct GridTableBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(GridTable::VT_NAME, name);
  }
  void add_columns(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Column>>> columns) {
    fbb_.AddOffset(GridTable::VT_COLUMNS, columns);
  }
  void add_rows(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Row>>> rows) {
    fbb_.AddOffset(GridTable::VT_ROWS, rows);
  }
  explicit GridTableBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  GridTableBuilder &operator=(const GridTableBuilder &);
  flatbuffers::Offset<GridTable> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<GridTable>(end);
    return o;
  }
};

inline flatbuffers::Offset<GridTable> CreateGridTable(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Column>>> columns = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Row>>> rows = 0) {
  GridTableBuilder builder_(_fbb);
  builder_.add_rows(rows);
  builder_.add_columns(columns);
  builder_.add_name(name);
  return builder_.Finish();
}

inline flatbuffers::Offset<GridTable> CreateGridTableDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    const std::vector<flatbuffers::Offset<Column>> *columns = nullptr,
    const std::vector<flatbuffers::Offset<Row>> *rows = nullptr) {
  return ks::grid::flat::CreateGridTable(
      _fbb,
      name ? _fbb.CreateString(name) : 0,
      columns ? _fbb.CreateVector<flatbuffers::Offset<Column>>(*columns) : 0,
      rows ? _fbb.CreateVector<flatbuffers::Offset<Row>>(*rows) : 0);
}

flatbuffers::Offset<GridTable> CreateGridTable(flatbuffers::FlatBufferBuilder &_fbb, const GridTableT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct RespT : public flatbuffers::NativeTable {
  typedef Resp TableType;
  std::vector<std::unique_ptr<GridTableT>> tables;
  RespT() {
  }
};

struct Resp FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef RespT NativeTableType;
  static const flatbuffers::TypeTable *MiniReflectTypeTable() {
    return RespTypeTable();
  }
  enum {
    VT_TABLES = 4
  };
  const flatbuffers::Vector<flatbuffers::Offset<GridTable>> *tables() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<GridTable>> *>(VT_TABLES);
  }
  flatbuffers::Vector<flatbuffers::Offset<GridTable>> *mutable_tables() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<GridTable>> *>(VT_TABLES);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_TABLES) &&
           verifier.Verify(tables()) &&
           verifier.VerifyVectorOfTables(tables()) &&
           verifier.EndTable();
  }
  RespT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(RespT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<Resp> Pack(flatbuffers::FlatBufferBuilder &_fbb, const RespT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct RespBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_tables(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<GridTable>>> tables) {
    fbb_.AddOffset(Resp::VT_TABLES, tables);
  }
  explicit RespBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  RespBuilder &operator=(const RespBuilder &);
  flatbuffers::Offset<Resp> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Resp>(end);
    return o;
  }
};

inline flatbuffers::Offset<Resp> CreateResp(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<GridTable>>> tables = 0) {
  RespBuilder builder_(_fbb);
  builder_.add_tables(tables);
  return builder_.Finish();
}

inline flatbuffers::Offset<Resp> CreateRespDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<GridTable>> *tables = nullptr) {
  return ks::grid::flat::CreateResp(
      _fbb,
      tables ? _fbb.CreateVector<flatbuffers::Offset<GridTable>>(*tables) : 0);
}

flatbuffers::Offset<Resp> CreateResp(flatbuffers::FlatBufferBuilder &_fbb, const RespT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

inline ValueT *Value::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = new ValueT();
  UnPackTo(_o, _resolver);
  return _o;
}

inline void Value::UnPackTo(ValueT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = bool_value(); _o->bool_value = _e; };
  { auto _e = i8(); _o->i8 = _e; };
  { auto _e = i16(); _o->i16 = _e; };
  { auto _e = i32(); _o->i32 = _e; };
  { auto _e = i64(); _o->i64 = _e; };
  { auto _e = float_value(); _o->float_value = _e; };
  { auto _e = double_value(); _o->double_value = _e; };
  { auto _e = string_value(); if (_e) _o->string_value = _e->str(); };
  { auto _e = int8_list_value(); if (_e) { _o->int8_list_value.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->int8_list_value[_i] = _e->Get(_i); } } };
  { auto _e = int16_list_value(); if (_e) { _o->int16_list_value.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->int16_list_value[_i] = _e->Get(_i); } } };
  { auto _e = int32_list_value(); if (_e) { _o->int32_list_value.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->int32_list_value[_i] = _e->Get(_i); } } };
  { auto _e = int64_list_value(); if (_e) { _o->int64_list_value.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->int64_list_value[_i] = _e->Get(_i); } } };
  { auto _e = float_list_value(); if (_e) { _o->float_list_value.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->float_list_value[_i] = _e->Get(_i); } } };
  { auto _e = double_list_value(); if (_e) { _o->double_list_value.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->double_list_value[_i] = _e->Get(_i); } } };
  { auto _e = string_list_value(); if (_e) { _o->string_list_value.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->string_list_value[_i] = _e->Get(_i)->str(); } } };
}

inline flatbuffers::Offset<Value> Value::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ValueT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateValue(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<Value> CreateValue(flatbuffers::FlatBufferBuilder &_fbb, const ValueT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ValueT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _bool_value = _o->bool_value;
  auto _i8 = _o->i8;
  auto _i16 = _o->i16;
  auto _i32 = _o->i32;
  auto _i64 = _o->i64;
  auto _float_value = _o->float_value;
  auto _double_value = _o->double_value;
  auto _string_value = _o->string_value.empty() ? 0 : _fbb.CreateString(_o->string_value);
  auto _int8_list_value = _o->int8_list_value.size() ? _fbb.CreateVector(_o->int8_list_value) : 0;
  auto _int16_list_value = _o->int16_list_value.size() ? _fbb.CreateVector(_o->int16_list_value) : 0;
  auto _int32_list_value = _o->int32_list_value.size() ? _fbb.CreateVector(_o->int32_list_value) : 0;
  auto _int64_list_value = _o->int64_list_value.size() ? _fbb.CreateVector(_o->int64_list_value) : 0;
  auto _float_list_value = _o->float_list_value.size() ? _fbb.CreateVector(_o->float_list_value) : 0;
  auto _double_list_value = _o->double_list_value.size() ? _fbb.CreateVector(_o->double_list_value) : 0;
  auto _string_list_value = _o->string_list_value.size() ? _fbb.CreateVectorOfStrings(_o->string_list_value) : 0;
  return ks::grid::flat::CreateValue(
      _fbb,
      _bool_value,
      _i8,
      _i16,
      _i32,
      _i64,
      _float_value,
      _double_value,
      _string_value,
      _int8_list_value,
      _int16_list_value,
      _int32_list_value,
      _int64_list_value,
      _float_list_value,
      _double_list_value,
      _string_list_value);
}

inline ColumnT *Column::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = new ColumnT();
  UnPackTo(_o, _resolver);
  return _o;
}

inline void Column::UnPackTo(ColumnT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); };
  { auto _e = type(); _o->type = _e; };
}

inline flatbuffers::Offset<Column> Column::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ColumnT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateColumn(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<Column> CreateColumn(flatbuffers::FlatBufferBuilder &_fbb, const ColumnT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ColumnT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _type = _o->type;
  return ks::grid::flat::CreateColumn(
      _fbb,
      _name,
      _type);
}

inline RowT *Row::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = new RowT();
  UnPackTo(_o, _resolver);
  return _o;
}

inline void Row::UnPackTo(RowT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = id(); _o->id = _e; };
  { auto _e = data(); if (_e) { _o->data.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->data[_i] = std::unique_ptr<ValueT>(_e->Get(_i)->UnPack(_resolver)); } } };
}

inline flatbuffers::Offset<Row> Row::Pack(flatbuffers::FlatBufferBuilder &_fbb, const RowT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateRow(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<Row> CreateRow(flatbuffers::FlatBufferBuilder &_fbb, const RowT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const RowT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _id = _o->id;
  auto _data = _o->data.size() ? _fbb.CreateVector<flatbuffers::Offset<Value>> (_o->data.size(), [](size_t i, _VectorArgs *__va) { return CreateValue(*__va->__fbb, __va->__o->data[i].get(), __va->__rehasher); }, &_va ) : 0;
  return ks::grid::flat::CreateRow(
      _fbb,
      _id,
      _data);
}

inline GridTableT *GridTable::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = new GridTableT();
  UnPackTo(_o, _resolver);
  return _o;
}

inline void GridTable::UnPackTo(GridTableT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); };
  { auto _e = columns(); if (_e) { _o->columns.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->columns[_i] = std::unique_ptr<ColumnT>(_e->Get(_i)->UnPack(_resolver)); } } };
  { auto _e = rows(); if (_e) { _o->rows.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->rows[_i] = std::unique_ptr<RowT>(_e->Get(_i)->UnPack(_resolver)); } } };
}

inline flatbuffers::Offset<GridTable> GridTable::Pack(flatbuffers::FlatBufferBuilder &_fbb, const GridTableT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateGridTable(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<GridTable> CreateGridTable(flatbuffers::FlatBufferBuilder &_fbb, const GridTableT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const GridTableT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _columns = _o->columns.size() ? _fbb.CreateVector<flatbuffers::Offset<Column>> (_o->columns.size(), [](size_t i, _VectorArgs *__va) { return CreateColumn(*__va->__fbb, __va->__o->columns[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _rows = _o->rows.size() ? _fbb.CreateVector<flatbuffers::Offset<Row>> (_o->rows.size(), [](size_t i, _VectorArgs *__va) { return CreateRow(*__va->__fbb, __va->__o->rows[i].get(), __va->__rehasher); }, &_va ) : 0;
  return ks::grid::flat::CreateGridTable(
      _fbb,
      _name,
      _columns,
      _rows);
}

inline RespT *Resp::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = new RespT();
  UnPackTo(_o, _resolver);
  return _o;
}

inline void Resp::UnPackTo(RespT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = tables(); if (_e) { _o->tables.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->tables[_i] = std::unique_ptr<GridTableT>(_e->Get(_i)->UnPack(_resolver)); } } };
}

inline flatbuffers::Offset<Resp> Resp::Pack(flatbuffers::FlatBufferBuilder &_fbb, const RespT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateResp(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<Resp> CreateResp(flatbuffers::FlatBufferBuilder &_fbb, const RespT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const RespT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _tables = _o->tables.size() ? _fbb.CreateVector<flatbuffers::Offset<GridTable>> (_o->tables.size(), [](size_t i, _VectorArgs *__va) { return CreateGridTable(*__va->__fbb, __va->__o->tables[i].get(), __va->__rehasher); }, &_va ) : 0;
  return ks::grid::flat::CreateResp(
      _fbb,
      _tables);
}

inline const flatbuffers::TypeTable *ColumnTypeTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    ColumnTypeTypeTable
  };
  static const char * const names[] = {
    "UNKNOWN_COLUMN",
    "BOOL",
    "INT32",
    "INT64",
    "FLOAT",
    "DOUBLE",
    "STRING",
    "INT32_LIST",
    "INT64_LIST",
    "FLOAT_LIST",
    "DOUBLE_LIST",
    "STRING_LIST",
    "TS_LIST",
    "INT8",
    "INT16",
    "INT8_LIST",
    "INT16_LIST"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_ENUM, 17, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ValueTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_BOOL, 0, -1 },
    { flatbuffers::ET_CHAR, 0, -1 },
    { flatbuffers::ET_SHORT, 0, -1 },
    { flatbuffers::ET_INT, 0, -1 },
    { flatbuffers::ET_LONG, 0, -1 },
    { flatbuffers::ET_FLOAT, 0, -1 },
    { flatbuffers::ET_DOUBLE, 0, -1 },
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_CHAR, 1, -1 },
    { flatbuffers::ET_SHORT, 1, -1 },
    { flatbuffers::ET_INT, 1, -1 },
    { flatbuffers::ET_LONG, 1, -1 },
    { flatbuffers::ET_FLOAT, 1, -1 },
    { flatbuffers::ET_DOUBLE, 1, -1 },
    { flatbuffers::ET_STRING, 1, -1 }
  };
  static const char * const names[] = {
    "bool_value",
    "i8",
    "i16",
    "i32",
    "i64",
    "float_value",
    "double_value",
    "string_value",
    "int8_list_value",
    "int16_list_value",
    "int32_list_value",
    "int64_list_value",
    "float_list_value",
    "double_list_value",
    "string_list_value"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 15, type_codes, nullptr, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ColumnTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_CHAR, 0, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    ColumnTypeTypeTable
  };
  static const char * const names[] = {
    "name",
    "type"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 2, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *RowTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_LONG, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    ValueTypeTable
  };
  static const char * const names[] = {
    "id",
    "data"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 2, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *GridTableTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 0 },
    { flatbuffers::ET_SEQUENCE, 1, 1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    ColumnTypeTable,
    RowTypeTable
  };
  static const char * const names[] = {
    "name",
    "columns",
    "rows"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 3, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *RespTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 1, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    GridTableTypeTable
  };
  static const char * const names[] = {
    "tables"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 1, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

}  // namespace flat
}  // namespace grid
}  // namespace ks

#endif  // FLATBUFFERS_GENERATED_GRID_KS_GRID_FLAT_H_
