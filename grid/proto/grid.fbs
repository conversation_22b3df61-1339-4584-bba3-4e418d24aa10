namespace ks.grid.flat;

enum ColumnType:byte {
  UNKNOWN_COLUMN = 0,
  BOOL = 1,
  INT32 = 2,
  INT64 = 3,
  FLOAT = 4,
  DOUBLE = 5,
  STRING = 6,
  INT32_LIST = 7,
  INT64_LIST = 8,
  FLOAT_LIST = 9,
  DOUBLE_LIST = 10,
  STRING_LIST = 11,
  TS_LIST = 12,
  INT8 = 13,
  INT16 = 14,
  INT8_LIST = 15,
  INT16_LIST = 16
}

table Value {
  bool_value:bool;
  i8:int8;
  i16:int16;
  i32:int32;
  i64:int64;
  float_value:float;
  double_value:double;
  string_value:string;
  int8_list_value:[int8];
  int16_list_value:[int16];
  int32_list_value:[int32];
  int64_list_value:[int64];
  float_list_value:[float];
  double_list_value:[double];
  string_list_value:[string];
}

table Column {
  name:string;
  type:ColumnType;
}

table Row {
  id:int64;
  data:[Value];
}

table GridTable {
  name:string;
  columns:[Column];
  rows:[Row];
}

table Resp {
  tables:[GridTable];
}