#!/usr/bin/env python3
# coding=utf-8
from teams.ad.grid.remote_table.remote_table_mixer import (
    RemoteTableOptMixer,
    RemoteTableDiff,
    GridTableOptMixer,
)
from dragonfly.ext.common.common_api_mixin import CommonApiMixin

REQUEST_TABLE = "global_ad_table"
RESPONSE_TABLE = "global_ad_table"
SEPARATOR = ","


class RemoteGridMixin(CommonApiMixin):
    """
    """
    def request_remote_table_opt(self, **kwargs):
        self._add_processor(RemoteTableOptMixer(kwargs))
        return self
    
    """
    """
    def remote_table_diff(self, **kwargs):
        self._add_processor(RemoteTableDiff(kwargs))
        return self
        
    """
    """
    def request_grid_table_opt(self, **kwargs):
        self._add_processor(GridTableOptMixer(kwargs))
        return self