#!/usr/bin/env python3
# coding=utf-8
import hashlib

from dragonfly.common_leaf_processor import LeafMix<PERSON>
from dragonfly.common_leaf_util import check_arg, strict_types, try_add_table_name
from .remote_table_util import extract_local_attrs, extract_key_attrs

"""
"""
class RemoteTableOptMixer(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "remote_table_opt_mixer"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        """获取当前 Processor 依赖的 item_attr 输入"""
        attrs = set()
        request_tables = self._config.get("request_tables", {})
        for [req_table, req_table_config] in request_tables.items():
            table_schema = req_table_config.get("remote_table_relation", {})
            attr_names = extract_key_attrs(table_schema)
            for table in req_table_config.get("item_tables", []):
                attrs.update(try_add_table_name(table, attr_names))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        """获取当前 Processor 产生的 item_attr 输出"""
        attrs = set()
        request_tables = self._config.get("request_tables", {})
        for [req_table, req_table_config] in request_tables.items():
            table_schema = req_table_config.get("remote_table_relation", {})
            attr_names = extract_local_attrs(table_schema)
            attr_cnt = {}
            for attr_name in attr_names:
                attr_cnt[attr_name] = 0
            for attr_name in attr_names:
                attr_cnt[attr_name] = attr_cnt[attr_name] + 1
                check_arg(
                    attr_cnt[attr_name] == 1,
                    f" remote_table_relation 存在相同的 local_field: {attr_name}",
                )
            for table in req_table_config.get("item_tables", []):
                attrs.update(try_add_table_name(table, attr_names))
            ## gen sdk version
            remote_attr = []
            local_attr = []
            for [t_name, t_schema] in table_schema.items():
                for [f_name, f_alias] in t_schema.get("local_field_alias").items():
                    remote_attr.append(f"{t_name}:{f_name}")
                    local_attr.append(f"{req_table}:{f_alias}")
            remote_attr.sort()
            local_attr.sort()
            remote_attr_str = ",".join(remote_attr)
            local_attr_str = ",".join(local_attr)
            req_table_config["sdk_version"] = (
                hashlib.md5(remote_attr_str.encode("utf-8")).hexdigest().upper()
            )
            req_table_config["local_version"] = (
                hashlib.md5(local_attr_str.encode("utf-8")).hexdigest().upper()
            )
        return attrs

    @strict_types
    def is_async(self) -> bool:
        return self._config.get("save_async_status_to") != None

class RemoteTableDiff(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "remote_table_diff_mixer"
      
"""
"""
class GridTableOptMixer(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "grid_table_opt_mixer"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        """获取当前 Processor 依赖的 item_attr 输入"""
        attrs = set()
        request_tables = self._config.get("index_search_config", {})
        for [req_table, req_table_config] in request_tables.items():
            search_config = req_table_config.get("grid_search_config", {})
            search_config = search_config.get("query", {})
            key_table = search_config.get("key_table", "")
            key_attr = search_config.get("key_table_attr", "")
            attrs.update(try_add_table_name(key_table, [key_attr]))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        """获取当前 Processor 产生的 item_attr 输出"""
        attrs = set()
        filter = {}
        request_tables = self._config.get("index_search_config", {})
        for [req_table, req_table_config] in request_tables.items():
            item_table = req_table_config.get("item_table", "")
            if item_table not in filter:
                filter[item_table] = {}
            attr_names = []
            for table in req_table_config.get("result_table_schema", []):
                attr_name = table.get("col_name", "")
                check_arg(
                  attr_name not in filter[item_table],
                  f" item_table: {item_table} 存在相同的 attr_name: {attr_name}",
                )
                filter[item_table][attr_name] = 1
                attr_names.append(attr_name)
            attrs.update(try_add_table_name(item_table, attr_names))
        return attrs

    @strict_types
    def is_async(self) -> bool:
        return self._config.get("save_async_status_to") != None