#!/usr/bin/env python3
# coding=utf-8

def extract_key_attrs(remote_table_schema:dict)->list:
    attrs=[]
    for [t_name, t_schema] in remote_table_schema.items():
        attrs.append(t_schema.get("item_table_key_attr", ""))
    return attrs

def extract_local_attrs(remote_table_schema:dict)->list:
    attrs=[]
    for [t_name, t_schema] in remote_table_schema.items():
        for [f_name, f_alias] in t_schema.get("local_field_alias").items():
            attrs.append(f_alias)
    return attrs
