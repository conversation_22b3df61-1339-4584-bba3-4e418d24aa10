#pragma once
#include <string>
#include <utility>

#include "dragon/src/processor/base/common_reco_base_mixer.h"


namespace ks {
namespace platform {
struct AttrInfo {
  ks::platform::AttrValue* attr_value;
  ks::platform::AttrType attr_type;
  std::string data;

  AttrInfo(ks::platform::AttrValue* attr_value, ks::platform::AttrType attr_type, std::string&& data)
      : attr_value(attr_value), attr_type(attr_type), data(std::move(data)) {}
};

}  // namespace platform
}  // namespace ks
