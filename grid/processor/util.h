#pragma once
#include <cstdint>
#include <string>
#include <vector>
#include <utility>
#include "dragon/src/core/common_reco_base.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_index_meta/table_schema.pb.h"
#include "teams/ad/grid/sdk/grid_type_trait.h"
#include "teams/ad/grid/sdk/grid_data.h"
#include "teams/ad/grid/processor/common.h"

inline void SetInt64(std::string* data, size_t attr_index, int64_t v) {
  memcpy(const_cast<int64_t *>(reinterpret_cast<const int64_t *>(data->data()) + attr_index), &v,
           sizeof(int64_t));
}

inline void SetDouble(std::string* data, size_t attr_index, double v) {
  memcpy(const_cast<double *>(reinterpret_cast<const double *>(data->data()) + attr_index), &v,
           sizeof(double));
}

inline int64_t GetInt64(const ks::grid::GridData& grid_data,
                        int32_t ref_index,
                        int32_t ref_col_index,
                        ks::ad_index_meta::proto::ColumnType type) {
  switch (type) {
    case ks::ad_index_meta::proto::ColumnType::BOOL: {
      return grid_data.UnSafeGetBool(ref_index, ref_col_index);
    } break;
    case ks::ad_index_meta::proto::ColumnType::INT8: {
      return grid_data.UnSafeGetInt8(ref_index, ref_col_index);
    } break;
    case ks::ad_index_meta::proto::ColumnType::INT16: {
      return grid_data.UnSafeGetInt16(ref_index, ref_col_index);
    } break;
    case ks::ad_index_meta::proto::ColumnType::INT32: {
      return grid_data.UnSafeGetInt32(ref_index, ref_col_index);
    } break;
    case ks::ad_index_meta::proto::ColumnType::INT64: {
      return grid_data.UnSafeGetInt64(ref_index, ref_col_index);
    } break;
    default: {
      LOG_EVERY_N(WARNING, 1000) << "unsupport type:" << type;
      return 0;
    }
  }
}

inline double GetDouble(const ks::grid::GridData& grid_data,
                        int32_t ref_index,
                        int32_t ref_col_index,
                        ks::ad_index_meta::proto::ColumnType type) {
  switch (type) {
    case ks::ad_index_meta::proto::ColumnType::FLOAT: {
      return grid_data.UnSafeGetFloat(ref_index, ref_col_index);
    } break;
    case ks::ad_index_meta::proto::ColumnType::DOUBLE: {
      return grid_data.UnSafeGetDouble(ref_index, ref_col_index);
    } break;
    default: {
      LOG_EVERY_N(WARNING, 1000) << "unsupport type:" << type;
      return 0;
    }
  }
}

inline void GetInt64List(const ks::grid::GridData& grid_data,
                          int32_t ref_index,
                          int32_t ref_col_index,
                          ks::ad_index_meta::proto::ColumnType type,
                          std::vector<int64_t>* v) {
  switch (type) {
    case ks::ad_index_meta::proto::ColumnType::INT8_LIST: {
      grid_data.UnSafeGetInt64ListTrans<std::vector<int8_t>>(ref_index, ref_col_index, v);
    } break;
    case ks::ad_index_meta::proto::ColumnType::INT16_LIST: {
      grid_data.UnSafeGetInt64ListTrans<std::vector<int16_t>>(ref_index, ref_col_index, v);
    } break;
    case ks::ad_index_meta::proto::ColumnType::INT32_LIST: {
      grid_data.UnSafeGetInt64ListTrans<std::vector<int32_t>>(ref_index, ref_col_index, v);
    } break;
    case ks::ad_index_meta::proto::ColumnType::INT64_LIST: {
      grid_data.UnSafeGetInt64ListTrans<std::vector<int64_t>>(ref_index, ref_col_index, v);
    } break;
    default: {
      LOG_EVERY_N(WARNING, 1000) << "unsupport type:" << type;
    }
  }
}

inline void GetDoubleList(const ks::grid::GridData& grid_data,
                          int32_t ref_index,
                          int32_t ref_col_index,
                          ks::ad_index_meta::proto::ColumnType type,
                          std::vector<double>* v) {
  switch (type) {
    case ks::ad_index_meta::proto::ColumnType::FLOAT_LIST: {
      grid_data.UnSafeGetDoubleListTrans<std::vector<float>>(ref_index, ref_col_index, v);
    } break;
    case ks::ad_index_meta::proto::ColumnType::DOUBLE_LIST: {
      grid_data.UnSafeGetDoubleListTrans<std::vector<double>>(ref_index, ref_col_index, v);
    } break;
    default: {
      LOG_EVERY_N(WARNING, 1000) << "unsupport type:" << type;
    }
  }
}

inline void
  GetString(const ks::grid::GridData& grid_data,
            int32_t ref_index,
            int32_t ref_col_index,
            ks::ad_index_meta::proto::ColumnType type,
            std::string* s) {
  switch (type) {
    case ks::ad_index_meta::proto::ColumnType::STRING: {
      grid_data.UnSafeGetString(ref_index, ref_col_index, s);
    } break;
    default: {
      LOG_EVERY_N(WARNING, 1000) << "unsupport type:" << type;
    }
  }
  return;
}

inline void
  GetStringList(const ks::grid::GridData& grid_data,
                int32_t ref_index,
                int32_t ref_col_index,
                ks::ad_index_meta::proto::ColumnType type,
                std::vector<std::string>* v) {
  switch (type) {
    case ks::ad_index_meta::proto::ColumnType::STRING_LIST: {
      grid_data.UnSafeGetStringList(ref_index, ref_col_index, v);
    } break;
    default: {
      LOG_EVERY_N(WARNING, 1000) << "unsupport type:" << type;
    }
  }
}

inline int64_t GetInt64(const ks::grid::flat::Value* value,
                        ks::ad_index_meta::proto::ColumnType type) {
  switch (type) {
    case ks::ad_index_meta::proto::ColumnType::BOOL: {
      return ks::grid::GridTypeTrait<bool>::FbAccessor(value);
    } break;
    case ks::ad_index_meta::proto::ColumnType::INT8: {
      return ks::grid::GridTypeTrait<int8_t>::FbAccessor(value);
    } break;
    case ks::ad_index_meta::proto::ColumnType::INT16: {
      return ks::grid::GridTypeTrait<int16_t>::FbAccessor(value);
    } break;
    case ks::ad_index_meta::proto::ColumnType::INT32: {
      return ks::grid::GridTypeTrait<int32_t>::FbAccessor(value);
    } break;
    case ks::ad_index_meta::proto::ColumnType::INT64: {
      return ks::grid::GridTypeTrait<int64_t>::FbAccessor(value);
    } break;
    default: {
      LOG_EVERY_N(WARNING, 1000) << "unsupport type:" << type;
      return 0;
    }
  }
}

inline double GetDouble(const ks::grid::flat::Value* value,
                        ks::ad_index_meta::proto::ColumnType type) {
  switch (type) {
    case ks::ad_index_meta::proto::ColumnType::FLOAT: {
      return ks::grid::GridTypeTrait<float>::FbAccessor(value);
    } break;
    case ks::ad_index_meta::proto::ColumnType::DOUBLE: {
      return ks::grid::GridTypeTrait<double>::FbAccessor(value);
    } break;
    default: {
      LOG_EVERY_N(WARNING, 1000) << "unsupport type:" << type;
      return 0;
    }
  }
}

inline void GetInt64List(const ks::grid::flat::Value* value,
                          ks::ad_index_meta::proto::ColumnType type,
                          std::vector<int64_t>* v) {
  switch (type) {
    case ks::ad_index_meta::proto::ColumnType::INT8_LIST: {
      auto raw_data = ks::grid::GridTypeTrait<std::vector<int8_t>>::FbAccessor(value);
      if (raw_data != nullptr) {
        v->assign(raw_data->begin(), raw_data->end());
      }
    } break;
    case ks::ad_index_meta::proto::ColumnType::INT16_LIST: {
      auto raw_data = ks::grid::GridTypeTrait<std::vector<int16_t>>::FbAccessor(value);
      if (raw_data != nullptr) {
        v->assign(raw_data->begin(), raw_data->end());
      }
    } break;
    case ks::ad_index_meta::proto::ColumnType::INT32_LIST: {
      auto raw_data = ks::grid::GridTypeTrait<std::vector<int32_t>>::FbAccessor(value);
      if (raw_data != nullptr) {
        v->assign(raw_data->begin(), raw_data->end());
      }
    } break;
    case ks::ad_index_meta::proto::ColumnType::INT64_LIST: {
      auto raw_data = ks::grid::GridTypeTrait<std::vector<int64_t>>::FbAccessor(value);
      if (raw_data != nullptr) {
        v->assign(raw_data->begin(), raw_data->end());
      }
    } break;
    default: {
      LOG_EVERY_N(WARNING, 1000) << "unsupport type:" << type;
    }
  }
}

inline void GetDoubleList(const ks::grid::flat::Value* value,
                          ks::ad_index_meta::proto::ColumnType type,
                          std::vector<double>* v) {
  switch (type) {
    case ks::ad_index_meta::proto::ColumnType::FLOAT_LIST: {
      auto raw_data = ks::grid::GridTypeTrait<std::vector<float>>::FbAccessor(value);
      if (raw_data != nullptr) {
        v->assign(raw_data->begin(), raw_data->end());
      }
    } break;
    case ks::ad_index_meta::proto::ColumnType::DOUBLE_LIST: {
      auto raw_data = ks::grid::GridTypeTrait<std::vector<double>>::FbAccessor(value);
      if (raw_data != nullptr) {
        v->assign(raw_data->begin(), raw_data->end());
      }
    } break;
    default: {
      LOG_EVERY_N(WARNING, 1000) << "unsupport type:" << type;
    }
  }
}

inline void
  GetString(const ks::grid::flat::Value* value,
            ks::ad_index_meta::proto::ColumnType type,
            std::string* s) {
  switch (type) {
    case ks::ad_index_meta::proto::ColumnType::STRING: {
      auto raw_data = ks::grid::GridTypeTrait<std::string>::FbAccessor(value);
      if (raw_data != nullptr) {
        s->assign(raw_data->begin(), raw_data->end());
      }
    } break;
    default: {
      LOG_EVERY_N(WARNING, 1000) << "unsupport type:" << type;
    }
  }
  return;
}

inline void
  GetStringList(const ks::grid::flat::Value* value,
                ks::ad_index_meta::proto::ColumnType type,
                std::vector<std::string>* v) {
  switch (type) {
    case ks::ad_index_meta::proto::ColumnType::STRING_LIST: {
      auto raw_data = ks::grid::GridTypeTrait<std::vector<std::string>>::FbAccessor(value);
      if (raw_data == nullptr) {
        return;
      }
      v->reserve(raw_data->size());
      for (int i = 0; i < raw_data->size(); ++i) {
        auto str = raw_data->GetAsString(i);
        v->emplace_back(str->c_str(), str->Length());
      }
    } break;
    default: {
      LOG_EVERY_N(WARNING, 1000) << "unsupport type:" << type;
    }
  }
}

inline bool FillAttr(ks::platform::AttrInfo* attr,
                      const ks::grid::flat::Value* value,
                      int32_t ref_col_index,
                      size_t target_index,
                      ks::ad_index_meta::proto::ColumnType src_type) {
  auto attr_value = attr->attr_value;
  switch (attr->attr_type) {
    case ks::platform::AttrType::INT: {
      int64_t v = GetInt64(value, src_type);
      // if (!attr_value->SetIntValue(target_index, v)) {
      //   return false;
      // }
      SetInt64(&(attr->data), target_index, v);
    } break;
    case ks::platform::AttrType::FLOAT: {
      double v = GetDouble(value, src_type);
      // if (!attr_value->SetDoubleValue(target_index, v)) {
      //   return false;
      // }
      SetDouble(&(attr->data), target_index, v);
    } break;
    case ks::platform::AttrType::STRING: {
      std::string v;
      GetString(value, src_type, &v);
      if (!attr_value->SetStringValue(target_index, std::move(v))) {
        return false;
      }
    } break;
    case ks::platform::AttrType::INT_LIST: {
      std::vector<int64_t> v;
      GetInt64List(value, src_type, &v);
      if (!attr_value->SetIntListValue(target_index, std::move(v))) {
        return false;
      }
    } break;
    case ks::platform::AttrType::FLOAT_LIST: {
      std::vector<double> v;
      GetDoubleList(value, src_type, &v);
      if (!attr_value->SetDoubleListValue(target_index, std::move(v))) {
        return false;
      }
    } break;
    case ks::platform::AttrType::STRING_LIST: {
      std::vector<std::string> v;
      GetStringList(value, src_type, &v);
      if (!attr_value->SetStringListValue(target_index, std::move(v))) {
        return false;
      }
    } break;
    default:
      LOG_EVERY_N(WARNING, 1000) << "unsupport type:" << static_cast<int>(attr->attr_type);
      break;
  }
  return true;
}


inline bool FillAttrStand(ks::platform::AttrInfo* attr,
                      const ks::grid::flat::Value* value,
                      int32_t ref_col_index,
                      size_t target_index,
                      ks::ad_index_meta::proto::ColumnType src_type) {
  auto attr_value = attr->attr_value;
  switch (attr->attr_type) {
    case ks::platform::AttrType::INT: {
      int64_t v = GetInt64(value, src_type);
      if (!attr_value->SetIntValue(target_index, v)) {
        return false;
      }
    } break;
    case ks::platform::AttrType::FLOAT: {
      double v = GetDouble(value, src_type);
      if (!attr_value->SetDoubleValue(target_index, v)) {
        return false;
      }
    } break;
    case ks::platform::AttrType::STRING: {
      std::string v;
      GetString(value, src_type, &v);
      if (!attr_value->SetStringValue(target_index, std::move(v))) {
        return false;
      }
    } break;
    case ks::platform::AttrType::INT_LIST: {
      std::vector<int64_t> v;
      GetInt64List(value, src_type, &v);
      if (!attr_value->SetIntListValue(target_index, std::move(v))) {
        return false;
      }
    } break;
    case ks::platform::AttrType::FLOAT_LIST: {
      std::vector<double> v;
      GetDoubleList(value, src_type, &v);
      if (!attr_value->SetDoubleListValue(target_index, std::move(v))) {
        return false;
      }
    } break;
    case ks::platform::AttrType::STRING_LIST: {
      std::vector<std::string> v;
      GetStringList(value, src_type, &v);
      if (!attr_value->SetStringListValue(target_index, std::move(v))) {
        return false;
      }
    } break;
    default:
      LOG_EVERY_N(WARNING, 1000) << "unsupport type:" << static_cast<int>(attr->attr_type);
      break;
  }
  return true;
}
