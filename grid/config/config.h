#pragma once
#include <memory>
#include <string>
#include "teams/ad/grid/proto/config.pb.h"
#include "teams/ad/ad_base/src/kconf/kconf_node.h"

namespace ks {
namespace grid {
class TableRouterConfig : public ks::ad_base::kconf::KconfInitProto<
                               ks::grid::proto::TableRouterConfig> {
 public:
  bool Init() override;

  const absl::flat_hash_map<std::string, ks::grid::proto::TableMeta>&
    GetTable() const {
    return table_metas_;
  }

 private:
  absl::flat_hash_map<std::string, ks::grid::proto::TableMeta> table_metas_;
};

class CacheConfig : public ks::ad_base::kconf::KconfInitProto<ks::grid::proto::CacheConfig> {
 public:
  proto::CacheOptions GetCacheOptions(const std::string& caller) const;
  inline bool HasTableCache(const std::string& table_name) const {
    return call_2_cache_options_.count(table_name) > 0;
  }
  bool Init() override;

 private:
  absl::flat_hash_map<std::string, proto::CacheOptions> call_2_cache_options_;
};

class  GridKconf {
 public:
  static const ks::grid::proto::DBconfList& GetDBConfig();

  DEFINE_PROTOBUF_NODE_KCONF(CacheConfig, ad.grid, GridCacheConfigs)
  DEFINE_BOOL_KCONF_NODE(ad.grid, EnableDebug, false);
  DEFINE_BOOL_KCONF_NODE(ad.grid, EnableFullAsync, false);
  DEFINE_BOOL_KCONF_NODE(ad.grid, EnableRemoteTableOptParallel, false);
  DEFINE_BOOL_KCONF_NODE(ad.grid, EnableCacheParallel, false);
  DEFINE_INT32_KCONF_NODE(ad.grid, ParallelNum, 4);
  DEFINE_PROTOBUF_NODE_KCONF(ks::grid::TableRouterConfig, ad.grid, TableRouterConfig)
  DEFINE_INT32_KCONF_NODE(ad.grid, logMissFreq, 16384)
  DEFINE_BOOL_KCONF_NODE(ad.grid, EnablePerfFillRate, false)
  DEFINE_BOOL_KCONF_NODE(ad.grid, EnableUnifiedClient, false)
};

}  // namespace grid
}  // namespace ks
