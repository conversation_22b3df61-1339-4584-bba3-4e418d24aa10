import os

def exe_shell(shell):
  ret = os.system(shell)
  return ret

root_path = "teams/ad/grid/"
flat_gen_path = "{}/proto".format(root_path)
flat_bin = "third_party/prebuilt/bin/flatc"
flat_gen_flags = "--gen-mutable --reflect-names --gen-object-api"
flat_src_files = "{}/grid.fbs".format(flat_gen_path)
gen_cpp_cmd = "%s %s -I ./ -o %s --cpp %s" % (flat_bin, flat_gen_flags, flat_gen_path, flat_src_files)
ret = exe_shell(gen_cpp_cmd)
if not ret:
  print("grid exe_shell succ:", ret, gen_cpp_cmd);
else:
  print("grid exe_shell fail:", ret, gen_cpp_cmd);


ASAN_CPPFLAGS = [
  "-g -O0",
]
ASAN_LINKOPTS = [
  "-fsanitize=address",
]

proto_library(
  name = "grid_proto",
  srcs = [
    "proto/*.proto",
  ],
  deps = [
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_index_meta_table_schema__proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_index_meta_transport_schema__proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_index_meta_index_schema__proto",
  ],
  cppflags = [
    '-std=c++17',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)


cc_binary(
  name = 'grid_server',
  srcs = [
    'service/*.cc',
    'search/*.cc',
    'index/*.cc',
    'handler/*.cc',
    'utils/*.cc',
    'config/*.cc',
    'web/*.cc'
  ],
  deps = [
    ":grid_proto",
    ":grid_sdk",
    "//teams/ad/ad_feature_index/BUILD:ad_feature_index_proto",
    "//teams/ad/ad_table/BUILD:ad_table",
    "//base/thread/BUILD:thread",
    '//teams/ad/ad_base/src/pb_helper/BUILD:pb_helper',
    "//teams/ad/ad_base/src/kess/BUILD:kess_client",
    '//ks/serving_util/BUILD:serving_util',
    '//infra/kess_grpc-v1100/BUILD:kess-rpc',
    '//infra/kess_grpc-v1100/BUILD:kess-framework',
    '//third_party/abseil/BUILD:abseil',
    '//third_party/grpc-v1100/BUILD:grpc++_reflection',
    '//infra/traffic_record/BUILD:grpc_traffic_record',
    "//third_party/flatbuffers/BUILD:flatbuffers",
    "//serving_base/thp_component/BUILD:kthp",
  ],
  cppflags = [
    '-Ithird_party/abseil/',
    "-Ithird_party/flatbuffers/include/",
    '-std=c++17',
  ]
)

cc_library(
  name = 'grid_sdk',
  srcs = [
    'sdk/*.cc',
    'config/*.cc',
    'client/rpc/*.cc',
    "client/*.cc",
    'utils/*.cc',
  ],
  deps = [
    ":grid_proto",
    "//base/thread/BUILD:thread",
    '//teams/ad/ad_base/src/pb_helper/BUILD:pb_helper',
    "//teams/ad/ad_base/src/kess/BUILD:kess_client",
    '//ks/serving_util/BUILD:serving_util',
    '//third_party/abseil/BUILD:abseil',
    "//third_party/flatbuffers/BUILD:flatbuffers",
    '//third_party/tbb/BUILD:tbb',
  ],
  cppflags = [
    '-Ithird_party/abseil/',
    "-Ithird_party/flatbuffers/include/",
    '-std=c++17',
  ]
)

cc_library(
  name = "remote_table",
  srcs = [
    'processor/*.cc'
  ],
  deps = [
    ":grid_sdk",
    "//teams/ad/engine_base/BUILD:dragon_node_warpper",
    '//third_party/abseil/BUILD:abseil',
    "//dragon/src/BUILD:framework_base",
    '//infra/kess_grpc-v1100/BUILD:kess-rpc'
  ],
  cppflags = [
    '-std=c++17',
  ],
  link_all_symbols = True,
)

cc_binary(
  name = 'grid_query_tool',
  srcs = [
    'tools/*.cc',
  ],
  deps = [
    ":grid_proto",
    ":grid_sdk",
    "//teams/ad/ad_base/src/kess/BUILD:kess_client",
    "//third_party/flatbuffers/BUILD:flatbuffers",
    "//base/hash_function/BUILD:hash_function",
    "//base/encoding/BUILD:encoding",
  ],
  cppflags = [
    '-Ithird_party/abseil/',
    '-std=c++17',
  ],
)

cc_binary(
  name = 'grid_client_test',
  srcs = [
    'test/grid_client_test.cc',
  ],
  deps = [
    ":grid_sdk",
    "//teams/ad/ad_base/src/kess/BUILD:kess_client",
  ],
  cppflags = [
    '-Ithird_party/abseil/',
    '-std=c++17',
  ] + ASAN_CPPFLAGS,
  linkopts = ASAN_LINKOPTS,
)

cc_binary(
  name = 'fb_client_test',
  srcs = [
    'test/fb_client_test.cc',
  ],
  deps = [
    ":grid_sdk",
    "//teams/ad/ad_base/src/kess/BUILD:kess_client",
    "//third_party/flatbuffers/BUILD:flatbuffers",
  ],
  cppflags = [
    '-Ithird_party/abseil/',
    "-Ithird_party/flatbuffers/include/",
    '-std=c++17',
  ] + ASAN_CPPFLAGS,
  linkopts = ASAN_LINKOPTS,
)

cc_binary(
  name = 'index_client_test',
  srcs = [
    'test/index_client_test.cc',
  ],
  deps = [
    ":grid_sdk",
    ":grid_proto",
    "//teams/ad/ad_base/src/kess/BUILD:kess_client",
    "//third_party/flatbuffers/BUILD:flatbuffers",
    "//base/hash_function/BUILD:hash_function",
  ],
  cppflags = [
    '-Ithird_party/abseil/',
    "-Ithird_party/flatbuffers/include/",
    '-std=c++17',
  ] + ASAN_CPPFLAGS,
  linkopts = ASAN_LINKOPTS,
)

cc_binary(
  name = 'forward_client_test',
  srcs = [
    'test/forward_client_test.cc',
  ],
  deps = [
    ":grid_sdk",
    ":grid_proto",
    "//teams/ad/ad_base/src/kess/BUILD:kess_client",
    "//third_party/flatbuffers/BUILD:flatbuffers",
    "//base/hash_function/BUILD:hash_function",
  ],
  cppflags = [
    '-Ithird_party/abseil/',
    "-Ithird_party/flatbuffers/include/",
    '-std=c++17',
  ] + ASAN_CPPFLAGS,
  linkopts = ASAN_LINKOPTS,
)

cc_test(
  name = "sdk_unit_test",
  srcs = [
    'test/unittest/*.cc',
  ],
  deps = [
    ':grid_sdk',
    '//third_party/gtest/BUILD:gtest',
    '//third_party/glog/BUILD:glog',
    "//teams/ad/engine_base/BUILD:dragon_node_warpper",
    "//dragon/src/BUILD:framework_base",
  ],
  cppflags = [
    '-std=c++17',
    '-fno-access-control',
  ]+ ASAN_CPPFLAGS,
  linkopts = ASAN_LINKOPTS,
)

cc_binary(
  name = "benchmark_pb",
  srcs = [
    "test/benchmark/transform_schema.cc"
  ],
  deps = [
    ':grid_proto',
    "//third_party/benchmark/BUILD:benchmark",
    "//third_party/flatbuffers/BUILD:flatbuffers",
  ],
  cppflags = [
    '-std=c++17',
    "-Ithird_party/flatbuffers/include/",
  ],
)
