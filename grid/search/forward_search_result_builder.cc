#include "teams/ad/grid/search/forward_search_result_builder.h"
#include <algorithm>
#include <cstdint>
#include <string>
#include <vector>
#include "teams/ad/grid/proto/grid_generated.h"
#include "teams/ad/grid/utils/utils.h"
#include "absl/types/span.h"

namespace ks {
namespace grid {

bool ForwardSearchResultBuilder::Build() {
  if (!result_) {
    return false;
  }

  const std::string& table_name = query_.table_name();
  auto& stat = Mertric::GetThreadStatInfo(caller_);
  Mertric::TableStat& stat_table = stat.tables[table_name];
  flatbuffers::FlatBufferBuilder fb_builder;
  std::vector<const ks::ad_table::ColumnDescriptor*> column_descs(
    query_.select_fields_size(), nullptr);

  auto col_vec = BuildColumnDesc(query_.select_fields(),
                                 ad_table_.GetTableSchema().AllColumns(),
                                 column_descs,
                                 &fb_builder,
                                 stat_table, caller_);

  int32_t row_size = query_.keys().buf().size()/query_.keys().width();
  std::vector<flatbuffers::Offset<flat::Row>> tmp_rows;
  tmp_rows.reserve(row_size);

  absl::Span<const int64_t> search_keys(
    reinterpret_cast<const int64_t*>(
      query_.keys().buf().data()), row_size);

  // 查询正排前要 guard 当前表
  auto guard = ad_table_.Guard();
  //  先支持主键查询
  int32_t valid_row = 0;
  for (int i = 0; i < row_size; ++i) {
    int64_t primary_id = search_keys[i];
    auto rw = ad_table_.Find(primary_id);
    if (rw.Empty()) {
      tmp_rows.push_back(flat::CreateRow(fb_builder, primary_id));
      LOG_EVERY_N(INFO, GridKconf::logMissFreq()) << "key not found. primary_id:" << primary_id
                                  << " table_name:" << table_name
                                  << " caller:" << caller_;
      continue;
    }
    ++valid_row;
    auto row = BuildRow(primary_id, rw, column_descs, &fb_builder);
    tmp_rows.push_back(row);
  }

  auto rows_vec = fb_builder.CreateVector(tmp_rows);
  auto name_fb = fb_builder.CreateString(table_name);
  auto fb_table = flat::CreateGridTable(fb_builder, name_fb, col_vec, rows_vec);
  fb_builder.Finish(fb_table);

  stat_table.col_count += column_descs.size();
  stat_table.row_count += row_size;
  stat_table.valid_row_count += valid_row;
  ++(stat_table.pv_count);

  auto block = result_->add_blocks();
  block->reserve(fb_builder.GetSize());
  block->assign(fb_builder.GetBufferPointer(),
                fb_builder.GetBufferPointer() + fb_builder.GetSize());
  return true;
}

}  // namespace grid
}  // namespace ks
