#include "teams/ad/grid/search/index_search_result_builder.h"
#include <algorithm>
#include <string>
#include <vector>
#include "teams/ad/grid/proto/grid_generated.h"
#include "teams/ad/grid/utils/utils.h"

namespace ks {
namespace grid {

bool IndexSearchResultBuilder::Build() {
  if (!result_) {
    return false;
  }
  if (dfs_.empty()) {
    return false;
  }

  const std::string& table_name = dfs_[0].GetTableSchema().TableName();
  const std::string& index_name = query_.index_name();
  auto& stat = Mertric::GetThreadStatInfo(caller_);
  Mertric::TableStat& stat_table = stat.tables[index_name];  // 倒排表名用 index_name
  flatbuffers::FlatBufferBuilder fb_builder;
  std::vector<const ks::ad_table::ColumnDescriptor*> column_descs(
    query_.select_fields_size(), nullptr);

  auto col_vec = BuildColumnDesc(query_.select_fields(),
                                 dfs_[0].GetTableSchema().AllColumns(),
                                 column_descs,
                                 &fb_builder,
                                 stat_table, caller_);

  size_t n_row = 0;
  for (auto& df : dfs_) {
    n_row += df.RowSize();
  }
  size_t n_query_limit = query_.limit();
  std::vector<flatbuffers::Offset<flat::Row>> tmp_rows;
  size_t n_reserve = std::min(n_query_limit, n_row);
  tmp_rows.reserve(n_reserve);
  result_->mutable_offsets()->Reserve(n_reserve);
  int32_t valid_row = 0;

  for (size_t i = 0; i < dfs_.size() && valid_row < n_query_limit; ++i) {
    auto& df = dfs_[i];
    auto& pkey_column_desc = df.GetTableSchema().PrimaryKeyColumn();
    for (size_t j = 0; j < df.RowSize() && valid_row < n_query_limit; ++j) {
      auto row_id = df.Row(j);
      auto rw = df.GetImmRowWrapper(row_id);
      if (rw.Empty()) { continue; }
      int64_t primary_id = pkey_column_desc.GetValue<int64_t>(rw);
      auto row = BuildRow(primary_id, rw, column_descs, &fb_builder);
      tmp_rows.push_back(row);
      ++valid_row;
      result_->mutable_offsets()->Add(i);
    }
  }

  auto fb_rows_vec = fb_builder.CreateVector(tmp_rows);
  auto fb_name = fb_builder.CreateString(table_name);
  auto fb_table = flat::CreateGridTable(fb_builder, fb_name, col_vec, fb_rows_vec);

  fb_builder.Finish(fb_table);

  stat_table.col_count += column_descs.size();
  stat_table.row_count += n_row;
  stat_table.valid_row_count += valid_row;
  ++(stat_table.pv_count);

  auto block = result_->add_blocks();
  block->reserve(fb_builder.GetSize());
  block->assign(fb_builder.GetBufferPointer(),
                fb_builder.GetBufferPointer() + fb_builder.GetSize());
  return true;
}

}  // namespace grid
}  // namespace ks
