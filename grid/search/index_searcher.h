#pragma once
#include <string>
#include <vector>
#include <utility>
#include "glog/logging.h"
#include "absl/types/span.h"
#include "ks/base/container/common.h"
#include "teams/ad/ad_table/table/dataframe.h"
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/proto/msg.pb.h"

namespace ks {
namespace grid {
namespace index {

extern int32_t Union(const ks::ad_table::DataFrame& df, const std::string& index_name,
                                            absl::Span<const ks::ad_table::Key128> search_keys,
                                            std::vector<ks::ad_table::DataFrame>* df_vec) {
  if (UNLIKELY(df.Empty() || df_vec == nullptr)) {
    return -1;
  }
  auto result_df = df.In(index_name, search_keys);
  if (!result_df) {
    LOG_EVERY_N(ERROR, 1024) << "Union, dataframe:"           << df.GetTableSchema().TableName()
                             << " didn't build index:" << index_name;
    return -1;
  }
  df_vec->emplace_back(std::move(result_df));
  return 0;
}

extern int32_t UnionALL(const ks::ad_table::DataFrame& df, const std::string index_name,
                                            absl::Span<const ks::ad_table::Key128> search_keys,
                                            std::vector<ks::ad_table::DataFrame>* df_vec) {
  if (UNLIKELY(df.Empty() || df_vec == nullptr)) {
    return -1;
  }
  df_vec->reserve(search_keys.size());
  for (auto& key : search_keys) {
    auto result_df = df.Eq(index_name, key);
    if (!result_df) {
      LOG_EVERY_N(ERROR, 1024) << "UnionALL, dataframe:"           << df.GetTableSchema().TableName()
                               << " didn't build index:" << index_name;
      return -1;
    }
    df_vec->emplace_back(std::move(result_df));
  }
  return 0;
}

}  // namespace index
}  // namespace grid
}  // namespace ks
