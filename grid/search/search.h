#pragma once

#include <vector>
#include <string>
#include "absl/types/span.h"
#include "teams/ad/ad_table/table/dataframe.h"
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/proto/msg.pb.h"

namespace ks {
namespace grid {

struct SearchContext {
  std::string caller;
  proto::ProtoType output_proto{proto::ProtoType::FB};
};

using IndexSearcher = std::function<int32_t(const ks::ad_table::DataFrame&, const std::string& index_name,
                                            absl::Span<const ks::ad_table::Key128>,
                                            std::vector<ks::ad_table::DataFrame>* df_vec)>;

extern int IndexSearch(const proto::GridQuery& query,
                       proto::GridResult* result,
                       SearchContext* context,
                       const IndexSearcher& searcher);

extern int ForwardSearch(const proto::GridQuery& query,
                       proto::GridResult* result,
                       SearchContext* context);

}  // namespace grid
}  // namespace ks
