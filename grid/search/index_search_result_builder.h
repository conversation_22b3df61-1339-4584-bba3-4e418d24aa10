#pragma once

#include <string>
#include <vector>
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/proto/msg.pb.h"
#include "teams/ad/ad_table/table/dataframe.h"

namespace ks {
namespace grid {

class IndexSearchResultBuilder {
 public:
  IndexSearchResultBuilder(const std::string& caller,
                           const std::vector<ks::ad_table::DataFrame>& dfs,
                           const proto::GridQuery& query,
                           proto::GridResult* result,
                           proto::ProtoType proto = proto::ProtoType::FB)
    : output_proto_(proto)
    , caller_(caller)
    , dfs_(dfs)
    , query_(query)
    , result_(result) {}

  // default implement is based on flatbuffer data protocol,
  // declare at FILE:teams/ad/grid/proto/grid.fbs
  virtual bool Build();

 private:
  proto::ProtoType output_proto_{proto::ProtoType::FB};
  const std::string& caller_;
  const std::vector<ks::ad_table::DataFrame>& dfs_;
  const proto::GridQuery& query_;
  ks::grid::proto::GridResult* result_{nullptr};
};

}  // namespace grid
}  // namespace ks
