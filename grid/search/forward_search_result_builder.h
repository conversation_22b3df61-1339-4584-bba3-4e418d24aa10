#pragma once

#include <string>
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/proto/msg.pb.h"
#include "teams/ad/ad_table/table/ad_table.h"

namespace ks {
namespace grid {

class ForwardSearchResultBuilder {
 public:
  ForwardSearchResultBuilder(const std::string& caller,
                           const ks::ad_table::AdTable& table,
                           const proto::GridQuery& query,
                           proto::GridResult* result,
                           proto::ProtoType proto = proto::ProtoType::FB)
    : output_proto_(proto)
    , caller_(caller)
    , ad_table_(table)
    , query_(query)
    , result_(result) {}

  // default implement is based on flatbuffer data protocol,
  // declare at FILE:teams/ad/grid/proto/grid.fbs
  virtual bool Build();

 private:
  proto::ProtoType output_proto_{proto::ProtoType::FB};
  const std::string& caller_;
  const ks::ad_table::AdTable& ad_table_;
  const proto::GridQuery& query_;
  ks::grid::proto::GridResult* result_{nullptr};
};

}  // namespace grid
}  // namespace ks
