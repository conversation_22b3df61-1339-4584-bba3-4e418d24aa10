#include "teams/ad/grid/search/search.h"

#include <cstddef>

#include "teams/ad/grid/index/index_mannger.h"
#include "teams/ad/grid/search/index_search_result_builder.h"
#include "teams/ad/grid/search/forward_search_result_builder.h"
#include "teams/ad/grid/utils/utils.h"
#include "flatbuffers/flatbuffers.h"


namespace ks {
namespace grid {

static const int K128_BYTE_SIZE = 16;
static const int K64_BYTE_SIZE = 8;

int IndexSearch(const ks::grid::proto::GridQuery& query,
                ks::grid::proto::GridResult* result,
                SearchContext* context,
                const IndexSearcher& searcher) {
  if (query.limit() == 0) {
    LOG_EVERY_N(ERROR, 1024) << "invalid `limit', table=" << query.table_name()
                             << ", index="                << query.index_name();
    return -1;
  }
  if (query.keys().width() != K128_BYTE_SIZE) {
    LOG_EVERY_N(ERROR, 1024) << "invalid `width', table=" << query.table_name()
                             << ", index="                << query.index_name();
    return -1;
  }
  if (query.keys().buf().size() % K128_BYTE_SIZE != 0) {
    LOG_EVERY_N(ERROR, 1024) << "invalid `buf'.size, table=" << query.table_name()
                             << ", index="                   << query.index_name();
    return -1;
  }

  int key_count = query.keys().buf().size() / query.keys().width();
  if (key_count == 0) {
    LOG_EVERY_N(WARNING, 1024) << "empty query, table=" << query.table_name()
                               << ", index="            << query.index_name();
    return 0;
  }

  auto db = DBManager::GetInstance().GetDB(query.table_name());
  if (KS_UNLIKELY(db == nullptr)) {
    LOG_EVERY_N(ERROR, 1024) << "db not found, table_name: " << query.table_name();
    return -1;
  }

  auto& table_env = db->Env();
  auto df_env = table_env.GetDataframeEnv();
  if (KS_UNLIKELY(df_env == nullptr)) {
    LOG_EVERY_N(ERROR, 1024) << "no dataframe was built";
    return -1;
  }

  auto df = df_env->Lookup(query.table_name());
  if (KS_UNLIKELY(df == nullptr)) {
    LOG_EVERY_N(ERROR, 1024) << "didn't exist dataframe:" << query.table_name();
    return -1;
  }
  if (KS_UNLIKELY(!(*df))) {
    LOG_EVERY_N(ERROR, 1024) << "dataframe:" << query.table_name()
                             << " is invalid";
    return -1;
  }

  absl::Span<const ks::ad_table::Key128> search_keys(
    reinterpret_cast<const ks::ad_table::Key128*>(
      query.keys().buf().data()), key_count);

  std::vector<ks::ad_table::DataFrame> dfs;
  if (searcher(*df, query.index_name(), search_keys, &dfs) < 0) {
    LOG(ERROR) << "search failed, dataframe:" << query.table_name() << ", index:" << query.index_name();
  }
  if (dfs.empty()) {
    LOG_EVERY_N(ERROR, 1024) << "dataframe:" << query.table_name()
                             << ", index:" << query.index_name()
                             << " didn't find any result";
    return -1;
  }

  IndexSearchResultBuilder result_builder(context->caller,
                                          dfs,
                                          query,
                                          result,
                                          context->output_proto);
  if (!result_builder.Build()) {
    LOG(ERROR) << "build result failed, dataframe:" << query.table_name()
               << ", index:"                        << query.index_name();
    return -1;
  }

  return 0;
}

int ForwardSearch(const proto::GridQuery &query, proto::GridResult *result, SearchContext *context) {
  if (query.keys().width() != K64_BYTE_SIZE) {
    LOG_EVERY_N(ERROR, 1024) << "invalid `width', Forward search table=" << query.table_name();
    return -1;
  }
  if (query.keys().buf().size() % K64_BYTE_SIZE != 0) {
    LOG_EVERY_N(ERROR, 1024) << "invalid `buf'.size, Forward search table=" << query.table_name();
    return -1;
  }
  int key_count = query.keys().buf().size() / query.keys().width();
  if (key_count == 0) {
    LOG_EVERY_N(WARNING, 1024) << "empty query, Forward search table=" << query.table_name();
    return 0;
  }

  auto db = DBManager::GetInstance().GetDB(query.table_name());
  if (KS_UNLIKELY(db == nullptr)) {
    LOG_EVERY_N(ERROR, 1024) << "db not found, Forward search table_name: " << query.table_name();
    return -1;
  }
  auto& table_env = db->Env();
  auto table_ptr = table_env.Lookup(query.table_name());
  if (KS_UNLIKELY(table_ptr == nullptr)) {
    LOG(ERROR) << query.table_name() << " not found";
    return -1;
  }

  ForwardSearchResultBuilder result_builder(context->caller,
                                          *table_ptr,
                                          query,
                                          result,
                                          context->output_proto);
  if (!result_builder.Build()) {
    LOG(ERROR) << "build result failed, dataframe:" << query.table_name()
               << ", index:"                        << query.index_name();
    return -1;
  }
  return 0;
}

}  // namespace grid
}  // namespace ks
