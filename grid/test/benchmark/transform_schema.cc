#include <benchmark/benchmark.h>
#include <cstddef>
#include <cstdint>
#include <vector>
#include "flatbuffers/flatbuffers.h"
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/proto/fbs.pb.h"
#include "teams/ad/grid/test/benchmark/common_generated.h"

using namespace ks::grid::flat; // NOLINT

struct MockRow {
  int8_t  i8;
  int16_t i16;
  int32_t i32;
  int64_t i64;
  float   f;
  double  d;
  std::string s;
  std::vector<int16_t> v16;
  std::vector<int32_t> v32;
  std::vector<int64_t> v64;
  std::vector<float>   vf;
  std::vector<double>  vd;
  std::vector<std::string> vs;
};

struct MockTable {
  std::string name;
  std::vector<MockRow> rows;
};

void MockPB(ks::grid::proto::Table& table) {  // NOLINT
  table.set_name("test");
  for (int i = 0; i < 10; ++i) {
    auto row = table.add_rows();
    row->add_data()->set_i8(1);
    row->add_data()->set_i16(2);
    row->add_data()->set_i32(3);
    row->add_data()->set_i64(4);
    row->add_data()->set_float_value(5.0);
    row->add_data()->set_double_value(6.0);
    row->add_data()->set_string_value("hello");
    row->add_data()->add_int16_list_value(7);
    row->add_data()->add_int32_list_value(8);
    row->add_data()->add_int64_list_value(9);
    row->add_data()->add_float_list_value(10.0);
    row->add_data()->add_double_list_value(11.0);
    row->add_data()->add_string_list_value("world");
  }
}

void MockFB(flatbuffers::FlatBufferBuilder& builder) {  // NOLINT
  std::vector<flatbuffers::Offset<ks::grid::flat::Row>> tmp_rows;
  for (int i = 0; i < 10; ++i) {
    std::vector<flatbuffers::Offset<ks::grid::flat::Value>> tmp_values;

    flatbuffers::Offset<ks::grid::flat::Value> tmp = CreateValue(builder, false,
      1, 0, 0, 0, 0, 0, 0, 0, 0.0f, 0.0, 0, 0, 0, 0, 0, 0, 0, 0);
    tmp_values.push_back(tmp);

    tmp = CreateValue(builder, false, 0, 2, 0, 0, 0, 0, 0, 0, 0.0f, 0,
      0, 0, 0, 0, 0, 0, 0, 0);
    tmp_values.push_back(tmp);

    tmp = CreateValue(builder, false, 0, 0, 3, 0, 0, 0, 0, 0, 0.0f, 0,
      0, 0, 0, 0, 0, 0, 0, 0);
    tmp_values.push_back(tmp);

    tmp = CreateValue(builder, false, 0, 0, 0, 4, 0, 0, 0, 0, 0.0f, 0,
      0, 0, 0, 0, 0, 0, 0, 0);
    tmp_values.push_back(tmp);

    tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0,
      0, 0, 0, 0, 0, 0, 0, 0);
    tmp_values.push_back(tmp);

    tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 0.0f, 6,
      0, 0, 0, 0, 0, 0, 0, 0);
    tmp_values.push_back(tmp);

    auto string_value = builder.CreateString("hello");
    tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 0.0f, 0.0,
      string_value, 0, 0, 0, 0, 0, 0, 0);
    tmp_values.push_back(tmp);

    auto int16_list_value = builder.CreateVector(std::vector<int32_t>{7});
    tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 0.0f, 0.0,
      0, 0, int16_list_value, 0, 0, 0, 0, 0);
    tmp_values.push_back(tmp);

    auto int32_list_value = builder.CreateVector(std::vector<int32_t>{8});
    tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 0.0f, 0.0,
      0, 0, 0, int32_list_value, 0, 0, 0, 0);
    tmp_values.push_back(tmp);

    auto int64_list_value = builder.CreateVector(std::vector<int64_t>{9});
    tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 0.0f, 0.0,
      0, 0, 0, 0, int64_list_value, 0, 0, 0);
    tmp_values.push_back(tmp);

    auto float_list_value = builder.CreateVector(std::vector<float>{10.0});
    tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 0.0f, 0.0,
      0, 0, 0, 0, 0, float_list_value, 0, 0);
    tmp_values.push_back(tmp);

    auto double_list_value = builder.CreateVector(std::vector<double>{11.0});
    tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 0.0f, 0.0,
      0, 0, 0, 0, 0, 0, double_list_value, 0);
    tmp_values.push_back(tmp);

    auto string_list_value = builder.CreateVectorOfStrings({"world"});
    tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 0.0f, 0.0,
      0, 0, 0, 0, 0, 0, 0, string_list_value);
    tmp_values.push_back(tmp);

    auto values = builder.CreateVector(tmp_values);

    tmp_rows.push_back(CreateRow(builder, values));
  }

  auto rows = builder.CreateVector(tmp_rows);

  auto name = builder.CreateString("test");
  auto table = CreateTable(builder, name, 0, rows);

  builder.Finish(table);
}

void MockFB_Row(flatbuffers::FlatBufferBuilder& builder) {  // NOLINT
  std::vector<flatbuffers::Offset<ks::grid::flat::Value>> tmp_values;

  flatbuffers::Offset<ks::grid::flat::Value> tmp = CreateValue(builder, false,
    1, 0, 0, 0, 0, 0, 0, 0, 0.0f, 0.0, 0, 0, 0, 0, 0, 0, 0, 0);
  tmp_values.push_back(tmp);

  tmp = CreateValue(builder, false, 0, 2, 0, 0, 0, 0, 0, 0, 0.0f, 0,
    0, 0, 0, 0, 0, 0, 0, 0);
  tmp_values.push_back(tmp);

  tmp = CreateValue(builder, false, 0, 0, 3, 0, 0, 0, 0, 0, 0.0f, 0,
    0, 0, 0, 0, 0, 0, 0, 0);
  tmp_values.push_back(tmp);

  tmp = CreateValue(builder, false, 0, 0, 0, 4, 0, 0, 0, 0, 0.0f, 0,
    0, 0, 0, 0, 0, 0, 0, 0);
  tmp_values.push_back(tmp);

  tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0,
    0, 0, 0, 0, 0, 0, 0, 0);
  tmp_values.push_back(tmp);

  tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 0.0f, 6,
    0, 0, 0, 0, 0, 0, 0, 0);
  tmp_values.push_back(tmp);

  auto string_value = builder.CreateString("hello");
  tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 0.0f, 0.0,
    string_value, 0, 0, 0, 0, 0, 0, 0);
  tmp_values.push_back(tmp);

  auto int16_list_value = builder.CreateVector(std::vector<int32_t>{7});
  tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 0.0f, 0.0,
    0, 0, int16_list_value, 0, 0, 0, 0, 0);
  tmp_values.push_back(tmp);

  auto int32_list_value = builder.CreateVector(std::vector<int32_t>{8});
  tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 0.0f, 0.0,
    0, 0, 0, int32_list_value, 0, 0, 0, 0);
  tmp_values.push_back(tmp);

  auto int64_list_value = builder.CreateVector(std::vector<int64_t>{9});
  tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 0.0f, 0.0,
    0, 0, 0, 0, int64_list_value, 0, 0, 0);
  tmp_values.push_back(tmp);

  auto float_list_value = builder.CreateVector(std::vector<float>{10.0});
  tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 0.0f, 0.0,
    0, 0, 0, 0, 0, float_list_value, 0, 0);
  tmp_values.push_back(tmp);

  auto double_list_value = builder.CreateVector(std::vector<double>{11.0});
  tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 0.0f, 0.0,
    0, 0, 0, 0, 0, 0, double_list_value, 0);
  tmp_values.push_back(tmp);

  auto string_list_value = builder.CreateVectorOfStrings({"world"});
  tmp = CreateValue(builder, false, 0, 0, 0, 0, 0, 0, 0, 0, 0.0f, 0.0,
    0, 0, 0, 0, 0, 0, 0, string_list_value);
  tmp_values.push_back(tmp);

  auto values = builder.CreateVector(tmp_values);

  auto final_row = CreateRow(builder, values);
  builder.Finish(final_row);
}

// PB 序列化
void serialize_pb(std::string& serialized_data) { // NOLINT
  ks::grid::proto::Table table;
  MockPB(table);
  serialized_data.clear();
  if (!table.SerializeToString(&serialized_data)) {
    throw std::runtime_error("Failed to serialize PB");
  }
}

// PB 反序列化
void deserialize_pb(const std::string& serialized_data, ks::grid::proto::Table& table) {  // NOLINT
  table.ParseFromString(serialized_data);
}

// PB 反序列化,修正
void deserialize_pb2(const std::string& serialized_data, MockTable& table) {  // NOLINT
  ks::grid::proto::Table tmp;
  tmp.ParseFromString(serialized_data);
  table.name = tmp.name();
  table.rows.resize(tmp.rows_size());
  for (int i = 0; i < tmp.rows_size(); ++i) {
    auto& row = table.rows[i];
    row.i8 = tmp.rows(i).data(0).i8();
    row.i16 = tmp.rows(i).data(1).i16();
    row.i32 = tmp.rows(i).data(2).i32();
    row.i64 = tmp.rows(i).data(3).i64();
    row.f = tmp.rows(i).data(4).float_value();
    row.d = tmp.rows(i).data(5).double_value();
    row.s = tmp.rows(i).data(6).string_value();

    auto& v16 = tmp.rows(i).data(7).int16_list_value();
    row.v16.assign(v16.begin(), v16.end());

    auto& v32 = tmp.rows(i).data(8).int32_list_value();
    row.v32.assign(v32.begin(), v32.end());

    auto& v64 = tmp.rows(i).data(9).int64_list_value();
    row.v64.assign(v64.begin(), v64.end());

    auto& vf = tmp.rows(i).data(10).float_list_value();
    row.vf.assign(vf.begin(), vf.end());

    auto& vd = tmp.rows(i).data(11).double_list_value();
    row.vd.assign(vd.begin(), vd.end());

    auto& vs = tmp.rows(i).data(12).string_list_value();
    row.vs.assign(vs.begin(), vs.end());
  }
}

// FB 序列化
void serialize_fb(std::string& serialized_data) { // NOLINT
  flatbuffers::FlatBufferBuilder builder;
  MockFB(builder);
  serialized_data.assign(builder.GetBufferPointer(), builder.GetBufferPointer() + builder.GetSize());
}

// FB 序列化,带行结构
void serialize_fb_row(std::string& serialized_data) { // NOLINT
  flatbuffers::FlatBufferBuilder builder;
  MockFB_Row(builder);
  serialized_data.assign(builder.GetBufferPointer(), builder.GetBufferPointer() + builder.GetSize());
}

// FB 反序列化
void deserialize_fb(const std::string& serialized_data) {
  const ks::grid::flat::Table* tmp = flatbuffers::GetRoot<ks::grid::flat::Table>(serialized_data.data());
}

// FB 反序列化，带数据解析
void deserialize_fb2(const std::string& serialized_data, MockTable& table) {  // NOLINT
  const ks::grid::flat::Table* tmp = flatbuffers::GetRoot<ks::grid::flat::Table>(serialized_data.data());

  table.name.assign(tmp->name()->begin(), tmp->name()->end());
  table.rows.resize(tmp->rows()->size());
  for (int i = 0; i < tmp->rows()->size(); ++i) {
    auto& row = table.rows[i];
    const auto& row_data = tmp->rows()->Get(i);

    row.i8 = row_data->data()->Get(0)->i8();
    row.i16 = row_data->data()->Get(1)->i16();
    row.i32 = row_data->data()->Get(2)->i32();
    row.i64 = row_data->data()->Get(3)->i64();
    row.f = row_data->data()->Get(4)->float_value();
    row.d = row_data->data()->Get(5)->double_value();

    const auto& s = row_data->data()->Get(6)->string_value();
    row.s.assign(s->begin(), s->end());

    const auto& v16 = row_data->data()->Get(7)->int16_list_value();
    row.v16.assign(v16->begin(), v16->end());

    const auto& v32 = row_data->data()->Get(8)->int32_list_value();
    row.v32.assign(v32->begin(), v32->end());

    const auto& v64 = row_data->data()->Get(9)->int64_list_value();
    row.v64.assign(v64->begin(), v64->end());

    const auto& vf = row_data->data()->Get(10)->float_list_value();
    row.vf.assign(vf->begin(), vf->end());

    const auto& vd = row_data->data()->Get(11)->double_list_value();
    row.vd.assign(vd->begin(), vd->end());

    const auto& vs = row_data->data()->Get(12)->string_list_value();
    for (int j = 0; j < vs->size(); ++j) {
      std::string s(vs->Get(j)->begin(), vs->Get(j)->end());
      row.vs.push_back(s);
    }
  }
}

// FB 反序列化，带数据嵌入
void deserialize_fb3(const std::string& serialized_data, MockTable& table) {  // NOLINT
  ks::grid::proto::fbs pb_table;
  pb_table.ParseFromString(serialized_data);
  const ks::grid::flat::Table* tmp = flatbuffers::GetRoot<ks::grid::flat::Table>(pb_table.table().data());

  table.name.assign(tmp->name()->begin(), tmp->name()->end());
  table.rows.resize(tmp->rows()->size());
  for (int i = 0; i < tmp->rows()->size(); ++i) {
    auto& row = table.rows[i];
    const auto& row_data = tmp->rows()->Get(i);

    row.i8 = row_data->data()->Get(0)->i8();
    row.i16 = row_data->data()->Get(1)->i16();
    row.i32 = row_data->data()->Get(2)->i32();
    row.i64 = row_data->data()->Get(3)->i64();
    row.f = row_data->data()->Get(4)->float_value();
    row.d = row_data->data()->Get(5)->double_value();

    const auto& s = row_data->data()->Get(6)->string_value();
    row.s.assign(s->begin(), s->end());

    const auto& v16 = row_data->data()->Get(7)->int16_list_value();
    row.v16.assign(v16->begin(), v16->end());

    const auto& v32 = row_data->data()->Get(8)->int32_list_value();
    row.v32.assign(v32->begin(), v32->end());

    const auto& v64 = row_data->data()->Get(9)->int64_list_value();
    row.v64.assign(v64->begin(), v64->end());

    const auto& vf = row_data->data()->Get(10)->float_list_value();
    row.vf.assign(vf->begin(), vf->end());

    const auto& vd = row_data->data()->Get(11)->double_list_value();
    row.vd.assign(vd->begin(), vd->end());

    const auto& vs = row_data->data()->Get(12)->string_list_value();
    for (int j = 0; j < vs->size(); ++j) {
      std::string s(vs->Get(j)->begin(), vs->Get(j)->end());
      row.vs.push_back(s);
    }
  }
}

// FB 反序列化，带行结构
void deserialize_fb_row(const std::string& serialized_data, MockTable& table) { // NOLINT
  ks::grid::proto::fbs2 pb_table;
  pb_table.ParseFromString(serialized_data);
  // const ks::grid::flat::Table* tmp = flatbuffers::GetRoot<ks::grid::flat::Table>(pb_table.table().data());

  table.name.assign(pb_table.name());
  table.rows.resize(pb_table.rows_size());
  for (int i = 0; i < pb_table.rows_size(); ++i) {
    const ks::grid::flat::Row* row_data = flatbuffers::GetRoot<ks::grid::flat::Row>(pb_table.rows(i).data());
    auto& row = table.rows[i];

    row.i8 = row_data->data()->Get(0)->i8();
    row.i16 = row_data->data()->Get(1)->i16();
    row.i32 = row_data->data()->Get(2)->i32();
    row.i64 = row_data->data()->Get(3)->i64();
    row.f = row_data->data()->Get(4)->float_value();
    row.d = row_data->data()->Get(5)->double_value();

    const auto& s = row_data->data()->Get(6)->string_value();
    row.s.assign(s->begin(), s->end());

    const auto& v16 = row_data->data()->Get(7)->int16_list_value();
    row.v16.assign(v16->begin(), v16->end());

    const auto& v32 = row_data->data()->Get(8)->int32_list_value();
    row.v32.assign(v32->begin(), v32->end());

    const auto& v64 = row_data->data()->Get(9)->int64_list_value();
    row.v64.assign(v64->begin(), v64->end());

    const auto& vf = row_data->data()->Get(10)->float_list_value();
    row.vf.assign(vf->begin(), vf->end());

    const auto& vd = row_data->data()->Get(11)->double_list_value();
    row.vd.assign(vd->begin(), vd->end());

    const auto& vs = row_data->data()->Get(12)->string_list_value();
    for (int j = 0; j < vs->size(); ++j) {
      std::string s(vs->Get(j)->begin(), vs->Get(j)->end());
      row.vs.push_back(s);
    }
  }
}

// 基准测试函数
static void BM_SerializePB(benchmark::State& state) { // NOLINT
  std::string serialized_data;
  for (auto _ : state) {
    serialize_pb(serialized_data);
    benchmark::DoNotOptimize(serialized_data);
  }
}

static void BM_DeserializePB_Direct(benchmark::State& state) { // NOLINT
  ks::grid::proto::Table table;
  MockPB(table);

  std::string serialized_data;
  serialize_pb(serialized_data);

  for (auto _ : state) {
    ks::grid::proto::Table table;
    deserialize_pb(serialized_data, table);
    benchmark::DoNotOptimize(serialized_data);
  }
}

static void BM_DeserializePB(benchmark::State& state) { // NOLINT
  ks::grid::proto::Table table;
  MockPB(table);

  std::string serialized_data;
  serialize_pb(serialized_data);

  for (auto _ : state) {
    MockTable table;
    deserialize_pb2(serialized_data, table);
    benchmark::DoNotOptimize(table);
  }
}

static void BM_SerializeFB(benchmark::State& state) { // NOLINT
  std::string serialized_data;
  for (auto _ : state) {
    serialize_fb(serialized_data);
    benchmark::DoNotOptimize(serialized_data);
  }
}

static void BM_DeserializeFB_Direct(benchmark::State& state) { // NOLINT  
  std::string serialized_data;
  serialize_fb(serialized_data);

  for (auto _ : state) {
    deserialize_fb(serialized_data);
    benchmark::DoNotOptimize(serialized_data);
  }
}

static void BM_DeserializeFB(benchmark::State& state) { // NOLINT
  std::string serialized_data;
  serialize_fb(serialized_data);

  for (auto _ : state) {
    MockTable table;
    deserialize_fb2(serialized_data, table);
    benchmark::DoNotOptimize(table);
  }
}

static void BM_DeserializeFB_PB(benchmark::State& state) { // NOLINT
  std::string serialized_data;
  serialize_fb(serialized_data);

  ks::grid::proto::fbs t;
  t.mutable_table()->assign(serialized_data);

  std::string serialized_data2;
  t.SerializeToString(&serialized_data2);

  for (auto _ : state) {
    MockTable table;
    deserialize_fb3(serialized_data2, table);
    benchmark::DoNotOptimize(table);
  }
}

static void BM_SerializeFB_Row(benchmark::State& state) { // NOLINT
  for (auto _ : state) {
    ks::grid::proto::fbs2 t;
    t.set_name("test");
    std::string serialized_data;
    for (int i = 0; i < 10; ++i) {
      serialize_fb_row(serialized_data);
      t.add_rows()->assign(serialized_data);
    }
    t.SerializeToString(&serialized_data);
    benchmark::DoNotOptimize(serialized_data);
  }
}

static void BM_DeserializeFB_Row(benchmark::State& state) { // NOLINT
  ks::grid::proto::fbs2 t;
  t.set_name("test");
  std::string serialized_data;
  for (int i = 0; i < 10; ++i) {
    serialize_fb_row(serialized_data);
    t.add_rows()->assign(serialized_data);
  }

  std::string serialized_data2;
  t.SerializeToString(&serialized_data2);

  for (auto _ : state) {
    MockTable table;
    deserialize_fb_row(serialized_data2, table);
    benchmark::DoNotOptimize(table);
  }
}

BENCHMARK(BM_SerializePB);
BENCHMARK(BM_DeserializePB_Direct);
BENCHMARK(BM_DeserializePB);
BENCHMARK(BM_SerializeFB);
BENCHMARK(BM_DeserializeFB_Direct);
BENCHMARK(BM_DeserializeFB);
BENCHMARK(BM_DeserializeFB_PB);
BENCHMARK(BM_SerializeFB_Row);
BENCHMARK(BM_DeserializeFB_Row);


int main(int argc, char** argv) {
  // std::string serialized_data;
  // serialize_fb(serialized_data);
  // const ks::grid::flat::Table* table = flatbuffers::GetRoot<ks::grid::flat::Table>(serialized_data.data());
  // std::cout << "table name: " << table->name()->c_str() << std::endl;
  // std::cout << "table rows: " << table->rows()->size() << std::endl;
  // for (int i = 0; i < 10; ++i) {
  //   const ks::grid::flat::Row* row = table->rows()->Get(i);
  //   std::cout << "row " << i << " values: " << row->data()->size() << std::endl;
  //   for (int j = 0; j < row->data()->size(); ++j) {
  //     const ks::grid::flat::Value* value = row->data()->Get(j);
  //     std::cout << "value " << j << ": " << value->i64() << std::endl;
  //   }
  // }


  // std::string serialized_data;
  // serialize_fb(serialized_data);

  // std::cout << "serialized_data:" << serialized_data.size() << std::endl;

  // ks::grid::proto::fbs t;
  // t.mutable_table()->assign(serialized_data);

  // std::cout << "mutable_table:" << t.table().size() << std::endl;

  // std::string serialized_data2;
  // t.SerializeToString(&serialized_data2);

  // MockTable table2;

  // std::cout << "test : " << serialized_data2 << std::endl;
  // deserialize_fb3(serialized_data2, table2);

  ks::grid::proto::Table table;
  MockPB(table);
  std::string serialized_data_pb;
  serialize_pb(serialized_data_pb);
  std::cout << "pb size: " << serialized_data_pb.size() << std::endl;

  std::string serialized_data_fb;
  serialize_fb(serialized_data_fb);
  std::cout << "fb size: " << serialized_data_fb.size() << std::endl;

  ks::grid::proto::fbs fbs;
  fbs.mutable_table()->assign(serialized_data_fb);
  std::string serialized_data_fb_pb;
  fbs.SerializeToString(&serialized_data_fb_pb);
  std::cout << "fb_pb size: " << serialized_data_fb_pb.size() << std::endl;

  ks::grid::proto::fbs2 t;
  t.set_name("test");
  std::string serialized_data;
  serialize_fb_row(serialized_data);
  std::cout << "fb row size: " << serialized_data.size() << std::endl;
  for (int i = 0; i < 10; ++i) {
    t.add_rows()->assign(serialized_data);
  }
  std::string serialized_data2;
  t.SerializeToString(&serialized_data2);
  std::cout << "fb2 size: " << serialized_data2.size() << std::endl;


  flatbuffers::FlatBufferBuilder builder;
  auto tmp = CreateValue(builder, false, 1, 0, 0, 0, 0, 0, 0, 0, 0.0f, 0.0,
    0, 0, 0, 0, 0, 0, 0, 0);
  builder.Finish(tmp);
  std::cout << "row1 size: " << builder.GetSize() << std::endl;

  flatbuffers::FlatBufferBuilder builder2;
  auto tmp2 = CreateValue(builder2, false, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
    0, 0, 0, 0, 0, 0, 0, 0);
  builder2.Finish(tmp2);
  std::cout << "row2 size: " << builder2.GetSize() << std::endl;

  benchmark::Initialize(&argc, argv);
  benchmark::RunSpecifiedBenchmarks();
  return 0;
}
