import os

def exe_shell(shell):
  ret = os.system(shell)
  return ret

root_path = "teams/ad/grid"
flat_gen_path = "{}/benchmark".format(root_path)
flat_bin = "third_party/prebuilt/bin/flatc"
flat_gen_flags = "--gen-mutable --reflect-names --gen-object-api"
flat_src_files = "{}/common.fbs".format(flat_gen_path)
gen_cpp_cmd = "%s %s -I ./ -o %s --cpp %s" % (flat_bin, flat_gen_flags, flat_gen_path, flat_src_files)
ret = exe_shell(gen_cpp_cmd)
if not ret:
  print("ad_feature_index exe_shell succ:", ret, gen_cpp_cmd);
else:
  print("ad_feature_index exe_shell fail:", ret, gen_cpp_cmd);