// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_COMMON_KS_GRID_FLAT_H_
#define FLATBUFFERS_GENERATED_COMMON_KS_GRID_FLAT_H_

#include "flatbuffers/flatbuffers.h"

namespace ks {
namespace grid {
namespace flat {

struct Value;
struct ValueT;

struct Key;
struct KeyT;

struct Column;
struct ColumnT;

struct Row;
struct RowT;

struct KVQuery;
struct KVQueryT;

struct Table;
struct TableT;

inline const flatbuffers::TypeTable *ValueTypeTable();

inline const flatbuffers::TypeTable *KeyTypeTable();

inline const flatbuffers::TypeTable *ColumnTypeTable();

inline const flatbuffers::TypeTable *RowTypeTable();

inline const flatbuffers::TypeTable *KVQueryTypeTable();

inline const flatbuffers::TypeTable *TableTypeTable();

enum ColumnType {
  ColumnType_UNKNOWN_COLUMN = 0,
  ColumnType_BOOL = 1,
  ColumnType_INT32 = 2,
  ColumnType_INT64 = 3,
  ColumnType_FLOAT = 4,
  ColumnType_DOUBLE = 5,
  ColumnType_STRING = 6,
  ColumnType_INT32_LIST = 7,
  ColumnType_INT64_LIST = 8,
  ColumnType_FLOAT_LIST = 9,
  ColumnType_DOUBLE_LIST = 10,
  ColumnType_STRING_LIST = 11,
  ColumnType_TS_LIST = 12,
  ColumnType_INT8 = 13,
  ColumnType_INT16 = 14,
  ColumnType_INT8_LIST = 15,
  ColumnType_INT16_LIST = 16,
  ColumnType_MIN = ColumnType_UNKNOWN_COLUMN,
  ColumnType_MAX = ColumnType_INT16_LIST
};

inline const ColumnType (&EnumValuesColumnType())[17] {
  static const ColumnType values[] = {
    ColumnType_UNKNOWN_COLUMN,
    ColumnType_BOOL,
    ColumnType_INT32,
    ColumnType_INT64,
    ColumnType_FLOAT,
    ColumnType_DOUBLE,
    ColumnType_STRING,
    ColumnType_INT32_LIST,
    ColumnType_INT64_LIST,
    ColumnType_FLOAT_LIST,
    ColumnType_DOUBLE_LIST,
    ColumnType_STRING_LIST,
    ColumnType_TS_LIST,
    ColumnType_INT8,
    ColumnType_INT16,
    ColumnType_INT8_LIST,
    ColumnType_INT16_LIST
  };
  return values;
}

inline const char * const *EnumNamesColumnType() {
  static const char * const names[] = {
    "UNKNOWN_COLUMN",
    "BOOL",
    "INT32",
    "INT64",
    "FLOAT",
    "DOUBLE",
    "STRING",
    "INT32_LIST",
    "INT64_LIST",
    "FLOAT_LIST",
    "DOUBLE_LIST",
    "STRING_LIST",
    "TS_LIST",
    "INT8",
    "INT16",
    "INT8_LIST",
    "INT16_LIST",
    nullptr
  };
  return names;
}

inline const char *EnumNameColumnType(ColumnType e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesColumnType()[index];
}

struct ValueT : public flatbuffers::NativeTable {
  typedef Value TableType;
  bool bool_value;
  int32_t i8;
  int32_t i16;
  int32_t i32;
  int64_t i64;
  uint32_t u8;
  uint32_t u16;
  uint32_t u32;
  uint64_t u64;
  float float_value;
  double double_value;
  std::string string_value;
  std::vector<int32_t> int8_list_value;
  std::vector<int32_t> int16_list_value;
  std::vector<int32_t> int32_list_value;
  std::vector<int64_t> int64_list_value;
  std::vector<float> float_list_value;
  std::vector<double> double_list_value;
  std::vector<std::string> string_list_value;
  ValueT()
      : bool_value(false),
        i8(0),
        i16(0),
        i32(0),
        i64(0),
        u8(0),
        u16(0),
        u32(0),
        u64(0),
        float_value(0.0f),
        double_value(0.0) {
  }
};

struct Value FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef ValueT NativeTableType;
  static const flatbuffers::TypeTable *MiniReflectTypeTable() {
    return ValueTypeTable();
  }
  enum {
    VT_BOOL_VALUE = 4,
    VT_I8 = 6,
    VT_I16 = 8,
    VT_I32 = 10,
    VT_I64 = 12,
    VT_U8 = 14,
    VT_U16 = 16,
    VT_U32 = 18,
    VT_U64 = 20,
    VT_FLOAT_VALUE = 22,
    VT_DOUBLE_VALUE = 24,
    VT_STRING_VALUE = 26,
    VT_INT8_LIST_VALUE = 28,
    VT_INT16_LIST_VALUE = 30,
    VT_INT32_LIST_VALUE = 32,
    VT_INT64_LIST_VALUE = 34,
    VT_FLOAT_LIST_VALUE = 36,
    VT_DOUBLE_LIST_VALUE = 38,
    VT_STRING_LIST_VALUE = 40
  };
  bool bool_value() const {
    return GetField<uint8_t>(VT_BOOL_VALUE, 0) != 0;
  }
  bool mutate_bool_value(bool _bool_value) {
    return SetField<uint8_t>(VT_BOOL_VALUE, static_cast<uint8_t>(_bool_value), 0);
  }
  int32_t i8() const {
    return GetField<int32_t>(VT_I8, 0);
  }
  bool mutate_i8(int32_t _i8) {
    return SetField<int32_t>(VT_I8, _i8, 0);
  }
  int32_t i16() const {
    return GetField<int32_t>(VT_I16, 0);
  }
  bool mutate_i16(int32_t _i16) {
    return SetField<int32_t>(VT_I16, _i16, 0);
  }
  int32_t i32() const {
    return GetField<int32_t>(VT_I32, 0);
  }
  bool mutate_i32(int32_t _i32) {
    return SetField<int32_t>(VT_I32, _i32, 0);
  }
  int64_t i64() const {
    return GetField<int64_t>(VT_I64, 0);
  }
  bool mutate_i64(int64_t _i64) {
    return SetField<int64_t>(VT_I64, _i64, 0);
  }
  uint32_t u8() const {
    return GetField<uint32_t>(VT_U8, 0);
  }
  bool mutate_u8(uint32_t _u8) {
    return SetField<uint32_t>(VT_U8, _u8, 0);
  }
  uint32_t u16() const {
    return GetField<uint32_t>(VT_U16, 0);
  }
  bool mutate_u16(uint32_t _u16) {
    return SetField<uint32_t>(VT_U16, _u16, 0);
  }
  uint32_t u32() const {
    return GetField<uint32_t>(VT_U32, 0);
  }
  bool mutate_u32(uint32_t _u32) {
    return SetField<uint32_t>(VT_U32, _u32, 0);
  }
  uint64_t u64() const {
    return GetField<uint64_t>(VT_U64, 0);
  }
  bool mutate_u64(uint64_t _u64) {
    return SetField<uint64_t>(VT_U64, _u64, 0);
  }
  float float_value() const {
    return GetField<float>(VT_FLOAT_VALUE, 0.0f);
  }
  bool mutate_float_value(float _float_value) {
    return SetField<float>(VT_FLOAT_VALUE, _float_value, 0.0f);
  }
  double double_value() const {
    return GetField<double>(VT_DOUBLE_VALUE, 0.0);
  }
  bool mutate_double_value(double _double_value) {
    return SetField<double>(VT_DOUBLE_VALUE, _double_value, 0.0);
  }
  const flatbuffers::String *string_value() const {
    return GetPointer<const flatbuffers::String *>(VT_STRING_VALUE);
  }
  flatbuffers::String *mutable_string_value() {
    return GetPointer<flatbuffers::String *>(VT_STRING_VALUE);
  }
  const flatbuffers::Vector<int32_t> *int8_list_value() const {
    return GetPointer<const flatbuffers::Vector<int32_t> *>(VT_INT8_LIST_VALUE);
  }
  flatbuffers::Vector<int32_t> *mutable_int8_list_value() {
    return GetPointer<flatbuffers::Vector<int32_t> *>(VT_INT8_LIST_VALUE);
  }
  const flatbuffers::Vector<int32_t> *int16_list_value() const {
    return GetPointer<const flatbuffers::Vector<int32_t> *>(VT_INT16_LIST_VALUE);
  }
  flatbuffers::Vector<int32_t> *mutable_int16_list_value() {
    return GetPointer<flatbuffers::Vector<int32_t> *>(VT_INT16_LIST_VALUE);
  }
  const flatbuffers::Vector<int32_t> *int32_list_value() const {
    return GetPointer<const flatbuffers::Vector<int32_t> *>(VT_INT32_LIST_VALUE);
  }
  flatbuffers::Vector<int32_t> *mutable_int32_list_value() {
    return GetPointer<flatbuffers::Vector<int32_t> *>(VT_INT32_LIST_VALUE);
  }
  const flatbuffers::Vector<int64_t> *int64_list_value() const {
    return GetPointer<const flatbuffers::Vector<int64_t> *>(VT_INT64_LIST_VALUE);
  }
  flatbuffers::Vector<int64_t> *mutable_int64_list_value() {
    return GetPointer<flatbuffers::Vector<int64_t> *>(VT_INT64_LIST_VALUE);
  }
  const flatbuffers::Vector<float> *float_list_value() const {
    return GetPointer<const flatbuffers::Vector<float> *>(VT_FLOAT_LIST_VALUE);
  }
  flatbuffers::Vector<float> *mutable_float_list_value() {
    return GetPointer<flatbuffers::Vector<float> *>(VT_FLOAT_LIST_VALUE);
  }
  const flatbuffers::Vector<double> *double_list_value() const {
    return GetPointer<const flatbuffers::Vector<double> *>(VT_DOUBLE_LIST_VALUE);
  }
  flatbuffers::Vector<double> *mutable_double_list_value() {
    return GetPointer<flatbuffers::Vector<double> *>(VT_DOUBLE_LIST_VALUE);
  }
  const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *string_list_value() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_STRING_LIST_VALUE);
  }
  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *mutable_string_list_value() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_STRING_LIST_VALUE);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_BOOL_VALUE) &&
           VerifyField<int32_t>(verifier, VT_I8) &&
           VerifyField<int32_t>(verifier, VT_I16) &&
           VerifyField<int32_t>(verifier, VT_I32) &&
           VerifyField<int64_t>(verifier, VT_I64) &&
           VerifyField<uint32_t>(verifier, VT_U8) &&
           VerifyField<uint32_t>(verifier, VT_U16) &&
           VerifyField<uint32_t>(verifier, VT_U32) &&
           VerifyField<uint64_t>(verifier, VT_U64) &&
           VerifyField<float>(verifier, VT_FLOAT_VALUE) &&
           VerifyField<double>(verifier, VT_DOUBLE_VALUE) &&
           VerifyOffset(verifier, VT_STRING_VALUE) &&
           verifier.Verify(string_value()) &&
           VerifyOffset(verifier, VT_INT8_LIST_VALUE) &&
           verifier.Verify(int8_list_value()) &&
           VerifyOffset(verifier, VT_INT16_LIST_VALUE) &&
           verifier.Verify(int16_list_value()) &&
           VerifyOffset(verifier, VT_INT32_LIST_VALUE) &&
           verifier.Verify(int32_list_value()) &&
           VerifyOffset(verifier, VT_INT64_LIST_VALUE) &&
           verifier.Verify(int64_list_value()) &&
           VerifyOffset(verifier, VT_FLOAT_LIST_VALUE) &&
           verifier.Verify(float_list_value()) &&
           VerifyOffset(verifier, VT_DOUBLE_LIST_VALUE) &&
           verifier.Verify(double_list_value()) &&
           VerifyOffset(verifier, VT_STRING_LIST_VALUE) &&
           verifier.Verify(string_list_value()) &&
           verifier.VerifyVectorOfStrings(string_list_value()) &&
           verifier.EndTable();
  }
  ValueT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(ValueT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<Value> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ValueT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct ValueBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_bool_value(bool bool_value) {
    fbb_.AddElement<uint8_t>(Value::VT_BOOL_VALUE, static_cast<uint8_t>(bool_value), 0);
  }
  void add_i8(int32_t i8) {
    fbb_.AddElement<int32_t>(Value::VT_I8, i8, 0);
  }
  void add_i16(int32_t i16) {
    fbb_.AddElement<int32_t>(Value::VT_I16, i16, 0);
  }
  void add_i32(int32_t i32) {
    fbb_.AddElement<int32_t>(Value::VT_I32, i32, 0);
  }
  void add_i64(int64_t i64) {
    fbb_.AddElement<int64_t>(Value::VT_I64, i64, 0);
  }
  void add_u8(uint32_t u8) {
    fbb_.AddElement<uint32_t>(Value::VT_U8, u8, 0);
  }
  void add_u16(uint32_t u16) {
    fbb_.AddElement<uint32_t>(Value::VT_U16, u16, 0);
  }
  void add_u32(uint32_t u32) {
    fbb_.AddElement<uint32_t>(Value::VT_U32, u32, 0);
  }
  void add_u64(uint64_t u64) {
    fbb_.AddElement<uint64_t>(Value::VT_U64, u64, 0);
  }
  void add_float_value(float float_value) {
    fbb_.AddElement<float>(Value::VT_FLOAT_VALUE, float_value, 0.0f);
  }
  void add_double_value(double double_value) {
    fbb_.AddElement<double>(Value::VT_DOUBLE_VALUE, double_value, 0.0);
  }
  void add_string_value(flatbuffers::Offset<flatbuffers::String> string_value) {
    fbb_.AddOffset(Value::VT_STRING_VALUE, string_value);
  }
  void add_int8_list_value(flatbuffers::Offset<flatbuffers::Vector<int32_t>> int8_list_value) {
    fbb_.AddOffset(Value::VT_INT8_LIST_VALUE, int8_list_value);
  }
  void add_int16_list_value(flatbuffers::Offset<flatbuffers::Vector<int32_t>> int16_list_value) {
    fbb_.AddOffset(Value::VT_INT16_LIST_VALUE, int16_list_value);
  }
  void add_int32_list_value(flatbuffers::Offset<flatbuffers::Vector<int32_t>> int32_list_value) {
    fbb_.AddOffset(Value::VT_INT32_LIST_VALUE, int32_list_value);
  }
  void add_int64_list_value(flatbuffers::Offset<flatbuffers::Vector<int64_t>> int64_list_value) {
    fbb_.AddOffset(Value::VT_INT64_LIST_VALUE, int64_list_value);
  }
  void add_float_list_value(flatbuffers::Offset<flatbuffers::Vector<float>> float_list_value) {
    fbb_.AddOffset(Value::VT_FLOAT_LIST_VALUE, float_list_value);
  }
  void add_double_list_value(flatbuffers::Offset<flatbuffers::Vector<double>> double_list_value) {
    fbb_.AddOffset(Value::VT_DOUBLE_LIST_VALUE, double_list_value);
  }
  void add_string_list_value(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> string_list_value) {
    fbb_.AddOffset(Value::VT_STRING_LIST_VALUE, string_list_value);
  }
  explicit ValueBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ValueBuilder &operator=(const ValueBuilder &);
  flatbuffers::Offset<Value> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Value>(end);
    return o;
  }
};

inline flatbuffers::Offset<Value> CreateValue(
    flatbuffers::FlatBufferBuilder &_fbb,
    bool bool_value = false,
    int32_t i8 = 0,
    int32_t i16 = 0,
    int32_t i32 = 0,
    int64_t i64 = 0,
    uint32_t u8 = 0,
    uint32_t u16 = 0,
    uint32_t u32 = 0,
    uint64_t u64 = 0,
    float float_value = 0.0f,
    double double_value = 0.0,
    flatbuffers::Offset<flatbuffers::String> string_value = 0,
    flatbuffers::Offset<flatbuffers::Vector<int32_t>> int8_list_value = 0,
    flatbuffers::Offset<flatbuffers::Vector<int32_t>> int16_list_value = 0,
    flatbuffers::Offset<flatbuffers::Vector<int32_t>> int32_list_value = 0,
    flatbuffers::Offset<flatbuffers::Vector<int64_t>> int64_list_value = 0,
    flatbuffers::Offset<flatbuffers::Vector<float>> float_list_value = 0,
    flatbuffers::Offset<flatbuffers::Vector<double>> double_list_value = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> string_list_value = 0) {
  ValueBuilder builder_(_fbb);
  builder_.add_double_value(double_value);
  builder_.add_u64(u64);
  builder_.add_i64(i64);
  builder_.add_string_list_value(string_list_value);
  builder_.add_double_list_value(double_list_value);
  builder_.add_float_list_value(float_list_value);
  builder_.add_int64_list_value(int64_list_value);
  builder_.add_int32_list_value(int32_list_value);
  builder_.add_int16_list_value(int16_list_value);
  builder_.add_int8_list_value(int8_list_value);
  builder_.add_string_value(string_value);
  builder_.add_float_value(float_value);
  builder_.add_u32(u32);
  builder_.add_u16(u16);
  builder_.add_u8(u8);
  builder_.add_i32(i32);
  builder_.add_i16(i16);
  builder_.add_i8(i8);
  builder_.add_bool_value(bool_value);
  return builder_.Finish();
}

inline flatbuffers::Offset<Value> CreateValueDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    bool bool_value = false,
    int32_t i8 = 0,
    int32_t i16 = 0,
    int32_t i32 = 0,
    int64_t i64 = 0,
    uint32_t u8 = 0,
    uint32_t u16 = 0,
    uint32_t u32 = 0,
    uint64_t u64 = 0,
    float float_value = 0.0f,
    double double_value = 0.0,
    const char *string_value = nullptr,
    const std::vector<int32_t> *int8_list_value = nullptr,
    const std::vector<int32_t> *int16_list_value = nullptr,
    const std::vector<int32_t> *int32_list_value = nullptr,
    const std::vector<int64_t> *int64_list_value = nullptr,
    const std::vector<float> *float_list_value = nullptr,
    const std::vector<double> *double_list_value = nullptr,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *string_list_value = nullptr) {
  return ks::grid::flat::CreateValue(
      _fbb,
      bool_value,
      i8,
      i16,
      i32,
      i64,
      u8,
      u16,
      u32,
      u64,
      float_value,
      double_value,
      string_value ? _fbb.CreateString(string_value) : 0,
      int8_list_value ? _fbb.CreateVector<int32_t>(*int8_list_value) : 0,
      int16_list_value ? _fbb.CreateVector<int32_t>(*int16_list_value) : 0,
      int32_list_value ? _fbb.CreateVector<int32_t>(*int32_list_value) : 0,
      int64_list_value ? _fbb.CreateVector<int64_t>(*int64_list_value) : 0,
      float_list_value ? _fbb.CreateVector<float>(*float_list_value) : 0,
      double_list_value ? _fbb.CreateVector<double>(*double_list_value) : 0,
      string_list_value ? _fbb.CreateVector<flatbuffers::Offset<flatbuffers::String>>(*string_list_value) : 0);
}

flatbuffers::Offset<Value> CreateValue(flatbuffers::FlatBufferBuilder &_fbb, const ValueT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct KeyT : public flatbuffers::NativeTable {
  typedef Key TableType;
  int32_t i32;
  int64_t i64;
  KeyT()
      : i32(0),
        i64(0) {
  }
};

struct Key FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef KeyT NativeTableType;
  static const flatbuffers::TypeTable *MiniReflectTypeTable() {
    return KeyTypeTable();
  }
  enum {
    VT_I32 = 4,
    VT_I64 = 6
  };
  int32_t i32() const {
    return GetField<int32_t>(VT_I32, 0);
  }
  bool mutate_i32(int32_t _i32) {
    return SetField<int32_t>(VT_I32, _i32, 0);
  }
  int64_t i64() const {
    return GetField<int64_t>(VT_I64, 0);
  }
  bool mutate_i64(int64_t _i64) {
    return SetField<int64_t>(VT_I64, _i64, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_I32) &&
           VerifyField<int64_t>(verifier, VT_I64) &&
           verifier.EndTable();
  }
  KeyT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(KeyT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<Key> Pack(flatbuffers::FlatBufferBuilder &_fbb, const KeyT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct KeyBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_i32(int32_t i32) {
    fbb_.AddElement<int32_t>(Key::VT_I32, i32, 0);
  }
  void add_i64(int64_t i64) {
    fbb_.AddElement<int64_t>(Key::VT_I64, i64, 0);
  }
  explicit KeyBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  KeyBuilder &operator=(const KeyBuilder &);
  flatbuffers::Offset<Key> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Key>(end);
    return o;
  }
};

inline flatbuffers::Offset<Key> CreateKey(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t i32 = 0,
    int64_t i64 = 0) {
  KeyBuilder builder_(_fbb);
  builder_.add_i64(i64);
  builder_.add_i32(i32);
  return builder_.Finish();
}

flatbuffers::Offset<Key> CreateKey(flatbuffers::FlatBufferBuilder &_fbb, const KeyT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ColumnT : public flatbuffers::NativeTable {
  typedef Column TableType;
  std::string name;
  ColumnType type;
  ColumnT()
      : type(ColumnType_UNKNOWN_COLUMN) {
  }
};

struct Column FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef ColumnT NativeTableType;
  static const flatbuffers::TypeTable *MiniReflectTypeTable() {
    return ColumnTypeTable();
  }
  enum {
    VT_NAME = 4,
    VT_TYPE = 6
  };
  const flatbuffers::String *name() const {
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String *mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
  ColumnType type() const {
    return static_cast<ColumnType>(GetField<int8_t>(VT_TYPE, 0));
  }
  bool mutate_type(ColumnType _type) {
    return SetField<int8_t>(VT_TYPE, static_cast<int8_t>(_type), 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.Verify(name()) &&
           VerifyField<int8_t>(verifier, VT_TYPE) &&
           verifier.EndTable();
  }
  ColumnT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(ColumnT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<Column> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ColumnT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct ColumnBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(Column::VT_NAME, name);
  }
  void add_type(ColumnType type) {
    fbb_.AddElement<int8_t>(Column::VT_TYPE, static_cast<int8_t>(type), 0);
  }
  explicit ColumnBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ColumnBuilder &operator=(const ColumnBuilder &);
  flatbuffers::Offset<Column> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Column>(end);
    return o;
  }
};

inline flatbuffers::Offset<Column> CreateColumn(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name = 0,
    ColumnType type = ColumnType_UNKNOWN_COLUMN) {
  ColumnBuilder builder_(_fbb);
  builder_.add_name(name);
  builder_.add_type(type);
  return builder_.Finish();
}

inline flatbuffers::Offset<Column> CreateColumnDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    ColumnType type = ColumnType_UNKNOWN_COLUMN) {
  return ks::grid::flat::CreateColumn(
      _fbb,
      name ? _fbb.CreateString(name) : 0,
      type);
}

flatbuffers::Offset<Column> CreateColumn(flatbuffers::FlatBufferBuilder &_fbb, const ColumnT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct RowT : public flatbuffers::NativeTable {
  typedef Row TableType;
  std::vector<std::unique_ptr<ValueT>> data;
  RowT() {
  }
};

struct Row FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef RowT NativeTableType;
  static const flatbuffers::TypeTable *MiniReflectTypeTable() {
    return RowTypeTable();
  }
  enum {
    VT_DATA = 4
  };
  const flatbuffers::Vector<flatbuffers::Offset<Value>> *data() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<Value>> *>(VT_DATA);
  }
  flatbuffers::Vector<flatbuffers::Offset<Value>> *mutable_data() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<Value>> *>(VT_DATA);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_DATA) &&
           verifier.Verify(data()) &&
           verifier.VerifyVectorOfTables(data()) &&
           verifier.EndTable();
  }
  RowT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(RowT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<Row> Pack(flatbuffers::FlatBufferBuilder &_fbb, const RowT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct RowBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_data(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Value>>> data) {
    fbb_.AddOffset(Row::VT_DATA, data);
  }
  explicit RowBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  RowBuilder &operator=(const RowBuilder &);
  flatbuffers::Offset<Row> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Row>(end);
    return o;
  }
};

inline flatbuffers::Offset<Row> CreateRow(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Value>>> data = 0) {
  RowBuilder builder_(_fbb);
  builder_.add_data(data);
  return builder_.Finish();
}

inline flatbuffers::Offset<Row> CreateRowDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<Value>> *data = nullptr) {
  return ks::grid::flat::CreateRow(
      _fbb,
      data ? _fbb.CreateVector<flatbuffers::Offset<Value>>(*data) : 0);
}

flatbuffers::Offset<Row> CreateRow(flatbuffers::FlatBufferBuilder &_fbb, const RowT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct KVQueryT : public flatbuffers::NativeTable {
  typedef KVQuery TableType;
  std::string table_name;
  std::vector<std::string> select_fields;
  std::vector<std::unique_ptr<KeyT>> keys;
  std::string field;
  KVQueryT() {
  }
};

struct KVQuery FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef KVQueryT NativeTableType;
  static const flatbuffers::TypeTable *MiniReflectTypeTable() {
    return KVQueryTypeTable();
  }
  enum {
    VT_TABLE_NAME = 4,
    VT_SELECT_FIELDS = 6,
    VT_KEYS = 8,
    VT_FIELD = 10
  };
  const flatbuffers::String *table_name() const {
    return GetPointer<const flatbuffers::String *>(VT_TABLE_NAME);
  }
  flatbuffers::String *mutable_table_name() {
    return GetPointer<flatbuffers::String *>(VT_TABLE_NAME);
  }
  const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *select_fields() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_SELECT_FIELDS);
  }
  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *mutable_select_fields() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_SELECT_FIELDS);
  }
  const flatbuffers::Vector<flatbuffers::Offset<Key>> *keys() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<Key>> *>(VT_KEYS);
  }
  flatbuffers::Vector<flatbuffers::Offset<Key>> *mutable_keys() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<Key>> *>(VT_KEYS);
  }
  const flatbuffers::String *field() const {
    return GetPointer<const flatbuffers::String *>(VT_FIELD);
  }
  flatbuffers::String *mutable_field() {
    return GetPointer<flatbuffers::String *>(VT_FIELD);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_TABLE_NAME) &&
           verifier.Verify(table_name()) &&
           VerifyOffset(verifier, VT_SELECT_FIELDS) &&
           verifier.Verify(select_fields()) &&
           verifier.VerifyVectorOfStrings(select_fields()) &&
           VerifyOffset(verifier, VT_KEYS) &&
           verifier.Verify(keys()) &&
           verifier.VerifyVectorOfTables(keys()) &&
           VerifyOffset(verifier, VT_FIELD) &&
           verifier.Verify(field()) &&
           verifier.EndTable();
  }
  KVQueryT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(KVQueryT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<KVQuery> Pack(flatbuffers::FlatBufferBuilder &_fbb, const KVQueryT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct KVQueryBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_table_name(flatbuffers::Offset<flatbuffers::String> table_name) {
    fbb_.AddOffset(KVQuery::VT_TABLE_NAME, table_name);
  }
  void add_select_fields(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> select_fields) {
    fbb_.AddOffset(KVQuery::VT_SELECT_FIELDS, select_fields);
  }
  void add_keys(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Key>>> keys) {
    fbb_.AddOffset(KVQuery::VT_KEYS, keys);
  }
  void add_field(flatbuffers::Offset<flatbuffers::String> field) {
    fbb_.AddOffset(KVQuery::VT_FIELD, field);
  }
  explicit KVQueryBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  KVQueryBuilder &operator=(const KVQueryBuilder &);
  flatbuffers::Offset<KVQuery> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<KVQuery>(end);
    return o;
  }
};

inline flatbuffers::Offset<KVQuery> CreateKVQuery(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> table_name = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> select_fields = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Key>>> keys = 0,
    flatbuffers::Offset<flatbuffers::String> field = 0) {
  KVQueryBuilder builder_(_fbb);
  builder_.add_field(field);
  builder_.add_keys(keys);
  builder_.add_select_fields(select_fields);
  builder_.add_table_name(table_name);
  return builder_.Finish();
}

inline flatbuffers::Offset<KVQuery> CreateKVQueryDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *table_name = nullptr,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *select_fields = nullptr,
    const std::vector<flatbuffers::Offset<Key>> *keys = nullptr,
    const char *field = nullptr) {
  return ks::grid::flat::CreateKVQuery(
      _fbb,
      table_name ? _fbb.CreateString(table_name) : 0,
      select_fields ? _fbb.CreateVector<flatbuffers::Offset<flatbuffers::String>>(*select_fields) : 0,
      keys ? _fbb.CreateVector<flatbuffers::Offset<Key>>(*keys) : 0,
      field ? _fbb.CreateString(field) : 0);
}

flatbuffers::Offset<KVQuery> CreateKVQuery(flatbuffers::FlatBufferBuilder &_fbb, const KVQueryT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct TableT : public flatbuffers::NativeTable {
  typedef Table TableType;
  std::string name;
  std::vector<std::unique_ptr<ColumnT>> columns;
  std::vector<std::unique_ptr<RowT>> rows;
  TableT() {
  }
};

struct Table FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef TableT NativeTableType;
  static const flatbuffers::TypeTable *MiniReflectTypeTable() {
    return TableTypeTable();
  }
  enum {
    VT_NAME = 4,
    VT_COLUMNS = 6,
    VT_ROWS = 8
  };
  const flatbuffers::String *name() const {
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String *mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
  const flatbuffers::Vector<flatbuffers::Offset<Column>> *columns() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<Column>> *>(VT_COLUMNS);
  }
  flatbuffers::Vector<flatbuffers::Offset<Column>> *mutable_columns() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<Column>> *>(VT_COLUMNS);
  }
  const flatbuffers::Vector<flatbuffers::Offset<Row>> *rows() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<Row>> *>(VT_ROWS);
  }
  flatbuffers::Vector<flatbuffers::Offset<Row>> *mutable_rows() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<Row>> *>(VT_ROWS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.Verify(name()) &&
           VerifyOffset(verifier, VT_COLUMNS) &&
           verifier.Verify(columns()) &&
           verifier.VerifyVectorOfTables(columns()) &&
           VerifyOffset(verifier, VT_ROWS) &&
           verifier.Verify(rows()) &&
           verifier.VerifyVectorOfTables(rows()) &&
           verifier.EndTable();
  }
  TableT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(TableT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<Table> Pack(flatbuffers::FlatBufferBuilder &_fbb, const TableT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct TableBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(Table::VT_NAME, name);
  }
  void add_columns(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Column>>> columns) {
    fbb_.AddOffset(Table::VT_COLUMNS, columns);
  }
  void add_rows(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Row>>> rows) {
    fbb_.AddOffset(Table::VT_ROWS, rows);
  }
  explicit TableBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  TableBuilder &operator=(const TableBuilder &);
  flatbuffers::Offset<Table> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Table>(end);
    return o;
  }
};

inline flatbuffers::Offset<Table> CreateTable(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Column>>> columns = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Row>>> rows = 0) {
  TableBuilder builder_(_fbb);
  builder_.add_rows(rows);
  builder_.add_columns(columns);
  builder_.add_name(name);
  return builder_.Finish();
}

inline flatbuffers::Offset<Table> CreateTableDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    const std::vector<flatbuffers::Offset<Column>> *columns = nullptr,
    const std::vector<flatbuffers::Offset<Row>> *rows = nullptr) {
  return ks::grid::flat::CreateTable(
      _fbb,
      name ? _fbb.CreateString(name) : 0,
      columns ? _fbb.CreateVector<flatbuffers::Offset<Column>>(*columns) : 0,
      rows ? _fbb.CreateVector<flatbuffers::Offset<Row>>(*rows) : 0);
}

flatbuffers::Offset<Table> CreateTable(flatbuffers::FlatBufferBuilder &_fbb, const TableT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

inline ValueT *Value::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = new ValueT();
  UnPackTo(_o, _resolver);
  return _o;
}

inline void Value::UnPackTo(ValueT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = bool_value(); _o->bool_value = _e; };
  { auto _e = i8(); _o->i8 = _e; };
  { auto _e = i16(); _o->i16 = _e; };
  { auto _e = i32(); _o->i32 = _e; };
  { auto _e = i64(); _o->i64 = _e; };
  { auto _e = u8(); _o->u8 = _e; };
  { auto _e = u16(); _o->u16 = _e; };
  { auto _e = u32(); _o->u32 = _e; };
  { auto _e = u64(); _o->u64 = _e; };
  { auto _e = float_value(); _o->float_value = _e; };
  { auto _e = double_value(); _o->double_value = _e; };
  { auto _e = string_value(); if (_e) _o->string_value = _e->str(); };
  { auto _e = int8_list_value(); if (_e) { _o->int8_list_value.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->int8_list_value[_i] = _e->Get(_i); } } };
  { auto _e = int16_list_value(); if (_e) { _o->int16_list_value.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->int16_list_value[_i] = _e->Get(_i); } } };
  { auto _e = int32_list_value(); if (_e) { _o->int32_list_value.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->int32_list_value[_i] = _e->Get(_i); } } };
  { auto _e = int64_list_value(); if (_e) { _o->int64_list_value.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->int64_list_value[_i] = _e->Get(_i); } } };
  { auto _e = float_list_value(); if (_e) { _o->float_list_value.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->float_list_value[_i] = _e->Get(_i); } } };
  { auto _e = double_list_value(); if (_e) { _o->double_list_value.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->double_list_value[_i] = _e->Get(_i); } } };
  { auto _e = string_list_value(); if (_e) { _o->string_list_value.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->string_list_value[_i] = _e->Get(_i)->str(); } } };
}

inline flatbuffers::Offset<Value> Value::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ValueT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateValue(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<Value> CreateValue(flatbuffers::FlatBufferBuilder &_fbb, const ValueT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ValueT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _bool_value = _o->bool_value;
  auto _i8 = _o->i8;
  auto _i16 = _o->i16;
  auto _i32 = _o->i32;
  auto _i64 = _o->i64;
  auto _u8 = _o->u8;
  auto _u16 = _o->u16;
  auto _u32 = _o->u32;
  auto _u64 = _o->u64;
  auto _float_value = _o->float_value;
  auto _double_value = _o->double_value;
  auto _string_value = _o->string_value.empty() ? 0 : _fbb.CreateString(_o->string_value);
  auto _int8_list_value = _o->int8_list_value.size() ? _fbb.CreateVector(_o->int8_list_value) : 0;
  auto _int16_list_value = _o->int16_list_value.size() ? _fbb.CreateVector(_o->int16_list_value) : 0;
  auto _int32_list_value = _o->int32_list_value.size() ? _fbb.CreateVector(_o->int32_list_value) : 0;
  auto _int64_list_value = _o->int64_list_value.size() ? _fbb.CreateVector(_o->int64_list_value) : 0;
  auto _float_list_value = _o->float_list_value.size() ? _fbb.CreateVector(_o->float_list_value) : 0;
  auto _double_list_value = _o->double_list_value.size() ? _fbb.CreateVector(_o->double_list_value) : 0;
  auto _string_list_value = _o->string_list_value.size() ? _fbb.CreateVectorOfStrings(_o->string_list_value) : 0;
  return ks::grid::flat::CreateValue(
      _fbb,
      _bool_value,
      _i8,
      _i16,
      _i32,
      _i64,
      _u8,
      _u16,
      _u32,
      _u64,
      _float_value,
      _double_value,
      _string_value,
      _int8_list_value,
      _int16_list_value,
      _int32_list_value,
      _int64_list_value,
      _float_list_value,
      _double_list_value,
      _string_list_value);
}

inline KeyT *Key::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = new KeyT();
  UnPackTo(_o, _resolver);
  return _o;
}

inline void Key::UnPackTo(KeyT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = i32(); _o->i32 = _e; };
  { auto _e = i64(); _o->i64 = _e; };
}

inline flatbuffers::Offset<Key> Key::Pack(flatbuffers::FlatBufferBuilder &_fbb, const KeyT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateKey(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<Key> CreateKey(flatbuffers::FlatBufferBuilder &_fbb, const KeyT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const KeyT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _i32 = _o->i32;
  auto _i64 = _o->i64;
  return ks::grid::flat::CreateKey(
      _fbb,
      _i32,
      _i64);
}

inline ColumnT *Column::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = new ColumnT();
  UnPackTo(_o, _resolver);
  return _o;
}

inline void Column::UnPackTo(ColumnT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); };
  { auto _e = type(); _o->type = _e; };
}

inline flatbuffers::Offset<Column> Column::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ColumnT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateColumn(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<Column> CreateColumn(flatbuffers::FlatBufferBuilder &_fbb, const ColumnT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ColumnT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _type = _o->type;
  return ks::grid::flat::CreateColumn(
      _fbb,
      _name,
      _type);
}

inline RowT *Row::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = new RowT();
  UnPackTo(_o, _resolver);
  return _o;
}

inline void Row::UnPackTo(RowT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = data(); if (_e) { _o->data.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->data[_i] = std::unique_ptr<ValueT>(_e->Get(_i)->UnPack(_resolver)); } } };
}

inline flatbuffers::Offset<Row> Row::Pack(flatbuffers::FlatBufferBuilder &_fbb, const RowT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateRow(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<Row> CreateRow(flatbuffers::FlatBufferBuilder &_fbb, const RowT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const RowT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _data = _o->data.size() ? _fbb.CreateVector<flatbuffers::Offset<Value>> (_o->data.size(), [](size_t i, _VectorArgs *__va) { return CreateValue(*__va->__fbb, __va->__o->data[i].get(), __va->__rehasher); }, &_va ) : 0;
  return ks::grid::flat::CreateRow(
      _fbb,
      _data);
}

inline KVQueryT *KVQuery::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = new KVQueryT();
  UnPackTo(_o, _resolver);
  return _o;
}

inline void KVQuery::UnPackTo(KVQueryT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = table_name(); if (_e) _o->table_name = _e->str(); };
  { auto _e = select_fields(); if (_e) { _o->select_fields.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->select_fields[_i] = _e->Get(_i)->str(); } } };
  { auto _e = keys(); if (_e) { _o->keys.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->keys[_i] = std::unique_ptr<KeyT>(_e->Get(_i)->UnPack(_resolver)); } } };
  { auto _e = field(); if (_e) _o->field = _e->str(); };
}

inline flatbuffers::Offset<KVQuery> KVQuery::Pack(flatbuffers::FlatBufferBuilder &_fbb, const KVQueryT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateKVQuery(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<KVQuery> CreateKVQuery(flatbuffers::FlatBufferBuilder &_fbb, const KVQueryT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const KVQueryT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _table_name = _o->table_name.empty() ? 0 : _fbb.CreateString(_o->table_name);
  auto _select_fields = _o->select_fields.size() ? _fbb.CreateVectorOfStrings(_o->select_fields) : 0;
  auto _keys = _o->keys.size() ? _fbb.CreateVector<flatbuffers::Offset<Key>> (_o->keys.size(), [](size_t i, _VectorArgs *__va) { return CreateKey(*__va->__fbb, __va->__o->keys[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _field = _o->field.empty() ? 0 : _fbb.CreateString(_o->field);
  return ks::grid::flat::CreateKVQuery(
      _fbb,
      _table_name,
      _select_fields,
      _keys,
      _field);
}

inline TableT *Table::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = new TableT();
  UnPackTo(_o, _resolver);
  return _o;
}

inline void Table::UnPackTo(TableT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); };
  { auto _e = columns(); if (_e) { _o->columns.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->columns[_i] = std::unique_ptr<ColumnT>(_e->Get(_i)->UnPack(_resolver)); } } };
  { auto _e = rows(); if (_e) { _o->rows.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->rows[_i] = std::unique_ptr<RowT>(_e->Get(_i)->UnPack(_resolver)); } } };
}

inline flatbuffers::Offset<Table> Table::Pack(flatbuffers::FlatBufferBuilder &_fbb, const TableT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateTable(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<Table> CreateTable(flatbuffers::FlatBufferBuilder &_fbb, const TableT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const TableT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _columns = _o->columns.size() ? _fbb.CreateVector<flatbuffers::Offset<Column>> (_o->columns.size(), [](size_t i, _VectorArgs *__va) { return CreateColumn(*__va->__fbb, __va->__o->columns[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _rows = _o->rows.size() ? _fbb.CreateVector<flatbuffers::Offset<Row>> (_o->rows.size(), [](size_t i, _VectorArgs *__va) { return CreateRow(*__va->__fbb, __va->__o->rows[i].get(), __va->__rehasher); }, &_va ) : 0;
  return ks::grid::flat::CreateTable(
      _fbb,
      _name,
      _columns,
      _rows);
}

inline const flatbuffers::TypeTable *ColumnTypeTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    ColumnTypeTypeTable
  };
  static const char * const names[] = {
    "UNKNOWN_COLUMN",
    "BOOL",
    "INT32",
    "INT64",
    "FLOAT",
    "DOUBLE",
    "STRING",
    "INT32_LIST",
    "INT64_LIST",
    "FLOAT_LIST",
    "DOUBLE_LIST",
    "STRING_LIST",
    "TS_LIST",
    "INT8",
    "INT16",
    "INT8_LIST",
    "INT16_LIST"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_ENUM, 17, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ValueTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_BOOL, 0, -1 },
    { flatbuffers::ET_INT, 0, -1 },
    { flatbuffers::ET_INT, 0, -1 },
    { flatbuffers::ET_INT, 0, -1 },
    { flatbuffers::ET_LONG, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_ULONG, 0, -1 },
    { flatbuffers::ET_FLOAT, 0, -1 },
    { flatbuffers::ET_DOUBLE, 0, -1 },
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_INT, 1, -1 },
    { flatbuffers::ET_INT, 1, -1 },
    { flatbuffers::ET_INT, 1, -1 },
    { flatbuffers::ET_LONG, 1, -1 },
    { flatbuffers::ET_FLOAT, 1, -1 },
    { flatbuffers::ET_DOUBLE, 1, -1 },
    { flatbuffers::ET_STRING, 1, -1 }
  };
  static const char * const names[] = {
    "bool_value",
    "i8",
    "i16",
    "i32",
    "i64",
    "u8",
    "u16",
    "u32",
    "u64",
    "float_value",
    "double_value",
    "string_value",
    "int8_list_value",
    "int16_list_value",
    "int32_list_value",
    "int64_list_value",
    "float_list_value",
    "double_list_value",
    "string_list_value"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 19, type_codes, nullptr, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *KeyTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_INT, 0, -1 },
    { flatbuffers::ET_LONG, 0, -1 }
  };
  static const char * const names[] = {
    "i32",
    "i64"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 2, type_codes, nullptr, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ColumnTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_CHAR, 0, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    ColumnTypeTypeTable
  };
  static const char * const names[] = {
    "name",
    "type"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 2, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *RowTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 1, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    ValueTypeTable
  };
  static const char * const names[] = {
    "data"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 1, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *KVQueryTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_STRING, 1, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 0 },
    { flatbuffers::ET_STRING, 0, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    KeyTypeTable
  };
  static const char * const names[] = {
    "table_name",
    "select_fields",
    "keys",
    "field"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 4, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *TableTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 0 },
    { flatbuffers::ET_SEQUENCE, 1, 1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    ColumnTypeTable,
    RowTypeTable
  };
  static const char * const names[] = {
    "name",
    "columns",
    "rows"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 3, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

}  // namespace flat
}  // namespace grid
}  // namespace ks

#endif  // FLATBUFFERS_GENERATED_COMMON_KS_GRID_FLAT_H_
