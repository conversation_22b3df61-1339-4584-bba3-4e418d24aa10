#include <unistd.h>
#include <string>
#include <vector>
#include <iostream>
#include "gflags/gflags.h"
#include "teams/ad/grid/sdk/grid_client.h"
#include "teams/ad/grid/sdk/grid_data.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "base/common/base.h"

using ks::grid::Querykey;
using ks::grid::GridQuery;
using ks::grid::GridData;
using ks::grid::GridClient;

GridQuery BuildQuery(std::vector<std::string> cols, std::vector<int64_t> ids) {
  GridQuery query;
  for (auto col : cols) {
    query.select_fields.push_back(col);
  }
  for (auto id : ids) {
    Querykey key;
    key.i64 = id;
    query.ids.push_back(key);
  }
  return query;
}

void func(const GridData& data, uint32_t row_index) {
  std::string t;
  std::vector<int8_t> vec8;
  std::vector<std::string> strs;
  std::cout << "cross row index Bool(0,0):" << data.GetBool(row_index, 0) << std::endl;
  std::cout << "cross row index Bool(0,100):" << data.GetBool(row_index, 100) << std::endl;
  std::cout << "cross row index Int8(0,0):" << data.GetInt8(row_index, 0) << std::endl;
  std::cout << "cross row index Int8(0,100):" << data.GetInt8(row_index, 100) << std::endl;
  std::cout << "cross row index Int16(0,0):" << data.GetInt16(row_index, 0) << std::endl;
  std::cout << "cross row index Int16(0,100):" << data.GetInt16(row_index, 100) << std::endl;
  std::cout << "cross row index Int32(0,0):" << data.GetInt32(row_index, 0) << std::endl;
  std::cout << "cross row index Int32(0,100):" << data.GetInt32(row_index, 100) << std::endl;
  std::cout << "cross row index Int64(0,0):" << data.GetInt64(row_index, 0) << std::endl;
  std::cout << "cross row index Int64(0,100):" << data.GetInt64(row_index, 100) << std::endl;
  std::cout << "cross row index Float(0,0):" << data.GetFloat(row_index, 0) << std::endl;
  std::cout << "cross row index Float(0,100):" << data.GetFloat(row_index, 100) << std::endl;
  std::cout << "cross row index Double(0,0):" << data.GetDouble(row_index, 0) << std::endl;
  std::cout << "cross row index Double(0,100):" << data.GetDouble(row_index, 100) << std::endl;

  data.GetString(row_index, 0, &t);
  data.GetInt8List(row_index, 0, &vec8);
  data.GetStringList(row_index, 0, &strs);
  std::cout << "cross row index String(0,0):" << t << std::endl;
  std::cout << "cross row index Int8List(0,0):" << vec8.size() << std::endl;
  std::cout << "cross row index StringList(0,0):" << strs.size() << std::endl;
  for (int i = 0; i < 100; ++i) std::cout << "*";
  std::cout << std::endl << std::endl;
}

int main(int argc, char **argv) {
  base::InitApp(&argc, &argv, "");
  AD_KESS_CLIENT_INIT_CHECK("init error");
  FLAGS_enable_grid_sdk_cache = true;

  // 正常请求
  {
    std::cout << "test 1" << std::endl;
    std::vector<std::string> cols = {
      "id", "user_id", "test"
    };
    std::vector<int64_t> ids = {
      844, 912, 940, 964, 1004
    };
    GridQuery query = BuildQuery(cols, ids);
    query.table_name = "ad_dsp_account";
    std::vector<GridQuery> req{query};
    std::vector<GridData> resp;

    auto res = GridClient::Instance()->BatchGetTable(req, &resp);

    if (res < 0) {
      std::cout << "failed" << std::endl;
    } else {
      std::cout << "success" << std::endl;
      std::cout << "table nums: " << resp.size() << std::endl;
      for (auto table : resp) {
        std::cout << "table row size" << table.RowSize() << std::endl;
        std::cout << "table col size" << table.ColSize() << std::endl;
      }
    }
  }

  // 缓存全部命中
  {
    std::cout << std::endl << "test 2" << std::endl;
    std::vector<std::string> cols = {
      "id", "user_id", "test"
    };
    std::vector<int64_t> ids = {
      844, 912, 940, 964, 1004
    };
    GridQuery query = BuildQuery(cols, ids);
    query.table_name = "ad_dsp_account";
    std::vector<GridQuery> req{query};
    std::vector<GridData> resp;

    auto res = GridClient::Instance()->BatchGetTable(req, &resp);

    if (res < 0) {
      std::cout << "failed" << std::endl;
    } else {
      std::cout << "success" << std::endl;
      std::cout << "table nums: " << resp.size() << std::endl;
      for (auto table : resp) {
        std::cout << "table row size" << table.RowSize() << std::endl;
        std::cout << "table col size" << table.ColSize() << std::endl;
      }
    }
  }

  // 一半缓存命中
  {
    std::cout << std::endl << "test 3" << std::endl;
    std::vector<std::string> cols = {
      "id", "user_id", "test"
    };
    std::vector<int64_t> ids = {
      844, 912, 940, 964, 1, 2
    };
    GridQuery query = BuildQuery(cols, ids);
    query.table_name = "ad_dsp_account";
    std::vector<GridQuery> req{query};
    std::vector<GridData> resp;

    auto res = GridClient::Instance()->BatchGetTable(req, &resp);

    if (res < 0) {
      std::cout << "failed" << std::endl;
    } else {
      std::cout << "success" << std::endl;
      std::cout << "table nums: " << resp.size() << std::endl;
      for (auto table : resp) {
        std::cout << "table row size" << table.RowSize() << std::endl;
        std::cout << "table col size" << table.ColSize() << std::endl;
      }
    }
  }
  // 两个有效表查询, 一个无缓存
  {
    std::cout << std::endl << "test 4" << std::endl;
    std::vector<std::string> cols = {
      "id", "user_id", "test"
    };
    std::vector<int64_t> ids = {
      844, 912, 940, 964, 1, 2
    };
    GridQuery query_account = BuildQuery(cols, ids);
    query_account.table_name = "ad_dsp_account";

    std::vector<std::string> cols2 = {
      "id", "campaign_id", "account_id"
    };
    std::vector<int64_t> ids2 = {
      **********, **********, **********
    };
    GridQuery query_unit = BuildQuery(cols2, ids2);
    query_unit.table_name = "ad_dsp_unit";


    std::vector<GridQuery> req{query_account, query_unit};
    std::vector<GridData> resp;

    auto res = GridClient::Instance()->BatchGetTable(req, &resp);

    if (res < 0) {
      std::cout << "failed" << std::endl;
    } else {
      std::cout << "success" << std::endl;
      std::cout << "table nums: " << resp.size() << std::endl;
      for (auto table : resp) {
        std::cout << "table row size" << table.RowSize() << std::endl;
        std::cout << "table col size" << table.ColSize() << std::endl;
      }
    }
  }

  // 两个有效表查询，都有缓存
  {
    std::cout << std::endl << "test 5" << std::endl;
    std::vector<std::string> cols = {
      "id", "user_id", "test"
    };
    std::vector<int64_t> ids = {
      844, 912, 940, 964
    };
    GridQuery query_account = BuildQuery(cols, ids);
    query_account.table_name = "ad_dsp_account";

    std::vector<std::string> cols2 = {
      "id", "campaign_id", "account_id"
    };
    std::vector<int64_t> ids2 = {
      **********, **********, **********
    };
    GridQuery query_unit = BuildQuery(cols2, ids2);
    query_unit.table_name = "ad_dsp_unit";


    std::vector<GridQuery> req{query_account, query_unit};
    std::vector<GridData> resp;

    auto res = GridClient::Instance()->BatchGetTable(req, &resp);

    if (res < 0) {
      std::cout << "failed" << std::endl;
    } else {
      std::cout << "success" << std::endl;
      std::cout << "table nums: " << resp.size() << std::endl;
      for (auto table : resp) {
        std::cout << "table row size" << table.RowSize() << std::endl;
        std::cout << "table col size" << table.ColSize() << std::endl;
      }
    }
  }

  // 两个有效表查询，只有部分缓存
  {
    std::cout << std::endl << "test 6" << std::endl;
    std::vector<std::string> cols = {
      "id", "user_id", "test"
    };
    std::vector<int64_t> ids = {
      1, 844, 912, 940, 964, 2
    };
    GridQuery query_account = BuildQuery(cols, ids);
    query_account.table_name = "ad_dsp_account";

    std::vector<std::string> cols2 = {
      "id", "campaign_id", "account_id"
    };
    std::vector<int64_t> ids2 = {
      3, **********, **********, **********, 4
    };
    GridQuery query_unit = BuildQuery(cols2, ids2);
    query_unit.table_name = "ad_dsp_unit";


    std::vector<GridQuery> req{query_account, query_unit};
    std::vector<GridData> resp;

    auto res = GridClient::Instance()->BatchGetTable(req, &resp);

    if (res < 0) {
      std::cout << "failed" << std::endl;
    } else {
      std::cout << "success" << std::endl;
      std::cout << "table nums: " << resp.size() << std::endl;
      for (auto table : resp) {
        std::cout << "table row size" << table.RowSize() << std::endl;
        std::cout << "table col size" << table.ColSize() << std::endl;
      }
    }
  }

  // 一个表不存在
  {
    std::cout << std::endl << "test 7" << std::endl;
    std::vector<std::string> cols = {
      "id", "user_id", "test"
    };
    std::vector<int64_t> ids = {
      1, 844, 912, 940, 964, 2
    };
    GridQuery query_account = BuildQuery(cols, ids);
    query_account.table_name = "ad_dsp_account";

    std::vector<std::string> cols2 = {
      "id", "campaign_id", "account_id"
    };
    std::vector<int64_t> ids2 = {
      3, **********, **********, **********, 4
    };
    GridQuery query_test = BuildQuery(cols2, ids2);
    query_test.table_name = "Test";


    std::vector<GridQuery> req1{query_account, query_test};
    std::vector<GridQuery> req2{query_test, query_account};
    std::vector<GridData> resp;

    auto res = GridClient::Instance()->BatchGetTable(req1, &resp);
    std::cout << "ACCOUNT , TEST" << std::endl;
    if (res < 0) {
      std::cout << "failed" << std::endl;
    } else {
      std::cout << "success" << std::endl;
      std::cout << "table nums: " << resp.size() << std::endl;
      for (auto table : resp) {
        std::cout << "table row size" << table.RowSize() << std::endl;
        std::cout << "table col size" << table.ColSize() << std::endl;
      }
    }

    std::cout << "TEST, ACCOUNT" << std::endl;
    res = GridClient::Instance()->BatchGetTable(req2, &resp);
    if (res < 0) {
      std::cout << "failed" << std::endl;
    } else {
      std::cout << "success" << std::endl;
      std::cout << "table nums: " << resp.size() << std::endl;
      for (auto table : resp) {
        std::cout << "table row size" << table.RowSize() << std::endl;
        std::cout << "table col size" << table.ColSize() << std::endl;
      }
    }

    func(resp[0], 0);
    func(resp[0], 100);

    func(resp[1], 0);
    func(resp[1], 100);

    func(resp[1], 1);
  }


  // 正常请求
  {
    std::cout << std::endl << "test 8" << std::endl;
    std::vector<std::string> cols = {
      "id", "user_id", "test"
    };
    std::vector<int64_t> ids = {
      844, 912, 940, 964, 1004
    };
    GridQuery query = BuildQuery(cols, ids);
    query.table_name = "ad_dsp_account";
    std::vector<GridQuery> req{query};
    std::vector<GridData> resp;

    auto res = GridClient::Instance()->BatchGetTable(req, &resp);
    if (res < 0) {
      std::cout << "failed" << std::endl;
    } else {
      std::cout << "success" << std::endl;
      std::cout << "table nums: " << resp.size() << std::endl;
      for (auto table : resp) {
        std::cout << "table row size" << table.RowSize() << std::endl;
        std::cout << "table col size" << table.ColSize() << std::endl;
      }
    }

    func(resp[0], 0);
  }

  // 单表 schema 不同
  {
    std::cout << std::endl << "test 9" << std::endl;
    std::vector<std::string> cols = {
      "id", "user_id", "test2"
    };
    std::vector<int64_t> ids = {
      844, 912, 940, 964, 1004
    };
    GridQuery query = BuildQuery(cols, ids);
    query.table_name = "ad_dsp_account";
    std::vector<GridQuery> req{query};
    std::vector<GridData> resp;

    auto res = GridClient::Instance()->BatchGetTable(req, &resp);

    if (res < 0) {
      std::cout << "failed" << std::endl;
    } else {
      std::cout << "success" << std::endl;
      std::cout << "table nums: " << resp.size() << std::endl;
      for (auto table : resp) {
        std::cout << "table row size" << table.RowSize() << std::endl;
        std::cout << "table col size" << table.ColSize() << std::endl;
      }
    }
  }

  // 单表 行或者列为空
  {
    std::cout << std::endl << "test 10" << std::endl;
    std::vector<std::string> cols1;
    std::vector<int64_t> ids1 = {
      844, 912, 940, 964, 1004
    };
    GridQuery query1 = BuildQuery(cols1, ids1);
    query1.table_name = "ad_dsp_account";
    std::vector<GridQuery> req1{query1};
    std::vector<GridData> resp1;

    auto res = GridClient::Instance()->BatchGetTable(req1, &resp1);

    if (res < 0) {
      std::cout << "failed" << std::endl;
    } else {
      std::cout << "success" << std::endl;
      std::cout << "table nums: " << resp1.size() << std::endl;
      for (auto table : resp1) {
        std::cout << "table row size" << table.RowSize() << std::endl;
        std::cout << "table col size" << table.ColSize() << std::endl;
      }
    }

    std::vector<std::string> cols2 = {
      "id", "user_id", "test2"
    };
    std::vector<int64_t> ids2;
    GridQuery query2 = BuildQuery(cols2, ids2);
    query2.table_name = "ad_dsp_account";
    std::vector<GridQuery> req2{query2};
    std::vector<GridData> resp2;

    res = GridClient::Instance()->BatchGetTable(req2, &resp2);

    if (res < 0) {
      std::cout << "failed" << std::endl;
    } else {
      std::cout << "success" << std::endl;
      std::cout << "table nums: " << resp2.size() << std::endl;
      for (auto table : resp2) {
        std::cout << "table row size" << table.RowSize() << std::endl;
        std::cout << "table col size" << table.ColSize() << std::endl;
      }
    }

    // 不设置名字
    std::vector<std::string> cols3 = {
      "id", "user_id", "test2"
    };
    std::vector<int64_t> ids3;
    GridQuery query3 = BuildQuery(cols3, ids3);
    std::vector<GridQuery> req3{query3};
    std::vector<GridData> resp3;

    res = GridClient::Instance()->BatchGetTable(req3, &resp3);

    if (res < 0) {
      std::cout << "failed" << std::endl;
    } else {
      std::cout << "success" << std::endl;
      std::cout << "table nums: " << resp3.size() << std::endl;
      for (auto table : resp3) {
        std::cout << "table row size" << table.RowSize() << std::endl;
        std::cout << "table col size" << table.ColSize() << std::endl;
      }
    }
  }
  // 多表 行或者列为空
  {
    std::cout << std::endl << "test 11" << std::endl;
    std::vector<std::string> cols1;
    std::vector<int64_t> ids1 = {
      844, 912, 940, 964, 1004
    };
    GridQuery query1 = BuildQuery(cols1, ids1);
    query1.table_name = "Test";

    std::vector<std::string> cols = {
      "id", "user_id", "test"
    };
    std::vector<int64_t> ids = {
      1, 844, 912, 940, 964, 2
    };
    GridQuery query_account = BuildQuery(cols, ids);
    query_account.table_name = "ad_dsp_account";

    std::vector<GridQuery> req1{query1, query_account};
    std::vector<GridData> resp1;

    auto res = GridClient::Instance()->BatchGetTable(req1, &resp1);

    if (res < 0) {
      std::cout << "failed" << std::endl;
    } else {
      std::cout << "success" << std::endl;
      std::cout << "table nums: " << resp1.size() << std::endl;
      for (auto table : resp1) {
        std::cout << "table row size" << table.RowSize() << std::endl;
        std::cout << "table col size" << table.ColSize() << std::endl;
      }
    }

    std::vector<std::string> cols2 = {
      "id", "user_id", "test2"
    };
    std::vector<int64_t> ids2;
    GridQuery query2 = BuildQuery(cols2, ids2);
    query2.table_name = "Test";
    std::vector<GridQuery> req2{query_account, query2};
    std::vector<GridData> resp2;

    res = GridClient::Instance()->BatchGetTable(req2, &resp2);

    if (res < 0) {
      std::cout << "failed" << std::endl;
    } else {
      std::cout << "success" << std::endl;
      std::cout << "table nums: " << resp2.size() << std::endl;
      for (auto table : resp2) {
        std::cout << "table row size" << table.RowSize() << std::endl;
        std::cout << "table col size" << table.ColSize() << std::endl;
      }
    }
  }


  // 多表 行或者列为空
  {
    std::cout << std::endl << "test 12" << std::endl;
    std::vector<std::string> cols = {
      "id", "user_id", "product_name", "min_cost_info_ocpx_action_type"
    };
    std::vector<int64_t> ids = {
      1, 844, 912, 940, 964, 2
    };
    GridQuery query_account = BuildQuery(cols, ids);
    query_account.table_name = "ad_dsp_account";

    std::vector<GridQuery> req{query_account};
    std::vector<GridData> resp;

    auto res = GridClient::Instance()->BatchGetTable(req, &resp);

    if (res < 0) {
      std::cout << "failed" << std::endl;
    } else {
      std::cout << "success" << std::endl;
      std::cout << "table nums: " << resp.size() << std::endl;
      for (auto table : resp) {
        std::cout << "table row size" << table.RowSize() << std::endl;
        std::cout << "table col size" << table.ColSize() << std::endl;
      }
    }

    for (int i = 0; i < ids.size(); ++i) {
      auto data = resp[0];
      std::vector<int32_t> t;
      std::string s;
      std::cout << "hh test here1" << std::endl;
      std::cout << "cross row index LIST:" <<
        data.GetInt32List(i, "min_cost_info_ocpx_action_type", &t) << std::endl;
      std::cout << "hh test here2" << std::endl;
      std::cout << "vec size: " << t.size() << std::endl;
      std::cout << "cross row index String:" << data.GetString(i, "product_name", &s) << std::endl;
      std::cout << "string : " << s << std::endl;
    }
  }

  {
    std::cout << std::endl << "test loop" << std::endl;
    int64_t count = 25000;
    while (count--) {
      usleep(1000);
      {
        std::vector<std::string> cols = {
          "id", "user_id", "product_name", "min_cost_info_ocpx_action_type"
        };
        std::vector<int64_t> ids = {
          844, 912, 940, 964, 1004
        };
        GridQuery query = BuildQuery(cols, ids);
        query.table_name = "ad_dsp_account";
        std::vector<GridQuery> req{query};
        std::vector<GridData> resp;
        auto res = GridClient::Instance()->BatchGetTable(req, &resp);
        if (count % 100 == 0) {
          if (res < 0) {
            std::cout << "failed" << std::endl;
          } else {
            std::cout << "success" << std::endl;
            std::cout << "table nums: " << resp.size() << std::endl;
            for (auto table : resp) {
              std::cout << "table row size" << table.RowSize() << std::endl;
              std::cout << "table col size" << table.ColSize() << std::endl;

              for (int i = 0; i < ids.size(); ++i) {
                std::vector<int32_t> t;
                std::string s;
                std::cout << "cross row index LIST:" <<
                  table.GetInt32List(i, "min_cost_info_ocpx_action_type", &t) << std::endl;
                std::cout << "vec size: " << t.size() << std::endl;
                std::cout << "cross row index String:" <<
                  table.GetString(i, "product_name", &s) << std::endl;
                std::cout << "string : " << s << std::endl;
              }
            }
          }
        }
      }
    }
  }
}
