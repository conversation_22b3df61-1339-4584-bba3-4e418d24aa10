#include <unistd.h>
#include <vector>
#include <string>
#include <iostream>
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "base/common/base.h"
#include "teams/ad/grid/proto/msg.pb.h"
#include "teams/ad/grid/sdk/grid_client.h"
#include "teams/ad/grid/sdk/grid_query.h"
#include "gflags/gflags.h"


using namespace ks::grid;   // NOLINT

GridQuery BuildQuery(std::vector<std::string> cols, std::vector<int64_t> ids) {
  GridQuery query;
  for (auto col : cols) {
    query.select_fields.push_back(col);
  }
  for (auto id : ids) {
    Querykey key;
    key.i64 = id;
    query.ids.push_back(key);
  }
  return query;
}

void BuildRequest(const std::vector<GridQuery>& querys, GridRequest* request) {
  request->set_type(proto::ProtoType::FB);
  auto kv_querys = request->mutable_kvquerys();
  kv_querys->Reserve(querys.size());
  for (int i = 0; i < querys.size(); ++i) {
    auto& query = querys[i];
    if (query.select_fields.size() == 0 || query.ids.size() == 0 || query.table_name.empty()) {
      LOG(WARNING) << "[Grid SDK] empty ids/select_fields/table_name"
                    << " id_size:" << query.ids.size()
                    << " select_fields_size:" << query.select_fields.size()
                    << " table_name:" << query.table_name;
      continue;
    }
    auto kv = kv_querys->Add();
    kv->set_table_name(query.table_name);
    kv->mutable_select_fields()->Assign(query.select_fields.begin(), query.select_fields.end());
    auto ids = kv->mutable_keys();
    ids->Reserve(query.ids.size());

    auto init_without_cache = [&ids, &query] () {
      for (auto& id : query.ids) {
        auto key = ids->Add();
        key->set_i64(id.i64);
      }
    };
    init_without_cache();
  }
  return;
}


int main(int argc, char **argv) {
  base::InitApp(&argc, &argv, "");
  AD_KESS_CLIENT_INIT_CHECK("init error");

  FLAGS_enable_grid_sdk_cache = false;
  {
    std::cout << "test 1: use cache" << std::endl;
    std::vector<std::string> cols = {
      "id", "user_id", "test"
    };
    std::vector<int64_t> ids = {
      844, 912, 940, 964, 1004, 844
    };
    GridQuery query = BuildQuery(cols, ids);
    query.table_name = "ad_dsp_account";
    std::vector<GridQuery> req{query};
    std::vector<GridData> resp;
    GridClient::Instance()->BatchGetTable(req, &resp);

    auto& grid_data = resp[0];

    std::cout << "col_size:" << grid_data.ColSize() << std::endl;
    std::cout << "row_size:" << grid_data.RowSize() << std::endl;
    for (int i = 0; i < grid_data.RowSize(); ++i) {
      std::cout << "isvalid:" << grid_data.IsValid(i) << std::endl;
      std::cout << "id: " << grid_data.GetInt64(i, "id") << std::endl;
      std::cout << "user_id: " << grid_data.GetInt64(i, "user_id") << std::endl;
      std::cout << "test: " << grid_data.GetInt64(i, "test") << std::endl;
    }
    sleep(1);
  }

  {
    LOG(INFO) << "test 2: cache hit";
    std::cout << "\ntest 2: cache hit" << std::endl;
    std::vector<std::string> cols = {
      "id", "user_id", "test"
    };
    std::vector<int64_t> ids = {
      844, 912, 940, 964, 1004, 844
    };
    GridQuery query = BuildQuery(cols, ids);
    query.table_name = "ad_dsp_account";
    std::vector<GridQuery> req{query};
    std::vector<GridData> resp;

    GridClient::Instance()->BatchGetTable(req, &resp);

    auto& grid_data = resp[0];

    std::cout << "col_size:" << grid_data.ColSize() << std::endl;
    std::cout << "row_size:" << grid_data.RowSize() << std::endl;
    for (int i = 0; i < grid_data.RowSize(); ++i) {
      std::cout << "isvalid:" << grid_data.IsValid(i) << std::endl;
      std::cout << "id: " << grid_data.GetInt64(i, "id") << std::endl;
      std::cout << "user_id: " << grid_data.GetInt64(i, "user_id") << std::endl;
      std::cout << "test: " << grid_data.GetInt64(i, "test") << std::endl;
    }
    sleep(1);
  }

  {
    LOG(INFO) << "test 3: different col";
    std::cout << "\ntest 3: different col" << std::endl;
    std::vector<std::string> cols = {
      "id", "user_id", "product_name", "put_status"
    };
    std::vector<int64_t> ids = {
      844, 912, 940, 964, 1004
    };
    GridQuery query = BuildQuery(cols, ids);
    query.table_name = "ad_dsp_account";
    query.CalculateMd5();
    LOG(INFO) << query.md5;
    std::vector<GridQuery> req{query};
    std::vector<GridData> resp;
    GridClient::Instance()->BatchGetTable(req, &resp);

    auto& grid_data = resp[0];

    std::cout << "col_size:" << grid_data.ColSize() << std::endl;
    std::cout << "row_size:" << grid_data.RowSize() << std::endl;
    std::string str;
    for (int i = 0; i < grid_data.RowSize(); ++i) {
      std::cout << "isvalid:" << grid_data.IsValid(i) << std::endl;
      std::cout << "id: " << grid_data.GetInt64(i, "id") << std::endl;
      std::cout << "user_id: " << grid_data.GetInt64(i, "user_id") << std::endl;
      grid_data.GetString(i, "product_name", &str);
      std::cout << "product_name: " << str << std::endl;
      std::cout << "put_status: " << grid_data.GetInt32(i, "put_status") << std::endl;
    }
    sleep(1);
  }

  {
    std::cout << "\ntest 4: different col cache hit" << std::endl;
    std::vector<std::string> cols = {
      "id", "user_id", "product_name", "put_status"
    };
    std::vector<int64_t> ids = {
      844, 912, 940, 964, 1004
    };
    GridQuery query = BuildQuery(cols, ids);
    query.table_name = "ad_dsp_account";
    query.CalculateMd5();
    LOG(INFO) << query.md5;
    std::vector<GridQuery> req{query};
    std::vector<GridData> resp;
    GridClient::Instance()->BatchGetTable(req, &resp);

    auto& grid_data = resp[0];

    std::cout << "col_size:" << grid_data.ColSize() << std::endl;
    std::cout << "row_size:" << grid_data.RowSize() << std::endl;
    std::string str;
    for (int i = 0; i < grid_data.RowSize(); ++i) {
      std::cout << "isvalid:" << grid_data.IsValid(i) << std::endl;
      std::cout << "id: " << grid_data.GetInt64(i, "id") << std::endl;
      std::cout << "user_id: " << grid_data.GetInt64(i, "user_id") << std::endl;
      grid_data.GetString(i, "product_name", &str);
      std::cout << "product_name: " << str << std::endl;
      std::cout << "put_status: " << grid_data.GetInt64(i, "put_status") << std::endl;
    }
    sleep(1);
  }


  {
    LOG(INFO) << "test 5: two table";
    std::cout << "\ntest 5: two table" << std::endl;
    std::vector<std::string> cols1 = {
      "id", "user_id", "product_name", "put_status"
    };
    std::vector<int64_t> ids1 = {
      844, 912, 940, 964, 1004
    };
    GridQuery query_account = BuildQuery(cols1, ids1);
    query_account.table_name = "ad_dsp_account";
    query_account.CalculateMd5();

    std::vector<std::string> cols2 = {
      "app_id", "app_name", "package_id"
    };
    std::vector<int64_t> ids2 = {
      *********, *********, *********
    };
    GridQuery query_target = BuildQuery(cols2, ids2);
    query_target.table_name = "ad_app_release";

    std::vector<GridQuery> req{query_account, query_target};
    std::vector<GridData> resp;
    GridClient::Instance()->BatchGetTable(req, &resp);

    auto& grid_data = resp[0];

    std::cout << "\ncol_size:" << grid_data.ColSize() << std::endl;
    std::cout << "row_size:" << grid_data.RowSize() << std::endl;
    std::string str;
    for (int i = 0; i < grid_data.RowSize(); ++i) {
      std::cout << "isvalid:" << grid_data.IsValid(i) << std::endl;
      std::cout << "id: " << grid_data.GetInt64(i, "id") << std::endl;
      std::cout << "user_id: " << grid_data.GetInt64(i, "user_id") << std::endl;
      grid_data.GetString(i, "product_name", &str);
      std::cout << "product_name: " << str << std::endl;
      std::cout << "put_status: " << grid_data.GetInt64(i, "put_status") << std::endl;
    }

    auto& grid_data2 = resp[1];
    std::cout << "\ncol_size:" << grid_data2.ColSize() << std::endl;
    std::cout << "row_size:" << grid_data2.RowSize() << std::endl;
    std::string t;
    for (int i = 0; i < grid_data2.RowSize(); ++i) {
      std::cout << "isvalid:" << grid_data2.IsValid(i) << std::endl;
      std::cout << "app_id: " << grid_data2.GetInt64(i, "app_id") << std::endl;
      grid_data2.GetString(i, "app_name", &t);
      std::cout << "app_name: " << t << std::endl;
    }
    sleep(1);
  }


  {
    std::cout << "\ntest 6: two table hit cache" << std::endl;
    std::vector<std::string> cols1 = {
      "id", "user_id", "product_name", "put_status"
    };
    std::vector<int64_t> ids1 = {
      844, 912, 940, 964, 1004
    };
    GridQuery query_account = BuildQuery(cols1, ids1);
    query_account.table_name = "ad_dsp_account";
    query_account.CalculateMd5();

    std::vector<std::string> cols2 = {
      "app_id", "app_name", "package_id"
    };
    std::vector<int64_t> ids2 = {
      *********, *********, *********
    };
    GridQuery query_target = BuildQuery(cols2, ids2);
    query_target.table_name = "ad_app_release";

    std::vector<GridQuery> req{query_account, query_target};
    std::vector<GridData> resp;
    GridClient::Instance()->BatchGetTable(req, &resp);

    auto& grid_data = resp[0];

    std::cout << "\ncol_size:" << grid_data.ColSize() << std::endl;
    std::cout << "row_size:" << grid_data.RowSize() << std::endl;
    std::string str;
    for (int i = 0; i < grid_data.RowSize(); ++i) {
      std::cout << "isvalid:" << grid_data.IsValid(i) << std::endl;
      std::cout << "id: " << grid_data.GetInt64(i, "id") << std::endl;
      std::cout << "user_id: " << grid_data.GetInt64(i, "user_id") << std::endl;
      grid_data.GetString(i, "product_name", &str);
      std::cout << "product_name: " << str << std::endl;
      std::cout << "put_status: " << grid_data.GetInt64(i, "put_status") << std::endl;
    }

    auto& grid_data2 = resp[1];
    std::cout << "\ncol_size:" << grid_data2.ColSize() << std::endl;
    std::cout << "row_size:" << grid_data2.RowSize() << std::endl;
    std::string t;
    for (int i = 0; i < grid_data2.RowSize(); ++i) {
      std::cout << "isvalid:" << grid_data2.IsValid(i) << std::endl;
      std::cout << "app_id: " << grid_data2.GetInt64(i, "app_id") << std::endl;
      grid_data2.GetString(i, "app_name", &t);
      std::cout << "app_name: " << t << std::endl;
    }
    sleep(1);
  }


  {
    std::cout << "\ntest 7: two table one hit cache" << std::endl;
    std::vector<std::string> cols1 = {
      "id", "user_id", "product_name", "put_status"
    };
    std::vector<int64_t> ids1 = {
      844, 912, 940, 964, 1
    };
    GridQuery query_account = BuildQuery(cols1, ids1);
    query_account.table_name = "ad_dsp_account";
    query_account.CalculateMd5();

    std::vector<std::string> cols2 = {
      "app_id", "app_name", "package_id"
    };
    std::vector<int64_t> ids2 = {
      *********, *********, *********
    };
    GridQuery query_target = BuildQuery(cols2, ids2);
    query_target.table_name = "ad_app_release";

    std::vector<GridQuery> req{query_account, query_target};
    std::vector<GridData> resp;
    GridClient::Instance()->BatchGetTable(req, &resp);

    auto& grid_data = resp[0];

    std::cout << "\ncol_size:" << grid_data.ColSize() << std::endl;
    std::cout << "row_size:" << grid_data.RowSize() << std::endl;
    std::string str;
    for (int i = 0; i < grid_data.RowSize(); ++i) {
      std::cout << "isvalid:" << grid_data.IsValid(i) << std::endl;
      std::cout << "id: " << grid_data.GetInt64(i, "id") << std::endl;
      std::cout << "user_id: " << grid_data.GetInt64(i, "user_id") << std::endl;
      grid_data.GetString(i, "product_name", &str);
      std::cout << "product_name: " << str << std::endl;
      std::cout << "put_status: " << grid_data.GetInt64(i, "put_status") << std::endl;
    }

    auto& grid_data2 = resp[1];
    std::cout << "col_size:" << grid_data2.ColSize() << std::endl;
    std::cout << "row_size:" << grid_data2.RowSize() << std::endl;
    std::string t;
    for (int i = 0; i < grid_data2.RowSize(); ++i) {
      std::cout << "isvalid:" << grid_data2.IsValid(i) << std::endl;
      std::cout << "app_id: " << grid_data2.GetInt64(i, "app_id") << std::endl;
      grid_data2.GetString(i, "app_name", &t);
      std::cout << "app_name: " << t << std::endl;
    }
    sleep(1);
  }

  {
    std::cout << "\ntest 8: two table with shard" << std::endl;
    std::vector<std::string> cols1 = {
      "id", "user_id", "product_name", "put_status"
    };
    std::vector<int64_t> ids1 = {
      844, 2, 912, 940, 964, 1
    };
    GridQuery query_account = BuildQuery(cols1, ids1);
    query_account.table_name = "ad_dsp_account";
    query_account.CalculateMd5();

    std::vector<std::string> cols2 = {
      "id", "account_id", "campaign_id"
    };
    std::vector<int64_t> ids2 = {
      0, **********, **********, **********
    };
    GridQuery query_unit = BuildQuery(cols2, ids2);
    query_unit.table_name = "ad_dsp_unit";

    std::vector<GridQuery> req{query_account, query_unit};
    std::vector<GridData> resp;
    GridClient::Instance()->BatchGetTable(req, &resp);

    auto& grid_data = resp[0];

    std::cout << "\ncol_size:" << grid_data.ColSize() << std::endl;
    std::cout << "row_size:" << grid_data.RowSize() << std::endl;
    std::string str;
    for (int i = 0; i < grid_data.RowSize(); ++i) {
      std::cout << "isvalid:" << grid_data.IsValid(i) << std::endl;
      std::cout << "id: " << grid_data.GetInt64(i, "id") << std::endl;
      std::cout << "user_id: " << grid_data.GetInt64(i, "user_id") << std::endl;
      grid_data.GetString(i, "product_name", &str);
      std::cout << "product_name: " << str << std::endl;
      std::cout << "put_status: " << grid_data.GetInt64(i, "put_status") << std::endl;
    }

    auto& grid_data2 = resp[1];
    std::cout << "\ncol_size:" << grid_data2.ColSize() << std::endl;
    std::cout << "row_size:" << grid_data2.RowSize() << std::endl;
    for (int i = 0; i < grid_data2.RowSize(); ++i) {
      std::cout << "isvalid:" << grid_data2.IsValid(i) << std::endl;
      std::cout << "id: " << grid_data2.GetInt64(i, "id") << std::endl;
      std::cout << "account_id: " << grid_data2.GetInt64(i, "account_id") << std::endl;
      std::cout << "campaign_id: " << grid_data2.GetInt64(i, "campaign_id") << std::endl;
    }
    sleep(1);
  }

  {
    std::cout << "\ntest 9: only shard" << std::endl;
    std::vector<std::string> cols1 = {
      "id", "photo_id", "unit_id", "campaign_id"
    };
    std::vector<int64_t> ids1 = {
      ************, 0, ************, 4
    };
    GridQuery query_creative = BuildQuery(cols1, ids1);
    query_creative.table_name = "ad_dsp_creative";

    std::vector<std::string> cols2 = {
      "id", "account_id", "campaign_id"
    };
    std::vector<int64_t> ids2 = {
      0, **********, **********, **********
    };
    GridQuery query_unit = BuildQuery(cols2, ids2);
    query_unit.table_name = "ad_dsp_unit";

    std::vector<GridQuery> req{query_creative, query_unit};
    std::vector<GridData> resp;
    GridClient::Instance()->BatchGetTable(req, &resp);

    auto& grid_data = resp[0];

    std::cout << "\ncol_size:" << grid_data.ColSize() << std::endl;
    std::cout << "row_size:" << grid_data.RowSize() << std::endl;
    std::string str;
    for (int i = 0; i < grid_data.RowSize(); ++i) {
      std::cout << "isvalid:" << grid_data.IsValid(i) << std::endl;
      std::cout << "id: " << grid_data.GetInt64(i, "id") << std::endl;
      std::cout << "photo_id: " << grid_data.GetInt64(i, "photo_id") << std::endl;
      std::cout << "unit_id: " << grid_data.GetInt64(i, "unit_id") << std::endl;
      std::cout << "campaign_id: " << grid_data.GetInt64(i, "campaign_id") << std::endl;
    }

    auto& grid_data2 = resp[1];
    std::cout << "\ncol_size:" << grid_data2.ColSize() << std::endl;
    std::cout << "row_size:" << grid_data2.RowSize() << std::endl;
    for (int i = 0; i < grid_data2.RowSize(); ++i) {
      std::cout << "isvalid:" << grid_data2.IsValid(i) << std::endl;
      std::cout << "id: " << grid_data2.GetInt64(i, "id") << std::endl;
      std::cout << "account_id: " << grid_data2.GetInt64(i, "account_id") << std::endl;
      std::cout << "campaign_id: " << grid_data2.GetInt64(i, "campaign_id") << std::endl;
    }
    sleep(1);
  }

  {
    std::cout << "\ntest 10: no rows/cols" << std::endl;
    std::vector<std::string> cols1 = {};
    std::vector<int64_t> ids1 = {
      ************, 0, ************, 4
    };
    GridQuery query1 = BuildQuery(cols1, ids1);
    query1.table_name = "ad_dsp_creative";

    std::vector<std::string> cols2 = {
      "id", "account_id", "campaign_id"
    };
    std::vector<int64_t> ids2 = {};
    GridQuery query2 = BuildQuery(cols2, ids2);
    query2.table_name = "ad_dsp_unit";

    std::vector<GridQuery> req{query1, query2};
    std::vector<GridData> resp;
    GridClient::Instance()->BatchGetTable(req, &resp);

    auto& grid_data = resp[0];

    std::cout << "\ncol_size:" << grid_data.ColSize() << std::endl;
    std::cout << "row_size:" << grid_data.RowSize() << std::endl;
    std::string str;
    for (int i = 0; i < grid_data.RowSize(); ++i) {
      std::cout << "isvalid:" << grid_data.IsValid(i) << std::endl;
      std::cout << "id: " << grid_data.GetInt64(i, "id") << std::endl;
      std::cout << "photo_id: " << grid_data.GetInt64(i, "photo_id") << std::endl;
      std::cout << "unit_id: " << grid_data.GetInt64(i, "unit_id") << std::endl;
      std::cout << "campaign_id: " << grid_data.GetInt64(i, "campaign_id") << std::endl;
    }

    auto& grid_data2 = resp[1];
    std::cout << "\ncol_size:" << grid_data2.ColSize() << std::endl;
    std::cout << "row_size:" << grid_data2.RowSize() << std::endl;
    for (int i = 0; i < grid_data2.RowSize(); ++i) {
      std::cout << "isvalid:" << grid_data2.IsValid(i) << std::endl;
      std::cout << "id: " << grid_data2.GetInt64(i, "id") << std::endl;
      std::cout << "account_id: " << grid_data2.GetInt64(i, "account_id") << std::endl;
      std::cout << "campaign_id: " << grid_data2.GetInt64(i, "campaign_id") << std::endl;
    }
    sleep(1);
  }

  {
    std::cout << "\ntest 11: null cache" << std::endl;
    std::vector<std::string> cols1 = {
      "id", "account_id", "campaign_id"
    };
    std::vector<int64_t> ids1 = {
      0, **********, **********, **********
    };
    GridQuery query1 = BuildQuery(cols1, ids1);
    query1.table_name = "ad_dsp_unit";

    std::vector<GridQuery> req{query1};
    std::vector<GridData> resp;
    GridClient::Instance()->BatchGetTable(req, &resp);

    auto& grid_data = resp[0];

    std::cout << "\ncol_size:" << grid_data.ColSize() << std::endl;
    std::cout << "row_size:" << grid_data.RowSize() << std::endl;
    std::string str;
    for (int i = 0; i < grid_data.RowSize(); ++i) {
      std::cout << "isvalid:" << grid_data.IsValid(i) << std::endl;
      std::cout << "id: " << grid_data.GetInt64(i, "id") << std::endl;
      std::cout << "photo_id: " << grid_data.GetInt64(i, "photo_id") << std::endl;
      std::cout << "unit_id: " << grid_data.GetInt64(i, "unit_id") << std::endl;
      std::cout << "campaign_id: " << grid_data.GetInt64(i, "campaign_id") << std::endl;
    }
    sleep(1);
  }
  return 0;
}
