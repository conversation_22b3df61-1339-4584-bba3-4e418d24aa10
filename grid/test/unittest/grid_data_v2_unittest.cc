#include <cstdint>
#include <string>
#include <vector>
#include <memory>
#include <thread>
#include "gtest/gtest.h"
#include "glog/logging.h"
#include "teams/ad/grid/utils/utils.h"
#include "teams/ad/grid/sdk/grid_data.h"
#include "teams/ad/grid/cache/cache_instance.h"

using ks::grid::ColumnDesc;
using ks::grid::FbRow;
using ks::grid::CreateValue;
using ks::grid::CreateListValue;
using ::ks::ad_index_meta::proto::ColumnType;
using ks::grid::flat::Value;

static std::vector<std::string> col_names = {
"bool", "id", "i8", "i16", "i32", "i64", "float", "double",
  "string", "i8_list", "i16_list", "i32_list", "i64_list",
  "float_list", "double_list", "string_list"
};

static std::vector<ColumnType> col_types = {
  ColumnType::BOOL, ColumnType::INT8, ColumnType::INT16, ColumnType::INT32,
  ColumnType::INT64, ColumnType::FLOAT, ColumnType::DOUBLE, ColumnType::STRING,
  ColumnType::INT8_LIST, ColumnType::INT16_LIST, ColumnType::INT32_LIST,
  ColumnType::INT64_LIST, ColumnType::FLOAT_LIST, ColumnType::DOUBLE_LIST,
  ColumnType::STRING_LIST
};

void mock_Row(ks::grid::FbRow* row, int id) {
  if (row == nullptr) return;
  flatbuffers::FlatBufferBuilder builder;
  std::vector<flatbuffers::Offset<Value>> row_values;
  row_values.push_back(CreateValue<bool>(&builder, true));
  row_values.push_back(CreateValue<int8_t>(&builder, 100));
  row_values.push_back(CreateValue<int16_t>(&builder, 100));
  row_values.push_back(CreateValue<int32_t>(&builder, 100));
  row_values.push_back(CreateValue<int64_t>(&builder, 100));
  row_values.push_back(CreateValue<float>(&builder, 100));
  row_values.push_back(CreateValue<double>(&builder, 100));
  std::string s = "test_string";
  std::vector<int8_t> v8 = {100};
  std::vector<int16_t> v16 = {200};
  std::vector<int32_t> v32 = {300};
  std::vector<int64_t> v64 = {400};
  std::vector<float> vf = {500};
  std::vector<double> vd = {600};
  std::vector<std::string> vs = {"test_string"};
  row_values.push_back(CreateListValue<std::string>(&builder, &s));
  row_values.push_back(CreateListValue<std::vector<int8_t>>(&builder, &v8));
  row_values.push_back(CreateListValue<std::vector<int16_t>>(&builder, &v16));
  row_values.push_back(CreateListValue<std::vector<int32_t>>(&builder, &v32));
  row_values.push_back(CreateListValue<std::vector<int64_t>>(&builder, &v64));
  row_values.push_back(CreateListValue<std::vector<float>>(&builder, &vf));
  row_values.push_back(CreateListValue<std::vector<double>>(&builder, &vd));
  row_values.push_back(CreateListValue<std::vector<std::string>>(&builder, &vs));

  auto rows = builder.CreateVector(row_values);
  auto res = ks::grid::flat::CreateRow(builder, id, rows);
  builder.Finish(res);
  row->guard = std::make_shared<std::string>(builder.GetBufferPointer(),
                builder.GetBufferPointer() + builder.GetSize());

  auto tmp = flatbuffers::GetRoot<ks::grid::flat::Row>(row->guard->data())->data();
  row->row = const_cast<flatbuffers::Vector<flatbuffers::Offset<ks::grid::flat::Value>>*>(tmp);
  return;
}

void mock_WrongRow(ks::grid::FbRow* row, int id, int size) {
  if (row == nullptr) return;
  flatbuffers::FlatBufferBuilder builder;
  std::vector<flatbuffers::Offset<Value>> row_values;
  for (int i = 0; i <= size; ++i) {
    row_values.push_back(CreateValue<int64_t>(&builder, i));
  }

  auto rows = builder.CreateVector(row_values);
  auto res = ks::grid::flat::CreateRow(builder, id, rows);
  builder.Finish(res);
  row->guard = std::make_shared<std::string>(builder.GetBufferPointer(),
                builder.GetBufferPointer() + builder.GetSize());

  auto tmp = flatbuffers::GetRoot<ks::grid::flat::Row>(row->guard->data())->data();
  row->row = const_cast<flatbuffers::Vector<flatbuffers::Offset<ks::grid::flat::Value>>*>(tmp);
  return;
}

std::shared_ptr<std::vector<ColumnDesc>> mock_Col() {
  auto res = std::make_shared<std::vector<ColumnDesc>>();
  for (int i = 0; i < col_names.size(); ++i) {
    ColumnDesc desc;
    desc.col_name = col_names[i];
    desc.type = col_types[i];
    res->push_back(desc);
  }
  return res;
}

ks::grid::GridData mock_GridData(uint32_t row_size) {
  std::shared_ptr<std::vector<FbRow>> rows = std::make_shared<std::vector<FbRow>>();
  std::shared_ptr<std::vector<ColumnDesc>> cols = mock_Col();
  rows->resize(row_size);
  for (int i = 0; i < row_size; ++i) {
    auto row = &(*rows)[i];
    mock_Row(row, i);
  }
  ks::grid::GridData data;
  data.ResetFB(rows, cols, std::string("normal"));
  return data;
}

// 没有行，没有列，行没对齐
std::vector<ks::grid::GridData> mock_GridDataBad(uint32_t row_size) {
  std::vector<ks::grid::GridData> res;
  res.resize(4);
  // 没有行
  {
    std::shared_ptr<std::vector<FbRow>> rows = std::make_shared<std::vector<FbRow>>();
    std::shared_ptr<std::vector<ColumnDesc>> cols = mock_Col();
    res[0].ResetFB(rows, cols, "no_row");
  }
  // 没有列
  {
    std::shared_ptr<std::vector<FbRow>> rows = std::make_shared<std::vector<FbRow>>();
    std::shared_ptr<std::vector<ColumnDesc>> cols = std::make_shared<std::vector<ColumnDesc>>();
    rows->resize(row_size);
    for (int i = 0; i < row_size; ++i) {
      auto row = &(*rows)[i];
      mock_Row(row, i);
    }
    res[1].ResetFB(rows, cols, "no_col");
  }
  // 行列没有对齐
  {
    std::shared_ptr<std::vector<FbRow>> rows = std::make_shared<std::vector<FbRow>>();
    std::shared_ptr<std::vector<ColumnDesc>> cols = std::make_shared<std::vector<ColumnDesc>>();

    rows->resize(row_size);
    cols->resize(row_size);
    for (int i = 0; i < row_size; ++i) {
      (*cols)[i].type = ColumnType::INT64;
    }

    for (int i = 0; i < row_size; ++i) {
      auto row = &(*rows)[i];
      mock_WrongRow(row, i, i);
    }
    res[2].ResetFB(rows, cols, std::string("table_no_align"));
  }
  // 空指针
  {
    std::shared_ptr<std::vector<FbRow>> rows;
    std::shared_ptr<std::vector<ColumnDesc>> cols;
    res[2].ResetFB(rows, cols, std::string("table_null"));
  }
  return res;
}

// 正常类型访问
TEST(GridDataV2, normal) {
  ks::grid::GridData data;
  uint32_t row_size = 10;
  data = mock_GridData(row_size);
  for (int i = 0; i < row_size; ++i) {
    uint32_t col_index = 0;
    EXPECT_TRUE(data.GetBool(i, col_index));
    EXPECT_TRUE(data.GetBool(i, col_names[col_index++]));

    EXPECT_EQ(100, data.GetInt8(i, col_index));
    EXPECT_EQ(100, data.GetInt8(i, col_names[col_index++]));

    EXPECT_EQ(100, data.GetInt16(i, col_index));
    EXPECT_EQ(100, data.GetInt16(i, col_names[col_index++]));

    EXPECT_EQ(100, data.GetInt32(i, col_index));
    EXPECT_EQ(100, data.GetInt32(i, col_names[col_index++]));

    EXPECT_EQ(100, data.GetInt64(i, col_index));
    EXPECT_EQ(100, data.GetInt64(i, col_names[col_index++]));

    EXPECT_EQ(100, data.GetFloat(i, col_index));
    EXPECT_EQ(100, data.GetFloat(i, col_names[col_index++]));

    EXPECT_EQ(100, data.GetDouble(i, col_index));
    EXPECT_EQ(100, data.GetDouble(i, col_names[col_index++]));

    std::string s;
    data.GetString(i, col_index, &s);
    EXPECT_EQ("test_string", s);
    data.GetString(i, col_names[col_index++], &s);
    EXPECT_EQ("test_string", s);

    std::vector<int8_t> v8;
    data.GetInt8List(i, col_index, &v8);
    EXPECT_EQ(1, v8.size());
    EXPECT_EQ(100, v8[0]);
    data.GetInt8List(i, col_names[col_index++], &v8);
    EXPECT_EQ(1, v8.size());
    EXPECT_EQ(100, v8[0]);

    std::vector<int16_t> v16;
    data.GetInt16List(i, col_index, &v16);
    EXPECT_EQ(1, v16.size());
    EXPECT_EQ(200, v16[0]);
    data.GetInt16List(i, col_names[col_index++], &v16);

    std::vector<int32_t> v32;
    data.GetInt32List(i, col_index, &v32);
    EXPECT_EQ(1, v32.size());
    EXPECT_EQ(300, v32[0]);
    data.GetInt32List(i, col_names[col_index++], &v32);
    EXPECT_EQ(1, v32.size());
    EXPECT_EQ(300, v32[0]);

    std::vector<int64_t> v64;
    data.GetInt64List(i, col_index, &v64);
    EXPECT_EQ(1, v64.size());
    EXPECT_EQ(400, v64[0]);
    data.GetInt64List(i, col_names[col_index++], &v64);
    EXPECT_EQ(1, v64.size());
    EXPECT_EQ(400, v64[0]);

    std::vector<float> vf;
    data.GetFloatList(i, col_index, &vf);
    EXPECT_EQ(1, vf.size());
    EXPECT_EQ(500, vf[0]);
    data.GetFloatList(i, col_names[col_index++], &vf);
    EXPECT_EQ(1, vf.size());
    EXPECT_EQ(500, vf[0]);

    std::vector<double> vd;
    data.GetDoubleList(i, col_index, &vd);
    EXPECT_EQ(1, vd.size());
    EXPECT_EQ(600, vd[0]);
    data.GetDoubleList(i, col_names[col_index++], &vd);
    EXPECT_EQ(1, vd.size());
    EXPECT_EQ(600, vd[0]);

    std::vector<std::string> vs;
    data.GetStringList(i, col_index, &vs);
    EXPECT_EQ(1, vs.size());
    EXPECT_EQ("test_string", vs[0]);
    data.GetStringList(i, col_names[col_index++], &vs);
    EXPECT_EQ(1, vs.size());
    EXPECT_EQ("test_string", vs[0]);
  }
}

// 类型误用
TEST(GridDataV2, wrongType) {
  uint32_t row_size = 10;
  ks::grid::GridData data = mock_GridData(row_size);;

  for (int i = 0; i < row_size; ++i) {
    EXPECT_TRUE(data.GetBool(i, 0));
    EXPECT_EQ(0, data.GetInt8(i, 0));
    EXPECT_EQ(0, data.GetInt16(i, 0));
    EXPECT_EQ(0, data.GetInt32(i, 0));
    EXPECT_EQ(0, data.GetInt64(i, 0));
    EXPECT_EQ(0, data.GetFloat(i, 0));
    EXPECT_EQ(0, data.GetDouble(i, 0));

    std::vector<int8_t> v8;
    EXPECT_EQ(false, data.GetInt8List(i, 0, &v8));
    EXPECT_EQ(false, data.GetInt8List(i, 0, NULL));

    std::vector<int16_t> v16;
    EXPECT_EQ(false, data.GetInt16List(i, 0, &v16));
    EXPECT_EQ(false, data.GetInt16List(i, 0, NULL));

    std::vector<int32_t> v32;
    EXPECT_EQ(false, data.GetInt32List(i, 0, &v32));
    EXPECT_EQ(false, data.GetInt32List(i, 0, NULL));

    std::vector<int64_t> v64;
    EXPECT_EQ(false, data.GetInt64List(i, 0, &v64));
    EXPECT_EQ(false, data.GetInt64List(i, 0, NULL));

    std::vector<float> vf;
    EXPECT_EQ(false, data.GetFloatList(i, 0, &vf));
    EXPECT_EQ(false, data.GetFloatList(i, 0, NULL));

    std::vector<double> vd;
    EXPECT_EQ(false, data.GetDoubleList(i, 0, &vd));
    EXPECT_EQ(false, data.GetDoubleList(i, 0, NULL));

    std::vector<std::string> vs;
    EXPECT_EQ(false, data.GetStringList(i, 0, &vs));
    EXPECT_EQ(false, data.GetStringList(i, 0, NULL));

    std::string s;
    EXPECT_EQ(false, data.GetString(i, 0, &s));
    EXPECT_EQ(false, data.GetString(i, 0, NULL));
  }
}

// 越界
TEST(GridDataV2, crossIndex) {
  uint32_t row_size = 10;
  ks::grid::GridData data = mock_GridData(row_size);;
  // 行越界
  for (int i = row_size; i < row_size + 10; ++i) {
    uint32_t col_index = 0;
    EXPECT_FALSE(data.GetBool(i, col_index));
    EXPECT_FALSE(data.GetBool(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetInt8(i, col_index));
    EXPECT_EQ(0, data.GetInt8(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetInt16(i, col_index));
    EXPECT_EQ(0, data.GetInt16(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetInt32(i, col_index));
    EXPECT_EQ(0, data.GetInt32(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetInt64(i, col_index));
    EXPECT_EQ(0, data.GetInt64(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetFloat(i, col_index));
    EXPECT_EQ(0, data.GetFloat(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetDouble(i, col_index));
    EXPECT_EQ(0, data.GetDouble(i, col_names[col_index++]));

    std::string s;
    data.GetString(i, col_index, &s);
    EXPECT_EQ("", s);
    data.GetString(i, col_names[col_index++], &s);
    EXPECT_EQ("", s);

    std::vector<int8_t> v8;
    data.GetInt8List(i, col_index, &v8);
    EXPECT_EQ(0, v8.size());
    data.GetInt8List(i, col_names[col_index++], &v8);
    EXPECT_EQ(0, v8.size());

    std::vector<int16_t> v16;
    data.GetInt16List(i, col_index, &v16);
    EXPECT_EQ(0, v16.size());
    data.GetInt16List(i, col_names[col_index++], &v16);
    EXPECT_EQ(0, v16.size());

    std::vector<int32_t> v32;
    data.GetInt32List(i, col_index, &v32);
    EXPECT_EQ(0, v32.size());
    data.GetInt32List(i, col_names[col_index++], &v32);
    EXPECT_EQ(0, v32.size());

    std::vector<int64_t> v64;
    data.GetInt64List(i, col_index, &v64);
    EXPECT_EQ(0, v64.size());
    data.GetInt64List(i, col_names[col_index++], &v64);
    EXPECT_EQ(0, v64.size());

    std::vector<float> vf;
    data.GetFloatList(i, col_index, &vf);
    EXPECT_EQ(0, vf.size());
    data.GetFloatList(i, col_names[col_index++], &vf);
    EXPECT_EQ(0, vf.size());

    std::vector<double> vd;
    data.GetDoubleList(i, col_index, &vd);
    EXPECT_EQ(0, vd.size());
    data.GetDoubleList(i, col_names[col_index++], &vd);
    EXPECT_EQ(0, vd.size());

    std::vector<std::string> vs;
    data.GetStringList(i, col_index, &vs);
    EXPECT_EQ(0, vs.size());
    data.GetStringList(i, col_names[col_index++], &vs);
    EXPECT_EQ(0, vs.size());
  }

  // 列越界
  std::string col_name = "test_col";
  for (int i = 0; i < row_size; ++i) {
    uint32_t col_index = 10;
    EXPECT_FALSE(data.GetBool(i, col_index++));
    EXPECT_FALSE(data.GetBool(i, col_name));

    EXPECT_EQ(0, data.GetInt8(i, col_index++));
    EXPECT_EQ(0, data.GetInt8(i, col_name));

    EXPECT_EQ(0, data.GetInt16(i, col_index++));
    EXPECT_EQ(0, data.GetInt16(i, col_name));

    EXPECT_EQ(0, data.GetInt32(i, col_index++));
    EXPECT_EQ(0, data.GetInt32(i, col_name));

    EXPECT_EQ(0, data.GetInt64(i, col_index++));
    EXPECT_EQ(0, data.GetInt64(i, col_name));

    EXPECT_EQ(0, data.GetFloat(i, col_index++));
    EXPECT_EQ(0, data.GetFloat(i, col_name));

    EXPECT_EQ(0, data.GetDouble(i, col_index++));
    EXPECT_EQ(0, data.GetDouble(i, col_name));

    std::string s;
    data.GetString(i, col_index++, &s);
    EXPECT_EQ("", s);
    data.GetString(i, col_name, &s);
    EXPECT_EQ("", s);

    std::vector<int8_t> v8;
    data.GetInt8List(i, col_index++, &v8);
    EXPECT_EQ(0, v8.size());
    data.GetInt8List(i, col_name, &v8);
    EXPECT_EQ(0, v8.size());

    std::vector<int16_t> v16;
    data.GetInt16List(i, col_index++, &v16);
    EXPECT_EQ(0, v16.size());
    data.GetInt16List(i, col_name, &v16);
    EXPECT_EQ(0, v16.size());

    std::vector<int32_t> v32;
    data.GetInt32List(i, col_index++, &v32);
    EXPECT_EQ(0, v32.size());
    data.GetInt32List(i, col_name, &v32);
    EXPECT_EQ(0, v32.size());

    std::vector<int64_t> v64;
    data.GetInt64List(i, col_index++, &v64);
    EXPECT_EQ(0, v64.size());
    data.GetInt64List(i, col_name, &v64);
    EXPECT_EQ(0, v64.size());

    std::vector<float> vf;
    data.GetFloatList(i, col_index++, &vf);
    EXPECT_EQ(0, vf.size());
    data.GetFloatList(i, col_name, &vf);
    EXPECT_EQ(0, vf.size());

    std::vector<double> vd;
    data.GetDoubleList(i, col_index++, &vd);
    EXPECT_EQ(0, vd.size());
    data.GetDoubleList(i, col_name, &vd);
    EXPECT_EQ(0, vd.size());

    std::vector<std::string> vs;
    data.GetStringList(i, col_index++, &vs);
    EXPECT_EQ(0, vs.size());
    data.GetStringList(i, col_name, &vs);
    EXPECT_EQ(0, vs.size());
  }
}

// 破坏性测试
TEST(GridDataV2, destruction) {
  ks::grid::GridData data1;
  ks::grid::GridData data2;
  ks::grid::GridData data3;
  uint32_t row_size = 10;
  auto data = mock_GridDataBad(row_size);
  data1 = data[0];
  data2 = data[1];
  data3 = data[2];

  for (int i = 0; i < row_size; ++i) {
    for (int j = 0; j < col_names.size(); ++j) {
      EXPECT_EQ(0, data1.GetInt64(i, j));
      EXPECT_EQ(0, data2.GetInt64(i, j));
    }

    for (int j = 0; j <= i; ++j) {
      EXPECT_EQ(j, data3.GetInt64(i, j));
    }
    for (int j = i + 1; j < row_size; ++j) {
      EXPECT_EQ(0, data3.GetInt64(i, j));
    }
  }
}

TEST(GridDataV2, noInit) {
  ks::grid::GridData data1;
  EXPECT_EQ(0, data1.ColSize());
  EXPECT_EQ(0, data1.RowSize());
  EXPECT_FALSE(data1.IsValid());
  EXPECT_FALSE(data1.IsValid(0));
  EXPECT_EQ(ks::ad_index_meta::proto::ColumnType::UNKNOWN_COLUMN, data1.GetColumnType(0));
  EXPECT_EQ(ks::ad_index_meta::proto::ColumnType::UNKNOWN_COLUMN, data1.GetColumnType("test"));

  EXPECT_EQ(false, data1.GetBool(0, 0));
  EXPECT_EQ(false, data1.GetBool(0, "test"));
  EXPECT_EQ(0, data1.GetInt8(0, 0));
  EXPECT_EQ(0, data1.GetInt8(0, "test"));
  EXPECT_EQ(0, data1.GetInt16(0, 0));
  EXPECT_EQ(0, data1.GetInt16(0, "test"));
  EXPECT_EQ(0, data1.GetInt32(0, 0));
  EXPECT_EQ(0, data1.GetInt32(0, "test"));
  EXPECT_EQ(0, data1.GetInt64(0, 0));
  EXPECT_EQ(0, data1.GetInt64(0, "test"));
  EXPECT_EQ(0, data1.GetFloat(0, 0));
  EXPECT_EQ(0, data1.GetFloat(0, "test"));
  EXPECT_EQ(0, data1.GetDouble(0, 0));
  EXPECT_EQ(0, data1.GetDouble(0, "test"));
  std::string s;
  data1.GetString(0, 0, &s);
  EXPECT_EQ("", s);
  data1.GetString(0, "test", &s);
  EXPECT_EQ("", s);

  std::vector<int8_t> v8;
  data1.GetInt8List(0, 0, &v8);
  EXPECT_EQ(0, v8.size());
  data1.GetInt8List(0, "test", &v8);
  EXPECT_EQ(0, v8.size());

  std::vector<int16_t> v16;
  data1.GetInt16List(0, 0, &v16);
  EXPECT_EQ(0, v16.size());
  data1.GetInt16List(0, "test", &v16);
  EXPECT_EQ(0, v16.size());

  std::vector<int32_t> v32;
  data1.GetInt32List(0, 0, &v32);
  EXPECT_EQ(0, v32.size());
  data1.GetInt32List(0, "test", &v32);
  EXPECT_EQ(0, v32.size());

  std::vector<int64_t> v64;
  data1.GetInt64List(0, 0, &v64);
  EXPECT_EQ(0, v64.size());
  data1.GetInt64List(0, "test", &v64);
  EXPECT_EQ(0, v64.size());

  std::vector<float> vf;
  data1.GetFloatList(0, 0, &vf);
  EXPECT_EQ(0, vf.size());
  data1.GetFloatList(0, "test", &vf);
  EXPECT_EQ(0, vf.size());

  std::vector<double> vd;
  data1.GetDoubleList(0, 0, &vd);
  EXPECT_EQ(0, vd.size());
  data1.GetDoubleList(0, "test", &vd);
  EXPECT_EQ(0, vd.size());

  std::vector<std::string> vs;
  data1.GetStringList(0, 0, &vs);
  EXPECT_EQ(0, vs.size());
  data1.GetStringList(0, "test", &vs);
  EXPECT_EQ(0, vs.size());
}

// 空行
TEST(GridDataV2, null) {
  flatbuffers::FlatBufferBuilder builder;
  auto row = ks::grid::flat::CreateRow(builder, 1);
  builder.Finish(row);
  auto str = std::make_shared<std::string>(builder.GetBufferPointer(),
                builder.GetBufferPointer() + builder.GetSize());
  auto tmp = flatbuffers::GetRoot<ks::grid::flat::Row>(str->data())->data();
  EXPECT_EQ(0, tmp);
}

TEST(GirdDatav2, CopyFBRow) {
  FbRow row;
  mock_Row(&row, 1);

  flatbuffers::FlatBufferBuilder builder;
  auto row_offset = ks::grid::CopyFBRow(2, row.row, &builder);
  builder.Finish(row_offset);
  auto str = std::make_shared<std::string>(builder.GetBufferPointer(),
                builder.GetBufferPointer() + builder.GetSize());
  auto tmp = flatbuffers::GetRoot<ks::grid::flat::Row>(str->data())->data();
  EXPECT_EQ(15, tmp->size());
  EXPECT_EQ(true, tmp->Get(0)->bool_value());
  EXPECT_EQ(100, tmp->Get(1)->i8());
  EXPECT_EQ(100, tmp->Get(2)->i16());
  EXPECT_EQ(100, tmp->Get(3)->i32());
  EXPECT_EQ(100, tmp->Get(4)->i64());
  EXPECT_EQ(100, tmp->Get(5)->float_value());
  EXPECT_EQ(100, tmp->Get(6)->double_value());
  EXPECT_EQ("test_string", tmp->Get(7)->string_value()->str());
  EXPECT_EQ(100, tmp->Get(8)->int8_list_value()->Get(0));
  EXPECT_EQ(200, tmp->Get(9)->int16_list_value()->Get(0));
  EXPECT_EQ(300, tmp->Get(10)->int32_list_value()->Get(0));
  EXPECT_EQ(400, tmp->Get(11)->int64_list_value()->Get(0));
  EXPECT_EQ(500, tmp->Get(12)->float_list_value()->Get(0));
  EXPECT_EQ(600, tmp->Get(13)->double_list_value()->Get(0));
  EXPECT_EQ("test_string", tmp->Get(14)->string_list_value()->Get(0)->str());
}


// 正常类型访问
TEST(GridDataV2, UnSafe) {
  ks::grid::GridData data;
  uint32_t row_size = 10;
  data = mock_GridData(row_size);
  for (int i = 0; i < row_size; ++i) {
    uint32_t col_index = 0;
    EXPECT_TRUE(data.UnSafeGetBool(i, col_index));
    EXPECT_TRUE(data.GetBool(i, col_names[col_index++]));

    EXPECT_EQ(100, data.UnSafeGetInt8(i, col_index));
    EXPECT_EQ(100, data.GetInt8(i, col_names[col_index++]));

    EXPECT_EQ(100, data.UnSafeGetInt16(i, col_index));
    EXPECT_EQ(100, data.GetInt16(i, col_names[col_index++]));

    EXPECT_EQ(100, data.UnSafeGetInt32(i, col_index));
    EXPECT_EQ(100, data.GetInt32(i, col_names[col_index++]));

    EXPECT_EQ(100, data.UnSafeGetInt64(i, col_index));
    EXPECT_EQ(100, data.GetInt64(i, col_names[col_index++]));

    EXPECT_EQ(100, data.UnSafeGetFloat(i, col_index));
    EXPECT_EQ(100, data.GetFloat(i, col_names[col_index++]));

    EXPECT_EQ(100, data.UnSafeGetDouble(i, col_index));
    EXPECT_EQ(100, data.GetDouble(i, col_names[col_index++]));

    std::string s;
    data.UnSafeGetString(i, col_index, &s);
    EXPECT_EQ("test_string", s);
    data.GetString(i, col_names[col_index++], &s);
    EXPECT_EQ("test_string", s);

    std::vector<int8_t> v8;
    data.UnSafeGetInt8List(i, col_index, &v8);
    EXPECT_EQ(1, v8.size());
    EXPECT_EQ(100, v8[0]);
    data.GetInt8List(i, col_names[col_index++], &v8);
    EXPECT_EQ(1, v8.size());
    EXPECT_EQ(100, v8[0]);

    std::vector<int16_t> v16;
    data.UnSafeGetInt16List(i, col_index, &v16);
    EXPECT_EQ(1, v16.size());
    EXPECT_EQ(200, v16[0]);
    data.GetInt16List(i, col_names[col_index++], &v16);

    std::vector<int32_t> v32;
    data.UnSafeGetInt32List(i, col_index, &v32);
    EXPECT_EQ(1, v32.size());
    EXPECT_EQ(300, v32[0]);
    data.GetInt32List(i, col_names[col_index++], &v32);
    EXPECT_EQ(1, v32.size());
    EXPECT_EQ(300, v32[0]);

    std::vector<int64_t> v64;
    data.UnSafeGetInt64List(i, col_index, &v64);
    EXPECT_EQ(1, v64.size());
    EXPECT_EQ(400, v64[0]);
    data.GetInt64List(i, col_names[col_index++], &v64);
    EXPECT_EQ(1, v64.size());
    EXPECT_EQ(400, v64[0]);

    std::vector<float> vf;
    data.UnSafeGetFloatList(i, col_index, &vf);
    EXPECT_EQ(1, vf.size());
    EXPECT_EQ(500, vf[0]);
    data.GetFloatList(i, col_names[col_index++], &vf);
    EXPECT_EQ(1, vf.size());
    EXPECT_EQ(500, vf[0]);

    std::vector<double> vd;
    data.UnSafeGetDoubleList(i, col_index, &vd);
    EXPECT_EQ(1, vd.size());
    EXPECT_EQ(600, vd[0]);
    data.GetDoubleList(i, col_names[col_index++], &vd);
    EXPECT_EQ(1, vd.size());
    EXPECT_EQ(600, vd[0]);

    std::vector<std::string> vs;
    data.UnSafeGetStringList(i, col_index, &vs);
    EXPECT_EQ(1, vs.size());
    EXPECT_EQ("test_string", vs[0]);
    data.GetStringList(i, col_names[col_index++], &vs);
    EXPECT_EQ(1, vs.size());
    EXPECT_EQ("test_string", vs[0]);
  }
}
