#include <unistd.h>
#include <cstddef>
#include <cstdint>
#include <string>
#include <vector>
#include <memory>
#include <thread>
#include "gtest/gtest.h"
#include "glog/logging.h"
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/sdk/grid_data.h"

using ::ks::grid::FbRow;


void SelectKElementsBFS(const std::vector<std::vector<FbRow>>& blocks_data, std::vector<FbRow>* result,
                        int k) {
  std::queue<std::pair<int, int>> q;  // 存储<block_index, element_index>
  for (int i = 0; i < blocks_data.size(); i++) {
    if (!blocks_data[i].empty()) {
      q.push({i, 0});
    }
  }
  while (!q.empty() && result->size() < k) {
    int sz = q.size();
    for (int i = 0; i < sz && result->size() < k; i++) {
      auto [block_idx, elem_idx] = q.front();
      q.pop();
      if (block_idx < blocks_data.size() && elem_idx < blocks_data[block_idx].size()) {
        result->push_back(blocks_data[block_idx][elem_idx]);
        std::pair<int, int> next = {block_idx, elem_idx + 1};
        q.push(next);
      }
    }
  }
  return;
}

void mock_row(std::vector<FbRow>* rows, uint32_t size) {
  if (rows == nullptr) {
    return;
  }
  std::shared_ptr<std::string> data = std::make_shared<std::string>(size, '\0');
  for (int i = 0; i < size; ++i) {
    FbRow row;
    row.guard = data;
    row.row = reinterpret_cast<flatbuffers::Vector<flatbuffers::Offset<ks::grid::flat::Value>>*>(data.get());
    (*rows).push_back(row);
  }
}

TEST(BFS, normal) {
  std::vector<std::vector<FbRow>> rows(4);
  for (int i = 0; i < 4; ++i) {
    mock_row(&rows[i], 10);
  }
  std::vector<FbRow> result;
  SelectKElementsBFS(rows, &result, 10);
  EXPECT_EQ(result.size(), 10);
}

TEST(BFS, abnormal) {
  std::vector<std::vector<FbRow>> rows(4);
  for (int i = 0; i < 4; ++i) {
    mock_row(&rows[i], 1);
  }
  std::vector<FbRow> result;
  SelectKElementsBFS(rows, &result, 10);
  EXPECT_EQ(result.size(), 4);
}

TEST(BFS, zero) {
  std::vector<std::vector<FbRow>> rows(4);
  std::vector<FbRow> result;
  SelectKElementsBFS(rows, &result, 10);
  EXPECT_EQ(result.size(), 0);
}
