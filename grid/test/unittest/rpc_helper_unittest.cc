#include "gtest/gtest.h"
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/proto/msg.pb.h"
#include "teams/ad/grid/sdk/grid_query.h"
#include "teams/ad/grid/client/rpc/rpc_helper.h"

void mock_query_rows(ks::grid::proto::KVQuery& query) {  // NOLINT
  for (int i = 0; i < 1000; ++i) {
    auto key = query.add_keys();
    key->set_i64(i);
  }
}

void mock_query_cols(ks::grid::proto::KVQuery& query) {  // NOLINT
  std::vector<std::string> select_field_names = {"col1", "col2"};
  query.mutable_select_fields()->Assign(select_field_names.begin(), select_field_names.end());
  return;
}

bool filter(int index) {
  return false;
}

TEST(RPCHelper, normal) {
  ks::grid::proto::TableMeta table_meta;
  table_meta.set_ksn("test");
  table_meta.set_shard_num(1);

  ks::grid::RpcHelper rpc_helper(table_meta);
  ks::grid::proto::KVQuery query;

  {
    mock_query_rows(query);
    mock_query_cols(query);
    query.set_table_name("test");
    auto res = rpc_helper.AddTableQuery(query, filter, "");
    EXPECT_EQ(0, res);
  }

  // 不设置 name
  {
    mock_query_rows(query);
    mock_query_cols(query);
    auto res = rpc_helper.AddTableQuery(query, filter, "");
    EXPECT_EQ(0, res);
  }

  // 不设置列
  {
    mock_query_rows(query);
    query.set_table_name("test");
    auto res = rpc_helper.AddTableQuery(query, filter, "");
    EXPECT_EQ(0, res);
  }

  // 不设置 名字和列
  {
    mock_query_rows(query);
    auto res = rpc_helper.AddTableQuery(query, filter, "");
    EXPECT_EQ(0, res);
  }
}
