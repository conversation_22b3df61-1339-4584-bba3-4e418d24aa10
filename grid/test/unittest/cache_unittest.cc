#include <unistd.h>
#include <cstddef>
#include <cstdint>
#include <string>
#include <vector>
#include <memory>
#include <thread>
#include "gtest/gtest.h"
#include "glog/logging.h"
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/cache/cache_list.h"

using ::ks::grid::proto::Row;
using ::ks::grid::CacheList;
using TRowSP = std::shared_ptr<Row>;
using TKey = uint64_t;

void mock_row(std::vector<TKey>* keys, std::vector<TRowSP>* data,
              uint32_t row_size, uint32_t start_index = 0) {
  data->clear();
  keys->clear();
  for (int i = 0; i < row_size; ++i) {
    auto row = std::make_shared<Row>();
    auto t = row->add_data();
    t->set_i64(start_index + i);
    data->push_back(row);
    keys->push_back(start_index + i);
  }
}

TEST(Cache, normal) {
  std::vector<TKey> keys;
  std::vector<TRowSP> data;
  mock_row(&keys, &data, 10);

  auto cache_ptr = CacheList<TRowSP>::Instance()
      .Cache("test", "")
      .value_or(nullptr);
  std::vector<std::string> select_fields;
  std::vector<::ks::grid::proto::Column> raw_columns;
  cache_ptr->Init(select_fields, raw_columns);
  EXPECT_TRUE(cache_ptr);

  cache_ptr->BatchSet(keys, data);
  cache_ptr->BatchGet(keys, &data);
  EXPECT_EQ(data.size(), keys.size());
  for (int i = 0; i < data.size(); ++i) {
    EXPECT_EQ(i, data[i]->data(0).i64());
  }

  //部分缓存命中
  std::vector<TKey> keys_part;
  for (int i = 0; i < 20; ++i) {
    keys_part.push_back(i);
  }
  cache_ptr->BatchGet(keys_part, &data);
  for (int i = 0; i < 10; ++i) {
    EXPECT_EQ(i, data[i]->data(0).i64());
  }
  for (int i = 10; i < 20; ++i) {
    EXPECT_EQ(nullptr, data[i]);
  }

  // 缓存失效
  // sleep(1);
  // std::vector<TKey> keys_evit;
  // std::vector<TRowSP> data_evit;
  // mock_row(&keys_evit, &data_evit, 1, 100);
  // cache_ptr->BatchSet(keys_evit, data_evit);
  // cache_ptr->BatchGet(keys, &data);
  // for (int i = 0; i < data.size(); ++i) {
  //   EXPECT_EQ(nullptr, data[i]);
  // }
}
