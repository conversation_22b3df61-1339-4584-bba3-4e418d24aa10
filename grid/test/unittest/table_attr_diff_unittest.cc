#include "dragon/src/core/common_reco_base.h"
#include "gtest/gtest.h"

void buildTable(ks::platform::AttrTable* table) {
  auto attr = table->GetOrInsertAttr("test_attr");
  for (int i = 0; i < 10000; ++i) {
    int64_t value = i;
    attr->SetIntValue(i, value);
  }

  auto diff_attr = table->GetOrInsertAttr("diff_attr");
  for (int i = 0; i < 10000; ++i) {
    int64_t value = i;
    diff_attr->SetIntValue(i, value);
  }
}

TEST(DIFF, normal) {
  ks::platform::AttrTable table("test_table");
  buildTable(&table);
  auto base_attr = table.GetAttr("test_attr");
  auto exp_attr = table.GetAttr("diff_attr");
  for (int i = 0; i < 10000; ++i) {
    int64_t base_value = base_attr->GetIntValue(i).value_or(0);
    int64_t exp_value = exp_attr->GetIntValue(i).value_or(0);
    EXPECT_EQ(base_value, exp_value);
  }
}
