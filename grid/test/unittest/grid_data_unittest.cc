#include <cstdint>
#include <string>
#include <vector>
#include <memory>
#include <thread>
#include "gtest/gtest.h"
#include "glog/logging.h"
#include "teams/ad/grid/sdk/grid_data.h"

using ::ks::grid::proto::Table;
using ::ks::grid::proto::GridResponse;
using ::ks::ad_index_meta::proto::ColumnType;

std::vector<std::string> col_names = {
"bool", "id", "i8", "i16", "i32", "i64", "float", "double",
  "string", "i8_list", "i16_list", "i32_list", "i64_list",
  "float_list", "double_list", "string_list"
};

std::vector<ColumnType> col_types = {
  ColumnType::BOOL, ColumnType::INT8, ColumnType::INT16, ColumnType::INT32,
  ColumnType::INT64, ColumnType::FLOAT, ColumnType::DOUBLE, ColumnType::STRING,
  ColumnType::INT8_LIST, ColumnType::INT16_LIST, ColumnType::INT32_LIST,
  ColumnType::INT64_LIST, ColumnType::FLOAT_LIST, ColumnType::DOUBLE_LIST,
  ColumnType::STRING_LIST
};

void mock_GridData(GridResponse* res, uint32_t row_size) {
  auto table = res->add_tables();
  table->set_name("test_table");

  for (int i = 0; i < col_names.size(); ++i) {
    auto col = table->add_columns();
    col->set_name(col_names[i]);
    col->set_type(col_types[i]);
  }

  for (int i = 0; i < row_size; ++i) {
    auto row = table->add_rows();
    auto data = row->add_data();
    data->set_bool_value(true);

    data = row->add_data();
    data->set_i8(100);

    data = row->add_data();
    data->set_i16(100);

    data = row->add_data();
    data->set_i32(100);

    data = row->add_data();
    data->set_i64(100);

    data = row->add_data();
    data->set_float_value(100);

    data = row->add_data();
    data->set_double_value(100);

    data = row->add_data();
    data->set_string_value("test_string");

    data = row->add_data();
    data->add_int8_list_value(100);

    data = row->add_data();
    data->add_int16_list_value(100);

    data = row->add_data();
    data->add_int32_list_value(100);

    data = row->add_data();
    data->add_int64_list_value(100);

    data = row->add_data();
    data->add_float_list_value(100);

    data = row->add_data();
    data->add_double_list_value(100);

    data = row->add_data();
    data->add_string_list_value("test_string");
  }
}

// 没有行，没有列，行没对齐
void mock_GridDataBad(GridResponse* res, uint32_t row_size) {
  // 没有行
  {
    auto table_no_row = res->add_tables();
    table_no_row->set_name("table_no_row");

    for (int i = 0; i < row_size; ++i) {
      auto col = table_no_row->add_columns();
      col->set_name(std::to_string(i));
      col->set_type(ColumnType::INT64);
    }
  }
  // 没有列
  {
    auto table_no_col = res->add_tables();
    table_no_col->set_name("table_no_col");
    for (int i = 0; i < row_size; ++i) {
      auto row = table_no_col->add_rows();
      for (int j = 0; j < row_size; ++j) {
        auto data = row->add_data();
        data->set_i64(j);
      }
    }
  }
  // 行列没有对齐
  {
    auto table_no_align = res->add_tables();
    table_no_align->set_name("table_no_align");
    for (int i = 0; i < row_size; ++i) {
      auto col = table_no_align->add_columns();
      col->set_name(std::to_string(i));
      col->set_type(ColumnType::INT64);
    }
    for (int i = 0; i < row_size; ++i) {
      auto row = table_no_align->add_rows();
      for (int j = 0; j <= i; ++j) {
        auto data = row->add_data();
        data->set_i64(j);
      }
    }
  }
}

// 正常类型访问
TEST(GridData, normal) {
  ks::grid::GridData data;
  uint32_t row_size = 10;
  // 验证数据过期
  {
    std::shared_ptr<GridResponse> ptr = std::make_shared<GridResponse>();
    mock_GridData(ptr.get(), row_size);
    data.Reset(ptr->mutable_tables(0), ptr);
  }
  for (int i = 0; i < row_size; ++i) {
    uint32_t col_index = 0;
    EXPECT_TRUE(data.GetBool(i, col_index));
    EXPECT_TRUE(data.GetBool(i, col_names[col_index++]));

    EXPECT_EQ(100, data.GetInt8(i, col_index));
    EXPECT_EQ(100, data.GetInt8(i, col_names[col_index++]));

    EXPECT_EQ(100, data.GetInt16(i, col_index));
    EXPECT_EQ(100, data.GetInt16(i, col_names[col_index++]));

    EXPECT_EQ(100, data.GetInt32(i, col_index));
    EXPECT_EQ(100, data.GetInt32(i, col_names[col_index++]));

    EXPECT_EQ(100, data.GetInt64(i, col_index));
    EXPECT_EQ(100, data.GetInt64(i, col_names[col_index++]));

    EXPECT_EQ(100, data.GetFloat(i, col_index));
    EXPECT_EQ(100, data.GetFloat(i, col_names[col_index++]));

    EXPECT_EQ(100, data.GetDouble(i, col_index));
    EXPECT_EQ(100, data.GetDouble(i, col_names[col_index++]));

    std::string s;
    data.GetString(i, col_index, &s);
    EXPECT_EQ("test_string", s);
    data.GetString(i, col_names[col_index++], &s);
    EXPECT_EQ("test_string", s);

    std::vector<int8_t> v8;
    data.GetInt8List(i, col_index, &v8);
    EXPECT_EQ(1, v8.size());
    EXPECT_EQ(100, v8[0]);
    data.GetInt8List(i, col_names[col_index++], &v8);
    EXPECT_EQ(1, v8.size());
    EXPECT_EQ(100, v8[0]);

    std::vector<int16_t> v16;
    data.GetInt16List(i, col_index, &v16);
    EXPECT_EQ(1, v16.size());
    EXPECT_EQ(100, v16[0]);
    data.GetInt16List(i, col_names[col_index++], &v16);

    std::vector<int32_t> v32;
    data.GetInt32List(i, col_index, &v32);
    EXPECT_EQ(1, v32.size());
    EXPECT_EQ(100, v32[0]);
    data.GetInt32List(i, col_names[col_index++], &v32);
    EXPECT_EQ(1, v32.size());
    EXPECT_EQ(100, v32[0]);

    std::vector<int64_t> v64;
    data.GetInt64List(i, col_index, &v64);
    EXPECT_EQ(1, v64.size());
    EXPECT_EQ(100, v64[0]);
    data.GetInt64List(i, col_names[col_index++], &v64);
    EXPECT_EQ(1, v64.size());
    EXPECT_EQ(100, v64[0]);

    std::vector<float> vf;
    data.GetFloatList(i, col_index, &vf);
    EXPECT_EQ(1, vf.size());
    EXPECT_EQ(100, vf[0]);
    data.GetFloatList(i, col_names[col_index++], &vf);
    EXPECT_EQ(1, vf.size());
    EXPECT_EQ(100, vf[0]);

    std::vector<double> vd;
    data.GetDoubleList(i, col_index, &vd);
    EXPECT_EQ(1, vd.size());
    EXPECT_EQ(100, vd[0]);
    data.GetDoubleList(i, col_names[col_index++], &vd);
    EXPECT_EQ(1, vd.size());
    EXPECT_EQ(100, vd[0]);

    std::vector<std::string> vs;
    data.GetStringList(i, col_index, &vs);
    EXPECT_EQ(1, vs.size());
    EXPECT_EQ("test_string", vs[0]);
    data.GetStringList(i, col_names[col_index++], &vs);
    EXPECT_EQ(1, vs.size());
    EXPECT_EQ("test_string", vs[0]);
  }

  // 验证 string 数据深拷贝
  std::string s;
  std::vector<std::string> t;
  {
    ks::grid::GridData data;
    std::shared_ptr<GridResponse> ptr = std::make_shared<GridResponse>();
    uint32_t row_size = 1;
    mock_GridData(ptr.get(), 1);
    data.Reset(ptr->mutable_tables(0), ptr);
    data.GetString(0, 7, &s);
    data.GetStringList(0, 14, &t);
  }
  EXPECT_EQ("test_string", s);
  EXPECT_EQ(1, t.size());
  EXPECT_EQ("test_string", t[0]);
}

// 类型误用
TEST(GridData, wrongType) {
  std::shared_ptr<GridResponse> ptr = std::make_shared<GridResponse>();
  uint32_t row_size = 10;
  mock_GridData(ptr.get(), row_size);
  ks::grid::GridData data;
  data.Reset(ptr->mutable_tables(0), ptr);

  for (int i = 0; i < row_size; ++i) {
    EXPECT_TRUE(data.GetBool(i, 0));
    EXPECT_EQ(0, data.GetInt8(i, 0));
    EXPECT_EQ(0, data.GetInt16(i, 0));
    EXPECT_EQ(0, data.GetInt32(i, 0));
    EXPECT_EQ(0, data.GetInt64(i, 0));
    EXPECT_EQ(0, data.GetFloat(i, 0));
    EXPECT_EQ(0, data.GetDouble(i, 0));

    std::vector<int8_t> v8;
    EXPECT_EQ(false, data.GetInt8List(i, 0, &v8));
    EXPECT_EQ(false, data.GetInt8List(i, 0, NULL));

    std::vector<int16_t> v16;
    EXPECT_EQ(false, data.GetInt16List(i, 0, &v16));
    EXPECT_EQ(false, data.GetInt16List(i, 0, NULL));

    std::vector<int32_t> v32;
    EXPECT_EQ(false, data.GetInt32List(i, 0, &v32));
    EXPECT_EQ(false, data.GetInt32List(i, 0, NULL));

    std::vector<int64_t> v64;
    EXPECT_EQ(false, data.GetInt64List(i, 0, &v64));
    EXPECT_EQ(false, data.GetInt64List(i, 0, NULL));

    std::vector<float> vf;
    EXPECT_EQ(false, data.GetFloatList(i, 0, &vf));
    EXPECT_EQ(false, data.GetFloatList(i, 0, NULL));

    std::vector<double> vd;
    EXPECT_EQ(false, data.GetDoubleList(i, 0, &vd));
    EXPECT_EQ(false, data.GetDoubleList(i, 0, NULL));

    std::vector<std::string> vs;
    EXPECT_EQ(false, data.GetStringList(i, 0, &vs));
    EXPECT_EQ(false, data.GetStringList(i, 0, NULL));

    std::string s;
    EXPECT_EQ(false, data.GetString(i, 0, &s));
    EXPECT_EQ(false, data.GetString(i, 0, NULL));
  }
}

// 越界
TEST(GridData, crossIndex) {
  ks::grid::GridData data;
  uint32_t row_size = 1;
  // 验证数据过期
  {
    std::shared_ptr<GridResponse> ptr = std::make_shared<GridResponse>();
    mock_GridData(ptr.get(), row_size);
    data.Reset(ptr->mutable_tables(0), ptr);
  }
  // 行越界
  for (int i = row_size; i < row_size + 10; ++i) {
    uint32_t col_index = 0;
    EXPECT_FALSE(data.GetBool(i, col_index));
    EXPECT_FALSE(data.GetBool(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetInt8(i, col_index));
    EXPECT_EQ(0, data.GetInt8(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetInt16(i, col_index));
    EXPECT_EQ(0, data.GetInt16(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetInt32(i, col_index));
    EXPECT_EQ(0, data.GetInt32(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetInt64(i, col_index));
    EXPECT_EQ(0, data.GetInt64(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetFloat(i, col_index));
    EXPECT_EQ(0, data.GetFloat(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetDouble(i, col_index));
    EXPECT_EQ(0, data.GetDouble(i, col_names[col_index++]));

    std::string s;
    data.GetString(i, col_index, &s);
    EXPECT_EQ("", s);
    data.GetString(i, col_names[col_index++], &s);
    EXPECT_EQ("", s);

    std::vector<int8_t> v8;
    data.GetInt8List(i, col_index, &v8);
    EXPECT_EQ(0, v8.size());
    data.GetInt8List(i, col_names[col_index++], &v8);
    EXPECT_EQ(0, v8.size());

    std::vector<int16_t> v16;
    data.GetInt16List(i, col_index, &v16);
    EXPECT_EQ(0, v16.size());
    data.GetInt16List(i, col_names[col_index++], &v16);
    EXPECT_EQ(0, v16.size());

    std::vector<int32_t> v32;
    data.GetInt32List(i, col_index, &v32);
    EXPECT_EQ(0, v32.size());
    data.GetInt32List(i, col_names[col_index++], &v32);
    EXPECT_EQ(0, v32.size());

    std::vector<int64_t> v64;
    data.GetInt64List(i, col_index, &v64);
    EXPECT_EQ(0, v64.size());
    data.GetInt64List(i, col_names[col_index++], &v64);
    EXPECT_EQ(0, v64.size());

    std::vector<float> vf;
    data.GetFloatList(i, col_index, &vf);
    EXPECT_EQ(0, vf.size());
    data.GetFloatList(i, col_names[col_index++], &vf);
    EXPECT_EQ(0, vf.size());

    std::vector<double> vd;
    data.GetDoubleList(i, col_index, &vd);
    EXPECT_EQ(0, vd.size());
    data.GetDoubleList(i, col_names[col_index++], &vd);
    EXPECT_EQ(0, vd.size());

    std::vector<std::string> vs;
    data.GetStringList(i, col_index, &vs);
    EXPECT_EQ(0, vs.size());
    data.GetStringList(i, col_names[col_index++], &vs);
    EXPECT_EQ(0, vs.size());
  }

  // 列越界
  // 行越界
  std::string col_name = "test_col";
  for (int i = 0; i < row_size; ++i) {
    uint32_t col_index = 10;
    EXPECT_FALSE(data.GetBool(i, col_index));
    EXPECT_FALSE(data.GetBool(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetInt8(i, col_index));
    EXPECT_EQ(0, data.GetInt8(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetInt16(i, col_index));
    EXPECT_EQ(0, data.GetInt16(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetInt32(i, col_index));
    EXPECT_EQ(0, data.GetInt32(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetInt64(i, col_index));
    EXPECT_EQ(0, data.GetInt64(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetFloat(i, col_index));
    EXPECT_EQ(0, data.GetFloat(i, col_names[col_index++]));

    EXPECT_EQ(0, data.GetDouble(i, col_index));
    EXPECT_EQ(0, data.GetDouble(i, col_names[col_index++]));

    std::string s;
    data.GetString(i, col_index, &s);
    EXPECT_EQ("", s);
    data.GetString(i, col_names[col_index++], &s);
    EXPECT_EQ("", s);

    std::vector<int8_t> v8;
    data.GetInt8List(i, col_index, &v8);
    EXPECT_EQ(0, v8.size());
    data.GetInt8List(i, col_names[col_index++], &v8);
    EXPECT_EQ(0, v8.size());

    std::vector<int16_t> v16;
    data.GetInt16List(i, col_index, &v16);
    EXPECT_EQ(0, v16.size());
    data.GetInt16List(i, col_names[col_index++], &v16);
    EXPECT_EQ(0, v16.size());

    std::vector<int32_t> v32;
    data.GetInt32List(i, col_index, &v32);
    EXPECT_EQ(0, v32.size());
    data.GetInt32List(i, col_names[col_index++], &v32);
    EXPECT_EQ(0, v32.size());

    std::vector<int64_t> v64;
    data.GetInt64List(i, col_index, &v64);
    EXPECT_EQ(0, v64.size());
    data.GetInt64List(i, col_names[col_index++], &v64);
    EXPECT_EQ(0, v64.size());

    std::vector<float> vf;
    data.GetFloatList(i, col_index, &vf);
    EXPECT_EQ(0, vf.size());
    data.GetFloatList(i, col_names[col_index++], &vf);
    EXPECT_EQ(0, vf.size());

    std::vector<double> vd;
    data.GetDoubleList(i, col_index, &vd);
    EXPECT_EQ(0, vd.size());
    data.GetDoubleList(i, col_names[col_index++], &vd);
    EXPECT_EQ(0, vd.size());

    std::vector<std::string> vs;
    data.GetStringList(i, col_index, &vs);
    EXPECT_EQ(0, vs.size());
    data.GetStringList(i, col_names[col_index++], &vs);
    EXPECT_EQ(0, vs.size());
  }
}

// 破坏性测试
TEST(GridData, destruction) {
  ks::grid::GridData data1;
  ks::grid::GridData data2;
  ks::grid::GridData data3;
  uint32_t row_size = 10;
  // 验证数据过期
  {
    std::shared_ptr<GridResponse> ptr = std::make_shared<GridResponse>();
    mock_GridDataBad(ptr.get(), row_size);
    data1.Reset(ptr->mutable_tables(0), ptr);
    data2.Reset(ptr->mutable_tables(1), ptr);
    data3.Reset(ptr->mutable_tables(2), ptr);
  }

  for (int i = 0; i < row_size; ++i) {
    for (int j = 0; j < col_names.size(); ++j) {
      EXPECT_EQ(0, data1.GetInt64(i, j));
      EXPECT_EQ(0, data2.GetInt64(i, j));
    }

    for (int j = 0; j <= i; ++j) {
      EXPECT_EQ(j, data3.GetInt64(i, j));
    }
    for (int j = i + 1; j < row_size; ++j) {
      EXPECT_EQ(0, data3.GetInt64(i, j));
    }
  }
}

TEST(GridData, noInit) {
  ks::grid::GridData data1;
  EXPECT_EQ(0, data1.ColSize());
  EXPECT_EQ(0, data1.RowSize());
  EXPECT_FALSE(data1.IsValid());
  EXPECT_FALSE(data1.IsValid(0));
  EXPECT_EQ(ks::ad_index_meta::proto::ColumnType::UNKNOWN_COLUMN, data1.GetColumnType(0));
  EXPECT_EQ(ks::ad_index_meta::proto::ColumnType::UNKNOWN_COLUMN, data1.GetColumnType("test"));

  EXPECT_EQ(false, data1.GetBool(0, 0));
  EXPECT_EQ(false, data1.GetBool(0, "test"));
  EXPECT_EQ(0, data1.GetInt8(0, 0));
  EXPECT_EQ(0, data1.GetInt8(0, "test"));
  EXPECT_EQ(0, data1.GetInt16(0, 0));
  EXPECT_EQ(0, data1.GetInt16(0, "test"));
  EXPECT_EQ(0, data1.GetInt32(0, 0));
  EXPECT_EQ(0, data1.GetInt32(0, "test"));
  EXPECT_EQ(0, data1.GetInt64(0, 0));
  EXPECT_EQ(0, data1.GetInt64(0, "test"));
  EXPECT_EQ(0, data1.GetFloat(0, 0));
  EXPECT_EQ(0, data1.GetFloat(0, "test"));
  EXPECT_EQ(0, data1.GetDouble(0, 0));
  EXPECT_EQ(0, data1.GetDouble(0, "test"));
  std::string s;
  data1.GetString(0, 0, &s);
  EXPECT_EQ("", s);
  data1.GetString(0, "test", &s);
  EXPECT_EQ("", s);

  std::vector<int8_t> v8;
  data1.GetInt8List(0, 0, &v8);
  EXPECT_EQ(0, v8.size());
  data1.GetInt8List(0, "test", &v8);
  EXPECT_EQ(0, v8.size());

  std::vector<int16_t> v16;
  data1.GetInt16List(0, 0, &v16);
  EXPECT_EQ(0, v16.size());
  data1.GetInt16List(0, "test", &v16);
  EXPECT_EQ(0, v16.size());

  std::vector<int32_t> v32;
  data1.GetInt32List(0, 0, &v32);
  EXPECT_EQ(0, v32.size());
  data1.GetInt32List(0, "test", &v32);
  EXPECT_EQ(0, v32.size());

  std::vector<int64_t> v64;
  data1.GetInt64List(0, 0, &v64);
  EXPECT_EQ(0, v64.size());
  data1.GetInt64List(0, "test", &v64);
  EXPECT_EQ(0, v64.size());

  std::vector<float> vf;
  data1.GetFloatList(0, 0, &vf);
  EXPECT_EQ(0, vf.size());
  data1.GetFloatList(0, "test", &vf);
  EXPECT_EQ(0, vf.size());

  std::vector<double> vd;
  data1.GetDoubleList(0, 0, &vd);
  EXPECT_EQ(0, vd.size());
  data1.GetDoubleList(0, "test", &vd);
  EXPECT_EQ(0, vd.size());

  std::vector<std::string> vs;
  data1.GetStringList(0, 0, &vs);
  EXPECT_EQ(0, vs.size());
  data1.GetStringList(0, "test", &vs);
  EXPECT_EQ(0, vs.size());
}
