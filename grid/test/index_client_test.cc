#include <unistd.h>

#include <iostream>
#include <string>
#include <vector>

#include "base/common/base.h"
#include "gflags/gflags.h"
#include "kenv/kenv.h"
#include "kenv/service_meta.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_table/table/key128.h"
#include "teams/ad/grid/client/sdk_client.h"
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/proto/msg.pb.h"
#include "teams/ad/grid/sdk/grid_query.h"

using namespace ks::grid;  // NOLINT
GridQuery BuildForwardQuery(const std::vector<std::string>& cols, const std::vector<int64_t>& ids) {
  GridQuery query;
  for (auto col : cols) { query.select_fields.push_back(col); }
  for (int i = 0; i < ids.size(); ++i) {
    Querykey key;
    key.i64 = ids[i];
    query.ids.push_back(key);
  }
  return query;
}

GridQuery BuildIndexQuery(const std::vector<std::string>& cols, const std::vector<int64_t>& low,
                          const std::vector<int64_t>& high) {
  GridQuery query;
  query.limit = 100;
  for (auto col : cols) { query.select_fields.push_back(col); }
  for (int i = 0; i < low.size(); ++i) {
    Querykey key;
    key.i128.low = low[i];
    key.i128.high = high[i];
    query.ids.push_back(key);
  }
  return query;
}

void TestBody(std::vector<GridQuery> req) {
  std::cout << "********************" << std::endl;
  std::vector<GridData> resp;
  SDKClient handler;
  auto res = handler.BatchGetTable(req);
  if (res != 0) {
    std::cout << "error. res: " << res << std::endl;
    return;
  }
  res = handler.Wait(&resp);
  if (res != 0) {
    std::cout << "error. res: " << res << std::endl;
    return;
  }
  if (resp.size() != req.size()) {
    std::cout << "error. resp size: " << resp.size() << " req size: " << req.size() << std::endl;
    return;
  }
  for (int i = 0; i < resp.size(); ++i) {
    std::cout << "--------------------" << std::endl;
    auto& grid_data = resp[i];
    if (grid_data.IsValid()) {
      std::cout << "col_size:" << grid_data.ColSize() << std::endl;
      std::cout << "row_size:" << grid_data.RowSize() << std::endl;
      for (int i = 0; i < grid_data.RowSize(); ++i) {
        std::cout << "Debugstring: " << grid_data.DebugString(i) << std::endl;
      }
      for (int j = 0; j < grid_data.RowSize(); ++j) {
        auto range = grid_data.GetIndexRange(j);
        std::cout << "range j " << j << " :" << range.first << " " << range.second << std::endl;
      }
    } else {
      std::cout << "invalid" << std::endl;
    }
    std::cout << "--------------------" << std::endl;
  }
  std::cout << "********************" << std::endl;
}

int main(int argc, char** argv) {
  base::InitApp(&argc, &argv, "");
  AD_KESS_CLIENT_INIT_CHECK("init error");

  auto lane = ks::infra::kenv::ServiceMeta::GetLane();
  std::cout << "debug lane:" << lane << std::endl;
  ks::infra::kenv::ServiceMeta::SetLane("ad_prt");
  std::cout << "debug lane:" << ks::infra::kenv::ServiceMeta::GetLane() << std::endl;
  {
    // creative_id 正常情况
    std::cout << "normal case: creative" << std::endl;
    std::vector<std::string> cols = {"id", "unit_id", "campaign_id", "account_id", "photo_id"};

    std::vector<int64_t> unit_id = {***********, ***********, 1, ***********};

    std::vector<int64_t> low, high;
    for (int i = 0; i < unit_id.size(); ++i) {
      ks::ad_table::GenerateKey128 ap_gen;
      ap_gen.Push(unit_id[i]);
      auto key = ap_gen.Generate();
      low.push_back(key.GetLow64Bits());
      high.push_back(key.GetHigh64Bits());
    }

    GridQuery query = BuildIndexQuery(cols, low, high);
    query.table_name = "ad_dsp_creative";
    query.index_name = "ad_dsp_creative.unit_id";
    query.op = proto::IndexOP::UNION_ALL;
    std::vector<GridQuery> req{query};
    TestBody(req);
  }

  {
    // live_id 正常情况
    std::cout << "normal case: live_id" << std::endl;
    std::vector<std::string> cols = {"creative_id", "live_id", "user_id", "item_id", "scene_oriented_type"};

    std::vector<int64_t> live_id = {***********, ***********};
    std::vector<int64_t> scene_oriented_type(live_id.size(), 24);

    std::vector<int64_t> low, high;
    for (int i = 0; i < live_id.size(); ++i) {
      ks::ad_table::GenerateKey128 ap_gen;
      ap_gen.Push(live_id[i]);
      ap_gen.Push(scene_oriented_type[i]);
      auto key = ap_gen.Generate();
      low.push_back(key.GetLow64Bits());
      high.push_back(key.GetHigh64Bits());
    }

    GridQuery query = BuildIndexQuery(cols, low, high);
    query.table_name = "wt_storewide_roi_data";
    query.index_name = "wt_storewide_roi_data.live_id";
    query.op = proto::IndexOP::UNION_ALL;
    std::vector<GridQuery> req{query};
    TestBody(req);
  }

  return 0;


  {
    // user_id_item_id 正常情况
    std::cout << "\n normal case: user_id_item_id" << std::endl;
    std::vector<std::string> cols = {"creative_id", "live_id", "user_id", "item_id", "scene_oriented_type"};
    std::vector<int64_t> user_id = {4633374876, 4633374876};
    std::vector<int64_t> item_id = {24349318622517, 24464400653900};
    std::vector<int64_t> scene_oriented_type(user_id.size(), 24);

    std::vector<int64_t> low, high;
    for (int i = 0; i < user_id.size(); ++i) {
      ks::ad_table::GenerateKey128 ap_gen;
      ap_gen.Push(user_id[i]);
      ap_gen.Push(item_id[i]);
      ap_gen.Push(scene_oriented_type[i]);
      auto key = ap_gen.Generate();
      low.push_back(key.GetLow64Bits());
      high.push_back(key.GetHigh64Bits());
    }
    GridQuery query = BuildIndexQuery(cols, low, high);
    std::cout << "debug query:" << query.DebugString() << std::endl;
    query.table_name = "wt_storewide_roi_data";
    query.index_name = "wt_storewide_roi_data.user_id_item_id";
    std::vector<GridQuery> req{query};
    TestBody(req);
  }

  {
    // live_id 边界情况
    std::cout << "abnormal case: live_id" << std::endl;
    std::vector<std::string> cols = {"creative_id",         "live_id", "user_id", "item_id",
                                     "scene_oriented_type", "test"};
    std::vector<int64_t> live_id = {-1, ***********, ***********};
    std::vector<int64_t> scene_oriented_type(live_id.size(), 24);
    std::vector<int64_t> low, high;
    for (int i = 0; i < live_id.size(); ++i) {
      ks::ad_table::GenerateKey128 ap_gen;
      ap_gen.Push(live_id[i]);
      ap_gen.Push(scene_oriented_type[i]);
      auto key = ap_gen.Generate();
      low.push_back(key.GetLow64Bits());
      high.push_back(key.GetHigh64Bits());
    }
    GridQuery query = BuildIndexQuery(cols, low, high);
    query.table_name = "wt_storewide_roi_data";
    query.index_name = "wt_storewide_roi_data.live_id";
    std::vector<GridQuery> req{query};
    TestBody(req);
  }

  {
    // 两个 query 一起
    std::cout << "normal case: two query" << std::endl;
    std::vector<std::string> cols = {"creative_id",         "live_id", "user_id", "item_id",
                                     "scene_oriented_type", "test"};
    std::vector<int64_t> live_id = {13676456962, 13652161484};
    std::vector<int64_t> scene_oriented_type(live_id.size(), 24);
    std::vector<int64_t> low, high;

    for (int i = 0; i < live_id.size(); ++i) {
      ks::ad_table::GenerateKey128 ap_gen;
      ap_gen.Push(live_id[i]);
      ap_gen.Push(scene_oriented_type[i]);
      auto key = ap_gen.Generate();
      low.push_back(key.GetLow64Bits());
      high.push_back(key.GetHigh64Bits());
    }
    GridQuery live_query = BuildIndexQuery(cols, low, high);
    live_query.table_name = "wt_storewide_roi_data";
    live_query.index_name = "wt_storewide_roi_data.live_id";

    std::vector<int64_t> user_id = {4001316757, 4134798352, 113922788, 3586917300, 2374774858};
    std::vector<int64_t> item_id = {23509564548757, 23526118259352, 20101269285788, 23532568415300,
                                    22498088668858};
    std::vector<int64_t> scene_oriented_type2(user_id.size(), 24);
    low.clear();
    high.clear();
    for (int i = 0; i < user_id.size(); ++i) {
      ks::ad_table::GenerateKey128 ap_gen;
      ap_gen.Push(user_id[i]);
      ap_gen.Push(item_id[i]);
      ap_gen.Push(scene_oriented_type2[i]);
      auto key = ap_gen.Generate();
      low.push_back(key.GetLow64Bits());
      high.push_back(key.GetHigh64Bits());
    }
    GridQuery query_user_item = BuildIndexQuery(cols, low, high);
    query_user_item.table_name = "wt_storewide_roi_data";
    query_user_item.index_name = "wt_storewide_roi_data.user_id_item_id";
    std::vector<GridQuery> req{live_query, query_user_item};
    TestBody(req);
  }

  {
    // 空值情况
    std::cout << "abnormal case: empty" << std::endl;
    std::vector<std::string> empty_cols = {};
    std::vector<std::string> cols = {"creative_id", "live_id",     "user_id",
                                     "account_id",  "campaign_id", "item_id"};
    std::vector<int64_t> ids = {***********};
    std::vector<int64_t> high = {24};
    std::vector<int64_t> empty_ids = {};

    {
      GridQuery empty_table_name_query = BuildIndexQuery(cols, ids, high);
      empty_table_name_query.index_name = "wt_storewide_roi_data.live_id";
      std::vector<GridQuery> req{empty_table_name_query};
      std::vector<GridData> resp;
      SDKClient handler;

      auto res = handler.BatchGetTable(req);
      if (res != 0) {
        std::cout << "table_name empty test success" << std::endl;
      }
    }

    {
      GridQuery empty_ids_query = BuildIndexQuery(cols, empty_ids, {});
      std::vector<GridQuery> req{empty_ids_query};
      std::vector<GridData> resp;
      SDKClient handler;

      auto res = handler.BatchGetTable(req);
      if (res != 0) {
        std::cout << "ids empty test success" << std::endl;
      }
    }

    {
      GridQuery empty_cols_query = BuildIndexQuery(empty_cols, ids, high);
      std::vector<GridQuery> req{empty_cols_query};
      std::vector<GridData> resp;
      SDKClient handler;

      auto res = handler.BatchGetTable(req);
      if (res != 0) {
        std::cout << "cols empty test success" << std::endl;
      }
    }
  }

  {
    // 分表路由
    std::cout << "two query different table" << std::endl;
    std::vector<std::string> cols = {"creative_id", "live_id",     "user_id",
                                     "account_id",  "campaign_id", "item_id"};
    std::vector<int64_t> ids1 = {***********};
    std::vector<int64_t> ids2 = {***********};
    std::vector<int64_t> high = {24};
    GridQuery query1 = BuildForwardQuery(cols, ids1);
    query1.table_name = "ad_dsp_creative";
    query1.index_name = "test";
    GridQuery query2 = BuildIndexQuery(cols, ids2, high);
    query2.table_name = "wt_storewide_roi_data";
    query2.index_name = "wt_storewide_roi_data.live_id";
    std::vector<GridQuery> req{query1, query2};
    TestBody(req);
  }
  return 0;
}
