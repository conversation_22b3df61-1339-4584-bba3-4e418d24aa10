#include <vector>
#include "teams/ad/grid/handler/grid_handler.h"
#include "teams/ad/grid/index/index_mannger.h"
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/utils/metric.h"
#include "teams/ad/grid/proto/grid_generated.h"
#include "teams/ad/grid/search/search.h"
#include "teams/ad/grid/search/index_searcher.h"
#include "flatbuffers/flatbuffers.h"

namespace ks {
namespace grid {

int32_t BatchGetTableHandler(const std::string& caller,
                             const proto::GridRequest* request,
                             proto::GridResponse* response) {
  auto& stat = Mertric::GetThreadStatInfo(caller);

  int64_t query_size = request->kvquerys_size();
  auto mutable_tables = response->mutable_tables();
  mutable_tables->Reserve(query_size);
  stat.pv_count_table += query_size;
  for (int i = 0; i < query_size; ++i) {
    mutable_tables->Add();
  }
  for (int i = 0; i < request->kvquerys_size(); ++i) {
    auto& kvquery = request->kvquerys(i);
    auto& table_name = kvquery.table_name();
    auto db = DBManager::GetInstance().GetDB(table_name);
    if (db == nullptr) {
      LOG_EVERY_N(ERROR, 1000) << "db not found, table_name: " << table_name;
      mutable_tables->Mutable(i)->set_name(table_name);
      continue;
    }
    auto data = db->BatchGetTable(table_name, &kvquery, &stat, caller);
    if (data == nullptr) {
      continue;
    }
    mutable_tables->Mutable(i)->Swap(data.get());
  }
  return 0;
}

int32_t BatchGetTableHandlerFB(const std::string& caller,
                               const proto::GridRequest* request,
                               proto::GridResponse* response) {
  flatbuffers::FlatBufferBuilder builder;
  int64_t query_size = request->querys_size();
  auto& stat = Mertric::GetThreadStatInfo(caller);
  stat.pv_count_table += query_size;

  std::vector<flatbuffers::Offset<flat::GridTable>> tables;
  tables.reserve(query_size);

  for (auto& [table_name, kvquery] : request->querys()) {
    auto db = DBManager::GetInstance().GetDB(table_name);
    // table 找不到请求失败
    if (db == nullptr) {
      LOG_EVERY_N(ERROR, 1000) << "db not found, table_name: " << table_name;
      return -1;
    }
    auto table = db->BatchGetTableFB(table_name, &kvquery, &stat, &builder, caller);
    tables.push_back(table);
  }

  auto tables_vec = builder.CreateVector(tables);
  auto res = flat::CreateResp(builder, tables_vec);
  builder.Finish(res);

  response->set_type(proto::ProtoType::FB);
  auto block = response->add_blocks();
  block->reserve(builder.GetSize());
  block->assign(builder.GetBufferPointer(),
                builder.GetBufferPointer() + builder.GetSize());
  return 0;
}

int32_t SerialSearchHandler(const std::string& caller,
                            const proto::GridRequest* request,
                            proto::GridResponse* response) {
  int query_size = request->grid_querys_size();
  auto& stat = Mertric::GetThreadStatInfo(caller);
  stat.pv_count_table += query_size;
  auto result = response->mutable_results();
  result->Reserve(query_size);

  for (int i = 0; i < query_size; ++i) {
    auto& query = request->grid_querys(i);
    auto mutable_result = result->Add();

    SearchContext ctx = {
      // NOTICE: no memory alloc happend at `.caller = caller`
      //         if no write operation at future.
      .caller = caller,
      .output_proto = request->type(),
    };

    if (!query.index_name().empty()) {
      auto& table_stat = stat.tables[query.index_name()];
      IndexSearcher searcher;
      switch (query.op()) {
        case proto::IndexOP::UNION: {
          searcher = index::Union;
          table_stat.index_op[proto::IndexOP::UNION]++;
          break;
        }
        case proto::IndexOP::UNION_ALL: {
          searcher = index::UnionALL;
          table_stat.index_op[proto::IndexOP::UNION_ALL]++;
          break;
        }
        default: {
          LOG(ERROR) << "unknown op:" << query.op() << " ,table:" << query.table_name()
                      <<", index:" << query.index_name() << ", caller:" << caller;
          return -1;
        }
      }
      if (searcher == nullptr) {
        LOG(ERROR) << "searcher not found, table:" << query.table_name() << ", index:" << query.index_name()
                   << ", caller:" << caller << ", op:" << query.op();
        continue;
      }
      int ret = IndexSearch(query, mutable_result, &ctx, searcher);
      if (ret != 0) {
        LOG(ERROR) << "IndexSearch failed, table:"
          << query.table_name()
          << ", index:"
          << query.index_name()
          << ", caller:"
          << caller;
        return ret;
      }
    } else {
      auto& query = request->grid_querys(i);
      SearchContext ctx = {
        .caller = caller,
        .output_proto = request->type(),
      };
      int ret = ForwardSearch(request->grid_querys(i), mutable_result, &ctx);
      if (ret != 0) {
        LOG(ERROR) << "ForwardSearch failed, table:"
          << query.table_name()
          << ", caller:"
          << caller;
        return ret;
      }
    }
  }
  response->set_type(request->type());
  return 0;
}

}  // namespace grid
}  // namespace ks
