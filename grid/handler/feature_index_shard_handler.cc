#include "teams/ad/grid/handler/feature_index_shard_handler.h"
#include "perfutil/perfutil.h"
#include "teams/ad/grid/utils/utils.h"
#include "teams/ad/grid/utils/perf.h"
#include "teams/ad/grid/index/index_mannger.h"

namespace ks {
namespace grid {

bool GetBatchSchemaFreeDataHandler(
    const std::string& caller,
    const ::ks::ad_feature_index::proto::GetBatchSchemaFreeDataReq* request,
    ::ks::ad_feature_index::proto::GetBatchSchemaFreeDataResp* response) {
  if (request == nullptr || response == nullptr) {
    return false;
  }

  static const int s_shard_id = GetShardId();
  static const std::string kShardId = std::to_string(s_shard_id);

  response->mutable_resps()->Reserve(request->reqs_size());
  for (int j = 0; j < request->reqs_size(); ++j) {
    response->mutable_resps()->Add();
  }

  for (int j = 0; j < request->reqs_size(); ++j) {
    const auto& req = request->reqs(j);
    const auto& table_name = req.table_name();

    if (req.ids_size() == 0 || req.attr_names_size() == 0) {
      ::ks::infra::PerfUtil::CountLogStash(
          1, "ad.grid", "shardv2.warning.invalid_req",
          caller, table_name);
      continue;
    }

    GRID_RATIO_PERF_INTERVAL_LOG_STASH(req.ids_size(),
                                       "shardv2.rpc.req.id.size",
                                       caller,
                                       table_name,
                                       kShardId);
    GRID_RATIO_PERF_INTERVAL_LOG_STASH(req.attr_names_size(),
                                       "shardv2.rpc.req.attr.size",
                                       caller,
                                       table_name);

    auto db = DBManager::GetInstance().GetDB("__all__");
    if (db == nullptr) {
      ::ks::infra::PerfUtil::CountLogStash(
          1, "ad.grid", "shardv2.index.invalidtable",
          caller, table_name);
      continue;
    }
    bool ok = db->GetSchemaFreeData(
        table_name, req, response->mutable_resps()->Mutable(j));
    if (!ok) {
      ::ks::infra::PerfUtil::CountLogStash(
          1, "ad.grid", "shardv2.warning.rpc.getfail",
          caller, table_name);
      continue;
    }
  }

  return true;
}

}  // namespace grid
}  // namespace ks
