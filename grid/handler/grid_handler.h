#pragma once

#include "string"
#include "teams/ad/grid/proto/msg.pb.h"

namespace ks {
namespace grid {

/*
  请求返回错误码：
    0: 成功
    -1：表不存在
*/
int32_t BatchGetTableHandler(const std::string& caller,
                             const proto::GridRequest* request,
                             proto::GridResponse* response);

int32_t BatchGetTableHandlerFB(const std::string& caller,
                               const proto::GridRequest* request,
                               proto::GridResponse* response);

int32_t SerialSearchHandler(const std::string& caller,
                            const proto::GridRequest* request,
                            proto::GridResponse* response);

}  // namespace grid
}  // namespace ks
