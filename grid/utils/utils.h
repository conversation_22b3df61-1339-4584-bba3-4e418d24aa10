#pragma once
#include <cstdint>
#include <vector>
#include <string>
#include <memory>
#include "ks/serving_util/dynamic_config.h"
#include "teams/ad/grid/proto/msg.pb.h"
#include "teams/ad/ad_table/table/table_schema.h"
#include "teams/ad/grid/utils/metric.h"
#include "teams/ad/grid/cache/cache_instance.h"
#include "teams/ad/grid/proto/grid_generated.h"

using ks::ad_table::ColumnDescriptor;
using ks::ad_table::ConstRowWrapper;
using ks::grid::flat::Value;

namespace ks {
namespace grid {
template <typename T>
std::string Vec2Str(const std::vector<T>& vec) {
  std::stringstream ss;
  ss << "[";
  for (auto& v : vec) {
    ss << v << ",";
  }
  ss << "]";
  return ss.str();
}

inline int32_t GetShardId() {
  return ks::DynamicJsonConfig::GetConfig()->GetInt("shard_id", -1);
}

inline void SetRow(proto::Row* row, const ConstRowWrapper& rw,
            const std::vector<const ks::ad_table::ColumnDescriptor*>& ad_table_column_descs) {
  auto row_data = row->mutable_data();
  for (auto& ad_table_column_desc : ad_table_column_descs) {
    // get value from row_wrapper;
    auto ret_col = row_data->Add();
    if (!ad_table_column_desc) {
      continue;
    }
    auto& parser = *(ad_table_column_desc);
    switch (parser.Type()) {
        case ks::ad_index_meta::proto::BOOL: {
            ret_col->set_bool_value(parser.UnsafeGetValue<bool>(rw));
        } break;
        case ks::ad_index_meta::proto::INT8: {
            ret_col->set_i8(parser.UnsafeGetValue<int8_t>(rw));
        } break;
        case ks::ad_index_meta::proto::INT16: {
            ret_col->set_i16(parser.UnsafeGetValue<int16_t>(rw));
        } break;
        case ks::ad_index_meta::proto::INT32: {
            ret_col->set_i32(parser.UnsafeGetValue<int32_t>(rw));
        } break;
        case ks::ad_index_meta::proto::INT64: {
            ret_col->set_i64(parser.UnsafeGetValue<int64_t>(rw));
        } break;
        case ks::ad_index_meta::proto::FLOAT: {
            ret_col->set_float_value(parser.UnsafeGetValue<float>(rw));
        } break;
        case ks::ad_index_meta::proto::DOUBLE: {
            ret_col->set_double_value(parser.UnsafeGetValue<double>(rw));
        } break;
        case ks::ad_index_meta::proto::STRING: {
            auto ptr = parser.UnsafeGetValue<const std::string*>(rw);
            if (ptr) {
              ret_col->set_string_value(*ptr);
            }
        } break;
        case ks::ad_index_meta::proto::INT8_LIST: {
            auto ptr = parser.UnsafeGetValue<const std::vector<int8_t>*>(rw);
            if (ptr) {
              ret_col->mutable_int8_list_value()->Assign(ptr->begin(), ptr->end());
            }
        } break;
        case ks::ad_index_meta::proto::INT16_LIST: {
            auto ptr = parser.UnsafeGetValue<const std::vector<int16_t>*>(rw);
            if (ptr) {
              ret_col->mutable_int16_list_value()->Assign(ptr->begin(), ptr->end());
            }
        } break;
        case ks::ad_index_meta::proto::INT32_LIST: {
            auto ptr = parser.UnsafeGetValue<const std::vector<int32_t>*>(rw);
            if (ptr) {
              ret_col->mutable_int32_list_value()->Assign(ptr->begin(), ptr->end());
            }
        } break;
        case ks::ad_index_meta::proto::INT64_LIST: {
            auto ptr = parser.UnsafeGetValue<const std::vector<int64_t>*>(rw);
            if (ptr) {
              ret_col->mutable_int64_list_value()->Assign(ptr->begin(), ptr->end());
            }
        } break;
        case ks::ad_index_meta::proto::FLOAT_LIST: {
            auto ptr = parser.UnsafeGetValue<const std::vector<float>*>(rw);
            if (ptr) {
              ret_col->mutable_float_list_value()->Assign(ptr->begin(), ptr->end());
            }
        } break;
        case ks::ad_index_meta::proto::DOUBLE_LIST: {
            auto ptr = parser.UnsafeGetValue<const std::vector<double>*>(rw);
            if (ptr) {
              ret_col->mutable_double_list_value()->Assign(ptr->begin(), ptr->end());
            }
        } break;
        case ks::ad_index_meta::proto::STRING_LIST: {
            auto ptr = parser.UnsafeGetValue<const std::vector<std::string>*>(rw);
            if (ptr) {
              for (auto& s : *ptr) {
                ret_col->add_string_list_value(s);
              }
            }
        } break;
        default: {}
    }
  }
}

template <typename T>
inline flatbuffers::Offset<Value> CreateValue(
  flatbuffers::FlatBufferBuilder* builder, T value) {
  return flat::CreateValue(*builder);
}

template<>
inline flatbuffers::Offset<Value> CreateValue<bool>(
  flatbuffers::FlatBufferBuilder* builder, bool value) {
  return flat::CreateValue(*builder,
                        value, 0, 0, 0, 0, 0.0f, 0, 0,
                        0, 0, 0, 0, 0, 0, 0);
}

template<>
inline flatbuffers::Offset<Value> CreateValue<int8_t>(
  flatbuffers::FlatBufferBuilder* builder, int8_t value) {
  return flat::CreateValue(*builder, false,
                        value, 0, 0, 0, 0.0f, 0, 0,
                        0, 0, 0, 0, 0, 0, 0);
}

template<>
inline flatbuffers::Offset<Value> CreateValue<int16_t>(
  flatbuffers::FlatBufferBuilder* builder, int16_t value) {
  return flat::CreateValue(*builder, false, 0,
                          value, 0, 0, 0.0f, 0, 0,
                          0, 0, 0, 0, 0, 0, 0);
}

template<>
inline flatbuffers::Offset<Value> CreateValue<int32_t>(
  flatbuffers::FlatBufferBuilder* builder, int32_t value) {
  return flat::CreateValue(*builder, false, 0, 0,
                          value, 0, 0.0f, 0, 0,
                          0, 0, 0, 0, 0, 0, 0);
}

template<>
inline flatbuffers::Offset<Value> CreateValue<int64_t>(
  flatbuffers::FlatBufferBuilder* builder, int64_t value) {
  return flat::CreateValue(*builder, false, 0, 0, 0,
                          value, 0.0f, 0, 0,
                          0, 0, 0, 0, 0, 0, 0);
}

template<>
inline flatbuffers::Offset<Value> CreateValue<float>(
  flatbuffers::FlatBufferBuilder* builder, float value) {
  return flat::CreateValue(*builder, false, 0, 0, 0, 0,
                        value, 0, 0,
                        0, 0, 0, 0, 0, 0, 0);
}

template<>
inline flatbuffers::Offset<Value> CreateValue<double>(
  flatbuffers::FlatBufferBuilder* builder, double value) {
  return flat::CreateValue(*builder, false, 0, 0, 0, 0, 0.0f,
                        value, 0,
                        0, 0, 0, 0, 0, 0, 0);
}

template <typename T>
inline flatbuffers::Offset<Value> CreateListValue(
  flatbuffers::FlatBufferBuilder* builder, const T* ptr) {
  return flat::CreateValue(*builder);
}

template<>
inline flatbuffers::Offset<Value> CreateListValue<std::string>(
  flatbuffers::FlatBufferBuilder* builder,
  const std::string* ptr) {
  if (ptr) {
    auto string_value = builder->CreateString(*ptr);
    return flat::CreateValue(*builder, false, 0, 0, 0, 0, 0.0f, 0, string_value,
                              0, 0, 0, 0, 0, 0, 0);
  } else {
    return flat::CreateValue(*builder);
  }
}

template<>
inline flatbuffers::Offset<Value> CreateListValue<std::vector<int8_t>>(
  flatbuffers::FlatBufferBuilder* builder,
  const std::vector<int8_t>* ptr) {
  if (ptr) {
    auto int8_list_value = builder->CreateVector(*ptr);
    return flat::CreateValue(*builder, false, 0, 0, 0, 0, 0.0f, 0, 0,
                              int8_list_value, 0, 0, 0, 0, 0, 0);
  } else {
    return flat::CreateValue(*builder);
  }
}

template<>
inline flatbuffers::Offset<Value> CreateListValue<std::vector<int16_t>>(
  flatbuffers::FlatBufferBuilder* builder,
  const std::vector<int16_t>* ptr) {
  if (ptr) {
    auto int16_list_value = builder->CreateVector(*ptr);
    return flat::CreateValue(*builder, false, 0, 0, 0, 0, 0.0f, 0, 0,
                              0, int16_list_value, 0, 0, 0, 0, 0);
  } else {
    return flat::CreateValue(*builder);
  }
}

template<>
inline flatbuffers::Offset<Value> CreateListValue<std::vector<int32_t>>(
  flatbuffers::FlatBufferBuilder* builder,
  const std::vector<int32_t>* ptr) {
  if (ptr) {
    auto int32_list_value = builder->CreateVector(*ptr);
    return flat::CreateValue(*builder, false, 0, 0, 0, 0, 0.0f, 0, 0,
                              0, 0, int32_list_value, 0, 0, 0, 0);
  } else {
    return flat::CreateValue(*builder);
  }
}

template<>
inline flatbuffers::Offset<Value> CreateListValue<std::vector<int64_t>>(
  flatbuffers::FlatBufferBuilder* builder,
  const std::vector<int64_t>* ptr) {
  if (ptr) {
    auto int64_list_value = builder->CreateVector(*ptr);
    return flat::CreateValue(*builder, false, 0, 0, 0, 0, 0.0f, 0, 0,
                              0, 0, 0, int64_list_value, 0, 0, 0);
  } else {
    return flat::CreateValue(*builder);
  }
}

template<>
inline flatbuffers::Offset<Value> CreateListValue<std::vector<float>>(
  flatbuffers::FlatBufferBuilder* builder,
  const std::vector<float>* ptr) {
  if (ptr) {
    auto float_list_value = builder->CreateVector(*ptr);
    return flat::CreateValue(*builder, false, 0, 0, 0, 0, 0.0f, 0, 0,
                              0, 0, 0, 0, float_list_value, 0, 0);
  } else {
    return flat::CreateValue(*builder);
  }
}

template<>
inline flatbuffers::Offset<Value> CreateListValue<std::vector<double>>(
  flatbuffers::FlatBufferBuilder* builder,
  const std::vector<double>* ptr) {
  if (ptr) {
    auto double_list_value = builder->CreateVector(*ptr);
    return flat::CreateValue(*builder, false, 0, 0, 0, 0, 0.0f, 0, 0,
                              0, 0, 0, 0, 0, double_list_value, 0);
  } else {
    return flat::CreateValue(*builder);
  }
}

template<>
inline flatbuffers::Offset<Value> CreateListValue<std::vector<std::string>>(
  flatbuffers::FlatBufferBuilder* builder,
  const std::vector<std::string>* ptr) {
  if (ptr) {
    auto string_list_value = builder->CreateVectorOfStrings(*ptr);
    return flat::CreateValue(*builder, false, 0, 0, 0, 0, 0.0f, 0, 0,
                              0, 0, 0, 0, 0, 0, string_list_value);
  } else {
    return flat::CreateValue(*builder);
  }
}

inline flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flat::Column>>>
  BuildColumnDesc(const google::protobuf::RepeatedPtrField<std::string>& select_fields,
                    const absl::flat_hash_map<std::string, ColumnDescriptor>& all_columns,
                    std::vector<const ks::ad_table::ColumnDescriptor*>& ad_table_column_descs, // NOLINT
                    flatbuffers::FlatBufferBuilder* builder,
                    Mertric::TableStat& stat,  // NOLINT
                    const std::string& caller) {
  int32_t valid_col_count = 0;
  std::vector<flatbuffers::Offset<flat::Column>> column_values;
  auto field_size = select_fields.size();
  column_values.reserve(field_size);
  ad_table_column_descs.resize(field_size, nullptr);

  for (int i = 0; i < field_size; ++i) {
    auto& col_name = select_fields.at(i);
    auto it = all_columns.find(col_name);
    auto col_fb = builder->CreateString(col_name);
    if (it != all_columns.end()) {
      ++valid_col_count;
      ad_table_column_descs[i] = &(it->second);
      auto column = flat::CreateColumn(*builder,
                                        col_fb,
                                        flat::ColumnType(static_cast<int>(it->second.Type())));
      column_values.push_back(column);
    } else {
      auto column = flat::CreateColumn(*builder, col_fb, flat::ColumnType::ColumnType_UNKNOWN_COLUMN);
      column_values.push_back(column);
      LOG_EVERY_N(WARNING, 8192) << "Column not found: " << col_name
                                << ", caller: " << caller;
    }
  }

  stat.valid_col_count += valid_col_count;

  auto values = builder->CreateVector(column_values);
  return values;
}

inline flatbuffers::Offset<flat::Row> BuildRow(int64_t id, const ConstRowWrapper& rw, std::vector<const ks::ad_table::ColumnDescriptor*>& ad_table_column_descs,  // NOLINT
                                                flatbuffers::FlatBufferBuilder* builder) {    // NOLINT
  std::vector<flatbuffers::Offset<flat::Value>> row_values;
  row_values.reserve(ad_table_column_descs.size());
  for (auto& ad_table_column_desc : ad_table_column_descs) {
    // get value from row_wrapper;
    if (!ad_table_column_desc) {
      auto value = flat::CreateValue(*builder);
      row_values.push_back(value);
      continue;
    }
    auto& parser = *(ad_table_column_desc);
    switch (parser.Type()) {
        case ks::ad_index_meta::proto::BOOL: {
          auto value = CreateValue<bool>(builder, parser.UnsafeGetValue<bool>(rw));
          row_values.push_back(value);
        } break;
        case ks::ad_index_meta::proto::INT8: {
          auto value = CreateValue<int8_t>(builder, parser.UnsafeGetValue<int8_t>(rw));
          row_values.push_back(value);
        } break;
        case ks::ad_index_meta::proto::INT16: {
          auto value = CreateValue<int16_t>(builder, parser.UnsafeGetValue<int16_t>(rw));
          row_values.push_back(value);
        } break;
        case ks::ad_index_meta::proto::INT32: {
          auto value = CreateValue<int32_t>(builder, parser.UnsafeGetValue<int32_t>(rw));
          row_values.push_back(value);
        } break;
        case ks::ad_index_meta::proto::INT64: {
          auto value = CreateValue<int64_t>(builder, parser.UnsafeGetValue<int64_t>(rw));
          row_values.push_back(value);
        } break;
        case ks::ad_index_meta::proto::FLOAT: {
          auto value = CreateValue<float>(builder, parser.UnsafeGetValue<float>(rw));
          row_values.push_back(value);
        } break;
        case ks::ad_index_meta::proto::DOUBLE: {
          auto value = CreateValue<double>(builder, parser.UnsafeGetValue<double>(rw));
          row_values.push_back(value);
        } break;
        case ks::ad_index_meta::proto::STRING: {
          auto ptr = parser.UnsafeGetValue<const std::string*>(rw);
          auto value = CreateListValue<std::string>(builder, ptr);
          row_values.push_back(value);
        } break;
        case ks::ad_index_meta::proto::INT8_LIST: {
          auto ptr = parser.UnsafeGetValue<const std::vector<int8_t>*>(rw);
          auto value = CreateListValue<std::vector<int8_t>>(builder, ptr);
          row_values.push_back(value);
        } break;
        case ks::ad_index_meta::proto::INT16_LIST: {
          auto ptr = parser.UnsafeGetValue<const std::vector<int16_t>*>(rw);
          auto value = CreateListValue<std::vector<int16_t>>(builder, ptr);
          row_values.push_back(value);
        } break;
        case ks::ad_index_meta::proto::INT32_LIST: {
          auto ptr = parser.UnsafeGetValue<const std::vector<int32_t>*>(rw);
          auto value = CreateListValue<std::vector<int32_t>>(builder, ptr);
          row_values.push_back(value);
        } break;
        case ks::ad_index_meta::proto::INT64_LIST: {
          auto ptr = parser.UnsafeGetValue<const std::vector<int64_t>*>(rw);
          auto value = CreateListValue<std::vector<int64_t>>(builder, ptr);
          row_values.push_back(value);
        } break;
        case ks::ad_index_meta::proto::FLOAT_LIST: {
          auto ptr = parser.UnsafeGetValue<const std::vector<float>*>(rw);
          auto value = CreateListValue<std::vector<float>>(builder, ptr);
          row_values.push_back(value);
        } break;
        case ks::ad_index_meta::proto::DOUBLE_LIST: {
          auto ptr = parser.UnsafeGetValue<const std::vector<double>*>(rw);
          auto value = CreateListValue<std::vector<double>>(builder, ptr);
          row_values.push_back(value);
        } break;
        case ks::ad_index_meta::proto::STRING_LIST: {
          auto ptr = parser.UnsafeGetValue<const std::vector<std::string>*>(rw);
          auto value = CreateListValue<std::vector<std::string>>(builder, ptr);
          row_values.push_back(value);
        } break;
        default: {}
    }
  }
  auto rows = builder->CreateVector(row_values);
  return flat::CreateRow(*builder, id, rows);
}

inline flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flat::Column>>>
  BuildFBColumnDesc(const std::shared_ptr<std::vector<ColumnDesc>>& all_columns,
                    flatbuffers::FlatBufferBuilder* builder) {
  std::vector<flatbuffers::Offset<flat::Column>> column_values;
  column_values.reserve(all_columns->size());
  for (auto& col : *all_columns) {
    auto col_fb = builder->CreateString(col.col_name);
    auto column = flat::CreateColumn(*builder,
                                      col_fb,
                                      flat::ColumnType(static_cast<int>(col.type)));
    column_values.push_back(column);
  }
  auto values = builder->CreateVector(column_values);
  return values;
}

inline flatbuffers::Offset<flat::Row> CopyFBRow(int64_t id, flatbuffers::Vector<flatbuffers::Offset<flat::Value>>* row,  // NOLINT
                                                flatbuffers::FlatBufferBuilder* builder) {    // NOLINT
  if (row == nullptr) {
    return flat::CreateRow(*builder, id);
  }
  std::vector<flatbuffers::Offset<flat::Value>> row_values;
  row_values.reserve(row->size());
  for (int i = 0; i < row->size(); ++i) {
    auto value = row->Get(i);
    flatbuffers::Offset<flatbuffers::String> string_value = 0;
    flatbuffers::Offset<flatbuffers::Vector<int8_t>> int8_list_value = 0;
    flatbuffers::Offset<flatbuffers::Vector<int16_t>> int16_list_value = 0;
    flatbuffers::Offset<flatbuffers::Vector<int32_t>> int32_list_value = 0;
    flatbuffers::Offset<flatbuffers::Vector<int64_t>> int64_list_value = 0;
    flatbuffers::Offset<flatbuffers::Vector<float>> float_list_value = 0;
    flatbuffers::Offset<flatbuffers::Vector<double>> double_list_value = 0;
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> string_list_value = 0;
    if (value->string_value()) {
      string_value = builder->CreateString(value->string_value()->c_str());
    }
    if (value->int8_list_value()) {
      int8_list_value = builder->CreateVector(value->int8_list_value()->data(),
                                                value->int8_list_value()->size());
    }
    if (value->int16_list_value()) {
      int16_list_value = builder->CreateVector(value->int16_list_value()->data(),
                                                value->int16_list_value()->size());
    }
    if (value->int32_list_value()) {
      int32_list_value = builder->CreateVector(value->int32_list_value()->data(),
                                                value->int32_list_value()->size());
    }
    if (value->int64_list_value()) {
      int64_list_value = builder->CreateVector(value->int64_list_value()->data(),
                                                value->int64_list_value()->size());
    }
    if (value->float_list_value()) {
      float_list_value = builder->CreateVector(value->float_list_value()->data(),
                                                value->float_list_value()->size());
    }
    if (value->double_list_value()) {
      double_list_value = builder->CreateVector(value->double_list_value()->data(),
                                                value->double_list_value()->size());
    }

    if (value->string_list_value()) {
      std::vector<flatbuffers::Offset<flatbuffers::String>> string_offsets;
      for (const auto& str : *value->string_list_value()) {
        string_offsets.push_back(builder->CreateString(str));
      }
      string_list_value = builder->CreateVector(string_offsets);
    }

    flatbuffers::Offset<flat::Value> value_offset =
      flat::CreateValue(*builder,
                        value->bool_value(),
                        value->i8(),
                        value->i16(),
                        value->i32(),
                        value->i64(),
                        value->float_value(),
                        value->double_value(),
                        string_value,
                        int8_list_value,
                        int16_list_value,
                        int32_list_value,
                        int64_list_value,
                        float_list_value,
                        double_list_value,
                        string_list_value);
    row_values.push_back(value_offset);
  }
  auto rows = builder->CreateVector(row_values);
  return flat::CreateRow(*builder, id, rows);
}

}  // namespace grid
}  // namespace ks
