#pragma once
#include <thread>
#include <string>
#include "absl/container/flat_hash_map.h"
#include "base/time/timestamp.h"
#include "perfutil/perfutil.h"
#include "teams/ad/grid/proto/common.pb.h"

namespace ks {
namespace grid {

class Mertric {
 public:
  struct TableStat {
    int32_t pv_count = 0;
    int32_t row_count = 0;
    int32_t col_count = 0;
    int32_t valid_row_count = 0;
    int32_t valid_col_count = 0;
    std::array<uint32_t, ks::grid::proto::IndexOP_ARRAYSIZE> index_op{};
  };

  struct RpcStat {
    int32_t req_count = 0;
    int32_t success_count = 0;
  };

  struct StatInfo {
    const int64_t perf_interval_second = 100000;  // 0.1s
    int32_t last_report_ts = 0;

    int32_t pv_count = 0;
    int32_t pv_count_table = 0;
    int32_t pv_count_invalid = 0;

    //  table_name : [ 请求数, 列数，有效列, 行数，有效行 ]
    absl::flat_hash_map<std::string, TableStat> tables;
    absl::flat_hash_map<std::string, RpcStat> downstreams;


    bool NeedReport() const;
    void Report(const std::string& caller);

    StatInfo& AddSuccessPv() {
      ++pv_count;
      return *this;
    }

    StatInfo& AddInvalidPv() {
      ++pv_count;
      ++pv_count_invalid;
      return *this;
    }
  };

 public:
  static StatInfo& GetThreadStatInfo(const std::string& caller);
};

}  // namespace grid
}  // namespace ks
