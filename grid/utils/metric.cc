#include <thread>
#include "absl/container/flat_hash_map.h"
#include "base/time/timestamp.h"
#include "ks/base/container/common.h"
#include "perfutil/perfutil.h"
#include "teams/ad/grid/utils/metric.h"
#include "glog/logging.h"


namespace ks {
namespace grid {
bool Mertric::StatInfo::NeedReport() const {
  return base::GetTimestamp() - last_report_ts >= perf_interval_second;
}

void Mertric::StatInfo::Report(const std::string& caller) {
  if (!NeedReport()) {
    return;
  }
  for (auto& [downstream, rpc_stat] : downstreams) {
    infra::PerfUtil::IntervalLogStash(
      rpc_stat.req_count, "ad.grid", "rpc", "total", downstream);
    infra::PerfUtil::IntervalLogStash(
      rpc_stat.success_count, "ad.grid", "rpc", "success", downstream);
      rpc_stat.success_count = 0;
      rpc_stat.req_count = 0;
  }

  if (pv_count != 0) {
    infra::PerfUtil::IntervalLogStash(
      pv_count, "ad.grid", "pv", "total", caller);

    infra::PerfUtil::IntervalLogStash(
      pv_count_invalid, "ad.grid", "pv", "invalid", caller);

    infra::PerfUtil::IntervalLogStash(
      pv_count_table, "ad.grid", "pv", "table_number", caller);

    for (auto& [table_name, table_stat] : tables) {
      int32_t col_num = table_stat.col_count;
      int32_t row_num = table_stat.row_count;
      int32_t valid_col_count = table_stat.valid_col_count;
      int32_t valid_row_count = table_stat.valid_row_count;
      int32_t pv_count = table_stat.pv_count;
      if (KS_UNLIKELY(pv_count == 0 || row_num == 0 || col_num == 0)) {
        continue;
      }

      infra::PerfUtil::IntervalLogStash(
        pv_count, "ad.grid", "item", "pv", table_name, caller);

      infra::PerfUtil::IntervalLogStash(
        col_num, "ad.grid", "item", "col_nums", table_name, caller);

      infra::PerfUtil::IntervalLogStash(
        row_num, "ad.grid", "item", "row_nums", table_name, caller);

      infra::PerfUtil::IntervalLogStash(
        valid_col_count, "ad.grid", "item", "column_hit", table_name, caller);

      infra::PerfUtil::IntervalLogStash(
        valid_row_count, "ad.grid", "item", "row_hit", table_name, caller);

      for (int i = 0; i < table_stat.index_op.size(); ++i) {
        if (table_stat.index_op[i] == 0) {
          continue;
        }
        infra::PerfUtil::IntervalLogStash(table_stat.index_op[i], "ad.grid", "item", "index", table_name,
                                          caller, ks::grid::proto::IndexOP_Name(i));
        table_stat.index_op[i] = 0;
      }

      table_stat.valid_col_count = 0;
      table_stat.valid_row_count = 0;
      table_stat.pv_count = 0;
      table_stat.col_count = 0;
      table_stat.row_count = 0;
    }
    pv_count_invalid = 0;
    pv_count_table = 0;
    pv_count = 0;
  }
}

Mertric::StatInfo& Mertric::GetThreadStatInfo(const std::string& caller) {
    static thread_local absl::flat_hash_map<std::string, StatInfo> stat_infos;
    return stat_infos[caller];
}
}  // namespace grid
}  // namespace ks
