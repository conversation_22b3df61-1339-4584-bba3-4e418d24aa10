#pragma once
#include "perfutil/perfutil.h"

#define GRID_PERF_NS "ad.grid"


namespace ks {
namespace grid {
#define GRID_PERF_INTERVAL_LOG_STASH(cnt, sub_tag, args...) \
  ::ks::infra::PerfUtil::IntervalLogStash(cnt, GRID_PERF_NS, sub_tag, args);

#define GRID_PERF_GAUGE_LOG_STASH(cnt, sub_tag, args...) \
  ::ks::infra::PerfUtil::GaugeLogStash(cnt, GRID_PERF_NS, sub_tag, args);

#define GRID_RATIO_PERF_INTERVAL_LOG_STASH(cnt, sub_tag, args...)                \
  {                                                                                 \
    thread_local int64_t ratio_num = 0;                                             \
    if (++ratio_num % 1024 == 0) {                                                  \
      ::ks::infra::PerfUtil::IntervalLogStash(cnt, GRID_PERF_NS, sub_tag, args); \
    }                                                                               \
  }
}  // namespace grid
}  // namespace ks
