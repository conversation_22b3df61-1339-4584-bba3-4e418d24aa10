#include <unistd.h>
#include <string>
#include <vector>
#include <iostream>
#include "gflags/gflags.h"
#include "absl/strings/str_split.h"
// #include "teams/ad/grid/client/sdk_client.h"
#include "teams/ad/grid/client/sdk_handler.h"
#include "teams/ad/grid/sdk/grid_client.h"
#include "teams/ad/grid/sdk/grid_data.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "base/common/base.h"
#include "base/encoding/base64.h"

namespace ks {
namespace grid {

DEFINE_string(input_table_name, "", "forward table name");
DEFINE_string(input_index_name, "", "index name");
DEFINE_string(input_selects, "", "select the fields from grid server, multi-fields combine with comma");
DEFINE_int64(input_pkey, 0, "the primary key value of the forward table");
DEFINE_string(input_index_key_value_base64, "", "the index key value encoding by base64");
DEFINE_int32(input_limit, 0, "the limit value of the index search");
DEFINE_string(dump_file, "", "the file to store the result from grid server");



void Dump(const std::string& output_file,
          const std::vector<GridData>& response) {
  if (response.size() != 1) {
    fprintf(stderr, "DUMP: response.size() != 1\n");
    return;
  }
  auto& data = response.at(0);
  if (!data.IsValid()) {
    fprintf(stderr, "DUMP: data is not valid\n");
    return;
  }
  printf("DUMP: GridData : [%lu, %lu]\n", data.RowSize(), data.ColSize());
  size_t n_row = data.RowSize();
  for (size_t i = 0; i < n_row; ++i) {
    printf("DUMP: GridData Row(%lu): %s\n", i, data.DebugString(i).c_str());
  }
}

void Run() {
  GridQuery query {
    .table_name = FLAGS_input_table_name,
    .select_fields = absl::StrSplit(FLAGS_input_selects, ','),
    .index_name = FLAGS_input_index_name,
    .limit = FLAGS_input_limit,
  };

  if (query.index_name.empty()) {
    // search forward
    Querykey qk;
    qk.i64 = FLAGS_input_pkey;
    query.ids.push_back(std::move(qk));
    query.CalculateMd5();

    std::vector<GridQuery> req{query};
    std::vector<GridData> resp;
    SDKHandler client;
    StatusCode ret = client.BatchGetTable(req);
    switch (ret) {
      case StatusCode::OK: {
        ret = client.Wait(&resp, false);
        if (ret != StatusCode::OK) {
          fprintf(stderr, "client Wait error, return code: %d\n",
                  static_cast<int>(ret));
          return;
        }
        Dump(FLAGS_dump_file, resp);
      } break;
      default: {
        fprintf(stderr, "BatchGetTable error, return code: %d\n",
                static_cast<int>(ret));
      } break;
    }

  } else {
    // search index
    std::string index_search_key;
    bool ok = base::Base64Decode(FLAGS_input_index_key_value_base64, &index_search_key);
    if (!ok) {
      fprintf(stderr, "decode index search key failed\n");
      return;
    }

    Querykey qk;
    qk.i64 = FLAGS_input_pkey;
    query.ids.push_back(std::move(qk));
    query.CalculateMd5();
    // TODO(tanweibin,huhao) 倒排查询一体化之后加上对应 sdk
  }
}

}  // namespace grid
}  // namespace ks

int main(int argc, char** argv) {
  base::InitApp(&argc, &argv, "");
  AD_KESS_CLIENT_INIT_CHECK("init error");
  ks::grid::Run();
  return 0;
}
