# -*- coding: UTF8 -*-
import json
import os

config = {
  # 基于 grpc 的 tcpcopy, 可以设置 dryrun 环境
  "grpc_tcp_copy" : {
    "default" : "online",
    #"ad-rs171.idcgz1.hn1.kwaidc.com" : "dryrun_tcpcopy_receiver",   # dryrun
  },
  "grpc" : {
    "test" : False,
    "server" : {
      "kess_name" : "USE_KSN_AS_SERVICE",  # kess_name 可以自动扩展为 kess_name + "_" + ksp 分组, default 不扩展
      "port" : 20053,
      "kcs_grpc_port_key" : "AUTO_PORT1",  # 支持服务部署到容器上， 此时 port 参数失效
      "quit_wait_seconds" : 120, # 默认 60s
      "thread_num": 10,  ## default cpu num
      "start_warmup_seconds" : 40, #默认 10s
      #"grpc_cq_num": 8, ## cq num
    },
    "client_map" : {
      ## key - > kess_name kess_name 可以自动扩展 KSP 分组, 如果满足以下条件
      #  1. 当前服务运行的环境 ksp 分组名字在 Kconf ad.engine.allowKspGroupNames 中
      #  2. kess_name + "_" + ksp 分组名字在 Kconf  ad.engine.kessGrpcClients 中
    }
  }
}

if __name__ == "__main__":
  service_name = os.getenv('KWS_SERVICE_NAME')

  shard_id = int(os.getenv('SHARD_ID', -1))
  if shard_id >= 0:
    config['shard_id'] = int(shard_id)
    config['grpc']['server']['shard'] = os.getenv('SHARD_ID')

  is_proxy = os.getenv('ENABLE_PROXY', False)
  if is_proxy:
    config['is_proxy'] = True

  cpu_num = os.getenv('KCS_CPU_CORE_NUM')
  if cpu_num:
    try:
      cpu_number = int(float(cpu_num))
      config['grpc']['client_event_loop'] = 2 * cpu_number
      config['grpc']['server']['thread_num'] = 2 * cpu_number
      if cpu_number == 50:
        config['grpc']['server']['grpc_cq_num'] = 4
    except ValueError:
      pass
  
  print(json.dumps(config, indent=2, sort_keys=True))
