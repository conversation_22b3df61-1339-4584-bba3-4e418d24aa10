#pragma once
namespace ks {
namespace grid {

enum StatusCode {
  // part of the requests succeed
  HALF_OK = 1,

  // Not an error; returned on success.
  OK = 0,

  // -1 ～ -10 内部错误
  // rpc failed
  RPC_FAILED = -1,
  // batch_waiter not init
  BATCH_WAITER_NOT_INIT = -2,

  // -11 ～ -20 请求/配置 错误
  // argument is not set correctly
  ARGUMENT_ERROR = -11,
  // rpc config is not set correctly
  RPC_CONFIG_ERROR = -12,
};

}  // namespace grid
}  // namespace ks
