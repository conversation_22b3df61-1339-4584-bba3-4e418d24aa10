#pragma once
#include <string>
#include <vector>
#include <memory>
#include "absl/container/inlined_vector.h"
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/sdk/grid_data.h"
#include "teams/ad/grid/sdk/grid_query.h"

using TKey = uint64_t;

namespace ks {
namespace grid {

struct GridQueryContext {
  // 通用配置
  std::string table_name;
  std::string index_name;
  uint32_t limit = 0;
  bool is_valid = true;
  std::shared_ptr<std::vector<ColumnDesc>> column_desc;  // 列信息
  std::vector<proto::GridResult> grid_results;  // 待解析的数据块

  // sdk 配置
  std::shared_ptr<std::vector<FbRow>> data;  // SDK 目标存储
  absl::flat_hash_map<int64_t, absl::InlinedVector<uint32_t, 4>> pos_index_map;  // 正排主键位置映射

  // proxy 配置
  proto::GridResult* result = nullptr;  // proxy 目标存储
  uint32_t query_size = 0;

  // 正排缓存
  uint32_t cache_hit = 0;
  CacheInstance<FbRow>* cache = nullptr;
  std::vector<TKey> keys2cache;
  std::vector<FbRow> rows2cache;

  // 倒排配置
  proto::IndexOP op = proto::IndexOP::UNION;
  std::vector<key128> indexkeys;  // 倒排 key 存档
  std::vector<std::vector<FbRow>> index2rows;
  std::vector<int32_t> offsets;  // 倒排结果范围 左闭右开 i 号元素代表第 i 个倒排结果的起始位置
};
}  // namespace grid
}  // namespace ks
