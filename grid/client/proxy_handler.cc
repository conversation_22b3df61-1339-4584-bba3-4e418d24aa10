#include <memory>
#include <utility>
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/sdk/grid_data.h"
#include "kess/rpc/batch_waiter.h"
#include "teams/ad/grid/proto/grid_generated.h"
#include "teams/ad/grid/client/proxy_handler.h"
#include "teams/ad/grid/cache/cache_list.h"
#include "teams/ad/grid/utils/utils.h"

namespace ks {
namespace grid {

StatusCode ProxyHandler::BatchGetTable(const GridRequest& request, GridResponse* response,
                                       const std::string& caller) {
  if (request.querys_size() == 0) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] querys size is 0";
    return StatusCode::ARGUMENT_ERROR;
  }

  if (response == nullptr) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] response is nullptr";
    return StatusCode::ARGUMENT_ERROR;
  }
  response_ = response;

  auto ret = Prepare(request, &data_, &index_map_, &rpc_helper_, &ksn_rpc_helper);
  if (ret != StatusCode::OK) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] Prepare failed";
    return ret;
  }
  if (FLAGS_enable_grid_sdk_cache) {
    TryGetFromCache(request, &data_);
  }
  BuildRequest(request, &data_, caller);
  Request(batch_waiter_.get(), &ksn_rpc_helper);
  FillCacheBlockFB(request, response, &data_);
  batch_waiter_->Wait();
  return StatusCode::OK;
}

StatusCode ProxyHandler::Prepare(
  const GridRequest& request,
  absl::flat_hash_map<std::string, DataWrapper>* data,
  absl::flat_hash_map<std::string, absl::flat_hash_map<int64_t, int64_t>>* index_map,
  absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>>* rpc_helper,
  absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>>* ksn_rpc_helper) {
  const auto& table_name_map = GridKconf::TableRouterConfig()->data().GetTable();
  for (auto& [table_name, query] : request.querys()) {
    if (query.table_name().empty()) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] empty table_name";
      continue;
    }
    if (query.select_fields_size() == 0) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] empty select_fields" << " table_name:" << table_name;
      continue;
    }
    if (query.keys_size() == 0) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] empty ids" << " table_name:" << table_name;
      continue;
    }
    (*data)[table_name].data = std::make_shared<std::vector<FbRow>>(query.keys_size());
    auto &cur_index_map = (*index_map)[table_name];
    for (int i = 0; i < query.keys_size(); ++i) {
      cur_index_map[query.keys(i).i64()] = i;
    }

    if (table_name_map.find(table_name) == table_name_map.end()) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] table_name=" << table_name
                                << " not found in router_option";
      return StatusCode::RPC_CONFIG_ERROR;
    }

    auto& ksn_meta = table_name_map.at(table_name);
    if (ksn_rpc_helper->find(ksn_meta.ksn()) == ksn_rpc_helper->end()) {
      (*ksn_rpc_helper)[ksn_meta.ksn()] = std::make_shared<RpcHelper>(ksn_meta);
    }
    (*rpc_helper)[table_name] = (*ksn_rpc_helper)[ksn_meta.ksn()];
  }
  if (FLAGS_enable_grid_sdk_cache) {
    cache_flag_.resize(request.querys_size(), 0);
  }

  batch_waiter_ = std::make_shared<ks::kess::rpc::BatchWaiter>();
  return StatusCode::OK;
}

void ProxyHandler::TryGetFromCache(
  const GridRequest& request,
  absl::flat_hash_map<std::string, DataWrapper>* data) {
  std::vector<TKey> keys;
  int32_t i = -1;
  for (auto& [table_name, query] : request.querys()) {
    ++i;
    if (query.select_fields_size() == 0 ||
        query.keys_size() == 0 ||
        table_name.empty() ||
        query.md5().empty()) {
      continue;
    }

    auto cache_ptr = CacheList<FbRow>::Instance()
      .Cache(table_name, query.md5())
      .value_or(nullptr);

    if (cache_ptr == nullptr) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] table_name=" << table_name << " cache instance not Found";
      continue;
    }

    cache_map_[table_name] = cache_ptr;

    keys.resize(query.keys_size());
    for (int j = 0; j < query.keys_size(); ++j) {
      keys[j] = query.keys(j).i64();
    }
    int ret = cache_ptr->BatchGet(keys, (*data)[table_name].data.get());
    cache_flag_[i] = ret;
    if (!cache_ptr->NeedInit()) {
      (*data)[table_name].column_desc = cache_ptr->GetTableSchema();
    }
  }
}

void ProxyHandler::BuildRequest(
  const GridRequest& request,
  absl::flat_hash_map<std::string, DataWrapper>* data,
  const std::string& caller) {
  int32_t index = -1;
  for (auto& [table_name, query] : request.querys()) {
    ++index;
    if (query.select_fields_size() == 0 ||
        query.keys_size() == 0 ||
        table_name.empty()) {
      continue;
    }
    if (!cache_flag_.empty() && cache_flag_[index] == query.keys_size()) {
      LOG_EVERY_N(INFO, 100) << "[Grid SDK] " << table_name << " hit cache";
      continue;
    }
    if (rpc_helper_[table_name] == nullptr) {
      LOG_EVERY_N(INFO, 1000) << "[Grid SDK] " << table_name << " rpc_helper is nullptr";
      continue;
    }
    auto& rpc_helper = rpc_helper_[table_name];
    auto filter = [&cache = (*data)[table_name].data](int32_t i) {
      if (cache == nullptr || cache->size() <= i) {
        return false;
      }
      if ((*cache)[i]) {
        return true;
      }
      return false;
    };
    rpc_helper->AddTableQuery(query, filter, caller);
  }
  return;
}

void ProxyHandler::Request(
  ks::kess::rpc::BatchWaiter* batch_waiter,
  absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>>* ksn_rpc_helper) {
  auto parser = [this](GridResponse* response) {
    this->ParserResponseFB(response);
  };

  for (auto& [_, rpc_helper] : *ksn_rpc_helper) {
    if (rpc_helper == nullptr) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] rpc_helper is nullptr";
      continue;
    }
    rpc_helper->Request(batch_waiter, parser);
  }
  return;
}

void ProxyHandler::ParserResponseFB(GridResponse* response) {
  if (response == nullptr) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] response is nullptr";
    return;
  }
  if (response->blocks_size() == 0) {
     LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] response is empty";
    return;
  }
  for (int i = response->blocks_size()-1; i >= 0; --i) {
    // 直接拷贝所有数据，由客户端侧统一解析，不做碎片化拷贝
    auto block = response->mutable_blocks()->ReleaseLast();
    response_->mutable_blocks()->AddAllocated(block);
    if (!FLAGS_enable_grid_sdk_cache) {
      continue;
    }
    // 缓存数据
    std::shared_ptr<std::string> data =
      std::make_shared<std::string>(block->data(), block->size());
    auto fb_data = flatbuffers::GetRoot<flat::Resp>(data->data());
    if (fb_data == nullptr || fb_data == 0) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] parse response failed, data=" << *data;
      continue;
    }
    auto tables = fb_data->tables();
    if (tables == 0) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] parse response failed, tables is null, data=" << *data;
      continue;
    }

    // 更新缓存
    for (int j = 0; j < tables->size(); j++) {
      auto table = tables->Get(j);
      if (table == 0) {
        LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] parse response failed, table is null, data=" << *data;
        continue;
      }
      if (table->name()->size() == 0 || table->columns() == 0 || table->rows() == 0) {
        LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] parse response failed, table is empty, name_size:"
                                << table->name()->size()
                                << " columns_size:" << table->columns()->size()
                                << " rows_size:" << table->rows()->size();
        continue;
      }
      std::vector<TKey> keys;
      std::vector<FbRow> rows;
      keys.reserve(table->rows()->size());
      rows.reserve(table->rows()->size());

      auto col_size = table->columns()->size();
      auto row_size = table->rows()->size();
      auto table_name = table->name()->str();
      auto& table_data = data_[table_name].data;
      auto& table_cols = data_[table_name].column_desc;

      auto& index_map = index_map_[table_name];

      if (cache_map_.find(table_name) == cache_map_.end()) {
        LOG_EVERY_N(ERROR, 8192) << "[Grid SDK] table_name="
                                  << table_name << " cache instance not Found";
        continue;
      }
      auto cache_ptr = cache_map_[table_name];
      for (int index_id = 0; index_id < row_size; ++index_id) {
        auto row = table->rows()->Get(index_id);
        int64_t id = row->id();
        int64_t id_index = index_map[id];
        auto row_data =
          const_cast<flatbuffers::Vector<flatbuffers::Offset<flat::Value>>*>(row->data());

        if (row_data != 0 && row_data->size() == col_size) {
          (*table_data)[id_index].guard = data;
          (*table_data)[id_index].row = row_data;
        }
        keys.push_back(id);
        rows.push_back((*table_data)[id_index]);
      }

      if (cache_ptr->NeedInit()) {
        cache_ptr->Init(table->columns());
      }
      auto ret = cache_ptr->BatchSet(keys, rows);
    }
  }
}

void ProxyHandler::FillCacheBlockFB(const GridRequest& request,
    GridResponse* response,
    absl::flat_hash_map<std::string, DataWrapper>* data) {
  flatbuffers::FlatBufferBuilder builder;
  std::vector<flatbuffers::Offset<flat::GridTable>> tables;
  int32_t index = -1;
  for (auto& [table_name, query] : request.querys()) {
    ++index;
    if (query.select_fields_size() == 0 ||
        query.keys_size() == 0 ||
        table_name.empty()) {
      continue;
    }
    auto& cache_data = (*data)[table_name].data;
    if (cache_data == nullptr) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] " << table_name << " cache_data is nullptr";
      continue;
    }
    // 缓存没命中
    if (!cache_flag_.empty() && cache_flag_[index] == 0) {
      continue;
    }
    // 构造列
    if (cache_map_.find(table_name) == cache_map_.end()) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] table_name="
                                << table_name << " cache instance not Found";
      continue;
    }
    auto cache_ptr = cache_map_[table_name];
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flat::Column>>> col_vec;
    if (cache_ptr->NeedInit()) {
      continue;
    } else {
      col_vec = BuildFBColumnDesc(cache_ptr->GetTableSchema(), &builder);
    }
    // 构造行
    std::vector<flatbuffers::Offset<flat::Row>> rows;
    for (int i = 0; i < cache_data->size(); ++i) {
      auto cache_row = (*cache_data)[i].row;
      int64_t key = query.keys(i).i64();
      if (!(cache_row)) {
        continue;
      }
      auto row = CopyFBRow(key, cache_row, &builder);
      rows.push_back(row);
    }
    if (rows.size() == 0) {
      continue;
    }
    // 构造 table
    auto rows_vec = builder.CreateVector(rows);
    auto name_fb = builder.CreateString(table_name);
    auto table = flat::CreateGridTable(builder, name_fb, col_vec, rows_vec);
    tables.push_back(table);
  }
  if (tables.size() != 0) {
    auto tables_vec = builder.CreateVector(tables);
    auto res = flat::CreateResp(builder, tables_vec);
    builder.Finish(res);

    response->set_type(proto::ProtoType::FB);
    auto block = response->add_blocks();
    block->reserve(builder.GetSize());
    block->assign(builder.GetBufferPointer(),
                  builder.GetBufferPointer() + builder.GetSize());
  }
}
}  // namespace grid
}  // namespace ks
