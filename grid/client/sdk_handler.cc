#include <cstdint>
#include <memory>
#include <sstream>
#include <utility>
#include <vector>
#include "tbb/parallel_for.h"
#include "tbb/task_arena.h"
#include "teams/ad/grid/sdk/grid_query.h"
#include "teams/ad/grid/sdk/grid_data.h"
#include "kess/rpc/batch_waiter.h"
#include "teams/ad/grid/proto/grid_generated.h"
#include "teams/ad/grid/client/sdk_handler.h"
#include "teams/ad/grid/cache/cache_list.h"

namespace ks {
namespace grid {

StatusCode SDKHandler::BatchGetTable(const std::vector<GridQuery>& querys) {
  if (querys.size() == 0) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] querys size is 0";
    return StatusCode::ARGUMENT_ERROR;
  }
  auto ret = Prepare(querys, &data_, &index_map_, &rpc_helper_, &ksn_rpc_helper_);
  if (ret != StatusCode::OK) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] Prepare failed";
    return ret;
  }
  if (FLAGS_enable_grid_sdk_cache) {
    if (GridKconf::EnableCacheParallel()) {
      ParallelTryGetFromCache(querys, &data_);
    } else {
      TryGetFromCache(querys, &data_);
    }
  }
  BuildRequest(querys, &data_);
  Request(batch_waiter_.get(), &ksn_rpc_helper_);
  return StatusCode::OK;
}

StatusCode SDKHandler::Wait(std::vector<GridData>* resp, bool update_cache) {
  if (resp == nullptr) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] resp is nullptr";
    return StatusCode::ARGUMENT_ERROR;
  }
  if (batch_waiter_ == nullptr) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] batch_waiter_ is nullptr";
    return StatusCode::BATCH_WAITER_NOT_INIT;
  }
  batch_waiter_->Wait();
  resp->clear();
  resp->resize(table_name_.size());
  for (int i = 0; i < table_name_.size(); ++i) {
    auto& table_name = table_name_[i];
    auto res = (*resp)[i].ResetFB(data_[table_name].data,
                        data_[table_name].column_desc,
                        table_name);
    if (!res) {
      bool data_null = data_[table_name].data == nullptr;
      bool column_desc_null = data_[table_name].column_desc == nullptr;
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] " << table_name << " ResetFB failed, data_null:" << data_null
                  << " column_desc_null:" << column_desc_null;
    }
  }
  if (update_cache && FLAGS_enable_grid_sdk_cache) {
    UpdateCache();
  }
  return StatusCode::OK;
}

void SDKHandler::Clear() {
  data_.clear();
  to_cache_data_.clear();
  index_map_.clear();
  cache_flag_.clear();
  table_name_.clear();
  cache_map_.clear();
  rpc_helper_.clear();
  ksn_rpc_helper_.clear();
  batch_waiter_.reset();
}

StatusCode SDKHandler::Prepare(
  const std::vector<GridQuery>& querys,
  absl::flat_hash_map<std::string, DataWrapper>* data,
  absl::flat_hash_map<std::string, absl::flat_hash_map<int64_t, absl::InlinedVector<uint32_t, 4>>>* index_map,
  absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>>* rpc_helper,
  absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>>* ksn_rpc_helper) {
  table_name_.reserve(querys.size());
  const auto& table_name_map = GridKconf::TableRouterConfig()->data().GetTable();
  for (auto& query : querys) {
    table_name_.push_back(query.table_name);
    if (query.table_name.empty()) {
      LOG_EVERY_N(ERROR, 10000) << "[Grid SDK] empty table_name";
      continue;
    }
    if (query.select_fields.size() == 0) {
      LOG_EVERY_N(ERROR, 10000) << "[Grid SDK] empty select_fields" << " table_name:" << query.table_name;
      continue;
    }
    if (query.ids.size() == 0) {
      LOG_EVERY_N(ERROR, 10000) << "[Grid SDK] empty ids" << " table_name:" << query.table_name;
      continue;
    }
    (*data)[query.table_name].data = std::make_shared<std::vector<FbRow>>(query.ids.size());
    auto &cur_index_map = (*index_map)[query.table_name];
    for (int i = 0; i < query.ids.size(); ++i) {
      cur_index_map[query.ids[i].i64].push_back(i);
    }

    if (table_name_map.find(query.table_name) == table_name_map.end()) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] table_name=" << query.table_name
                                << " not found in router_option";
      return StatusCode::RPC_CONFIG_ERROR;
    }

    auto& ksn_meta = table_name_map.at(query.table_name);
    if (ksn_rpc_helper->find(ksn_meta.ksn()) == ksn_rpc_helper->end()) {
      (*ksn_rpc_helper)[ksn_meta.ksn()] = std::make_shared<RpcHelper>(ksn_meta);
    }
    (*rpc_helper)[query.table_name] = (*ksn_rpc_helper)[ksn_meta.ksn()];
    if (FLAGS_enable_grid_sdk_cache) {
      auto cache_ptr = CacheList<FbRow>::Instance()
      .Cache(query.table_name, query.md5)
      .value_or(nullptr);
      if (cache_ptr == nullptr) {
        LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] table_name="
                                  << query.table_name << " cache instance not Found";
      }
      cache_map_[query.table_name] = cache_ptr;
    }
  }
  if (FLAGS_enable_grid_sdk_cache) {
    cache_flag_.resize(querys.size(), 0);
  }
  batch_waiter_ = std::make_shared<ks::kess::rpc::BatchWaiter>();
  return StatusCode::OK;
}

void SDKHandler::ParallelTryGetFromCache(
  const std::vector<GridQuery>& querys,
  absl::flat_hash_map<std::string, DataWrapper>* data) {
  auto parse_one_query = [&](int32_t i, const GridQuery& query) {
    std::vector<TKey> keys;
    if (query.select_fields.size() == 0 || query.ids.size() == 0 || query.table_name.empty()) {
      return;
    }

    auto cache_ptr = cache_map_[query.table_name];
    if (cache_ptr == nullptr) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] table_name=" << query.table_name << " cache instance not Found";
      return;
    }

    keys.resize(query.ids.size());
    for (int j = 0; j < query.ids.size(); ++j) { keys[j] = query.ids[j].i64; }
    int ret = cache_ptr->BatchGet(keys, (*data)[query.table_name].data.get());
    if (!cache_ptr->NeedInit()) {
      (*data)[query.table_name].column_desc = cache_ptr->GetTableSchema();
    }
    cache_flag_[i] = ret;
  };

  tbb::task_arena limited_arena(ks::grid::GridKconf::ParallelNum());
  limited_arena.execute([&] {
    tbb::parallel_for(tbb::blocked_range<int>(0, querys.size()), [&](tbb::blocked_range<int> r) {
      for (int i = r.begin(); i < r.end(); ++i) { parse_one_query(i, querys[i]); }
    });
  });
}

void SDKHandler::TryGetFromCache(
  const std::vector<GridQuery>& querys,
  absl::flat_hash_map<std::string, DataWrapper>* data) {
  std::vector<TKey> keys;
  for (int i = 0; i < querys.size(); ++i) {
    auto& query = querys[i];
    if (query.select_fields.size() == 0 ||
        query.ids.size() == 0 ||
        query.table_name.empty()) {
      continue;
    }

    auto cache_ptr = CacheList<FbRow>::Instance()
      .Cache(query.table_name, query.md5)
      .value_or(nullptr);

    if (cache_ptr == nullptr) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] table_name="
                                << query.table_name << " cache instance not Found";
      continue;
    }

    cache_map_[query.table_name] = cache_ptr;

    keys.resize(query.ids.size());
    for (int j = 0; j < query.ids.size(); ++j) {
      keys[j] = query.ids[j].i64;
    }
    int ret = cache_ptr->BatchGet(keys, (*data)[query.table_name].data.get());
    if (!cache_ptr->NeedInit()) {
      (*data)[query.table_name].column_desc = cache_ptr->GetTableSchema();
    }
    cache_flag_[i] = ret;
  }
}

void SDKHandler::BuildRequest(
  const std::vector<GridQuery>& querys,
  absl::flat_hash_map<std::string, DataWrapper>* data) {
  for (int i = 0; i < querys.size(); ++i) {
    auto& query = querys[i];
    if (query.select_fields.size() == 0 ||
        query.ids.size() == 0 ||
        query.table_name.empty()) {
      continue;
    }
    if (!cache_flag_.empty() && cache_flag_[i] == query.ids.size()) {
      LOG_EVERY_N(INFO, 1000) << "[Grid SDK] " << query.table_name << " hit cache";
      continue;
    }
    if (rpc_helper_[query.table_name] == nullptr) {
      LOG_EVERY_N(INFO, 1000) << "[Grid SDK] " << query.table_name << " rpc_helper is nullptr";
      continue;
    }
    auto& rpc_helper = rpc_helper_[query.table_name];
    auto filter = [&cache = (*data)[query.table_name].data](int32_t i) {
      if (cache == nullptr || cache->size() <= i) {
        return false;
      }
      if ((*cache)[i]) {
        return true;
      }
      return false;
    };
    rpc_helper->AddTableQuery(query, filter);
  }
  return;
}

void SDKHandler::Request(
  ks::kess::rpc::BatchWaiter* batch_waiter,
  absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>>* ksn_rpc_helper) {
  auto parser = [this](GridResponse* response) {
    this->ParserResponse(response);
  };

  for (auto& [_, rpc_helper] : *ksn_rpc_helper) {
    if (rpc_helper == nullptr) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] rpc_helper is nullptr";
      continue;
    }
    rpc_helper->Request(batch_waiter, parser);
  }
  return;
}

void SDKHandler::ParserResponse(GridResponse* response) {
  if (response == nullptr) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] response is nullptr";
    return;
  }
  if (response->blocks_size() == 0) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] response is empty";
    return;
  }
  for (int i = 0; i < response->blocks_size(); ++i) {
    auto& block = response->blocks(i);
    std::shared_ptr<std::string> data =
      std::make_shared<std::string>(std::move(block));
    auto fb_data = flatbuffers::GetRoot<flat::Resp>(data->data());
    if (fb_data == nullptr || fb_data == 0) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] parse response failed, data=" << *data;
      continue;
    }
    auto tables = fb_data->tables();
    if (tables == 0) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] parse response failed, tables is null, data=" << *data;
      continue;
    }
    for (int j = 0; j < tables->size(); j++) {
      auto table = tables->Get(j);
      if (table == 0) {
        LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] parse response failed, table is null, data=" << *data;
        continue;
      }
      if (table->name()->size() == 0 || table->columns() == 0 || table->rows() == 0) {
        LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] parse response failed, table is empty, name_size:"
                                << table->name()->size()
                                << " columns_size:" << table->columns()->size()
                                << " rows_size:" << table->rows()->size();
        continue;
      }

      auto col_size = table->columns()->size();
      auto row_size = table->rows()->size();
      auto table_name = table->name()->str();
      auto& keys = to_cache_data_[table_name].keys;
      auto& rows = to_cache_data_[table_name].rows;
      auto& table_data = data_[table_name].data;
      auto& table_cols = data_[table_name].column_desc;

      auto& index_map = index_map_[table_name];
      for (int index_id = 0; index_id < row_size; ++index_id) {
        auto row = table->rows()->Get(index_id);
        int64_t id = row->id();
        auto row_data =
          const_cast<flatbuffers::Vector<flatbuffers::Offset<flat::Value>>*>(row->data());

        FbRow fb_row;
        const auto& id_index = index_map[id];
        if (row_data != 0 && row_data->size() == col_size) {
          for (uint32_t pos_id : id_index) {
            (*table_data)[pos_id].guard = data;
            (*table_data)[pos_id].row = row_data;
          }
          fb_row.guard = data;
          fb_row.row = row_data;
        }
        keys.push_back(id);
        rows.push_back(fb_row);
      }
      // 使用缓存的情况
      auto itr = cache_map_.find(table_name);
      if (itr != cache_map_.end()) {
        auto cache_ptr = itr->second;
        if (cache_ptr->NeedInit()) {
          cache_ptr->Init(table->columns());
        }
        if (table_cols == nullptr) {
          table_cols = cache_ptr->GetTableSchema();
        }
      }
      // 不使用缓存，需要构造 col 类型
      if (table_cols == nullptr) {
        auto columns = table->columns();
        table_cols = std::make_shared<std::vector<ColumnDesc>>(columns->size());
        for (size_t i = 0; i < columns->size(); ++i) {
          const auto& column = columns->Get(i);
          (*table_cols)[i].col_name = column->name()->str();
          (*table_cols)[i].type = ad_index_meta::proto::ColumnType(static_cast<int>(column->type()));
        }
      }
    }
  }
}

void SDKHandler::UpdateCache() {
  for (auto& [table_name, cache_ptr] : cache_map_) {
    auto& keys = to_cache_data_[table_name].keys;
    auto& rows = to_cache_data_[table_name].rows;
    cache_ptr->BatchSet(keys, rows);
  }
}
}  // namespace grid
}  // namespace ks
