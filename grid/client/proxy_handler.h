#pragma once
#include <string>
#include <memory>
#include <vector>
#include "absl/container/flat_hash_map.h"
#include "kess/rpc/batch_waiter.h"
#include "teams/ad/grid/sdk/grid_data.h"
#include "teams/ad/grid/proto/msg.pb.h"
#include "teams/ad/grid/client/rpc/rpc_helper.h"
#include "teams/ad/grid/client/rpc/status.h"
#include "teams/ad/grid/sdk/grid_query.h"

using ::ks::grid::proto::GridRequest;
using ::ks::grid::proto::GridResponse;
using TKey = uint64_t;

namespace ks {
namespace grid {
class ProxyHandler {
 public:
  StatusCode BatchGetTable(const GridRequest& request, GridResponse* response, const std::string& caller);

 private:
  struct DataWrapper {
    std::shared_ptr<std::vector<FbRow>> data;
    std::shared_ptr<std::vector<ColumnDesc>> column_desc;
  };

  StatusCode Prepare(
    const GridRequest& request,
    absl::flat_hash_map<std::string, DataWrapper>* data,
    absl::flat_hash_map<std::string, absl::flat_hash_map<int64_t, int64_t>>* index_map,
    absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>>* rpc_helper,
    absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>>* ksn_rpc_helper);

  void TryGetFromCache(
    const GridRequest& request,
    absl::flat_hash_map<std::string, DataWrapper>* data);

  void BuildRequest(const GridRequest& request,
                    absl::flat_hash_map<std::string, DataWrapper>* data,
                    const std::string& caller);
  void Request(ks::kess::rpc::BatchWaiter* batch_waiter,
                absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>>* ksn_rpc_helper);

  void ParserResponseFB(GridResponse* response);

  // 将缓存结果构建为 FB 结构
  void FillCacheBlockFB(const GridRequest& request,
                      GridResponse* response,
                      absl::flat_hash_map<std::string, DataWrapper>* data);

 private:
  absl::flat_hash_map<std::string, DataWrapper> data_;
  // table: (key : index)  保存主键到位置的映射关系
  absl::flat_hash_map<std::string, absl::flat_hash_map<int64_t, int64_t>> index_map_;
  std::vector<uint32_t> cache_flag_;

  absl::flat_hash_map<std::string, CacheInstance<FbRow>*> cache_map_;

  GridResponse* response_;

  // table -> rpc_helper
  absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>> rpc_helper_;
  // ksn -> rpc_helper
  absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>> ksn_rpc_helper;

  std::shared_ptr<ks::kess::rpc::BatchWaiter> batch_waiter_;
};

}  // namespace grid
}  // namespace ks
