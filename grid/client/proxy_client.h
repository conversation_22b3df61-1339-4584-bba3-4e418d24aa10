#pragma once
#include <memory>
#include <string>
#include <vector>

#include "absl/container/flat_hash_map.h"
#include "kess/rpc/batch_waiter.h"
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/proto/msg.pb.h"
#include "teams/ad/grid/client/grid_query_context.h"
#include "teams/ad/grid/client/rpc/rpc_helper.h"
#include "teams/ad/grid/client/rpc/status.h"
#include "teams/ad/grid/sdk/grid_data.h"
#include "teams/ad/grid/sdk/grid_query.h"

using ::ks::grid::proto::GridRequest;
using ::ks::grid::proto::GridResponse;
using TKey = uint64_t;

namespace ks {
namespace grid {
class ProxyClient {
 public:
  StatusCode BatchGetTable(const GridRequest& request, GridResponse* response, const std::string& caller);

 private:
  StatusCode Init(const GridRequest& request);
  void BuildRequest(const proto::GridQuery& query, uint32_t index);
  void Request(ks::kess::rpc::Bat<PERSON><PERSON><PERSON><PERSON>* batch_waiter,
               absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>>* ksn_rpc_helper,
               const std::string& caller);
  void ParserResponse(GridResponse* response, std::vector<GridQueryContext*>& contexts); // NOLINT

  // 当前只支持正排缓存
  void TryGetFromCache(const proto::GridQuery& query, GridQueryContext* context);
  void UpdateCache();
  void UpdateOneCache(GridQueryContext* context);
  void FillCacheBlockFB(const GridRequest& request);  // 将缓存结果构建为 FB 结构

 private:
  GridResponse* response_;
  std::vector<GridQueryContext> search_contexts_;  // 每个 query 对应一个 context

  // table -> rpc_helper
  absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>> rpc_helper_;
  // ksn -> rpc_helper
  absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>> ksn_rpc_helper_;
  std::shared_ptr<ks::kess::rpc::BatchWaiter> batch_waiter_;
};

}  // namespace grid
}  // namespace ks
