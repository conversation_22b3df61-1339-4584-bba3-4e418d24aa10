#pragma once
#include <cstdint>
#include <string>
#include <memory>
#include <vector>
#include "absl/container/flat_hash_map.h"
#include "absl/container/inlined_vector.h"
#include "kess/rpc/batch_waiter.h"
#include "teams/ad/grid/sdk/grid_data.h"
#include "teams/ad/grid/proto/msg.pb.h"
#include "teams/ad/grid/client/rpc/rpc_helper.h"
#include "teams/ad/grid/client/rpc/status.h"
#include "teams/ad/grid/sdk/grid_query.h"

using ::ks::grid::proto::GridRequest;
using ::ks::grid::proto::GridResponse;
using TKey = uint64_t;

#define PERF_TIME(CNT, NAME) infra::PerfUtil::IntervalLogStash(CNT, "ad.remote_table", "time_cost", NAME);

namespace ks {
namespace grid {

struct DataWrapper {
  std::shared_ptr<std::vector<FbRow>> data;
  std::shared_ptr<std::vector<ColumnDesc>> column_desc;
};

struct CacheWrapper {
  std::vector<TKey> keys;
  std::vector<FbRow> rows;
};

class SDKHandler {
 public:
  StatusCode BatchGetTable(const std::vector<GridQuery>& querys);
  StatusCode Wait(std::vector<GridData>* resp, bool update_cache = true);

  void UpdateCache();
  void Clear();

 private:
  StatusCode Prepare(
      const std::vector<GridQuery>& querys, absl::flat_hash_map<std::string, DataWrapper>* data,
      absl::flat_hash_map<std::string, absl::flat_hash_map<int64_t, absl::InlinedVector<uint32_t, 4>>>*
          index_map,
      absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>>* rpc_helper,
      absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>>* ksn_rpc_helper);

  void TryGetFromCache(
    const std::vector<GridQuery>& querys,
    absl::flat_hash_map<std::string, DataWrapper>* data);

  void ParallelTryGetFromCache(
    const std::vector<GridQuery>& querys,
    absl::flat_hash_map<std::string, DataWrapper>* data);

  void BuildRequest(const std::vector<GridQuery>& querys,
                    absl::flat_hash_map<std::string, DataWrapper>* data);
  void Request(
    ks::kess::rpc::BatchWaiter* batch_waiter,
    absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>>* ksn_rpc_helper);


  void ParserResponse(GridResponse* response);

 private:
  absl::flat_hash_map<std::string, DataWrapper> data_;
  absl::flat_hash_map<std::string, CacheWrapper> to_cache_data_;
  // table: (key : index)  保存主键到位置的映射关系
  absl::flat_hash_map<std::string, absl::flat_hash_map<int64_t, absl::InlinedVector<uint32_t, 4>>> index_map_;
  std::vector<uint32_t> cache_flag_;
  std::vector<std::string> table_name_;

  absl::flat_hash_map<std::string, CacheInstance<FbRow>*> cache_map_;


  // table -> rpc_helper
  absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>> rpc_helper_;
  // ksn -> rpc_helper
  absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>> ksn_rpc_helper_;

  std::shared_ptr<ks::kess::rpc::BatchWaiter> batch_waiter_;
};

}  // namespace grid
}  // namespace ks
